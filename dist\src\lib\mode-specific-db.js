"use strict";
// jscpd:ignore-file
/**
 * Production database connection handler
 *
 * Provides database connections for the production environment.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.closeAllConnections = exports.getCurrentDbConnection = exports.getDbConnectionForMode = void 0;
const client_1 = require("@prisma/client");
const logger_1 = require("./logger");
const OperationalModeService_1 = require("../services/system/OperationalModeService");
// Connection pools for different modes
const connectionPools = {};
/**
 * Get a database connection for the specified operational mode
 *
 * @param mode Operational mode
 * @returns PrismaClient instance for the specified mode
 */
const getDbConnectionForMode = async (mode) => {
    try {
        // Check if connection already exists
        if (connectionPools[mode]) {
            return connectionPools[mode];
        }
        // Always use production connection
        connectionPools[mode] = new client_1.PrismaClient({
            log: ["error", "warn"],
            errorFormat: "minimal"
        });
        logger_1.logger.info(`Created database connection for ${mode} mode`);
        return connectionPools[mode];
    }
    catch (error) {
        logger_1.logger.error(`Error creating database connection for ${mode} mode:`, error);
        throw error;
    }
};
exports.getDbConnectionForMode = getDbConnectionForMode;
// Demo mode middleware has been removed - only production is supported
/**
 * Get the current database connection
 *
 * @returns PrismaClient instance for production
 */
const getCurrentDbConnection = async () => {
    try {
        // Always use production mode
        return await (0, exports.getDbConnectionForMode)(OperationalModeService_1.OperationalMode.PRODUCTION);
    }
    catch (error) {
        logger_1.logger.error("Error getting database connection:", error);
        // Fall back to the default connection
        return new client_1.PrismaClient({
            log: ["error", "warn"],
            errorFormat: "minimal"
        });
    }
};
exports.getCurrentDbConnection = getCurrentDbConnection;
/**
 * Close all database connections
 */
const closeAllConnections = async () => {
    try {
        // Close all connections
        for (const [mode, connection] of Object.entries(connectionPools)) {
            await connection.$disconnect();
            logger_1.logger.info(`Closed database connection for ${mode} mode`);
        }
        // Clear connection pools
        Object.keys(connectionPools).forEach(key);
    }
    finally { }
};
exports.closeAllConnections = closeAllConnections;
{
    delete connectionPools[key];
}
;
try { }
catch (error) {
    logger_1.logger.error("Error closing database connections:", error);
    throw error;
}
;
//# sourceMappingURL=mode-specific-db.js.map