{"file": "F:\\Amazing pay flow\\src\\shared\\modules\\utils\\responseUtils.ts", "mappings": ";AAAA;;;;GAIG;;;AAIH;;GAEG;AACI,MAAM,WAAW,GAAO,CAAC,GAAa,EAAE,OAAY,EAAE,EAAE,UAAkB,SAAS,EAAE,aAAqB,GAAG,EAAE,EAAE;IACtH,OAAO,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC;QACjC,OAAO,EAAE,IAAI;QACb,OAAO;QACP,IAAI;KACL,CAAC,CAAC;AACL,CAAC,CAAC;AANW,QAAA,WAAW,eAMtB;AAEF;;GAEG;AACI,MAAM,SAAS,GAAO,CAAC,GAAa,EAAE,UAAkB,OAAO,EAAE,aAAqB,GAAG,EAAE,QAAa,IAAI,EAAE,EAAE;IACrH,OAAO,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC;QACjC,OAAO,EAAE,KAAK;QACd,OAAO;QACP,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,CAAE,KAAe,CAAC,OAAO,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI;KAC1D,CAAC,CAAC;AACL,CAAC,CAAC;AANW,QAAA,SAAS,aAMpB;AAEF;;GAEG;AACI,MAAM,iBAAiB,GAAO,CAAC,OAAgB,EAAE,OAAe,EAAE,OAAY,IAAI,EAAE,QAAa,IAAI,EAAE,EAAE;IAC9G,OAAO;QACL,OAAO;QACP,OAAO;QACP,IAAI;QACJ,KAAK;KACN,CAAC;AACJ,CAAC,CAAC;AAPW,QAAA,iBAAiB,qBAO5B", "names": [], "sources": ["F:\\Amazing pay flow\\src\\shared\\modules\\utils\\responseUtils.ts"], "sourcesContent": ["/**\n * Response Utilities\n * \n * This module provides utility functions for handling API responses.\n */\n\nimport { Response } from 'express';\n\n/**\n * Send a success response\n */\nexport const sendSuccess: any =(res: Response, data: any = {}, message: string = 'Success', statusCode: number = 200) => {\n  return res.status(statusCode).json({\n    success: true,\n    message,\n    data\n  });\n};\n\n/**\n * Send an error response\n */\nexport const sendError: any =(res: Response, message: string = 'Error', statusCode: number = 500, error: any = null) => {\n  return res.status(statusCode).json({\n    success: false,\n    message,\n    error: error ? ((error as Error).message || error) : null\n  });\n};\n\n/**\n * Create a standard API response\n */\nexport const createApiResponse: any =(success: boolean, message: string, data: any = null, error: any = null) => {\n  return {\n    success,\n    message,\n    data,\n    error\n  };\n};"], "version": 3}