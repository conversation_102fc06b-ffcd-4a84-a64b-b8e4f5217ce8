"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.verifyPayment = exports.getVerificationMethodTypes = exports.getVerificationMethodsForPaymentMethod = exports.deleteVerificationMethod = exports.updateVerificationMethod = exports.createVerificationMethod = exports.getVerificationMethodById = exports.getAllVerificationMethods = void 0;
const verification_method_service_1 = require("../services/verification-method.service");
const error_middleware_1 = require("../middlewares/error.middleware");
const database_1 = __importDefault(require("../config/database"));
// Get all verification methods
exports.getAllVerificationMethods = (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const verificationMethods = await database_1.default.verificationMethod.findMany({
        include: { paymentMethod: {
                select: { id: true,
                    name: true,
                    type: true,
                    merchantId: true
                }
            }
        },
        orderBy: { createdAt: "desc" }
    });
    res.status(200).json(verificationMethods);
});
// Get verification method by ID
exports.getVerificationMethodById = (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const verificationMethod = await verification_method_service_1.VerificationMethodService.getVerificationMethodById(id);
    res.status(200).json(verificationMethod);
});
// Create a new verification method
exports.createVerificationMethod = (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const { name, type, paymentMethodId, isActive, verificationRequirements, internalSettings, displaySettings } = req.body;
    // Validate required fields
    if (!name || !type || !paymentMethodId) {
        throw new error_middleware_1.AppError({
            message: "Missing required fields",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }
    // Create verification method
    const verificationMethod = await verification_method_service_1.VerificationMethodService.createVerificationMethod({
        name,
        type,
        paymentMethodId,
        isActive,
        verificationRequirements,
        internalSettings,
        displaySettings
    });
    res.status(201).json(verificationMethod);
});
// Update verification method
exports.updateVerificationMethod = (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const { name, isActive, verificationRequirements, internalSettings, displaySettings } = req.body;
    // Update verification method
    const verificationMethod = await verification_method_service_1.VerificationMethodService.updateVerificationMethod(id, {
        name,
        isActive,
        verificationRequirements,
        internalSettings,
        displaySettings
    });
    res.status(200).json(verificationMethod);
});
// Delete verification method
exports.deleteVerificationMethod = (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const result = await verification_method_service_1.VerificationMethodService.deleteVerificationMethod(id);
    res.status(200).json(result);
});
// Get verification methods for payment method
exports.getVerificationMethodsForPaymentMethod = (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const { paymentMethodId } = req.params;
    const verificationMethods = await verification_method_service_1.VerificationMethodService.getVerificationMethodsForPaymentMethod(paymentMethodId);
    res.status(200).json(verificationMethods);
});
// Get verification method types
exports.getVerificationMethodTypes = (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const types = verification_method_service_1.VerificationMethodService.getVerificationMethodTypes();
    res.status(200).json(types);
});
// Verify payment
exports.verifyPayment = (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const { transactionId, verificationMethodId, verificationData } = req.body;
    // Validate required fields
    if (!transactionId || !verificationMethodId || !verificationData) {
        throw new error_middleware_1.AppError({
            message: "Missing required fields",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }
    // Verify payment
    const result = await verification_method_service_1.VerificationMethodService.verifyPayment({
        transactionId,
        verificationMethodId,
        verificationData
    });
    res.status(200).json(result);
});
//# sourceMappingURL=verification-method.controller.js.map