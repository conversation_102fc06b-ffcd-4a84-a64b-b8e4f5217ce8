{"date": "<PERSON><PERSON> May 27 2025 20:31:45 GMT+0300 (Eastern European Summer Time)", "error": {}, "exception": true, "level": "error", "message": "uncaughtException: Route.get() requires a callback function but got a [object Object]\nError: Route.get() requires a callback function but got a [object Object]\n    at Route.<computed> [as get] (F:\\Amazingpayflow\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at Function.proto.<computed> [as get] (F:\\Amazingpayflow\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (F:\\Amazingpayflow\\src\\routes\\health.routes.ts:169:8)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazingpayflow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-31067810263912876.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-31067810263912876.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-31067810263912876.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazingpayflow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)", "os": {"loadavg": [0, 0, 0], "uptime": 50987.234}, "process": {"argv": ["C:\\Program Files\\nodejs\\node.exe", "src/index.ts"], "cwd": "F:\\Amazingpayflow", "execPath": "C:\\Program Files\\nodejs\\node.exe", "gid": null, "memoryUsage": {"arrayBuffers": 159874, "external": 3422581, "heapTotal": 35979264, "heapUsed": 22445384, "rss": 71651328}, "pid": 9432, "uid": null, "version": "v20.19.1"}, "stack": "Error: Route.get() requires a callback function but got a [object Object]\n    at Route.<computed> [as get] (F:\\Amazingpayflow\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at Function.proto.<computed> [as get] (F:\\Amazingpayflow\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (F:\\Amazingpayflow\\src\\routes\\health.routes.ts:169:8)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazingpayflow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-31067810263912876.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-31067810263912876.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-31067810263912876.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazingpayflow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)", "trace": [{"column": 15, "file": "F:\\Amazingpayflow\\node_modules\\express\\lib\\router\\route.js", "function": "Route.<computed> [as get]", "line": 216, "method": "<computed> [as get]", "native": false}, {"column": 19, "file": "F:\\Amazingpayflow\\node_modules\\express\\lib\\router\\index.js", "function": "Function.proto.<computed> [as get]", "line": 521, "method": "<computed> [as get]", "native": false}, {"column": 8, "file": "F:\\Amazingpayflow\\src\\routes\\health.routes.ts", "function": null, "line": 169, "method": null, "native": false}, {"column": 14, "file": "node:internal/modules/cjs/loader", "function": "Module._compile", "line": 1529, "method": "_compile", "native": false}, {"column": 25, "file": "F:\\Amazingpayflow\\node_modules\\source-map-support\\source-map-support.js", "function": "Module._compile", "line": 521, "method": "_compile", "native": false}, {"column": 33, "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-31067810263912876.js", "function": "Module.m._compile", "line": 69, "method": "_compile", "native": false}, {"column": 10, "file": "node:internal/modules/cjs/loader", "function": "Module._extensions..js", "line": 1613, "method": ".js", "native": false}, {"column": 20, "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-31067810263912876.js", "function": "require.extensions..jsx.require.extensions..js", "line": 114, "method": ".js", "native": false}, {"column": 20, "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-31067810263912876.js", "function": "require.extensions.<computed>", "line": 71, "method": "<computed>", "native": false}, {"column": 13, "file": "F:\\Amazingpayflow\\node_modules\\ts-node-dev\\lib\\hook.js", "function": "Object.nodeDevHook [as .ts]", "line": 63, "method": "ts]", "native": false}]}