// jscpd:ignore-file
import { Router } from "express";
import { PushNotificationController } from "../controllers/refactored/push-notification.controller.ts";
import { authenticate } from '../middlewares/auth';
import { PushNotificationController } from "../controllers/refactored/push-notification.controller.ts";
import { authenticate } from '../middlewares/auth';

const router: unknown =Router();

// Public routes
router.get("/vapid-public-key", PushNotificationController.getPublicKey);

// Protected routes
router.post("/subscribe", authenticate, PushNotificationController.subscribe);
router.post("/unsubscribe", authenticate, PushNotificationController.unsubscribe);
router.post("/test", authenticate, PushNotificationController.sendTest);
router.get("/subscriptions", authenticate, PushNotificationController.getUserSubscriptions);
router.get("/merchant-subscriptions", authenticate, PushNotificationController.getMerchantSubscriptions);
router.delete("/subscriptions/:id", authenticate, PushNotificationController.deleteSubscription);

export default router;
