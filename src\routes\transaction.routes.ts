// jscpd:ignore-file
import express from "express";
import { authMiddleware as authenticate, authorize } from '../middlewares/auth.middleware';
import { Merchant } from '../types';
import {
    getAllTransactions,
    getTransactionById,
    getTransactionByReference,
    createTransaction,
    updateTransactionStatus,
    verifyPayment,
    getMerchantTransactions,
    getTransactionStats
} from "../controllers/refactored/transaction.controller.ts";
import { authMiddleware as authenticate, authorize } from '../middlewares/auth.middleware';
import { Merchant } from '../types';


const router: any =express.Router();

// All routes require authentication
router.use(authenticate);

// Routes accessible by ADMIN and SUPER_ADMIN
router.get("/", authorize(["ADMIN", "SUPER_ADMIN"]), getAllTransactions);
router.get("/stats", authorize(["ADMIN", "SUPER_ADMIN"]), getTransactionStats);

// Routes accessible by ADMIN, SUPER_ADMIN, and MERCHANT
router.get("/:id", authorize(["ADMIN", "SUPER_ADMIN", "MERCHANT"]), getTransactionById);
router.get("/reference/:reference", authorize(["ADMIN", "SUPER_ADMIN", "MERCHANT"]), getTransactionByReference);
router.post("/", authorize(["ADMIN", "SUPER_ADMIN", "MERCHANT"]), createTransaction);
router.put("/:id/status", authorize(["ADMIN", "SUPER_ADMIN", "MERCHANT"]), updateTransactionStatus);
router.post("/verify", authorize(["ADMIN", "SUPER_ADMIN", "MERCHANT"]), verifyPayment);

// Merchant-specific routes
router.get("/merchant/:merchantId", authorize(["ADMIN", "SUPER_ADMIN", "MERCHANT"]), getMerchantTransactions);
router.get("/merchant/:merchantId/stats", authorize(["ADMIN", "SUPER_ADMIN", "MERCHANT"]), getTransactionStats);

export default router;
