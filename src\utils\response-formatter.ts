// jscpd:ignore-file
/**
 * Response Formatter Utility
 * 
 * This utility provides standardized response formatting for the application.
 * It includes functions for formatting success and error responses.
 */

import { Request, Response, NextFunction } from 'express';
import { isProduction } from './environment-validator';
import { isProduction } from './environment-validator';

/**
 * Success response structure
 */
export interface SuccessResponse<T> {
  status: string;
  statusCode: number;
  message?: string;
  data: T;
  timestamp: string;
  requestId?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

/**
 * Send success response
 * @param res Express response
 * @param data Response data
 * @param message Success message
 * @param statusCode HTTP status code
 * @param pagination Pagination information
 */
export const sendSuccess: any =<T>(
  res: Response,
  data: T,
  message?: string,
  statusCode: number = 200,
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  }
) => {
  // Create success response
  const successResponse: SuccessResponse<T> = {
    status: 'success',
    statusCode,
    data,
    timestamp: new Date().toISOString()
  };

  // Add message if provided
  if (message) {
    successResponse.message = message;
  }

  // Add request ID if available
  if (res.locals.requestId) {
    successResponse.requestId = res.locals.requestId;
  }

  // Add pagination if provided
  if (pagination) {
    successResponse.pagination = pagination;
  }

  // Send success response
  res.status(statusCode).json(successResponse);
};

/**
 * Send created response
 * @param res Express response
 * @param data Response data
 * @param message Success message
 */
export const sendCreated: any =<T>(
  res: Response,
  data: T,
  message: string = 'Resource created successfully'
) => {
  sendSuccess(res, data, message, 201);
};

/**
 * Send no content response
 * @param res Express response
 */
export const sendNoContent: any =(res: Response) => {
  res.status(204).end();
};

/**
 * Send accepted response
 * @param res Express response
 * @param message Success message
 */
export const sendAccepted: any =(
  res: Response,
  message: string = 'Request accepted for processing'
) => {
  sendSuccess(res, null, message, 202);
};

/**
 * Send paginated response
 * @param res Express response
 * @param data Response data
 * @param page Current page
 * @param limit Items per page
 * @param total Total items
 * @param message Success message
 */
export const sendPaginated: any =<T>(
  res: Response,
  data: T[],
  page: number,
  limit: number,
  total: number,
  message?: string
) => {
  const totalPages = Math.ceil(total / limit);
  
  sendSuccess(
    res,
    data,
    message,
    200,
    {
      page,
      limit,
      total,
      totalPages
    }
  );
};

/**
 * Format API response for controllers
 * @param res Express response
 */
export const formatApiResponse: any =(res: Response) => {
  return {
    success: <T>(data: T, message?: string, statusCode: number = 200) => 
      sendSuccess(res, data, message, statusCode),
    
    created: <T>(data: T, message?: string) => 
      sendCreated(res, data, message),
    
    noContent: () => 
      sendNoContent(res),
    
    accepted: (message?: string) => 
      sendAccepted(res, message),
    
    paginated: <T>(data: T[], page: number, limit: number, total: number, message?: string) => 
      sendPaginated(res, data, page, limit, total, message)
  };
};

export default {
  sendSuccess,
  sendCreated,
  sendNoContent,
  sendAccepted,
  sendPaginated,
  formatApiResponse
};
