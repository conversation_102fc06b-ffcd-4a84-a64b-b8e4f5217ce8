"use strict";
/**
 * Identity Verification Controller
 *
 * Modular controller for identity verification operations.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IdentityVerificationController = void 0;
const base_controller_1 = require("../base.controller");
const asyncHandler_1 = require("../../utils/asyncHandler");
const identity_verification_1 = require("../../services/identity-verification");
const prisma_1 = __importDefault(require("../../lib/prisma"));
const IdentityVerificationAuthService_1 = require("./services/IdentityVerificationAuthService");
const IdentityVerificationValidationService_1 = require("./services/IdentityVerificationValidationService");
const IdentityVerificationResponseMapper_1 = require("./mappers/IdentityVerificationResponseMapper");
/**
 * Modular Identity Verification Controller
 */
class IdentityVerificationController extends base_controller_1.BaseController {
    constructor() {
        super();
        /**
         * Verify identity using Ethereum signature
         */
        this.verifyEthereumSignature = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Authorization
                const authContext = this.authService.createAuthorizationContext(req.user, 'verification', 'create');
                const permission = await this.authService.checkPermission(authContext);
                if (!permission.allowed) {
                    this.authService.handleAuthorizationError(permission);
                }
                // Validation
                this.validationService.validateEthereumSignature(req.body);
                this.authService.extractUserContext(req);
                // Business logic
                const result = await this.identityService.verifyEthereumSignature({
                    address: validatedData.address,
                    message: validatedData.message,
                    signature: validatedData.signature,
                    userId,
                    merchantId,
                });
                // Response
                IdentityVerificationResponseMapper_1.IdentityVerificationResponseMapper.sendVerificationResult(res, result);
            }
            catch (error) {
                IdentityVerificationResponseMapper_1.IdentityVerificationResponseMapper.sendError(res, error);
            }
        });
        /**
         * Verify identity using ERC-1484
         */
        this.verifyERC1484Identity = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Authorization
                const authContext = this.authService.createAuthorizationContext(req.user, 'verification', 'create');
                const permission = await this.authService.checkPermission(authContext);
                if (!permission.allowed) {
                    this.authService.handleAuthorizationError(permission);
                }
                // Validation
                this.validationService.validateERC1484Identity(req.body);
                this.authService.extractUserContext(req);
                // Business logic - Placeholder for ERC1484 verification
                const result = {
                    success: false,
                    message: 'ERC1484 verification not yet implemented',
                    verificationId: 'placeholder',
                    method: 'ERC1484',
                };
                // Response
                IdentityVerificationResponseMapper_1.IdentityVerificationResponseMapper.sendVerificationResult(res, result);
            }
            catch (error) {
                IdentityVerificationResponseMapper_1.IdentityVerificationResponseMapper.sendError(res, error);
            }
        });
        /**
         * Verify identity using ERC-725
         */
        this.verifyERC725Identity = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Authorization
                const authContext = this.authService.createAuthorizationContext(req.user, 'verification', 'create');
                const permission = await this.authService.checkPermission(authContext);
                if (!permission.allowed) {
                    this.authService.handleAuthorizationError(permission);
                }
                // Validation
                this.validationService.validateERC725Identity(req.body);
                this.authService.extractUserContext(req);
                // Business logic - Placeholder for ERC725 verification
                const result = {
                    success: false,
                    message: 'ERC725 verification not yet implemented',
                    verificationId: 'placeholder',
                    method: 'ERC725',
                };
                // Response
                IdentityVerificationResponseMapper_1.IdentityVerificationResponseMapper.sendVerificationResult(res, result);
            }
            catch (error) {
                IdentityVerificationResponseMapper_1.IdentityVerificationResponseMapper.sendError(res, error);
            }
        });
        /**
         * Verify identity using ENS
         */
        this.verifyENS = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Authorization
                const authContext = this.authService.createAuthorizationContext(req.user, 'verification', 'create');
                const permission = await this.authService.checkPermission(authContext);
                if (!permission.allowed) {
                    this.authService.handleAuthorizationError(permission);
                }
                // Validation
                this.validationService.validateENSVerification(req.body);
                this.authService.extractUserContext(req);
                // Business logic - Placeholder for ENS verification
                const result = {
                    success: false,
                    message: 'ENS verification not yet implemented',
                    verificationId: 'placeholder',
                    method: 'ENS',
                };
                // Response
                IdentityVerificationResponseMapper_1.IdentityVerificationResponseMapper.sendVerificationResult(res, result);
            }
            catch (error) {
                IdentityVerificationResponseMapper_1.IdentityVerificationResponseMapper.sendError(res, error);
            }
        });
        /**
         * Verify identity using Polygon ID
         */
        this.verifyPolygonID = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Authorization
                const authContext = this.authService.createAuthorizationContext(req.user, 'verification', 'create');
                const permission = await this.authService.checkPermission(authContext);
                if (!permission.allowed) {
                    this.authService.handleAuthorizationError(permission);
                }
                // Validation
                this.validationService.validatePolygonID(req.body);
                this.authService.extractUserContext(req);
                // Business logic - Placeholder for Polygon ID verification
                const result = {
                    success: false,
                    message: 'Polygon ID verification not yet implemented',
                    verificationId: 'placeholder',
                    method: 'PolygonID',
                };
                // Response
                IdentityVerificationResponseMapper_1.IdentityVerificationResponseMapper.sendVerificationResult(res, result);
            }
            catch (error) {
                IdentityVerificationResponseMapper_1.IdentityVerificationResponseMapper.sendError(res, error);
            }
        });
        /**
         * Verify identity using Worldcoin
         */
        this.verifyWorldcoin = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Authorization
                const authContext = this.authService.createAuthorizationContext(req.user, 'verification', 'create');
                const permission = await this.authService.checkPermission(authContext);
                if (!permission.allowed) {
                    this.authService.handleAuthorizationError(permission);
                }
                // Validation
                this.validationService.validateWorldcoin(req.body);
                this.authService.extractUserContext(req);
                // Business logic - Placeholder for Worldcoin verification
                const result = {
                    success: false,
                    message: 'Worldcoin verification not yet implemented',
                    verificationId: 'placeholder',
                    method: 'Worldcoin',
                };
                // Response
                IdentityVerificationResponseMapper_1.IdentityVerificationResponseMapper.sendVerificationResult(res, result);
            }
            catch (error) {
                IdentityVerificationResponseMapper_1.IdentityVerificationResponseMapper.sendError(res, error);
            }
        });
        /**
         * Verify identity using Unstoppable Domains
         */
        this.verifyUnstoppableDomains = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Authorization
                const authContext = this.authService.createAuthorizationContext(req.user, 'verification', 'create');
                const permission = await this.authService.checkPermission(authContext);
                if (!permission.allowed) {
                    this.authService.handleAuthorizationError(permission);
                }
                // Validation
                this.validationService.validateUnstoppableDomains(req.body);
                this.authService.extractUserContext(req);
                // Business logic - Placeholder for Unstoppable Domains verification
                const result = {
                    success: false,
                    message: 'Unstoppable Domains verification not yet implemented',
                    verificationId: 'placeholder',
                    method: 'UnstoppableDomains',
                };
                // Response
                IdentityVerificationResponseMapper_1.IdentityVerificationResponseMapper.sendVerificationResult(res, result);
            }
            catch (error) {
                IdentityVerificationResponseMapper_1.IdentityVerificationResponseMapper.sendError(res, error);
            }
        });
        /**
         * Get verification by ID
         */
        this.getVerificationById = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Authorization
                const authContext = this.authService.createAuthorizationContext(req.user, 'verification', 'read', req.params.id);
                const permission = await this.authService.checkPermission(authContext);
                if (!permission.allowed) {
                    this.authService.handleAuthorizationError(permission);
                }
                // Validation
                const verificationId = this.validationService.validateId(req.params.id, 'Verification ID');
                // Business logic
                const verification = await this.identityService.getVerificationById(verificationId);
                // Response
                const transformedVerification = IdentityVerificationResponseMapper_1.IdentityVerificationResponseMapper.transformVerification(verification);
                IdentityVerificationResponseMapper_1.IdentityVerificationResponseMapper.sendVerification(res, transformedVerification);
            }
            catch (error) {
                IdentityVerificationResponseMapper_1.IdentityVerificationResponseMapper.sendError(res, error);
            }
        });
        /**
         * Get verifications for user
         */
        this.getVerificationsForUser = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Authorization
                const authContext = this.authService.createAuthorizationContext(req.user, 'verification', 'read');
                const permission = await this.authService.checkPermission(authContext);
                if (!permission.allowed) {
                    this.authService.handleAuthorizationError(permission);
                }
                // Get user ID
                const { userId } = this.authService.extractUserContext(req);
                if (!userId) {
                    throw new Error('User ID is required');
                }
                // Business logic
                const verifications = await this.identityService.getVerificationsForUser(userId);
                // Response
                const transformedVerifications = verifications.map(IdentityVerificationResponseMapper_1.IdentityVerificationResponseMapper.transformVerification);
                IdentityVerificationResponseMapper_1.IdentityVerificationResponseMapper.sendVerificationsList(res, transformedVerifications, transformedVerifications.length);
            }
            catch (error) {
                IdentityVerificationResponseMapper_1.IdentityVerificationResponseMapper.sendError(res, error);
            }
        });
        /**
         * Health check endpoint
         */
        this.healthCheck = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                IdentityVerificationResponseMapper_1.IdentityVerificationResponseMapper.sendSuccess(res, {
                    status: 'healthy',
                    timestamp: new Date(),
                    version: '1.0.0',
                    services: {
                        authorization: 'active',
                        validation: 'active',
                        identityService: 'active',
                    },
                }, 'Identity Verification Controller is healthy');
            }
            catch (error) {
                IdentityVerificationResponseMapper_1.IdentityVerificationResponseMapper.sendError(res, error);
            }
        });
        this.authService = new IdentityVerificationAuthService_1.IdentityVerificationAuthService();
        this.validationService = new IdentityVerificationValidationService_1.IdentityVerificationValidationService();
        this.identityService = new identity_verification_1.IdentityVerificationService(prisma_1.default);
    }
}
exports.IdentityVerificationController = IdentityVerificationController;
//# sourceMappingURL=IdentityVerificationController.js.map