/**
 * Payment Routing Controller
 *
 * This controller handles payment routing-related API endpoints.
 */
import { Request, Response } from 'express';
import { BaseController } from './base.controller';
/**
 * Payment routing controller
 */
export declare class PaymentRoutingController extends BaseController {
    private paymentRoutingService;
    private prisma;
    constructor();
    /**
     * Get optimal payment method for a transaction
     * @param req Request
     * @param res Response
     */
    getOptimalPaymentMethod: (req: Request, res: Response) => Promise<void>;
    /**
     * Record routing decision
     * @param req Request
     * @param res Response
     */
    recordRoutingDecision: (req: Request, res: Response) => Promise<void>;
    /**
     * Create routing rule
     * @param req Request
     * @param res Response
     */
    createRoutingRule: (req: Request, res: Response) => Promise<void>;
    /**
     * Get routing rules for merchant
     * @param req Request
     * @param res Response
     */
    getRoutingRules: (req: Request, res: Response) => Promise<void>;
    /**
     * Update routing rule
     * @param req Request
     * @param res Response
     */
    updateRoutingRule: (req: Request, res: Response) => Promise<void>;
    /**
     * Delete routing rule
     * @param req Request
     * @param res Response
     */
    deleteRoutingRule: (req: Request, res: Response) => Promise<void>;
    /**
     * Get payment method metrics
     * @param req Request
     * @param res Response
     */
    getPaymentMethodMetrics: (req: Request, res: Response) => Promise<void>;
}
declare const _default: PaymentRoutingController;
export default _default;
//# sourceMappingURL=payment-routing.controller.d.ts.map