// jscpd:ignore-file
import { Request, Response, NextFunction } from 'express';
import { BaseController } from "../base/BaseController";
import { AlertAnalyticsService } from "../../services/alert-analytics.service";
import { asyncHandler } from "../../utils/asyncHandler";
import { AppError } from "../../utils/appError";
import { Alert } from '../types';
import { BaseController } from "../base/BaseController";
import { AlertAnalyticsService } from "../../services/alert-analytics.service";
import { asyncHandler } from "../../utils/asyncHandler";
import { AppError } from "../../utils/appError";
import { Alert } from '../types';


/**
 * Alert analytics controller
 */
export class AlertAnalyticsController extends BaseController {
  private analyticsService: AlertAnalyticsService;

  constructor() {
    super();
    this.analyticsService = new AlertAnalyticsService();
  }

  /**
   * Get alert count by status
   * @route GET /api/alerts/analytics/count-by-status
   */
  getAlertCountByStatus = asyncHandler(async (req: Request, res: Response) => {
    // Check authorization
    const { userRole, merchantId } = this.checkAuthorization(req);

    // Parse date range
    const { startDate, endDate } = this.parseDateRange(
      req.query.startDate as string,
      req.query.endDate as string
    );

    // Determine target merchant ID
    const targetMerchantId: any =this.determineTargetMerchantId(
      userRole,
      merchantId,
      req.query.merchantId as string
    );

    // Get alert count by status
    const data: any =await this.analyticsService.getAlertCountByStatus(
      startDate,
      endDate,
      targetMerchantId
    );

    // Return data
    return this.sendSuccess(res, data);
  });

  /**
   * Get alert count by severity
   * @route GET /api/alerts/analytics/count-by-severity
   */
  getAlertCountBySeverity = asyncHandler(async (req: Request, res: Response) => {
    // Check authorization
    const { userRole, merchantId } = this.checkAuthorization(req);

    // Parse date range
    const { startDate, endDate } = this.parseDateRange(
      req.query.startDate as string,
      req.query.endDate as string
    );

    // Determine target merchant ID
    const targetMerchantId: any =this.determineTargetMerchantId(
      userRole,
      merchantId,
      req.query.merchantId as string
    );

    // Get alert count by severity
    const data: any =await this.analyticsService.getAlertCountBySeverity(
      startDate,
      endDate,
      targetMerchantId
    );

    // Return data
    return this.sendSuccess(res, data);
  });

  /**
   * Get alert count by type
   * @route GET /api/alerts/analytics/count-by-type
   */
  getAlertCountByType = asyncHandler(async (req: Request, res: Response) => {
    // Check authorization
    const { userRole, merchantId } = this.checkAuthorization(req);

    // Parse date range
    const { startDate, endDate } = this.parseDateRange(
      req.query.startDate as string,
      req.query.endDate as string
    );

    // Determine target merchant ID
    const targetMerchantId: any =this.determineTargetMerchantId(
      userRole,
      merchantId,
      req.query.merchantId as string
    );

    // Get alert count by type
    const data: any =await this.analyticsService.getAlertCountByType(
      startDate,
      endDate,
      targetMerchantId
    );

    // Return data
    return this.sendSuccess(res, data);
  });

  /**
   * Get alert count by day
   * @route GET /api/alerts/analytics/count-by-day
   */
  getAlertCountByDay = asyncHandler(async (req: Request, res: Response) => {
    // Check authorization
    const { userRole, merchantId } = this.checkAuthorization(req);

    // Parse date range
    const { startDate, endDate } = this.parseDateRange(
      req.query.startDate as string,
      req.query.endDate as string
    );

    // Determine target merchant ID
    const targetMerchantId: any =this.determineTargetMerchantId(
      userRole,
      merchantId,
      req.query.merchantId as string
    );

    // Get alert count by day
    const data: any =await this.analyticsService.getAlertCountByDay(
      startDate,
      endDate,
      targetMerchantId
    );

    // Return data
    return this.sendSuccess(res, data);
  });

  /**
   * Get alert count by hour
   * @route GET /api/alerts/analytics/count-by-hour
   */
  getAlertCountByHour = asyncHandler(async (req: Request, res: Response) => {
    // Check authorization
    const { userRole, merchantId } = this.checkAuthorization(req);

    // Parse date
    const date: any =this.parseDate(req.query.date as string, "date");

    // Determine target merchant ID
    const targetMerchantId: any =this.determineTargetMerchantId(
      userRole,
      merchantId,
      req.query.merchantId as string
    );

    // Get alert count by hour
    const data: any =await this.analyticsService.getAlertCountByHour(
      date,
      targetMerchantId
    );

    // Return data
    return this.sendSuccess(res, data);
  });

  /**
   * Get top merchants by alert count
   * @route GET /api/alerts/analytics/top-merchants
   */
  getTopMerchantsByAlertCount = asyncHandler(async (req: Request, res: Response) => {
    // Check authorization
    const { userRole } = this.checkAuthorization(req);

    // Only admins can access this endpoint
    this.checkAdminRole(userRole);

    // Parse date range
    const { startDate, endDate } = this.parseDateRange(
      req.query.startDate as string,
      req.query.endDate as string
    );

    // Parse limit
    const limit: any =this.parseInteger(req.query.limit as string, "limit", 10);

    // Get top merchants by alert count
    const data: any =await this.analyticsService.getTopMerchantsByAlertCount(
      startDate,
      endDate,
      limit
    );

    // Return data
    return this.sendSuccess(res, data);
  });

  /**
   * Get alert resolution time statistics
   * @route GET /api/alerts/analytics/resolution-time
   */
  getAlertResolutionTimeStats = asyncHandler(async (req: Request, res: Response) => {
    // Check authorization
    const { userRole, merchantId } = this.checkAuthorization(req);

    // Parse date range
    const { startDate, endDate } = this.parseDateRange(
      req.query.startDate as string,
      req.query.endDate as string
    );

    // Determine target merchant ID
    const targetMerchantId: any =this.determineTargetMerchantId(
      userRole,
      merchantId,
      req.query.merchantId as string
    );

    // Get alert resolution time statistics
    const data: any =await this.analyticsService.getAlertResolutionTimeStats(
      startDate,
      endDate,
      targetMerchantId
    );

    // Return data
    return this.sendSuccess(res, data);
  });

  /**
   * Get alert trends
   * @route GET /api/alerts/analytics/trends
   */
  getAlertTrends = asyncHandler(async (req: Request, res: Response) => {
    // Check authorization
    const { userRole, merchantId } = this.checkAuthorization(req);

    // Parse days
    const days: any =this.parseInteger(req.query.days as string, "days", 30);

    // Determine target merchant ID
    const targetMerchantId: any =this.determineTargetMerchantId(
      userRole,
      merchantId,
      req.query.merchantId as string
    );

    // Get alert trends
    const data: any =await this.analyticsService.getAlertTrends(
      days,
      targetMerchantId
    );

    // Return data
    return this.sendSuccess(res, data);
  });
}
