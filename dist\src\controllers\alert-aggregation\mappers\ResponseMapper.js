"use strict";
/**
 * Response Mapper
 *
 * Handles response formatting for alert aggregation operations.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResponseMapper = void 0;
const AppError_1 = require("../../../utils/errors/AppError");
/**
 * Response mapper for alert aggregation
 */
class ResponseMapper {
    /**
     * Send success response
     */
    static sendSuccess(res, data, message, statusCode = 200, pagination) {
        const response = {
            success: true,
            data,
            message,
            pagination,
            timestamp: new Date(),
            requestId: res.locals.requestId ?? 'unknown',
        };
        res.status(statusCode).json(response);
    }
    /**
     * Send error response
     */
    static sendError(res, error, statusCode) {
        let errorResponse;
        if (error instanceof AppError_1.AppError) {
            errorResponse = {
                success: false,
                error: {
                    message: error.message,
                    code: error.code,
                    type: error.type,
                    details: error.details,
                },
                timestamp: new Date(),
                requestId: res.locals.requestId ?? 'unknown',
            };
            statusCode = statusCode ?? error.statusCode ?? 400;
        }
        else {
            errorResponse = {
                success: false,
                error: {
                    message: error.message || 'Internal server error',
                    code: 'INTERNAL_SERVER_ERROR',
                    type: 'INTERNAL',
                },
                timestamp: new Date(),
                requestId: res.locals.requestId ?? 'unknown',
            };
            statusCode = statusCode ?? 500;
        }
        res.status(statusCode).json(errorResponse);
    }
    /**
     * Send aggregation rules list response
     */
    static sendAggregationRulesList(res, rules, total, page = 1, limit = 10) {
        const totalPages = Math.ceil(total / limit);
        this.sendSuccess(res, rules, `Retrieved ${rules.length} aggregation rules`, 200, {
            page,
            limit,
            total,
            totalPages,
        });
    }
    /**
     * Send single aggregation rule response
     */
    static sendAggregationRule(res, rule, message) {
        this.sendSuccess(res, rule, message ?? 'Aggregation rule retrieved successfully');
    }
    /**
     * Send aggregation rule created response
     */
    static sendAggregationRuleCreated(res, rule) {
        this.sendSuccess(res, rule, 'Aggregation rule created successfully', 201);
    }
    /**
     * Send aggregation rule updated response
     */
    static sendAggregationRuleUpdated(res, rule) {
        this.sendSuccess(res, rule, 'Aggregation rule updated successfully');
    }
    /**
     * Send aggregation rule deleted response
     */
    static sendAggregationRuleDeleted(res) {
        this.sendSuccess(res, null, 'Aggregation rule deleted successfully');
    }
    /**
     * Send correlation rules list response
     */
    static sendCorrelationRulesList(res, rules, total, page = 1, limit = 10) {
        const totalPages = Math.ceil(total / limit);
        this.sendSuccess(res, rules, `Retrieved ${rules.length} correlation rules`, 200, {
            page,
            limit,
            total,
            totalPages,
        });
    }
    /**
     * Send single correlation rule response
     */
    static sendCorrelationRule(res, rule, message) {
        this.sendSuccess(res, rule, message ?? 'Correlation rule retrieved successfully');
    }
    /**
     * Send correlation rule created response
     */
    static sendCorrelationRuleCreated(res, rule) {
        this.sendSuccess(res, rule, 'Correlation rule created successfully', 201);
    }
    /**
     * Send correlation rule updated response
     */
    static sendCorrelationRuleUpdated(res, rule) {
        this.sendSuccess(res, rule, 'Correlation rule updated successfully');
    }
    /**
     * Send correlation rule deleted response
     */
    static sendCorrelationRuleDeleted(res) {
        this.sendSuccess(res, null, 'Correlation rule deleted successfully');
    }
    /**
     * Send validation error response
     */
    static sendValidationError(res, errors, message = 'Validation failed') {
        const error = new AppError_1.AppError({
            message,
            type: 'VALIDATION',
            code: 'INVALID_INPUT',
            details: { errors },
        });
        this.sendError(res, error, 400);
    }
    /**
     * Send authorization error response
     */
    static sendAuthorizationError(res, message = 'Access denied', requiredRole) {
        const error = new AppError_1.AppError({
            message,
            type: 'AUTHENTICATION',
            code: 'INVALID_CREDENTIALS',
            details: { requiredRole },
        });
        this.sendError(res, error, 403);
    }
    /**
     * Send not found error response
     */
    static sendNotFoundError(res, resource = 'Resource') {
        const error = new AppError_1.AppError({
            message: `${resource} not found`,
            type: 'NOT_FOUND',
            code: 'RESOURCE_NOT_FOUND',
        });
        this.sendError(res, error, 404);
    }
    /**
     * Send internal server error response
     */
    static sendInternalServerError(res, message = 'Internal server error') {
        const error = new AppError_1.AppError({
            message,
            type: 'INTERNAL',
            code: 'INTERNAL_SERVER_ERROR',
        });
        this.sendError(res, error, 500);
    }
    /**
     * Format pagination metadata
     */
    static formatPagination(page, limit, total) {
        const totalPages = Math.ceil(total / limit);
        return {
            page,
            limit,
            total,
            totalPages,
            hasNext: page < totalPages,
            hasPrev: page > 1,
        };
    }
    /**
     * Create API response wrapper
     */
    static createApiResponse(success, data, message, error) {
        return {
            success,
            data,
            message,
            error,
        };
    }
    /**
     * Handle async controller method
     */
    static asyncHandler(fn) {
        return (req, res, next) => {
            Promise.resolve(fn(req, res, next)).catch((error) => next(error));
        };
    }
    /**
     * Set response headers for API
     */
    static setApiHeaders(res) {
        res.setHeader('Content-Type', 'application/json');
        res.setHeader('X-API-Version', '1.0');
        res.setHeader('X-Response-Time', Date.now());
    }
    /**
     * Log response for debugging
     */
    static logResponse(method, url, statusCode, responseTime) {
        console.log(`${method} ${url} - ${statusCode} - ${responseTime}ms`);
    }
}
exports.ResponseMapper = ResponseMapper;
//# sourceMappingURL=ResponseMapper.js.map