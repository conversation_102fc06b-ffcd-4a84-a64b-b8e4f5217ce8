import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function seedReportTemplates(): Promise<void> {
  // Get admin user
  const adminUser = await prisma.user.findFirst({
    where: {
      role: 'ADMIN',
    },
  });

  if (!adminUser) {
    console.error('Admin user not found. Cannot seed report templates.');
    return;
  }

  // Create system report templates
  const reportTemplates = [
    {
      name: 'Monthly Transaction Summary',
      description: 'Summary of all transactions for the month',
      type: 'TRANSACTION',
      config: {
        columns: [
          'reference',
          'amount',
          'currency',
          'status',
          'paymentMethod',
          'merchantName',
          'createdAt',
        ],
        groupBy: [],
        sortBy: 'createdAt',
        sortDirection: 'desc',
      },
      isSystem: true,
      createdById: adminUser.id,
    },
    {
      name: 'Customer Activity Report',
      description: 'Activity report for all customers',
      type: 'CUSTOMER',
      config: {
        columns: ['email', 'firstName', 'lastName', 'status', 'createdAt'],
        groupBy: [],
        sortBy: 'createdAt',
        sortDirection: 'desc',
      },
      isSystem: true,
      createdById: adminUser.id,
    },
    {
      name: 'Payment Method Analysis',
      description: 'Analysis of payment methods used',
      type: 'PAYMENT_METHOD',
      config: {
        columns: [
          'type',
          'last4',
          'expiryMonth',
          'expiryYear',
          'isDefault',
          'customerEmail',
          'createdAt',
        ],
        groupBy: ['type'],
        sortBy: 'type',
        sortDirection: 'asc',
      },
      isSystem: true,
      createdById: adminUser.id,
    },
    {
      name: 'Subscription Status Report',
      description: 'Status report for all subscriptions',
      type: 'SUBSCRIPTION',
      config: {
        columns: [
          'status',
          'startDate',
          'endDate',
          'nextBillingDate',
          'planName',
          'amount',
          'currency',
          'customerEmail',
        ],
        groupBy: ['status'],
        sortBy: 'nextBillingDate',
        sortDirection: 'asc',
      },
      isSystem: true,
      createdById: adminUser.id,
    },
  ];

  // Create report templates
  for (const template of reportTemplates) {
    // Check if template already exists
    const existingTemplate = await prisma.reportTemplate.findFirst({
      where: {
        name: template.name,
        createdById: adminUser.id,
      },
    });

    if (existingTemplate) {
      // Update existing template
      await prisma.reportTemplate.update({
        where: {
          id: existingTemplate.id,
        },
        data: template,
      });
    } else {
      // Create new template
      await prisma.reportTemplate.create({
        data: template,
      });
    }
  }

  console.log(`Seeded ${reportTemplates.length} report templates`);
}
