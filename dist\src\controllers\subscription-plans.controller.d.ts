import { Request, Response } from "express";
declare class SubscriptionPlansController {
    getAllPlans(req: Request, res: Response): Promise<Response<any, Record<string, any>>>;
    getPlanById(req: Request, res: Response): Promise<Response<any, Record<string, any>>>;
    createPlan(req: Request, res: Response): Promise<Response<any, Record<string, any>>>;
    updatePlan(req: Request, res: Response): Promise<Response<any, Record<string, any>>>;
    deletePlan(req: Request, res: Response): Promise<Response<any, Record<string, any>>>;
}
declare const _default: SubscriptionPlansController;
export default _default;
//# sourceMappingURL=subscription-plans.controller.d.ts.map