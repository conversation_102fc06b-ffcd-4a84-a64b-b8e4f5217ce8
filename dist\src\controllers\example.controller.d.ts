import { Request, Response, NextFunction } from "express";
import { BaseController } from "./base.controller";
/**
 * Example controller
 */
export declare class ExampleController extends BaseController {
    private exampleService;
    constructor();
    /**
   * Get all examples
   */
    getAll: (req: Request, res: Response, next: NextFunction) => void;
    /**
   * Get example by ID
   */
    getById: (req: Request, res: Response, next: NextFunction) => void;
    /**
   * Create example
   */
    create: (req: Request, res: Response, next: NextFunction) => void;
    /**
   * Update example
   */
    update: (req: Request, res: Response, next: NextFunction) => void;
    /**
   * Delete example
   */
    delete: (req: Request, res: Response, next: NextFunction) => void;
}
//# sourceMappingURL=example.controller.d.ts.map