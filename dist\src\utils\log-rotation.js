"use strict";
// jscpd:ignore-file
/**
 * Log Rotation Utility
 *
 * This utility handles log rotation to prevent log files from growing too large
 * and to maintain a history of logs for a specified period.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.scheduleLogRotation = exports.compressOldLogs = exports.cleanupOldLogs = void 0;
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const date_fns_1 = require("date-fns");
const logger_1 = require("../lib/logger");
const environment_1 = require("../config/environment");
// Configuration
const MAX_LOG_AGE_DAYS = 30; // Maximum age of log files in days
// Get environment-specific logs directory
const getLogDirectory = () => {
    const env = (0, environment_1.getEnvironment)();
    return path_1.default.join(process.cwd(), 'logs', env);
};
const LOG_TYPES = ['error', 'combined', 'exceptions', 'rejections'];
/**
 * Clean up old log files
 * @returns Promise that resolves when cleanup is complete
 */
const cleanupOldLogs = async () => {
    try {
        // Get environment-specific log directory
        const logDirectory = getLogDirectory();
        // Ensure logs directory exists
        if (!fs_1.default.existsSync(logDirectory)) {
            logger_1.logger.info(`Log directory ${logDirectory} does not exist, creating it`);
            fs_1.default.mkdirSync(logDirectory, { recursive: true });
            return;
        }
        // Get all files in the logs directory
        const files = fs_1.default.readdirSync(logDirectory);
        // Calculate cutoff date
        const cutoffDate = (0, date_fns_1.subDays)(new Date(), MAX_LOG_AGE_DAYS);
        // Filter log files older than cutoff date
        const oldLogFiles = files.filter((file) => {
            // Check if file is a log file
            const isLogFile = LOG_TYPES.some((type) => file.startsWith(`${type}-`)) && file.endsWith('.log');
            if (!isLogFile) {
                return false;
            }
            // Extract date from filename (format: type-YYYY-MM-DD.log)
            const dateMatch = file.match(/\d{4}-\d{2}-\d{2}/);
            if (!dateMatch) {
                return false;
            }
            // Parse date from filename
            const fileDate = new Date(dateMatch[0]);
            // Check if file is older than cutoff date
            return fileDate < cutoffDate;
        });
        // Delete old log files
        for (const file of oldLogFiles) {
            const filePath = path_1.default.join(logDirectory, file);
            fs_1.default.unlinkSync(filePath);
            logger_1.logger.info(`Deleted old log file: ${file}`);
        }
        logger_1.logger.info(`Log rotation complete. Deleted ${oldLogFiles.length} old log files.`);
    }
    catch (error) {
        logger_1.logger.error('Error during log rotation:', error);
    }
};
exports.cleanupOldLogs = cleanupOldLogs;
/**
 * Compress log files older than a specified number of days
 * @param days Number of days to keep uncompressed
 * @returns Promise that resolves when compression is complete
 */
const compressOldLogs = async (days = 7) => {
    // This is a placeholder for future implementation
    // We would use a library like zlib to compress old log files
    logger_1.logger.info('Log compression not implemented yet');
};
exports.compressOldLogs = compressOldLogs;
/**
 * Schedule log rotation to run daily
 */
const scheduleLogRotation = () => {
    // Run log rotation immediately on startup
    (0, exports.cleanupOldLogs)();
    // Schedule log rotation to run daily at midnight
    const now = new Date();
    const midnight = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1, 0, 0, 0);
    const timeUntilMidnight = midnight.getTime() - now.getTime();
    // Run log rotation daily
    const timer = setInterval(() => {
        (0, exports.cleanupOldLogs)();
    }, 24 * 60 * 60 * 1000); // 24 hours
    // Run first log rotation at midnight
    setTimeout(() => {
        (0, exports.cleanupOldLogs)();
    }, timeUntilMidnight);
    return timer;
};
exports.scheduleLogRotation = scheduleLogRotation;
exports.default = {
    cleanupOldLogs: exports.cleanupOldLogs,
    compressOldLogs: exports.compressOldLogs,
    scheduleLogRotation: exports.scheduleLogRotation,
};
//# sourceMappingURL=log-rotation.js.map