// jscpd:ignore-file
import { BaseService } from "../shared/modules/services/BaseService";
import { ServiceError } from "../shared/modules/services/ServiceError";
import { BlockchainApiService, BlockchainNetwork, BlockchainToken } from "./blockchain/blockchain-api.service";
import { BinanceApiService } from "./blockchain/binance-api.service";
import { PaymentMethod, Transaction, Merchant } from '../types';
import { ServiceError } from "../shared/modules/services/ServiceError";
import { BlockchainApiService, BlockchainNetwork, BlockchainToken } from "./blockchain/blockchain-api.service";
import { BinanceApiService } from "./blockchain/binance-api.service";
import { PaymentMethod, Transaction, Merchant } from '../types';

/**
 * Payment verification result
 */
export interface PaymentVerificationResult {
    verified: boolean;
    method: PaymentMethod;
    amount: string;
    currency: string;
    transactionId: string;
    timestamp: string;
    sender?: string;
    recipient?: string;
    rawData?: any;
}

/**
 * Payment verification service
 */
export class PaymentVerificationService extends BaseService {
    private blockchainApiService: BlockchainApiService;
    private binanceApiService: BinanceApiService;

    constructor() {
        super();
        this.blockchainApiService = new BlockchainApiService();
        this.binanceApiService = new BinanceApiService();
    }

    /**
     * Execute a database operation with error handling
     * @param operation Operation to execute
     * @param errorMessage Error message
     * @param context Error context
     * @returns Operation result
     */
    private async executeDbOperation<T>(
        operation: () => Promise<T>,
        errorMessage: string,
        context: string
    ): Promise<T> {
        try {
            return await operation();
        } catch (error) {
            if (error instanceof ServiceError) {
                throw error;
            }

            console.error(`${context} error:`, error);
            throw this.paymentError(errorMessage);
        }
    }

    /**
     * Create a payment error
     * @param message Error message
     * @returns Service error
     */
    private paymentError(message: string): ServiceError {
        return new ServiceError(message, 400, 'PAYMENT_ERROR');
    }

    /**
   * Verify a payment
   * @param method Payment method
   * @param transactionId Transaction ID
   * @param amount Expected amount
   * @param currency Expected currency
   * @param recipientAddress Expected recipient address
   * @param merchantApiKey Merchant API key
   * @param merchantSecretKey Merchant secret key
   * @returns Payment verification result
   */
    async verifyPayment(
        method: PaymentMethod,
        transactionId: string,
        amount: string,
        currency: string,
        recipientAddress: string,
        merchantApiKey?: string,
        merchantSecretKey?: string
    ): Promise<PaymentVerificationResult> {
        return this.executeDbOperation(
            async () => {
                switch (method) {
                case PaymentMethod.BINANCE_PAY:
                    return this.verifyBinancePayPayment(transactionId, amount, currency, merchantApiKey, merchantSecretKey);
                case PaymentMethod.BINANCE_C2C:
                    return this.verifyBinanceC2CPayment(transactionId, amount, currency, merchantApiKey, merchantSecretKey);
                case PaymentMethod.BINANCE_TRC20:
                    return this.verifyBinanceTRC20Payment(transactionId, amount, currency, recipientAddress, merchantApiKey, merchantSecretKey);
                case PaymentMethod.CRYPTO_TRANSFER:
                    return this.verifyCryptoTransferPayment(transactionId, amount, currency, recipientAddress);
                default:
                    throw this.paymentError(`Unsupported payment method: ${method}`);
                }
            },
            `Failed to verify payment with method ${method} and transaction ID ${transactionId}`,
            'Payment'
        );
    }

    /**
     * Verify a Binance Pay payment
     * @param transactionId Transaction ID
     * @param amount Expected amount
     * @param currency Expected currency
     * @param merchantApiKey Merchant API key
     * @param merchantSecretKey Merchant secret key
     * @returns Payment verification result
     */
    private async verifyBinancePayPayment(
        transactionId: string,
        amount: string,
        currency: string,
        merchantApiKey?: string,
        merchantSecretKey?: string
    ): Promise<PaymentVerificationResult> {
        return this.executeDbOperation(
            async () => {
                if (!merchantApiKey || !merchantSecretKey) {
                    throw this.paymentError("Merchant API key and secret key are required for Binance Pay verification");
                }

                // Verify transaction
                const result: any =await this.binanceApiService.verifyBinancePayTransaction(
                    transactionId,
                    merchantApiKey,
                    merchantSecretKey,
                    amount,
                    currency
                );

                return {
                    verified: result.status === "PAID",
                    method: PaymentMethod.BINANCE_PAY,
                    amount: result.amount,
                    currency: result.currency,
                    transactionId: result.transactionId,
                    timestamp: result.timestamp,
                    rawData: result.rawData
                };
            },
            `Failed to verify Binance Pay payment with transaction ID ${transactionId}`,
            'BinancePayPayment'
        );
    }

    /**
     * Verify a Binance C2C payment
     * @param note Transaction note
     * @param amount Expected amount
     * @param currency Expected currency
     * @param merchantApiKey Merchant API key
     * @param merchantSecretKey Merchant secret key
     * @returns Payment verification result
     */
    private async verifyBinanceC2CPayment(
        note: string,
        amount: string,
        currency: string,
        merchantApiKey?: string,
        merchantSecretKey?: string
    ): Promise<PaymentVerificationResult> {
        return this.executeDbOperation(
            async () => {
                if (!merchantApiKey || !merchantSecretKey) {
                    throw this.paymentError("Merchant API key and secret key are required for Binance C2C verification");
                }

                // Verify transaction
                const result: any =await this.binanceApiService.verifyBinanceC2CTransactionByNote(
                    note,
                    merchantApiKey,
                    merchantSecretKey,
                    amount,
                    currency
                );

                return {
                    verified: result.verified,
                    method: PaymentMethod.BINANCE_C2C,
                    amount: result.amount,
                    currency: result.currency,
                    transactionId: result.transactionId,
                    timestamp: result.timestamp,
                    sender: result.sender,
                    recipient: result.recipient,
                    rawData: result.rawData
                };
            },
            `Failed to verify Binance C2C payment with note ${note}`,
            'BinanceC2CPayment'
        );
    }

    /**
     * Verify a Binance TRC20 payment
     * @param txHash Transaction hash
     * @param amount Expected amount
     * @param currency Expected currency
     * @param recipientAddress Expected recipient address
     * @param merchantApiKey Merchant API key
     * @param merchantSecretKey Merchant secret key
     * @returns Payment verification result
     */
    private async verifyBinanceTRC20Payment(
        txHash: string,
        amount: string,
        currency: string,
        recipientAddress: string,
        merchantApiKey?: string,
        merchantSecretKey?: string
    ): Promise<PaymentVerificationResult> {
        return this.executeDbOperation(
            async () => {
                if (!merchantApiKey || !merchantSecretKey) {
                    throw this.paymentError("Merchant API key and secret key are required for Binance TRC20 verification");
                }

                // Verify transaction
                const result: any =await this.binanceApiService.verifyBinanceTRC20Transaction(
                    txHash,
                    merchantApiKey,
                    merchantSecretKey,
                    amount,
                    recipientAddress
                );

                return {
                    verified: result.status === "PAID",
                    method: PaymentMethod.BINANCE_TRC20,
                    amount: result.amount,
                    currency: result.currency,
                    transactionId: result.transactionId,
                    timestamp: result.timestamp,
                    recipient: result.recipient,
                    rawData: result.rawData
                };
            },
            `Failed to verify Binance TRC20 payment with transaction hash ${txHash}`,
            'BinanceTRC20Payment'
        );
    }

    /**
   * Verify a crypto transfer payment
   * @param txHash Transaction hash
   * @param amount Expected amount
   * @param currency Expected currency
   * @param recipientAddress Expected recipient address
   * @returns Payment verification result
   */
    private async verifyCryptoTransferPayment(
        txHash: string,
        amount: string,
        currency: string,
        recipientAddress: string
    ): Promise<PaymentVerificationResult> {
        return this.executeDbOperation(
            async () => {
                // Map currency to blockchain network and token
                const { network, token } = this.mapCurrencyToNetworkAndToken(currency);

                // Verify transaction
                const result: any =await this.blockchainApiService.verifyTransaction(
                    txHash,
                    network,
                    token,
                    amount,
                    recipientAddress
                );

                return {
                    verified: result.status === "confirmed",
                    method: PaymentMethod.CRYPTO_TRANSFER,
                    amount: result.amount,
                    currency: token,
                    transactionId: result.hash,
                    timestamp: result.timestamp,
                    sender: result.from,
                    recipient: result.to,
                    rawData: result.rawData
                };
            },
            `Failed to verify crypto transfer payment with transaction hash ${txHash}`,
            'CryptoTransferPayment'
        );
    }

    /**
   * Map currency to blockchain network and token
   * @param currency Currency
   * @returns Blockchain network and token
   */
    private mapCurrencyToNetworkAndToken(currency: string): { network: BlockchainNetwork; token: BlockchainToken } {
    // Format: USDT_TRC20, USDC_ERC20, etc.
        const parts: any =currency.split("_");

        if (parts.length !== 2) {
            throw this.paymentError(`Invalid currency format: ${currency}`);
        }

        const [tokenStr, networkStr] = parts;

        // Map token
        let token: BlockchainToken;
        switch (tokenStr.toUpperCase()) {
        case "USDT":
            token = BlockchainToken.USDT;
            break;
        case "USDC":
            token = BlockchainToken.USDC;
            break;
        case "BUSD":
            token = BlockchainToken.BUSD;
            break;
        default:
            throw this.paymentError(`Unsupported token: ${tokenStr}`);
        }

        // Map network
        let network: BlockchainNetwork;
        switch (networkStr.toUpperCase()) {
        case "TRC20":
            network = BlockchainNetwork.TRON;
            break;
        case "ERC20":
            network = BlockchainNetwork.ETHEREUM;
            break;
        case "BEP20":
            network = BlockchainNetwork.BSC;
            break;
        case "POLYGON":
            network = BlockchainNetwork.POLYGON;
            break;
        default:
            throw this.paymentError(`Unsupported network: ${networkStr}`);
        }

        return { network, token };
    }
}