{"version": 3, "file": "security-test.js", "sourceRoot": "", "sources": ["../../../src/tests/security-test.ts"], "names": [], "mappings": ";AAAA,oBAAoB;AACpB;;;;;;;;;;;;;;GAcG;;;;;AAEH,kDAA0B;AAC1B,+BAA8B;AAE9B,gBAAgB;AAChB,MAAM,QAAQ,GAAW,uBAAuB,CAAC;AAEjD,yBAAyB;AACzB,KAAK,UAAU,gBAAgB;IAC7B,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;IAE1C,IAAI,WAAW,GAAW,CAAC,CAAC;IAC5B,IAAI,UAAU,GAAG,CAAC,CAAC;IAEnB,gCAAgC;IAChC,KAAK,UAAU,OAAO,CAAC,IAAY,EAAE,MAA2B;QAC9D,UAAU,EAAE,CAAC;QACb,IAAI,CAAC;YACH,MAAM,MAAM,EAAE,CAAC;YACf,OAAO,CAAC,GAAG,CAAC,aAAa,IAAI,EAAE,CAAC,CAAC;YACjC,WAAW,EAAE,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,aAAa,IAAI,EAAE,CAAC,CAAC;YACnC,OAAO,CAAC,KAAK,CAAC,aAAc,KAAe,CAAC,OAAO,EAAE,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAED,wBAAwB;IACxB,MAAM,OAAO,CAAC,uBAAuB,EAAE,KAAK,IAAI,EAAE;QAChD,MAAM,QAAQ,GAAY,MAAM,eAAK,CAAC,GAAG,CAAC,GAAG,QAAQ,SAAS,CAAC,CAAC;QAEhE,6BAA6B;QAC7B,IAAA,aAAM,EAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,wBAAwB,EAAE,SAAS,CAAC,CAAC;QAC/E,IAAA,aAAM,EAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;QAC9D,IAAA,aAAM,EAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC;QAC7D,IAAA,aAAM,EAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,yBAAyB,CAAC,CAAC;QACrE,IAAA,aAAM,EAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC;IAC/D,CAAC,CAAC,CAAC;IAEH,uBAAuB;IACvB,MAAM,OAAO,CAAC,sBAAsB,EAAE,KAAK,IAAI,EAAE;QAC/C,IAAI,CAAC;YACH,oDAAoD;YACpD,MAAM,eAAK,CAAC,IAAI,CAAC,GAAG,QAAQ,iBAAiB,EAAE;gBAC7C,KAAK,EAAE,kBAAkB;gBACzB,QAAQ,EAAE,UAAU;aACrB,CAAC,CAAC;YAEH,2DAA2D;YAC3D,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;QAC/D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,kCAAkC;YAClC,IAAA,aAAM,EAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC9C,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,sBAAsB;IACtB,MAAM,OAAO,CAAC,qBAAqB,EAAE,KAAK,IAAI,EAAE;QAC9C,IAAI,CAAC;YACH,6CAA6C;YAC7C,MAAM,UAAU,GAAW,+BAA+B,CAAC;YAE3D,MAAM,eAAK,CAAC,GAAG,CAAC,GAAG,QAAQ,iBAAiB,UAAU,EAAE,CAAC,CAAC;YAE1D,0DAA0D;YAC1D,mFAAmF;QACrF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,+CAA+C;YAC/C,IAAA,aAAM,EAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,gCAAgC;IAChC,MAAM,OAAO,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;QACxD,IAAI,CAAC;YACH,uDAAuD;YACvD,MAAM,mBAAmB,GAAW,aAAa,CAAC;YAElD,MAAM,eAAK,CAAC,GAAG,CAAC,GAAG,QAAQ,iBAAiB,mBAAmB,EAAE,CAAC,CAAC;YAEnE,8EAA8E;QAChF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,+CAA+C;YAC/C,IAAA,aAAM,EAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,qBAAqB;IACrB,MAAM,OAAO,CAAC,oBAAoB,EAAE,KAAK,IAAI,EAAE;QAC7C,kDAAkD;QAClD,MAAM,QAAQ,GAAc,EAAE,CAAC;QAC/B,KAAK,IAAI,CAAC,GAAW,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;YACrC,QAAQ,CAAC,IAAI,CAAC,eAAK,CAAC,GAAG,CAAC,GAAG,QAAQ,aAAa,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC;QACtF,CAAC;QAED,MAAM,SAAS,GAAY,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAEvD,8DAA8D;QAC9D,MAAM,WAAW,GAAY,SAAS,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,IAAI,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,CAAC;QAChF,IAAA,aAAM,EAAC,WAAW,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACrC,CAAC,CAAC,CAAC;IAEH,sBAAsB;IACtB,MAAM,OAAO,CAAC,qBAAqB,EAAE,KAAK,IAAI,EAAE;QAC9C,IAAI,CAAC;YACH,gEAAgE;YAChE,MAAM,eAAK,CAAC,GAAG,CAAC,GAAG,QAAQ,gBAAgB,CAAC,CAAC;YAE7C,+DAA+D;YAC/D,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;QAC1E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,qCAAqC;YACrC,IAAA,aAAM,EAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC9C,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,mBAAmB;IACnB,MAAM,OAAO,CAAC,kBAAkB,EAAE,KAAK,IAAI,EAAE;QAC3C,IAAI,CAAC;YACH,6DAA6D;YAC7D,MAAM,eAAK,CAAC,GAAG,CAAC,GAAG,QAAQ,gBAAgB,EAAE;gBAC3C,OAAO,EAAE,EAAE,aAAa,EAAE,0BAA0B,EAAE;aACvD,CAAC,CAAC;YAEH,4DAA4D;YAC5D,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;QACpE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,qCAAqC;YACrC,IAAA,aAAM,EAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC9C,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,mBAAmB;IACnB,MAAM,OAAO,CAAC,kBAAkB,EAAE,KAAK,IAAI,EAAE;QAC3C,uDAAuD;QACvD,MAAM,UAAU,GACd,mLAAmL,CAAC;QAEtL,IAAI,CAAC;YACH,6DAA6D;YAC7D,MAAM,eAAK,CAAC,GAAG,CAAC,GAAG,QAAQ,gBAAgB,EAAE;gBAC3C,OAAO,EAAE,EAAE,aAAa,EAAE,UAAU,UAAU,EAAE,EAAE;aACnD,CAAC,CAAC;YAEH,4DAA4D;YAC5D,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;QACpE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,qCAAqC;YACrC,IAAA,aAAM,EAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC9C,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,gBAAgB;IAChB,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;IACxC,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;IACrC,OAAO,CAAC,GAAG,CACT,WAAW,WAAW,IAAI,UAAU,KAAK,CAAC,CAAC,WAAW,GAAG,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAC3F,CAAC;IAEF,4BAA4B;IAC5B,MAAM,SAAS,GAAY,WAAW,KAAK,UAAU,CAAC;IAEtD,IAAI,SAAS,EAAE,CAAC;QACd,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;IAC/C,CAAC;SAAM,CAAC;QACN,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;IAChD,CAAC;IAED,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,6DAA6D;AAC7D,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC5B,gBAAgB,EAAE;SACf,IAAI,CAAC,CAAC,MAAe,EAAE,EAAE;QACxB,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B,CAAC,CAAC;SACD,KAAK,CAAC,CAAC,KAAU,EAAE,EAAE;QACpB,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;QAC1D,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC;AACP,CAAC;AAED,kBAAe,gBAAgB,CAAC"}