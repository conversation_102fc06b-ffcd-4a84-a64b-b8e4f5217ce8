"use strict";
// jscpd:ignore-file
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const dotenv_1 = __importDefault(require("dotenv"));
// Load environment variables
dotenv_1.default.config();
exports.default = {
    NODE_ENV: process.env.NODE_ENV || "production",
    PORT: process.env.PORT || 3002, // Use port 3002 as default
    JWT_SECRET: process.env.JWT_SECRET || "", // No default value for security
    JWT_EXPIRES_IN: process.env.JWT_EXPIRES_IN || "1d",
    BINANCE_API_URL: process.env.BINANCE_API_URL || "https://api.binance.com",
    DEVELOPMENT_MODE: false, // Always in production mode
    USE_REAL_DATA: true, // Always use real data
    TRONSCAN_API_URL: process.env.TRONSCAN_API_URL || "https://apilist.tronscan.org/api",
    ETHERSCAN_API_URL: process.env.ETHERSCAN_API_URL || "https://api.etherscan.io/api",
    ETHERSCAN_API_KEY: process.env.ETHERSCAN_API_KEY || ""
};
//# sourceMappingURL=env.config.js.map