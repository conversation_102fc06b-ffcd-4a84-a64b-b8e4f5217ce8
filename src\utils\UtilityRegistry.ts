// jscpd:ignore-file
/**
 * Utility Registry
 * 
 * This file serves as a central registry for all utility functions in the application.
 * It helps eliminate duplication by providing a single source of truth for utility functions.
 */

// Import all utility functions
import * as errorHandling from './errorHandling';
import * as repositoryUtils from './repositoryUtils';
import * as controllerUtils from './controllerUtils';
import * as common from './common';
import { Repository } from '../types/database';


/**
 * Utility registry
 */
export const UtilityRegistry: any = {
  // Error handling utilities
  handleError: errorHandling.handleError,
  validateField: errorHandling.validateField,
  createSuccessResponse: errorHandling.createSuccessResponse,
  createPaginatedResponse: errorHandling.createPaginatedResponse,
  
  // Repository utilities
  executeRepositoryMethod: repositoryUtils.executeRepositoryMethod,
  executeTransaction: repositoryUtils.executeTransaction,
  createWhereClause: repositoryUtils.createWhereClause,
  createOrderByClause: repositoryUtils.createOrderByClause,
  
  // Controller utilities
  sendSuccess: controllerUtils.sendSuccess,
  sendPaginatedResponse: controllerUtils.sendPaginatedResponse,
  checkAuthorization: controllerUtils.checkAuthorization,
  checkAdminRole: controllerUtils.checkAdminRole,
  parseDateRange: controllerUtils.parseDateRange,
  determineTargetMerchantId: controllerUtils.determineTargetMerchantId,
  
  // Common utilities
  isDefined: common.isDefined,
  isUndefined: common.isUndefined,
  isEmpty: common.isEmpty,
  isNotEmpty: common.isNotEmpty
};

/**
 * Get utility registry
 * @returns Utility registry
 */
export function getUtilityRegistry(): typeof UtilityRegistry {
  return UtilityRegistry;
}