// jscpd:ignore-file
import { Router } from "express";
import { AlertAnalyticsController } from "../controllers/refactored/alert-analytics.controller.ts";
import { authenticate } from '../middlewares/auth';
import { Alert } from '../types';
import { AlertAnalyticsController } from "../controllers/refactored/alert-analytics.controller.ts";
import { authenticate } from '../middlewares/auth';
import { Alert } from '../types';


const router: any =Router();

// Alert analytics routes
router.get("/analytics/count-by-status", authenticate, AlertAnalyticsController.getAlertCountByStatus);
router.get("/analytics/count-by-severity", authenticate, AlertAnalyticsController.getAlertCountBySeverity);
router.get("/analytics/count-by-type", authenticate, AlertAnalyticsController.getAlertCountByType);
router.get("/analytics/count-by-day", authenticate, AlertAnalyticsController.getAlertCountByDay);
router.get("/analytics/count-by-hour", authenticate, AlertAnalyticsController.getAlertCountByHour);
router.get("/analytics/top-merchants", authenticate, AlertAnalyticsController.getTopMerchantsByAlertCount);
router.get("/analytics/resolution-time", authenticate, AlertAnalyticsController.getAlertResolutionTimeStats);
router.get("/analytics/trends", authenticate, AlertAnalyticsController.getAlertTrends);

export default router;
