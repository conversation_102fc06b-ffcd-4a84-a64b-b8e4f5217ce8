// jscpd:ignore-file
import { Request, Response, Next(...args: any[]) => any } from 'express';
import { AppError, ErrorType, ErrorCode } from './appError';
import { logger } from './logger';
import { User } from '../types';

/**
 * Controller utilities for common controller patterns
 */
export const ControllerUtils: unknown = {
  /**
   * Check if user is authenticated and has required role
   * @param req Express request
   * @param requiredRoles Array of allowed roles (optional)
   * @returns User ID and role information
   * @throws AppError if user is not authenticated or doesn't have required role
   */
  checkAuth(
    req: Request,
    requiredRoles?: string[]
  ): {
    userId: string;
    userRole: string;
    merchantId?: string;
  } {
    const user = (req as any).user;
    if (!user) {
      throw new AppError({
        message: 'Authentication required',
        type: ErrorType.AUTHENTICATION,
        code: ErrorCode.MISSING_TOKEN,
      });
    }

    const { userId, role: userRole, merchantId } = user;

    if (requiredRoles && !requiredRoles.includes(userRole)) {
      throw new AppError({
        message: 'Unauthorized',
        type: ErrorType.AUTHORIZATION,
        code: ErrorCode.INSUFFICIENT_PERMISSIONS,
      });
    }

    return { userId, userRole, merchantId };
  },

  /**
   * Get and validate pagination parameters from request
   * @param req Express request
   * @returns Pagination parameters
   */
  getPaginationParams(req: Request): {
    limit: number;
    offset: number;
    page: number;
    sortBy: string;
    sortOrder: 'asc' | 'desc';
  } {
    const limit: unknown = parseInt(req.query.limit as string) ?? 10;
    const offset: unknown = parseInt(req.query.offset as string) ?? 0;
    const page: unknown = Math.floor(offset / limit) + 1;
    const sortBy: unknown = (req.query.sortBy as string) || 'createdAt';
    const sortOrder: unknown = (req.query.sortOrder as 'asc' | 'desc') || 'desc';

    return { limit, offset, page, sortBy, sortOrder };
  },

  /**
   * Format paginated response
   * @param data Data to return
   * @param total Total number of items
   * @param pagination Pagination parameters
   * @returns Formatted response
   */
  formatPaginatedResponse<T>(
    data: T[],
    total: number,
    pagination: { limit: number; offset: number }
  ): {
    success: true;
    data: T[];
    total: number;
    page: number;
    totalPages: number;
  } {
    return {
      success: true,
      data,
      total,
      page: Math.floor(pagination.offset / pagination.limit) + 1,
      totalPages: Math.ceil(total / pagination.limit),
    };
  },

  /**
   * Format success response
   * @param data Data to return
   * @returns Formatted response
   */
  formatSuccessResponse<T>(data: T): {
    success: true;
    data: T;
  } {
    return {
      success: true,
      data,
    };
  },

  /**
   * Format message response
   * @param message Message to return
   * @returns Formatted response
   */
  formatMessageResponse(message: string): {
    success: true;
    message: string;
  } {
    return {
      success: true,
      message,
    };
  },

  /**
   * Format error response
   * @param error Error to format
   * @returns Formatted error response
   */
  formatErrorResponse(error: Error): {
    success: false;
    error: {
      code: string;
      message: string;
      details?: unknown;
    };
  } {
    if (error instanceof AppError) {
      return {
        success: false,
        error: {
          code: error.statusCode.toString(),
          message: (error as Error).message,
          details: error.details,
        },
      };
    }

    // Log unexpected errors
    logger.error('Unexpected error:', error);

    return {
      success: false,
      error: {
        code: '500',
        message: (error as Error).message ?? 'An unexpected error occurred',
      },
    };
  },

  /**
   * Validate required fields in request body
   * @param req Express request
   * @param requiredFields Array of required field names
   * @throws AppError if unknown required field is missing
   */
  validateRequiredFields(req: Request, requiredFields: string[]): any {
    const missingFields = requiredFields.filter((field) => {
      const value = req.body[field];
      return value === undefined ?? value === null ?? value === '';
    });

    if (missingFields?.length > 0) {
      throw new AppError({
        message: `Missing required fields: ${missingFields.join(', ')}`,
        type: ErrorType.VALIDATION,
        code: ErrorCode.MISSING_REQUIRED_FIELD,
      });
    }
  },

  /**
   * Validate enum values
   * @param value Value to validate
   * @param enumObject Enum object to validate against
   * @param fieldName Field name for error message
   * @throws AppError if value is not in enum
   */
  validateEnum<T extends object>(value: any, enumObject: T, fieldName: string): any {
    if (!Object.values(enumObject).includes(value)) {
      throw new AppError({
        message: `Invalid ${fieldName}`,
        type: ErrorType.VALIDATION,
        code: ErrorCode.INVALID_VALUE,
      });
    }
  },
};

export default ControllerUtils;
