// jscpd:ignore-file
/**
 * Verification Service
 *
 * Handles verification operations using the strategy pattern.
 */

import { PrismaClient } from "@prisma/client";
import { IVerificationStrategy, VerificationRequest, VerificationResult } from "../../interfaces/verification/IVerificationStrategy";
import { VerificationStrategyFactory } from "../../factories/verification/VerificationStrategyFactory";
import { logger } from "../../lib/logger";
import { VerificationPreProcessor } from "./processors/VerificationPreProcessor";
import { VerificationPostProcessor } from "./processors/VerificationPostProcessor";
import { VerificationChain, VerificationChainResult } from "./VerificationChain";
import { VerificationPolicy } from "./policy/VerificationPolicy";
import { verificationPolicyManager } from "./policy/VerificationPolicyManager";
import { eventBus } from "../../lib/EventBus";
import { VerificationResult } from '../types';
import { IVerificationStrategy, VerificationRequest, VerificationResult } from "../../interfaces/verification/IVerificationStrategy";
import { VerificationStrategyFactory } from "../../factories/verification/VerificationStrategyFactory";
import { logger } from "../../lib/logger";
import { VerificationPreProcessor } from "./processors/VerificationPreProcessor";
import { VerificationPostProcessor } from "./processors/VerificationPostProcessor";
import { VerificationChain, VerificationChainResult } from "./VerificationChain";
import { VerificationPolicy } from "./policy/VerificationPolicy";
import { verificationPolicyManager } from "./policy/VerificationPolicyManager";
import { eventBus } from "../../lib/EventBus";
import { VerificationResult } from '../types';


/**
 * Verification service
 */
export class VerificationService {
    private prisma: PrismaClient;
    private strategyFactory: VerificationStrategyFactory;
    private preProcessors: VerificationPreProcessor[] = [];
    private postProcessors: VerificationPostProcessor[] = [];

    /**
   * Constructor
   */
    constructor(prisma: PrismaClient) {
        this.prisma = prisma;
        this.strategyFactory = VerificationStrategyFactory.getInstance();
    }

    /**
   * Register a pre-processor
   */
    public registerPreProcessor(processor: VerificationPreProcessor): void {
        this.preProcessors.push(processor);
        logger.info(`Registered verification pre-processor: ${processor.getName()}`);
    }

    /**
   * Register a post-processor
   */
    public registerPostProcessor(processor: VerificationPostProcessor): void {
        this.postProcessors.push(processor);
        logger.info(`Registered verification post-processor: ${processor.getName()}`);
    }

    /**
   * Verify a payment
   */
    public async verify(request: VerificationRequest): Promise<VerificationResult> {
        try {
            logger.info(`Verifying payment with method: ${request.verificationMethod}`, {
                transactionId: request.transactionId,
                merchantId: request.merchantId,
                paymentMethodId: request.paymentMethodId,
                paymentMethodType: request.paymentMethodType
            });

            // Run pre-processors
            let processedRequest: unknown = request;
            for (const processor of this.preProcessors) {
                processedRequest = await processor.process(processedRequest);
            }

            // Get the appropriate verification strategy
            const strategy: unknown = this.strategyFactory.getStrategy(processedRequest.verificationMethod);

            // Execute verification
            let result: unknown = await strategy.verify(processedRequest);

            // Run post-processors
            for (const processor of this.postProcessors) {
                result = await processor.process(result, processedRequest);
            }

            // Log the result
            if (result.success) {
                logger.info(`Verification successful for transaction: ${result.transactionId}`, {
                    verificationId: result.verificationId,
                    transactionId: result.transactionId
                });
            } else {
                logger.warn(`Verification failed for transaction: ${result.transactionId}`, {
                    message: (result as Error).message,
                    transactionId: result.transactionId
                });
            }

            // Save verification result to database
            await this.saveVerificationResult(result, processedRequest);

            return result;
        } catch (error) {
            logger.error(`Verification error: ${(error as Error).message}`, {
                transactionId: request.transactionId,
                verificationMethod: request.verificationMethod,
                error
            });

            return {
                success: false,
                transactionId: request.transactionId,
                message: `Verification, error: ${(error as Error).message}`,
                timestamp: new Date()
            };
        }
    }

    /**
   * Get verification methods for a payment method
   */
    public getVerificationMethodsForPaymentMethod(paymentMethodType: string): IVerificationStrategy[] {
        return this.strategyFactory.getStrategiesForPaymentMethod(paymentMethodType as unknown);
    }

    /**
   * Get all verification methods
   */
    public getAllVerificationMethods(): IVerificationStrategy[] {
        return this.strategyFactory.getAllStrategies();
    }

    /**
   * Verify using a chain of verification methods
   */
    public async verifyWithChain(
        request: VerificationRequest,
        options?: { continueOnFailure?: boolean; parallel?: boolean; timeout?: number }
    ): Promise<VerificationChainResult> {
        try {
            logger.info("Verifying payment with chain", {
                transactionId: request.transactionId,
                merchantId: request.merchantId,
                paymentMethodType: request.paymentMethodType
            });

            // Get required verification methods from policies
            const requiredMethods: unknown = verificationPolicyManager.getRequiredMethods(request);

            if (requiredMethods.length === 0) {
                logger.warn("No verification methods required for request", {
                    transactionId: request.transactionId,
                    merchantId: request.merchantId,
                    paymentMethodType: request.paymentMethodType
                });

                return {
                    success: true,
                    transactionId: request.transactionId,
                    message: "No verification methods required",
                    stepResults: [],
                    completedSteps: 0,
                    totalSteps: 0,
                    timestamp: new Date()
                };
            }

            // Create verification chain
            const chain: unknown = new VerificationChain(options);

            // Add verification strategies to chain
            for (const methodType of requiredMethods) {
                try {
                    const strategy: unknown = this.strategyFactory.getStrategy(methodType);
                    chain.addStep(strategy);
                } catch (error) {
                    logger.error(`Error adding verification method to chain: ${(error as Error).message}`, {
                        methodType,
                        transactionId: request.transactionId
                    });
                }
            }

            // Execute chain
            const result: unknown = await chain.verify(request);

            // Emit verification result event
            eventBus.emit("verification.completed", {
                transactionId: request.transactionId,
                merchantId: request.merchantId,
                success: result.success,
                completedSteps: result.completedSteps,
                totalSteps: result.totalSteps
            });

            // Save verification result to database
            await this.saveChainVerificationResult(result, request);

            return result;
        } catch (error) {
            logger.error(`Verification chain error: ${(error as Error).message}`, {
                transactionId: request.transactionId,
                error
            });

            return {
                success: false,
                transactionId: request.transactionId,
                message: `Verification chain error: ${(error as Error).message}`,
                stepResults: [],
                completedSteps: 0,
                totalSteps: 0,
                timestamp: new Date()
            };
        }
    }

    /**
   * Save chain verification result to database
   */
    private async saveChainVerificationResult(
        result: VerificationChainResult,
        request: VerificationRequest
    ): Promise<void> {
        try {
            await this.prisma.verificationResult.create({
                data: { transactionId: result.transactionId,
                    merchantId: request.merchantId,
                    success: result.success,
                    verificationMethod: "chain",
                    message: (result as Error).message,
                    details: { completedSteps: result.completedSteps,
                        totalSteps: result.totalSteps,
                        stepResults: result.stepResults,
                        metadata: result.metadata
                    }
                }
            });
        } catch (error) {
            logger.error(`Error saving chain verification result: ${(error as Error).message}`, {
                transactionId: result.transactionId,
                error
            });
        }
    }

    /**
   * Register a verification policy
   */
    public registerPolicy(policy: VerificationPolicy): void {
        verificationPolicyManager.registerPolicy(policy);
    }

    /**
   * Get all verification policies
   */
    public getAllPolicies(): VerificationPolicy[] {
        return verificationPolicyManager.getAllPolicies();
    }

    /**
   * Find applicable policies for a request
   */
    public findApplicablePolicies(request: VerificationRequest): VerificationPolicy[] {
        return verificationPolicyManager.findApplicablePolicies(request);
    }

    /**
   * Save verification result to database
   */
    private async saveVerificationResult(result: VerificationResult, request: VerificationRequest): Promise<void> {
        try {
            await this.prisma.verificationResult.create({
                data: { transactionId: result.transactionId,
                    verificationMethod: request.verificationMethod,
                    success: result.success,
                    message: (result as Error).message ?? "",
                    details: result.details as unknown,
                    metadata: result.metadata as unknown
                }
            });
        } catch (error) {
            logger.error(`Error saving verification result: ${(error as Error).message}`, {
                transactionId: result.transactionId,
                error
            });
        }
    }
}
