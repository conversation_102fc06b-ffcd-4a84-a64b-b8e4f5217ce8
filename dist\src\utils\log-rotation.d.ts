/**
 * Log Rotation Utility
 *
 * This utility handles log rotation to prevent log files from growing too large
 * and to maintain a history of logs for a specified period.
 */
/**
 * Clean up old log files
 * @returns Promise that resolves when cleanup is complete
 */
export declare const cleanupOldLogs: unknown;
/**
 * Compress log files older than a specified number of days
 * @param days Number of days to keep uncompressed
 * @returns Promise that resolves when compression is complete
 */
export declare const compressOldLogs: unknown;
/**
 * Schedule log rotation to run daily
 */
export declare const scheduleLogRotation: unknown;
declare const _default: {
    cleanupOldLogs: unknown;
    compressOldLogs: unknown;
    scheduleLogRotation: unknown;
};
export default _default;
//# sourceMappingURL=log-rotation.d.ts.map