// jscpd:ignore-file
import express from "express";
import monitoring<PERSON>ontroller from "../controllers/monitoring.controller";
import { authenticateJWT, isAdmin } from '../middlewares/auth';

const router: any =express.Router();

/**
 * @route   GET /api/monitoring/metrics
 * @desc    Get monitoring metrics
 * @access  Admin
 */
router.get(
    "/metrics",
    authenticateJWT,
    isAdmin,
    monitoringController.getMetrics
);

/**
 * @route   GET /api/monitoring/alerts
 * @desc    Get alerts
 * @access  Admin
 */
router.get(
    "/alerts",
    authenticateJWT,
    isAdmin,
    monitoringController.getAlerts
);

/**
 * @route   POST /api/monitoring/alerts/:index/resolve
 * @desc    Resolve alert
 * @access  Admin
 */
router.post(
    "/alerts/:index/resolve",
    authenticateJWT,
    isAdmin,
    monitoringController.resolveAlert
);

/**
 * @route   POST /api/monitoring/alerts
 * @desc    Create alert
 * @access  Admin
 */
router.post(
    "/alerts",
    authenticateJWT,
    isAdmin,
    monitoringController.createAlert
);

/**
 * @route   POST /api/monitoring/metrics/reset
 * @desc    Reset metrics
 * @access  Admin
 */
router.post(
    "/metrics/reset",
    authenticateJWT,
    isAdmin,
    monitoringController.resetMetrics
);

/**
 * @route   POST /api/monitoring/events
 * @desc    Emit monitoring event
 * @access  Admin
 */
router.post(
    "/events",
    authenticateJWT,
    isAdmin,
    monitoringController.emitEvent
);

export default router;
