/**
 * Admin Controller
 *
 * Modular controller for admin operations.
 */
import { BaseController } from '../base.controller';
/**
 * Modular Admin Controller
 */
export declare class AdminController extends BaseController {
    private authService;
    private validationService;
    private businessService;
    constructor();
    /**
     * Get dashboard data
     */
    getDashboardData: any;
    /**
     * Get dashboard statistics
     */
    getDashboardStatistics: any;
    /**
     * Get all admin users
     */
    getAdminUsers: any;
    /**
     * Get admin user by ID
     */
    getAdminUserById: any;
    /**
     * Create admin user
     */
    createAdminUser: any;
    /**
     * Update admin user
     */
    updateAdminUser: any;
    /**
     * Delete admin user
     */
    deleteAdminUser: any;
    /**
     * Get system health
     */
    getSystemHealth: any;
    /**
     * Health check endpoint
     */
    healthCheck: any;
}
//# sourceMappingURL=AdminController.d.ts.map