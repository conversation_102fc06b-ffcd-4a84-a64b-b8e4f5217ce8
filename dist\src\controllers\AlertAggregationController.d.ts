import { BaseController } from "../base/BaseController";
/**
 * Alert aggregation controller
 */
export declare class AlertAggregationController extends BaseController {
    constructor();
    /**
     * Get aggregation rules
     * @route GET /api/alerts/aggregation/rules
     */
    getAggregationRules: any;
    /**
     * Get aggregation rule by ID
     * @route GET /api/alerts/aggregation/rules/:id
     */
    getAggregationRule: any;
    /**
     * Create aggregation rule
     * @route POST /api/alerts/aggregation/rules
     */
    createAggregationRule: any;
    /**
     * Update aggregation rule
     * @route PUT /api/alerts/aggregation/rules/:id
     */
    updateAggregationRule: any;
    /**
     * Delete aggregation rule
     * @route DELETE /api/alerts/aggregation/rules/:id
     */
    deleteAggregationRule: any;
    /**
     * Get correlation rules
     * @route GET /api/alerts/correlation/rules
     */
    getCorrelationRules: any;
    /**
     * Get correlation rule by ID
     * @route GET /api/alerts/correlation/rules/:id
     */
    getCorrelationRule: any;
}
//# sourceMappingURL=AlertAggregationController.d.ts.map