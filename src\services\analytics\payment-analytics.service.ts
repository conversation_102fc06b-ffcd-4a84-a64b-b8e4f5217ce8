// jscpd:ignore-file
import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger';
import { logger } from '../utils/logger';
// jscpd:ignore-start
// jscpd:ignore-start
// Duplicated code removed and replaced with import
// jscpd:ignore-end
export enum AnalyticsPeriod {
  DAY = 'day',
  WEEK = 'week',
  MONTH = 'month',
  // jscpd:ignore-end
  YEAR = 'year',
  CUSTOM = 'custom',
}

/**
 * Analytics filter
 */
export interface AnalyticsFilter {
  merchantId?: string;
  paymentMethodType?: string;
  currency?: string;
  startDate?: Date;
  endDate?: Date;
  period?: AnalyticsPeriod;
}

/**
 * Payment analytics service
 */
class PaymentAnalyticsService {
  private static instance: PaymentAnalyticsService;
  private prisma: PrismaClient;

  /**
   * Private constructor for singleton pattern
   */
  private constructor() {
    // Initialize service
    this.prisma = new PrismaClient();
  }

  /**
   * Get singleton instance
   */
  public static getInstance(): PaymentAnalyticsService {
    if (!PaymentAnalyticsService.instance) {
      PaymentAnalyticsService.instance = new PaymentAnalyticsService();
    }
    return PaymentAnalyticsService.instance;
  }

  /**
   * Get payment analytics
   * @param filter Analytics filter
   */
  public async getPaymentAnalytics(filter: AnalyticsFilter = {}): Promise<Record<string, any>> {
    try {
      // Generate cache key
      const cacheKey: any = generateAnalyticsCacheKey('payments', {
        merchantId: filter.merchantId || 'all',
        paymentMethodType: filter.paymentMethodType || 'all',
        currency: filter.currency || 'all',
        period: filter.period || AnalyticsPeriod.DAY,
        startDate: filter.startDate?.toISOString() || 'default',
        endDate: filter.endDate?.toISOString() || 'default',
      });

      // Get TTL based on period
      const ttl: any = analyticsCacheService.getTTL('payments', filter.period);

      // Try to get from cache or execute query
      return await analyticsCacheService.wrap(
        cacheKey,
        async () => {
          // Prepare date range
          const { startDate, endDate } = this.getDateRange(filter);

          // Prepare filter with optimized query
          const where: any = {
            createdAt: { gte: startDate, lte: endDate },
          };

          if (filter.merchantId) {
            where.merchantId = filter.merchantId;
          }

          if (filter.paymentMethodType) {
            // Use join instead of nested query for better performance
            where.paymentMethod = {
              type: filter.paymentMethodType,
            };
          }

          if (filter.currency) {
            where.currency = filter.currency;
          }

          // Execute queries in parallel for better performance
          const [
            totalPayments,
            totalAmount,
            paymentsByStatus,
            paymentsByMethod,
            paymentsByCurrency,
            timeSeriesData,
            conversionRate,
            avgVerificationTime,
          ] = await Promise.all([
            // Get total payments count
            this.prisma.transaction.count({
              where,
            }),

            // Get total amount
            this.prisma.transaction.aggregate({
              where,
              _sum: { amount: true },
            }),

            // Get payments by status
            this.prisma.transaction.groupBy({
              by: ['status'],
              where,
              _count: { id: true },
              _sum: { amount: true },
            }),

            // Get payments by method with optimized query
            this.getPaymentsByMethod(where),

            // Get payments by currency
            this.prisma.transaction.groupBy({
              by: ['currency'],
              where,
              _count: { id: true },
              _sum: { amount: true },
            }),

            // Get time series data
            this.getTimeSeriesData(where, filter.period || AnalyticsPeriod.DAY),

            // Get conversion rate
            this.getConversionRate(where),

            // Get average verification time
            this.getAverageVerificationTime(where),
          ]);

          // Return analytics data
          return {
            totalPayments,
            totalAmount: totalAmount._sum.amount || 0,
            paymentsByStatus,
            paymentsByMethod,
            paymentsByCurrency,
            timeSeriesData,
            conversionRate,
            avgVerificationTime,
            period: filter.period || AnalyticsPeriod.DAY,
            startDate,
            endDate,
          };
        },
        ttl
      );
    } catch (error) {
      logger.error('Error getting payment analytics:', error);
      throw error;
    }
  }

  /**
   * Get payments by method with optimized query
   * @param where Filter conditions
   */
  private async getPaymentsByMethod(where): Promise<any[]> {
    try {
      // Use a more efficient query with a single join
      const result: any = await this.prisma.$queryRaw`
        SELECT
          pm.id AS "paymentMethodId",
          pm.name AS "methodName",
          pm.type AS "methodType",
          COUNT(t.id) AS "count",
          SUM(t.amount) AS "sum",
          COUNT(CASE WHEN t.status = 'verified' THEN 1 END) * 100.0 / COUNT(t.id) AS "successRate"
        FROM
          "Transaction" t
        JOIN
          "PaymentMethod" pm ON t."paymentMethodId" = pm.id
        WHERE
          t."createdAt" >= ${where.createdAt.gte} AND
          t."createdAt" <= ${where.createdAt.lte}
          ${where.merchantId ? ` AND t."merchantId" = ${where.merchantId}` : ''}
          ${where.currency ? ` AND t."currency" = ${where.currency}` : ''}
          ${where.paymentMethod?.type ? ` AND pm."type" = ${where.paymentMethod.type}` : ''}
        GROUP BY
          pm.id, pm.name, pm.type
        ORDER BY
          "count" DESC
      `;

      // Format the result to match the expected structure
      return (result as any).map((item: any) => ({
        paymentMethodId: item.paymentMethodId,
        methodName: item.methodName,
        methodType: item.methodType,
        _count: { id: parseInt(item.count) },
        _sum: { amount: parseFloat(item.sum) },
        successRate: parseFloat(item.successRate),
      }));
    } catch (error) {
      logger.error('Error getting payments by method:', error);

      // Fallback to the original implementation if raw query fails
      const paymentsByMethod: any = await this.prisma.transaction.groupBy({
        by: ['paymentMethodId'],
        where,
        _count: { id: true },
        _sum: { amount: true },
      });

      return await Promise.all(
        (paymentsByMethod as any).map(async (item: any) => {
          const method = await this.prisma.paymentMethod.findUnique({
            where: { id: item.paymentMethodId },
            select: { name: true, type: true },
          });

          // Calculate success rate
          const successCount = await this.prisma.transaction.count({
            where: {
              ...where,
              paymentMethodId: item.paymentMethodId,
              status: 'verified',
            },
          });

          const successRate = item._count.id > 0 ? (successCount / item._count.id) * 100 : 0;

          return {
            ...item,
            methodName: method?.name || 'Unknown',
            methodType: method?.type || 'Unknown',
            successRate,
          };
        })
      );
    }
  }

  /**
   * Get merchant analytics
   * @param merchantId Merchant ID
   * @param filter Analytics filter
   */
  public async getMerchantAnalytics(
    merchantId: string,
    filter: AnalyticsFilter = {}
  ): Promise<Record<string, any>> {
    try {
      // Add merchant ID to filter
      const merchantFilter: AnalyticsFilter = {
        ...filter,
        merchantId,
      };

      // Get payment analytics
      const paymentAnalytics: any = await this.getPaymentAnalytics(merchantFilter);

      // Get merchant details
      const merchant: any = await this.prisma.merchant.findUnique({
        where: { id: merchantId },
        select: { id: true, name: true, email: true, createdAt: true },
      });

      // Get merchant payment methods
      const paymentMethods: any = await this.prisma.paymentMethod.findMany({
        where: { merchantId },
        select: {
          id: true,
          name: true,
          type: true,
          isActive: true,
          _count: {
            select: {
              transactions: true,
            },
          },
        },
      });

      // Return merchant analytics
      return {
        merchant,
        paymentMethods,
        ...paymentAnalytics,
      };
    } catch (error) {
      logger.error(`Error getting merchant analytics for ${merchantId}:`, error);
      throw error;
    }
  }

  /**
   * Get payment method analytics
   * @param paymentMethodType Payment method type
   * @param filter Analytics filter
   */
  public async getPaymentMethodAnalytics(
    paymentMethodType: string,
    filter: AnalyticsFilter = {}
  ): Promise<Record<string, any>> {
    try {
      // Add payment method type to filter
      const methodFilter: AnalyticsFilter = {
        ...filter,
        paymentMethodType,
      };

      // Get payment analytics
      const paymentAnalytics: any = await this.getPaymentAnalytics(methodFilter);

      // Get payment methods of this type
      const paymentMethods: any = await this.prisma.paymentMethod.findMany({
        where: { type: paymentMethodType },
        select: {
          id: true,
          name: true,
          merchantId: true,
          isActive: true,
          _count: {
            select: {
              transactions: true,
            },
          },
        },
      });

      // Get success rate by payment method
      const successRateByMethod = await Promise.all(
        (paymentMethods as any).map(async (method: any) => {
          const successRate = await this.getSuccessRateForMethod(method.id, filter);
          return {
            methodId: method.id,
            methodName: method.name,
            merchantId: method.merchantId,
            successRate,
          };
        })
      );

      // Return payment method analytics
      return {
        paymentMethodType,
        paymentMethods,
        successRateByMethod,
        ...paymentAnalytics,
      };
    } catch (error) {
      logger.error(`Error getting payment method analytics for ${paymentMethodType}:`, error);
      throw error;
    }
  }

  /**
   * Get date range for analytics
   * @param filter Analytics filter
   */
  private getDateRange(filter: AnalyticsFilter): { startDate: Date; endDate: Date } {
    const endDate = filter.endDate || new Date();
    let startDate: any = filter.startDate;

    if (!startDate) {
      const now: Date = new Date();
      switch (filter.period) {
        case AnalyticsPeriod.DAY:
          startDate = new Date(now.setDate(now.getDate() - 1));
          break;
        case AnalyticsPeriod.WEEK:
          startDate = new Date(now.setDate(now.getDate() - 7));
          break;
        case AnalyticsPeriod.MONTH:
          startDate = new Date(now.setMonth(now.getMonth() - 1));
          break;
        case AnalyticsPeriod.YEAR:
          startDate = new Date(now.setFullYear(now.getFullYear() - 1));
          break;
        default:
          // Default to last 30 days
          startDate = new Date(now.setDate(now.getDate() - 30));
      }
    }

    return { startDate, endDate };
  }

  /**
   * Get time series data for analytics
   * @param where Filter conditions
   * @param period Time period
   */
  private async getTimeSeriesData(where, period: AnalyticsPeriod): Promise<any[]> {
    try {
      // Define the date format based on the period
      let dateFormat: string;
      let groupBy: string;

      switch (period) {
        case AnalyticsPeriod.DAY:
          // Group by hour
          dateFormat = 'YYYY-MM-DD HH24:00';
          groupBy = 'hour';
          break;
        case AnalyticsPeriod.WEEK:
        case AnalyticsPeriod.MONTH:
          // Group by day
          dateFormat = 'YYYY-MM-DD';
          groupBy = 'day';
          break;
        case AnalyticsPeriod.YEAR:
          // Group by month
          dateFormat = 'YYYY-MM';
          groupBy = 'month';
          break;
        default:
          // Default to day
          dateFormat = 'YYYY-MM-DD';
          groupBy = 'day';
      }

      // Use optimized raw SQL query with date formatting and grouping
      try {
        // PostgreSQL-specific query with date_trunc for efficient grouping
        const result: any = await this.prisma.$queryRaw`
          SELECT
            CASE
              WHEN ${groupBy} = 'hour' THEN TO_CHAR("createdAt", 'YYYY-MM-DD HH24:00')
              WHEN ${groupBy} = 'day' THEN TO_CHAR("createdAt", 'YYYY-MM-DD')
              WHEN ${groupBy} = 'month' THEN TO_CHAR("createdAt", 'YYYY-MM')
              ELSE TO_CHAR("createdAt", 'YYYY-MM-DD')
            END AS date,
            COUNT(*) AS count,
            SUM(amount) AS amount,
            COUNT(CASE WHEN status = 'verified' THEN 1 END) AS verified
          FROM
            "Transaction"
          WHERE
            "createdAt" >= ${where.createdAt.gte} AND
            "createdAt" <= ${where.createdAt.lte}
            ${where.merchantId ? ` AND "merchantId" = ${where.merchantId}` : ''}
            ${where.currency ? ` AND "currency" = ${where.currency}` : ''}
            ${
              where.paymentMethod?.type
                ? ` AND "paymentMethodId" IN (SELECT id FROM "PaymentMethod" WHERE type = ${where.paymentMethod.type})`
                : ''
            }
          GROUP BY
            date
          ORDER BY
            date ASC
        `;

        // Format the result
        return (result as any).map((item: any) => ({
          date: item.date,
          count: parseInt(item.count),
          amount: parseFloat(item.amount) || 0,
          verified: parseInt(item.verified) || 0,
        }));
      } catch (error) {
        logger.error('Error executing raw SQL for time series data:', error);

        // Fall back to the original implementation if raw query fails
        const transactions: any = await this.prisma.transaction.findMany({
          where,
          select: { id: true, amount: true, status: true, createdAt: true },
          orderBy: { createdAt: 'asc' },
        });

        // Group transactions by time period
        const groupedData: Record<string, { count: number; amount: number; verified: number }> = {};

        (transactions as any).forEach((tx: any) => {
          let key: string;
          const date = new Date(tx.createdAt);

          switch (period) {
            case AnalyticsPeriod.DAY:
              // Group by hour
              key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(
                date.getDate()
              ).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:00`;
              break;
            case AnalyticsPeriod.WEEK:
            case AnalyticsPeriod.MONTH:
              // Group by day
              key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(
                date.getDate()
              ).padStart(2, '0')}`;
              break;
            case AnalyticsPeriod.YEAR:
              // Group by month
              key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
              break;
            default:
              // Group by day
              key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(
                date.getDate()
              ).padStart(2, '0')}`;
          }

          if (!groupedData[key]) {
            groupedData[key] = { count: 0, amount: 0, verified: 0 };
          }

          groupedData[key].count++;
          groupedData[key].amount += tx.amount || 0;
          if (tx.status === 'verified') {
            groupedData[key].verified++;
          }
        });

        // Convert to array and sort by date
        return Object.entries(groupedData)
          .map(([key, value]) => ({
            date: key,
            count: value.count,
            amount: value.amount,
            verified: value.verified,
          }))
          .sort((a, b) => a.date.localeCompare(b.date));
      }
    } catch (error) {
      logger.error('Error getting time series data:', error);
      return [];
    }
  }

  /**
   * Get conversion rate (verified / total)
   * @param where Filter conditions
   */
  private async getConversionRate(where): Promise<number> {
    try {
      // In a real implementation, this would calculate the conversion rate
      // For now, we'll return a placeholder value
      return 0;
    } catch (error) {
      logger.error('Error getting conversion rate:', error);
      return 0;
    }
  }

  /**
   * Get average verification time
   * @param where Filter conditions
   */
  private async getAverageVerificationTime(where): Promise<number> {
    try {
      const verifiedTransactions: any = await this.prisma.transaction.findMany({
        where: {
          ...where,
          status: 'verified',
          verifiedAt: { not: null },
        },
        select: { createdAt: true, verifiedAt: true },
      });

      if (verifiedTransactions.length === 0) {
        return 0;
      }

      const totalTime = (verifiedTransactions as any).reduce((sum: number, tx: any) => {
        const createdAt = new Date(tx.createdAt).getTime();
        const verifiedAt = new Date(tx.verifiedAt!).getTime();
        return sum + (verifiedAt - createdAt);
      }, 0);

      // Return average time in seconds
      return totalTime / verifiedTransactions.length / 1000;
    } catch (error) {
      logger.error('Error getting average verification time:', error);
      return 0;
    }
  }

  /**
   * Get success rate for a payment method
   * @param methodId Payment method ID
   * @param filter Analytics filter
   */
  private async getSuccessRateForMethod(
    methodId: string,
    filter: AnalyticsFilter = {}
  ): Promise<number> {
    try {
      // Prepare date range
      const { startDate, endDate } = this.getDateRange(filter);

      // Prepare filter
      const where: any = {
        paymentMethodId: methodId,
        createdAt: { gte: startDate, lte: endDate },
      };

      if (filter.merchantId) {
        where.merchantId = filter.merchantId;
      }

      if (filter.currency) {
        where.currency = filter.currency;
      }

      // In a real implementation, this would calculate the success rate
      // For now, we'll return a placeholder value
      return 0;
    } catch (error) {
      logger.error('Error getting success rate for method:', error);
      return 0;
    }
  }
}

// Export singleton instance
export const paymentAnalyticsService = PaymentAnalyticsService.getInstance();

export default paymentAnalyticsService;
