// jscpd:ignore-file
/**
 * Merchant Self-Service Controller
 *
 * This controller handles API requests related to merchant self-service tools.
 */

import { Request, Response, NextFunction } from 'express';
import { BaseController } from "./base.controller";
import { BaseController } from "./base.controller";
import {
    MerchantSelfServiceService,
    ApiKeyPermission,
    WebhookEventType,
    NotificationChannel
} from "../services/merchant-self-service.service";
import { logger } from "../utils/logger";
import { asyncHandler } from '../utils/asyncHandler';
import { Merchant } from '../types';
import {
    MerchantSelfServiceService,
    ApiKeyPermission,
    WebhookEventType,
    NotificationChannel
} from "../services/merchant-self-service.service";
import { logger } from "../utils/logger";
import { asyncHandler } from '../utils/asyncHandler';
import { Merchant } from '../types';

/**
 * Merchant self-service controller
 */
export class MerchantSelfServiceController extends BaseController {
    private merchantSelfServiceService: MerchantSelfServiceService;

    constructor() {
        super();
        this.merchantSelfServiceService = new MerchantSelfServiceService();
    }

    /**
   * Create API key
   */
    createApiKey = this.asyncHandler(async (req: Request, res: Response) => {
        try {
            const { merchantId } = req.params;
            const { name, permissions, expiresAt } = req.body;

            // Validate required fields
            if (!name || !permissions || !Array.isArray(permissions)) {
                return res.badRequest("Name and permissions array are required");
            }

            // Validate permissions
            const validPermissions: any =Object.values(ApiKeyPermission);
            const invalidPermissions: any =permissions.filter(p => !validPermissions.includes(p));

            if (invalidPermissions.length > 0) {
                return res.badRequest(`Invalid permissions: ${invalidPermissions.join(", ")}. Valid permissions are: ${validPermissions.join(", ")}`);
            }

            // Create API key
            const apiKey: any =await this.merchantSelfServiceService.createApiKey(
                merchantId,
                name,
                permissions,
                expiresAt ? new Date(expiresAt) : undefined
            );

            return res.success("API key created", apiKey);
        } catch (error) {
            logger.error("Error creating API key:", error);
            return res.serverError("Failed to create API key");
        }
    });

    /**
   * Get merchant API keys
   */
    getMerchantApiKeys = this.asyncHandler(async (req: Request, res: Response) => {
        try {
            const { merchantId } = req.params;

            // Get API keys
            const apiKeys: any =await this.merchantSelfServiceService.getMerchantApiKeys(merchantId);

            return res.success("Merchant API keys retrieved", apiKeys);
        } catch (error) {
            logger.error("Error getting merchant API keys:", error);
            return res.serverError("Failed to get merchant API keys");
        }
    });

    /**
   * Delete API key
   */
    deleteApiKey = this.asyncHandler(async (req: Request, res: Response) => {
        try {
            const { apiKeyId } = req.params;

            // Delete API key
            const apiKey: any =await this.merchantSelfServiceService.deleteApiKey(apiKeyId);

            return res.success("API key deleted", apiKey);
        } catch (error) {
            logger.error("Error deleting API key:", error);
            return res.serverError("Failed to delete API key");
        }
    });

    /**
   * Create webhook
   */
    createWebhook = this.asyncHandler(async (req: Request, res: Response) => {
        try {
            const { merchantId } = req.params;
            const { url, events } = req.body;

            // Validate required fields
            if (!url || !events || !Array.isArray(events)) {
                return res.badRequest("URL and events array are required");
            }

            // Validate URL
            try {
                new URL(url);
            } catch (error) {
                return res.badRequest("Invalid URL");
            }

            // Validate events
            const webhook: any =await this.merchantSelfServiceService.createWebhook(
                merchantId,
                url,
                events
            );

            return res.success("Webhook created", webhook);
        } catch (error) {
            logger.error("Error creating webhook:", error);
            return res.serverError("Failed to create webhook");
        }
    });

    /**
   * Get merchant webhooks
   */
    getMerchantWebhooks = this.asyncHandler(async (req: Request, res: Response) => {
        try {
            const { merchantId } = req.params;

            // Get webhooks
            const webhooks: any =await this.merchantSelfServiceService.getMerchantWebhooks(merchantId);

            return res.success("Merchant webhooks retrieved", webhooks);
        } catch (error) {
            logger.error("Error getting merchant webhooks:", error);
            return res.serverError("Failed to get merchant webhooks");
        }
    });

    /**
   * Update webhook
   */
    updateWebhook = this.asyncHandler(async (req: Request, res: Response) => {
        try {
            const { webhookId } = req.params;
            const { url, events, isActive } = req.body;

            // Validate URL if provided
            if (url) {
                try {
                    new URL(url);
                } catch (error) {
                    return res.badRequest("Invalid URL");
                }
            }

            // Validate events if provided
            if (events) {
                if (!Array.isArray(events)) {
                    return res.badRequest("Events must be an array");
                }
            }

            // Update webhook
            const webhook: any =await this.merchantSelfServiceService.updateWebhook(
                webhookId,
                url,
                events,
                isActive
            );

            return res.success("Webhook updated", webhook);
        } catch (error) {
            logger.error("Error updating webhook:", error);
            return res.serverError("Failed to update webhook");
        }
    });

    /**
   * Delete webhook
   */
    deleteWebhook = this.asyncHandler(async (req: Request, res: Response) => {
        try {
            const { webhookId } = req.params;

            // Delete webhook
            const webhook: any =await this.merchantSelfServiceService.deleteWebhook(webhookId);

            return res.success("Webhook deleted", webhook);
        } catch (error) {
            logger.error("Error deleting webhook:", error);
            return res.serverError("Failed to delete webhook");
        }
    });

    /**
   * Set notification preference
   */
    setNotificationPreference = this.asyncHandler(async (req: Request, res: Response) => {
        try {
            const { merchantId } = req.params;
            const { channel, eventType, enabled } = req.body;

            // Validate required fields
            if (!channel || !eventType || enabled === undefined) {
                return res.badRequest("Channel, eventType, and enabled are required");
            }

            // Validate channel
            if (!Object.values(NotificationChannel).includes(channel)) {
                return res.badRequest(`Invalid channel. Must be one of: ${Object.values(NotificationChannel).join(", ")}`);
            }

            // Validate event type
            if (!Object.values(WebhookEventType).includes(eventType)) {
                return res.badRequest(`Invalid event type. Must be one of: ${Object.values(WebhookEventType).join(", ")}`);
            }

            // Set notification preference
            const preference: any =await this.merchantSelfServiceService.setNotificationPreference(
                merchantId,
                channel,
                eventType,
                enabled
            );

            return res.success("Notification preference set", preference);
        } catch (error) {
            logger.error("Error setting notification preference:", error);
            return res.serverError("Failed to set notification preference");
        }
    });

    /**
   * Get merchant notification preferences
   */
    getMerchantNotificationPreferences = this.asyncHandler(async (req: Request, res: Response) => {
        try {
            const { merchantId } = req.params;

            // Get notification preferences
            const preferences: any =await this.merchantSelfServiceService.getMerchantNotificationPreferences(merchantId);

            return res.success("Merchant notification preferences retrieved", preferences);
        } catch (error) {
            logger.error("Error getting merchant notification preferences:", error);
            return res.serverError("Failed to get merchant notification preferences");
        }
    });
}
