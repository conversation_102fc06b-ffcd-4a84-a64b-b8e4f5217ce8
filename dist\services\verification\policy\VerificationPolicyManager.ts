// jscpd:ignore-file
/**
 * Verification Policy Manager
 * 
 * Manages verification policies.
 */

import { VerificationPolicy } from "./VerificationPolicy";
import { VerificationRequest } from "../../../interfaces/verification/IVerificationStrategy";
import { logger } from "../../../lib/logger";
import { VerificationRequest } from "../../../interfaces/verification/IVerificationStrategy";
import { logger } from "../../../lib/logger";

/**
 * Verification policy manager
 */
export class VerificationPolicyManager {
    private static instance: VerificationPolicyManager;
    private policies: Map<string, VerificationPolicy> = new Map();
  
    /**
   * Get the singleton instance
   */
    public static getInstance(): VerificationPolicyManager {
        if (!VerificationPolicyManager.instance) {
            VerificationPolicyManager.instance = new VerificationPolicyManager();
        }
    
        return VerificationPolicyManager.instance;
    }
  
    /**
   * Register a policy
   * 
   * @param policy Verification policy
   */
    public registerPolicy(policy: VerificationPolicy): void {
        const name = policy.getName();
        this.policies.set(name, policy);
    
        logger.debug(`Registered verification policy: ${name}`);
    }
  
    /**
   * Get a policy by name
   * 
   * @param name Policy name
   * @returns Verification policy or undefined if not found
   */
    public getPolicy(name: string): VerificationPolicy | undefined {
        return this.policies.get(name);
    }
  
    /**
   * Get all policies
   * 
   * @returns Array of verification policies
   */
    public getAllPolicies(): VerificationPolicy[] {
        return Array.from(this.policies.values());
    }
  
    /**
   * Find policies that apply to a verification request
   * 
   * @param request Verification request
   * @returns Array of applicable verification policies
   */
    public findApplicablePolicies(request: VerificationRequest): VerificationPolicy[] {
        const applicablePolicies: VerificationPolicy[] = [];
    
        for (const policy of this.policies.values()) {
            if (policy.appliesTo(request)) {
                applicablePolicies.push(policy);
            }
        }
    
        logger.debug(`Found ${applicablePolicies.length} applicable verification policies`, {
            transactionId: request.transactionId,
            merchantId: request.merchantId,
            paymentMethodType: request.paymentMethodType,
            amount: request.amount,
            currency: request.currency
        });
    
        return applicablePolicies;
    }
  
    /**
   * Get required verification methods for a request
   * 
   * @param request Verification request
   * @returns Array of required verification method types
   */
    public getRequiredMethods(request: VerificationRequest): string[] {
        const applicablePolicies: any =this.findApplicablePolicies(request);
        const requiredMethods: any =new Set<string>();
    
        for (const policy of applicablePolicies) {
            for (const method of policy.getRequiredMethods()) {
                requiredMethods.add(method);
            }
        }
    
        return Array.from(requiredMethods);
    }
  
    /**
   * Remove a policy
   * 
   * @param name Policy name
   * @returns True if policy was removed, false if not found
   */
    public removePolicy(name: string): boolean {
        return this.policies.delete(name);
    }
  
    /**
   * Clear all policies
   */
    public clearPolicies(): void {
        this.policies.clear();
        logger.debug("Cleared all verification policies");
    }
}

// Export singleton instance
export const verificationPolicyManager: any =VerificationPolicyManager.getInstance();
