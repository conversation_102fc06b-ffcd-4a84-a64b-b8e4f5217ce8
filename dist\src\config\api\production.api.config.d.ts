/**
 * Production API Configuration
 *
 * This file contains the configuration for third-party APIs in production.
 * It includes settings for API keys, endpoints, and other production-specific options.
 */
/**
 * Binance API configuration for production
 */
export declare const binanceApiConfig: any;
/**
 * Email configuration for production
 */
export declare const emailConfig: any;
/**
 * Sentry configuration for production
 */
export declare const sentryConfig: any;
/**
 * Initialize third-party API configurations
 * This function validates the configurations and logs warnings for missing values
 */
export declare const initializeApiConfigurations: any;
/**
 * Production API configuration
 */
export declare const productionApiConfig: any;
export default productionApiConfig;
//# sourceMappingURL=production.api.config.d.ts.map