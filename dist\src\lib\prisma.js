"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// jscpd:ignore-file
const client_1 = require("@prisma/client");
const logger_1 = require("./logger");
const secrets_manager_1 = require("../utils/secrets-manager");
/**
 * Create a new PrismaClient instance with appropriate logging
 * Uses database URL from secrets manager
 */
const createPrismaClient = async () => {
    const isProd = process.env.NODE_ENV === 'production';
    // Initialize secrets manager
    await secrets_manager_1.secretsManager.initialize();
    // Get database URL from secrets manager
    const databaseUrl = secrets_manager_1.secretsManager.getDatabaseUrl();
    // Configure logging based on environment
    const prismaOptions = {
        datasources: {
            db: {
                url: databaseUrl,
            },
        },
        log: isProd ? [] : [
            { level: 'query', emit: 'event' },
            { level: 'info', emit: 'event' },
            { level: 'warn', emit: 'event' },
            { level: 'error', emit: 'event' }
        ]
    };
    const client = new client_1.PrismaClient(prismaOptions);
    // Set up event listeners for logging in development
    if (!isProd) {
        // Use any type for event handlers to avoid type errors
        // This is a workaround for the Prisma client type issues
        client.$use(async (params, next) => {
            const before = Date.now();
            const result = await next(params);
            const after = Date.now();
            logger_1.logger.debug(`Query: ${params.model}.${params.action}`, {
                params: JSON.stringify(params.args),
                duration: after - before,
            });
            return result;
        });
        // Log other events using console directly to avoid type errors
        logger_1.logger.info('Prisma client initialized with logging middleware');
    }
    return client;
};
// Create a singleton instance of PrismaClient
let prisma;
// Initialize Prisma client - synchronous initialization to avoid "PrismaClient is not defined" errors
if (!global.prisma) {
    // Use a synchronous approach for the initial creation
    try {
        // For initial synchronous creation, use the environment variable directly
        // The async secrets will be loaded when createPrismaClient is called later
        const databaseUrl = process.env.DATABASE_URL;
        const isProd = process.env.NODE_ENV === 'production';
        // Configure logging based on environment
        const prismaOptions = {
            datasources: {
                db: {
                    url: databaseUrl,
                },
            },
            log: isProd ? [] : [
                { level: 'query', emit: 'event' },
                { level: 'info', emit: 'event' },
                { level: 'warn', emit: 'event' },
                { level: 'error', emit: 'event' }
            ]
        };
        global.prisma = new client_1.PrismaClient(prismaOptions);
        prisma = global.prisma;
        logger_1.logger.info('PrismaClient initialized synchronously');
    }
    catch (error) {
        logger_1.logger.error('Failed to initialize PrismaClient synchronously', error);
        // Create a minimal client that will be replaced later
        global.prisma = new client_1.PrismaClient();
        prisma = global.prisma;
    }
}
else {
    prisma = global.prisma;
}
// Initialize Prisma client with full configuration asynchronously
const initializePrisma = async () => {
    try {
        // Replace the synchronously created client with the properly configured one
        global.prisma = await createPrismaClient();
        prisma = global.prisma;
        return global.prisma;
    }
    catch (error) {
        logger_1.logger.error('Failed to initialize PrismaClient with full configuration', error);
        // Continue using the existing client
        return global.prisma;
    }
};
// Test database connection
const testConnection = async () => {
    try {
        // Execute a simple query to test the connection
        await prisma.$queryRaw `SELECT 1`;
        logger_1.logger.info('Database connection established successfully');
        return true;
    }
    catch (error) {
        // Check if the error is related to authentication
        if (error instanceof Error && error.message.includes('Authentication failed')) {
            logger_1.logger.error('Database authentication failed. Please check your database credentials.');
            logger_1.logger.info('Attempting to connect with default credentials...');
            try {
                // Try connecting with environment variables
                const host = process.env.DB_HOST || 'localhost';
                const port = process.env.DB_PORT || '5432';
                const username = process.env.DB_USERNAME || 'postgres';
                const password = process.env.DB_PASSWORD || '';
                const database = process.env.DB_NAME || 'Amazingpay';
                const fallbackUrl = `postgresql://${username}:${password}@${host}:${port}/${database}?schema=public`;
                logger_1.logger.info('Attempting to connect with environment variables');
                const tempPrisma = new client_1.PrismaClient({
                    datasources: {
                        db: {
                            url: fallbackUrl,
                        },
                    },
                });
                await tempPrisma.$queryRaw `SELECT 1`;
                logger_1.logger.info('Connected to database with default credentials');
                // Update the global prisma instance
                global.prisma = tempPrisma;
                prisma = tempPrisma;
                return true;
            }
            catch (fallbackError) {
                logger_1.logger.error('Failed to connect with default credentials', fallbackError);
                return false;
            }
        }
        else {
            logger_1.logger.error('Failed to connect to database', error);
            return false;
        }
    }
};
// Initialize connection with full configuration asynchronously
(async () => {
    try {
        await initializePrisma();
        const connected = await testConnection();
        if (!connected) {
            logger_1.logger.warn('Database connection failed. Some features may not work correctly.');
            logger_1.logger.info('The application will continue to run with limited functionality.');
            // Set up a retry mechanism
            const retryInterval = 60000; // 1 minute
            logger_1.logger.info(`Will retry database connection every ${retryInterval / 1000} seconds.`);
            setInterval(async () => {
                logger_1.logger.info('Retrying database connection...');
                const retrySuccess = await testConnection();
                if (retrySuccess) {
                    logger_1.logger.info('Successfully reconnected to the database!');
                }
            }, retryInterval);
        }
    }
    catch (error) {
        logger_1.logger.error('Error during database initialization', error);
        // Don't exit the process, allow the application to continue with basic functionality
        logger_1.logger.info('The application will continue to run with limited functionality.');
    }
})();
// Handle graceful shutdown
process.on('SIGINT', async () => {
    logger_1.logger.info('Received SIGINT signal, closing database connections');
    if (prisma) {
        await prisma.$disconnect();
    }
    process.exit(0);
});
process.on('SIGTERM', async () => {
    logger_1.logger.info('Received SIGTERM signal, closing database connections');
    if (prisma) {
        await prisma.$disconnect();
    }
    process.exit(0);
});
// Export initialized prisma client
exports.default = prisma;
//# sourceMappingURL=prisma.js.map