/**
 * Verification Policy Controller
 *
 * Handles verification policy operations.
 */
/**
 * Get all verification policies
 */
export declare const getAllPolicies: any;
/**
 * Create a verification policy
 */
export declare const createPolicy: any;
/**
 * Get applicable policies for a verification request
 */
export declare const getApplicablePolicies: any;
/**
 * Verify using policy chain
 */
export declare const verifyWithPolicyChain: any;
declare const _default: {
    getAllPolicies: any;
    createPolicy: any;
    getApplicablePolicies: any;
    verifyWithPolicyChain: any;
};
export default _default;
//# sourceMappingURL=verification-policy.controller.d.ts.map