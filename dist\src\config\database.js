"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getDatabaseUrl = exports.testConnection = void 0;
// jscpd:ignore-file
const logger_1 = require("../lib/logger");
const secrets_manager_1 = require("../utils/secrets-manager");
const prisma_1 = __importDefault(require("../lib/prisma"));
/**
 * Test database connection
 * Uses the connection string from secrets manager
 */
const testConnection = async () => {
    try {
        // Initialize secrets manager if not already initialized
        await secrets_manager_1.secretsManager.initialize();
        // Connect to database
        await prisma_1.default.$connect();
        logger_1.logger.info("Database connection established successfully");
        return true;
    }
    catch (error) {
        logger_1.logger.error(`Database connection failed: ${error}`);
        return false;
    }
};
exports.testConnection = testConnection;
/**
 * Get database connection URL
 * This is used by Prisma client
 */
const getDatabaseUrl = () => {
    return secrets_manager_1.secretsManager.getDatabaseUrl();
};
exports.getDatabaseUrl = getDatabaseUrl;
exports.default = prisma_1.default;
//# sourceMappingURL=database.js.map