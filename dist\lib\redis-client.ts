// jscpd:ignore-file
/**
 * Redis client with fallback to in-memory store
 * This provides a unified interface for Redis operations with a fallback to an in-memory store
 *
 * @deprecated Use redisManager from './redis-manager.ts' instead
 */

import { logger } from "./logger";
import redisManager from "./redis-manager";

class RedisClient {
    /**
     * Get a value from Redis or memory store
     * @param key Key
     * @returns Value or null if not found
     */
    async get(key: string): Promise<any> {
        return await redisManager.get(key);
    }

    /**
     * Set a value in Redis or memory store
     * @param key Key
     * @param value Value
     * @param expiry Expiry in seconds
     */
    async set(key: string, value, expiry?: number): Promise<void> {
        await redisManager.set(key, value, expiry);
    }

    /**
     * Delete a key from Redis or memory store
     * @param key Key
     */
    async del(key: string): Promise<void> {
        await redisManager.del(key);
    }

    /**
     * Get connection status
     * @returns True if connected to Redis or using memory store
     */
    getIsConnected(): boolean {
        return redisManager.getIsConnected();
    }

    /**
     * Close Redis client
     */
    async close(): Promise<void> {
        await redisManager.close();
    }
}

// Export singleton instance
export const redisClient: any =new RedisClient();

// Log deprecation warning
logger.warn("RedisClient is deprecated. Use redisManager from './redis-manager.ts' instead.");

export default redisClient;
