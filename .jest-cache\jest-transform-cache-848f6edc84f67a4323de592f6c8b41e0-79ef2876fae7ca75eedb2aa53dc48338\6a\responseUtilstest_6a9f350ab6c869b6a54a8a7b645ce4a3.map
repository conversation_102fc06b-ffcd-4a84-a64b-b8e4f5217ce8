{"file": "F:\\Amazing pay flow\\src\\tests\\shared\\modules\\utils\\responseUtils.test.ts", "mappings": ";;AAAA,kFAA2G;AAG3G,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;IAC7B,IAAI,YAA+B,CAAC;IAEpC,UAAU,CAAC,GAAG,EAAE;QACd,YAAY,GAAG;YACb,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;YAClC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;SACjC,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM;YACN,IAAA,2BAAW,EAAC,YAAwB,CAAC,CAAC;YAEtC,SAAS;YACT,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;YACtD,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC;gBAC7C,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,SAAS;gBAClB,IAAI,EAAE,EAAE;aACT,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,UAAU;YACV,MAAM,IAAI,GAAQ,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;YAC1C,MAAM,OAAO,GAAG,gBAAgB,CAAC;YACjC,MAAM,UAAU,GAAU,GAAG,CAAC;YAE9B,MAAM;YACN,IAAA,2BAAW,EAAC,YAAwB,EAAE,IAAI,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;YAEjE,SAAS;YACT,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;YAC7D,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC;gBAC7C,OAAO,EAAE,IAAI;gBACb,OAAO;gBACP,IAAI;aACL,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,WAAW,EAAE,GAAG,EAAE;QACzB,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM;YACN,IAAA,yBAAS,EAAC,YAAwB,CAAC,CAAC;YAEpC,SAAS;YACT,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;YACtD,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC;gBAC7C,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,OAAO;gBAChB,KAAK,EAAE,IAAI;aACZ,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,UAAU;YACV,MAAM,OAAO,GAAU,cAAc,CAAC;YACtC,MAAM,UAAU,GAAU,GAAG,CAAC;YAC9B,MAAM,KAAK,GAAO,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;YAE1C,MAAM;YACN,IAAA,yBAAS,EAAC,YAAwB,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;YAEhE,SAAS;YACT,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;YAC7D,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC;gBAC7C,OAAO,EAAE,KAAK;gBACd,OAAO;gBACP,KAAK,EAAG,KAAe,CAAC,OAAO;aAChC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,UAAU;YACV,MAAM,KAAK,GAAQ,EAAE,OAAO,EAAE,eAAe,EAAE,CAAC;YAEhD,MAAM;YACN,IAAA,yBAAS,EAAC,YAAwB,EAAE,OAAO,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YAEzD,SAAS;YACT,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC;gBAC7C,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,OAAO;gBAChB,KAAK,EAAE,eAAe;aACvB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,UAAU;YACV,MAAM,KAAK,GAAU,cAAc,CAAC;YAEpC,MAAM;YACN,IAAA,yBAAS,EAAC,YAAwB,EAAE,OAAO,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YAEzD,SAAS;YACT,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC;gBAC7C,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,OAAO;gBAChB,KAAK,EAAE,cAAc;aACtB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,UAAU;YACV,MAAM,OAAO,GAAW,IAAI,CAAC;YAC7B,MAAM,OAAO,GAAU,iBAAiB,CAAC;YACzC,MAAM,IAAI,GAAQ,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC;YAE5B,MAAM;YACN,MAAM,MAAM,GAAO,IAAA,iCAAiB,EAAC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;YAE7D,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC;gBACrB,OAAO;gBACP,OAAO;gBACP,IAAI;gBACJ,KAAK,EAAE,IAAI;aACZ,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,UAAU;YACV,MAAM,OAAO,GAAW,KAAK,CAAC;YAC9B,MAAM,OAAO,GAAU,eAAe,CAAC;YACvC,MAAM,KAAK,GAAU,eAAe,CAAC;YAErC,MAAM;YACN,MAAM,MAAM,GAAO,IAAA,iCAAiB,EAAC,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;YAEpE,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC;gBACrB,OAAO;gBACP,OAAO;gBACP,IAAI,EAAE,IAAI;gBACV,KAAK;aACN,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,UAAU;YACV,MAAM,OAAO,GAAW,IAAI,CAAC;YAC7B,MAAM,OAAO,GAAU,SAAS,CAAC;YAEjC,MAAM;YACN,MAAM,MAAM,GAAO,IAAA,iCAAiB,EAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAEvD,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC;gBACrB,OAAO;gBACP,OAAO;gBACP,IAAI,EAAE,IAAI;gBACV,KAAK,EAAE,IAAI;aACZ,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["F:\\Amazing pay flow\\src\\tests\\shared\\modules\\utils\\responseUtils.test.ts"], "sourcesContent": ["import { sendSuccess, sendError, createApiResponse } from '../../../../shared/modules/utils/responseUtils';\nimport { Response } from 'express';\n\ndescribe('responseUtils', () => {\n  let mockResponse: Partial<Response>;\n\n  beforeEach(() => {\n    mockResponse = {\n      status: jest.fn().mockReturnThis(),\n      json: jest.fn().mockReturnThis()\n    };\n  });\n\n  describe('sendSuccess', () => {\n    it('should send a success response with default values', () => {\n      // Act\n      sendSuccess(mockResponse as Response);\n      \n      // Assert\n      expect(mockResponse.status).toHaveBeenCalledWith(200);\n      expect(mockResponse.json).toHaveBeenCalledWith({\n        success: true,\n        message: 'Success',\n        data: {}\n      });\n    });\n\n    it('should send a success response with custom values', () => {\n      // Arrange\n      const data: any = { id: 1, name: 'Test' };\n      const message = 'Custom message';\n      const statusCode: number =201;\n      \n      // Act\n      sendSuccess(mockResponse as Response, data, message, statusCode);\n      \n      // Assert\n      expect(mockResponse.status).toHaveBeenCalledWith(statusCode);\n      expect(mockResponse.json).toHaveBeenCalledWith({\n        success: true,\n        message,\n        data\n      });\n    });\n  });\n\n  describe('sendError', () => {\n    it('should send an error response with default values', () => {\n      // Act\n      sendError(mockResponse as Response);\n      \n      // Assert\n      expect(mockResponse.status).toHaveBeenCalledWith(500);\n      expect(mockResponse.json).toHaveBeenCalledWith({\n        success: false,\n        message: 'Error',\n        error: null\n      });\n    });\n\n    it('should send an error response with custom values', () => {\n      // Arrange\n      const message: string ='Custom error';\n      const statusCode: number =400;\n      const error: any =new Error('Test error');\n      \n      // Act\n      sendError(mockResponse as Response, message, statusCode, error);\n      \n      // Assert\n      expect(mockResponse.status).toHaveBeenCalledWith(statusCode);\n      expect(mockResponse.json).toHaveBeenCalledWith({\n        success: false,\n        message,\n        error: (error as Error).message\n      });\n    });\n\n    it('should handle error objects with message property', () => {\n      // Arrange\n      const error: any = { message: 'Error message' };\n      \n      // Act\n      sendError(mockResponse as Response, 'Error', 500, error);\n      \n      // Assert\n      expect(mockResponse.json).toHaveBeenCalledWith({\n        success: false,\n        message: 'Error',\n        error: 'Error message'\n      });\n    });\n\n    it('should handle non-object errors', () => {\n      // Arrange\n      const error: string ='String error';\n      \n      // Act\n      sendError(mockResponse as Response, 'Error', 500, error);\n      \n      // Assert\n      expect(mockResponse.json).toHaveBeenCalledWith({\n        success: false,\n        message: 'Error',\n        error: 'String error'\n      });\n    });\n  });\n\n  describe('createApiResponse', () => {\n    it('should create a success response', () => {\n      // Arrange\n      const success: boolean =true;\n      const message: string ='Success message';\n      const data: any = { id: 1 };\n      \n      // Act\n      const result: any =createApiResponse(success, message, data);\n      \n      // Assert\n      expect(result).toEqual({\n        success,\n        message,\n        data,\n        error: null\n      });\n    });\n\n    it('should create an error response', () => {\n      // Arrange\n      const success: boolean =false;\n      const message: string ='Error message';\n      const error: string ='Error details';\n      \n      // Act\n      const result: any =createApiResponse(success, message, null, error);\n      \n      // Assert\n      expect(result).toEqual({\n        success,\n        message,\n        data: null,\n        error\n      });\n    });\n\n    it('should use default values when not provided', () => {\n      // Arrange\n      const success: boolean =true;\n      const message: string ='Message';\n      \n      // Act\n      const result: any =createApiResponse(success, message);\n      \n      // Assert\n      expect(result).toEqual({\n        success,\n        message,\n        data: null,\n        error: null\n      });\n    });\n  });\n});\n"], "version": 3}