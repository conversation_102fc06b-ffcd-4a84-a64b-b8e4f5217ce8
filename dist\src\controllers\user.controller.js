"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserController = void 0;
const BaseController_1 = require("../../core/BaseController");
const user_service_1 = require("../../services/refactored/user.service");
const ErrorFactory_1 = require("../../utils/errors/ErrorFactory");
const bcryptjs_1 = __importDefault(require("bcryptjs"));
/**
 * User controller
 * This controller handles user-related operations
 */
class UserController extends BaseController_1.BaseController {
    /**
     * Create a new user controller
     */
    constructor() {
        super();
        /**
         * Get all users
         */
        this.getUsers = this.createHandler(async (req, res) => {
            // Check authorization
            const { userRole } = this.checkAuthorization(req);
            // Only admins can get all users
            this.checkAdminRole(userRole);
            // Parse pagination parameters
            const { limit, offset } = this.parsePagination(req);
            // Get users
            const result = await this.userService.getUsers({
                limit,
                offset
            });
            // Send paginated response
            return this.sendPaginatedSuccess(res, result.data, result.total, limit, offset);
        });
        /**
         * Get a user by ID
         */
        this.getUser = this.createHandler(async (req, res) => {
            // Check authorization
            const { userRole, userId } = this.checkAuthorization(req);
            // Get user ID from params
            const { id } = req.params;
            // Check if user has permission to view this user
            if (userRole !== 'ADMIN' && userId !== id) {
                throw ErrorFactory_1.ErrorFactory.authorization('You do not have permission to view this user');
            }
            // Get user
            const user = await this.userService.getUserById(id);
            // Check if user exists
            if (!user) {
                throw ErrorFactory_1.ErrorFactory.notFound('User', id);
            }
            // Send success response
            return this.sendSuccess(res, user);
        });
        /**
         * Create a new user
         */
        this.createUser = this.createHandler(async (req, res) => {
            // Check authorization
            const { userRole } = this.checkAuthorization(req);
            // Only admins can create users
            this.checkAdminRole(userRole);
            // Get request body
            const { email, password, name, role, merchantId } = req.body;
            // Validate required fields
            if (!email || !password || !name) {
                throw ErrorFactory_1.ErrorFactory.validation('Email, password, and name are required');
            }
            // Check if email is valid
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                throw ErrorFactory_1.ErrorFactory.validation('Invalid email format');
            }
            // Check if password is strong enough
            if (password.length < 8) {
                throw ErrorFactory_1.ErrorFactory.validation('Password must be at least 8 characters long');
            }
            // Hash password
            const hashedPassword = await bcryptjs_1.default.hash(password, 10);
            // Create user
            const user = await this.userService.createUser({
                email,
                hashedPassword,
                name,
                role: role || 'USER',
                merchantId
            });
            // Send success response
            return this.sendSuccess(res, user, 201);
        });
        /**
         * Update a user
         */
        this.updateUser = this.createHandler(async (req, res) => {
            // Check authorization
            const { userRole, userId } = this.checkAuthorization(req);
            // Get user ID from params
            const { id } = req.params;
            // Check if user has permission to update this user
            if (userRole !== 'ADMIN' && userId !== id) {
                throw ErrorFactory_1.ErrorFactory.authorization('You do not have permission to update this user');
            }
            // Get request body
            const { name, email, role, merchantId, password } = req.body;
            // Get user
            const user = await this.userService.getUserById(id);
            // Check if user exists
            if (!user) {
                throw ErrorFactory_1.ErrorFactory.notFound('User', id);
            }
            // Prepare update data
            const updateData = {};
            if (name)
                updateData.name = name;
            if (email) {
                // Check if email is valid
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(email)) {
                    throw ErrorFactory_1.ErrorFactory.validation('Invalid email format');
                }
                updateData.email = email;
            }
            // Only admins can update role and merchantId
            if (userRole === 'ADMIN') {
                if (role)
                    updateData.role = role;
                if (merchantId !== undefined)
                    updateData.merchantId = merchantId;
            }
            // Update password if provided
            if (password) {
                // Check if password is strong enough
                if (password.length < 8) {
                    throw ErrorFactory_1.ErrorFactory.validation('Password must be at least 8 characters long');
                }
                // Hash password
                updateData.hashedPassword = await bcryptjs_1.default.hash(password, 10);
            }
            // Update user
            const updatedUser = await this.userService.updateUser(id, updateData);
            // Send success response
            return this.sendSuccess(res, updatedUser);
        });
        /**
         * Delete a user
         */
        this.deleteUser = this.createHandler(async (req, res) => {
            // Check authorization
            const { userRole } = this.checkAuthorization(req);
            // Only admins can delete users
            this.checkAdminRole(userRole);
            // Get user ID from params
            const { id } = req.params;
            // Delete user
            await this.userService.deleteUser(id);
            // Send success response
            return this.sendSuccess(res, { message: 'User deleted successfully' });
        });
        /**
         * Get current user
         */
        this.getCurrentUser = this.createHandler(async (req, res) => {
            // Check authorization
            const { userId } = this.checkAuthorization(req);
            // Get user
            const user = await this.userService.getUserById(userId);
            // Check if user exists
            if (!user) {
                throw ErrorFactory_1.ErrorFactory.notFound('User', userId);
            }
            // Send success response
            return this.sendSuccess(res, user);
        });
        /**
         * Update current user
         */
        this.updateCurrentUser = this.createHandler(async (req, res) => {
            // Check authorization
            const { userId } = this.checkAuthorization(req);
            // Get request body
            const { name, email, password } = req.body;
            // Prepare update data
            const updateData = {};
            if (name)
                updateData.name = name;
            if (email) {
                // Check if email is valid
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(email)) {
                    throw ErrorFactory_1.ErrorFactory.validation('Invalid email format');
                }
                updateData.email = email;
            }
            // Update password if provided
            if (password) {
                // Check if password is strong enough
                if (password.length < 8) {
                    throw ErrorFactory_1.ErrorFactory.validation('Password must be at least 8 characters long');
                }
                // Hash password
                updateData.hashedPassword = await bcryptjs_1.default.hash(password, 10);
            }
            // Update user
            const updatedUser = await this.userService.updateUser(userId, updateData);
            // Send success response
            return this.sendSuccess(res, updatedUser);
        });
        this.userService = new user_service_1.UserService();
    }
}
exports.UserController = UserController;
//# sourceMappingURL=user.controller.js.map