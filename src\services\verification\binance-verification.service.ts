// jscpd:ignore-file
/**
 * Binance Verification Service
 *
 * This service handles verification of Binance transactions.
 */
import { BaseService } from "../base.service";
import { BinanceApiService, BinancePaymentMethod, BinanceTransactionStatus } from "../blockchain/binance-api.service";
import { logger } from '../utils/logger';
import { BinanceApiService, BinancePaymentMethod, BinanceTransactionStatus } from "../blockchain/binance-api.service";
import { logger } from '../utils/logger';

export enum BinanceVerificationStatus {
  PENDING = "PENDING",
  VERIFIED = "VERIFIED",
  FAILED = "FAILED",
  EXPIRED = "EXPIRED",
}

export interface BinanceTransaction {
  id: string;
  method: BinancePaymentMethod;
  fromAddress?: string;
  toAddress: string;
  amount: string;
  currency: string;
  timestamp: number;
  status: BinanceTransactionStatus;
  note?: string;
}

export class BinanceVerificationService extends BaseService {
    private binanceApiService: BinanceApiService;

    constructor() {
        super();
        this.binanceApiService = new BinanceApiService();
    }

    /**
   * Verify a Binance Pay transaction
   * @param transactionId - The transaction ID to verify
   * @param expectedAmount - The expected amount
   * @param expectedCurrency - The expected currency
   * @returns The verification result
   */
    async verifyBinancePay(
        transactionId: string,
        expectedAmount: string,
        expectedCurrency: string
    ): Promise<{
        success: boolean;
        transaction?: BinanceTransaction;
        message?: string;
        status: BinanceVerificationStatus;
    }> {
        try {
            logger.info(`Verifying Binance Pay transaction: ${transactionId}`);

            // Get transaction details from Binance API
            const transaction: unknown =await this.binanceApiService.getTransaction(
                transactionId,
                BinancePaymentMethod.BINANCE_PAY
            );

            if (!transaction) {
                return {
                    success: false,
                    message: "Transaction not found",
                    status: BinanceVerificationStatus.FAILED
                };
            }

            // Verify amount and currency
            if (transaction.amount !== expectedAmount || transaction.currency !== expectedCurrency) {
                return {
                    success: false,
                    transaction,
                    message: "Amount or currency mismatch",
                    status: BinanceVerificationStatus.FAILED
                };
            }

            // Check transaction status
            if (transaction.status === BinanceTransactionStatus.COMPLETED) {
                return {
                    success: true,
                    transaction,
                    message: "Transaction verified",
                    status: BinanceVerificationStatus.VERIFIED
                };
            } else if (transaction.status === BinanceTransactionStatus.PENDING) {
                return {
                    success: false,
                    transaction,
                    message: "Transaction is still pending",
                    status: BinanceVerificationStatus.PENDING
                };
            } else if (transaction.status === BinanceTransactionStatus.EXPIRED) {
                return {
                    success: false,
                    transaction,
                    message: "Transaction has expired",
                    status: BinanceVerificationStatus.EXPIRED
                };
            } else {
                return {
                    success: false,
                    transaction,
                    message: `Transaction failed with status: ${transaction.status}`,
                    status: BinanceVerificationStatus.FAILED
                };
            }
        } catch (error) {
            logger.error(`Error verifying Binance Pay transaction: ${(error as Error).message}`, {
                transactionId,
                error
            });

            return {
                success: false,
                message: `Verification, error: ${(error as Error).message}`,
                status: BinanceVerificationStatus.FAILED
            };
        }
    }

    /**
     * Verify a Binance TRC20 transaction
     * @param transactionId - The transaction ID to verify
     * @param expectedAmount - The expected amount
     * @param expectedCurrency - The expected currency
     * @param expectedAddress - The expected recipient address
     * @returns The verification result
     */
    async verifyBinanceTrc20(
        transactionId: string,
        expectedAmount: string,
        expectedCurrency: string,
        expectedAddress: string
    ): Promise<{
        success: boolean;
        transaction?: BinanceTransaction;
        message?: string;
        status: BinanceVerificationStatus;
    }> {
        try {
            logger.info(`Verifying Binance TRC20 transaction: ${transactionId}`);

            // Get transaction details from Binance API
            const transaction: unknown =await this.binanceApiService.getTransaction(
                transactionId,
                BinancePaymentMethod.BINANCE_TRC20
            );

            if (!transaction) {
                return {
                    success: false,
                    message: "Transaction not found",
                    status: BinanceVerificationStatus.FAILED
                };
            }

            // Verify amount, currency and address
            if (
                transaction.amount !== expectedAmount ||
                transaction.currency !== expectedCurrency ||
                (expectedAddress && transaction.toAddress !== expectedAddress)
            ) {
                return {
                    success: false,
                    transaction,
                    message: "Amount, currency or address mismatch",
                    status: BinanceVerificationStatus.FAILED
                };
            }

            // Check transaction status
            if (transaction.status === BinanceTransactionStatus.COMPLETED) {
                return {
                    success: true,
                    transaction,
                    message: "Transaction verified",
                    status: BinanceVerificationStatus.VERIFIED
                };
            } else if (transaction.status === BinanceTransactionStatus.PENDING) {
                return {
                    success: false,
                    transaction,
                    message: "Transaction is still pending",
                    status: BinanceVerificationStatus.PENDING
                };
            } else if (transaction.status === BinanceTransactionStatus.EXPIRED) {
                return {
                    success: false,
                    transaction,
                    message: "Transaction has expired",
                    status: BinanceVerificationStatus.EXPIRED
                };
            } else {
                return {
                    success: false,
                    transaction,
                    message: `Transaction failed with status: ${transaction.status}`,
                    status: BinanceVerificationStatus.FAILED
                };
            }
        } catch (error) {
            logger.error(`Error verifying Binance TRC20 transaction: ${(error as Error).message}`, {
                transactionId,
                error
            });

            return {
                success: false,
                message: `Verification, error: ${(error as Error).message}`,
                status: BinanceVerificationStatus.FAILED
            };
        }
    }
}

export default new BinanceVerificationService();