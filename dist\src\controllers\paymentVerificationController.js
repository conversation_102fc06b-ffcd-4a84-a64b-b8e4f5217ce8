"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentVerificationController = void 0;
const paymentVerificationService_1 = require("../services/paymentVerificationService");
const logger_1 = require("../utils/logger");
/**
 * Controller for payment verification
 */
class PaymentVerificationController {
    /**
   * Create a new PaymentVerificationController instance
   * @param prisma Prisma client
   */
    constructor(prisma) {
        this.paymentVerificationService = new paymentVerificationService_1.PaymentVerificationService(prisma);
    }
    /**
   * Verify a payment
   * @param req Request
   * @param res Response
   */
    async verifyPayment(req, res) {
        try {
            const { transactionId, amount, currency, paymentMethodId, merchantId } = req.body;
            // Validate required fields
            if (!transactionId || !amount || !currency || !paymentMethodId || !merchantId) {
                res.status(400).json({
                    success: false,
                    message: "Missing required fields"
                });
                return;
            }
            // Verify the payment
            const transaction = await this.paymentVerificationService.verifyPayment(transactionId, parseFloat(amount), currency, paymentMethodId, merchantId);
            if (!transaction) {
                res.status(400).json({
                    success: false,
                    message: "Payment verification failed"
                });
                return;
            }
            res.status(200).json({
                success: true,
                message: "Payment verified successfully",
                transaction
            });
        }
        catch (error) {
            logger_1.logger.error("Error verifying payment", { error });
            res.status(500).json({
                success: false,
                message: "An error occurred while verifying the payment"
            });
        }
    }
    /**
   * Get a transaction by ID
   * @param req Request
   * @param res Response
   */
    async getTransaction(req, res) {
        try {
            const { id } = req.params;
            // Get the transaction
            const transaction = await this.paymentVerificationService.getTransaction(id);
            if (!transaction) {
                res.status(404).json({
                    success: false,
                    message: "Transaction not found"
                });
                return;
            }
            res.status(200).json({
                success: true,
                transaction
            });
        }
        catch (error) {
            logger_1.logger.error("Error getting transaction", { error });
            res.status(500).json({
                success: false,
                message: "An error occurred while getting the transaction"
            });
        }
    }
    /**
   * Update a transaction status
   * @param req Request
   * @param res Response
   */
    async updateTransactionStatus(req, res) {
        try {
            const { id } = req.params;
            const { status } = req.body;
            // Validate status
            if (!status || !["pending", "processing", "success", "failed"].includes(status)) {
                res.status(400).json({
                    success: false,
                    message: "Invalid status"
                });
                return;
            }
            // Update the transaction status
            const transaction = await this.paymentVerificationService.updateTransactionStatus(id, status);
            if (!transaction) {
                res.status(404).json({
                    success: false,
                    message: "Transaction not found"
                });
                return;
            }
            res.status(200).json({
                success: true,
                message: "Transaction status updated successfully",
                transaction
            });
        }
        catch (error) {
            logger_1.logger.error("Error updating transaction status", { error });
            res.status(500).json({
                success: false,
                message: "An error occurred while updating the transaction status"
            });
        }
    }
}
exports.PaymentVerificationController = PaymentVerificationController;
exports.default = PaymentVerificationController;
//# sourceMappingURL=paymentVerificationController.js.map