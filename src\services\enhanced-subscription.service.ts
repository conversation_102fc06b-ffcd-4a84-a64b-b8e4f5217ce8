// jscpd:ignore-file
/**
 * Enhanced Subscription Service
 *
 * Handles subscription operations with enhanced payment method control.
 */

import { BaseService } from "../shared/modules/services/BaseService";
import prisma from "../lib/prisma";
import { logger } from "../utils/logger";
import { PaymentMethodType } from '../types';
import { logger } from "../utils/logger";
import { PaymentMethodType } from '../types';


/**
 * Subscription capability
 */
export interface SubscriptionCapability {
  name: string;
  description: string;
  value: boolean | number | string;
}

/**
 * Enhanced subscription service
 */
export class EnhancedSubscriptionService extends BaseService {
    /**
     * Constructor
     */
    constructor() {
        super();
    }

    /**
     * Check if a merchant can use a specific payment method
     * @param merchantId Merchant ID
     * @param paymentMethodType Payment method type
     * @returns Boolean indicating if merchant can use the payment method
     */
    public async canUsePaymentMethod(merchantId: string, paymentMethodType: PaymentMethodType): Promise<boolean> {
        try {
            // Get merchant with subscription
            const merchant: unknown = await prisma.merchant.findUnique({
                where: { id: merchantId },
                include: { subscription: true }
            });

            if (!merchant || !merchant.subscription) {
                return false;
            }

            // Get subscription plan
            const plan: unknown = await prisma.subscriptionPlan.findUnique({
                where: { id: merchant.subscription.planId }
            });

            if (!plan) {
                return false;
            }

            // Check if plan allows this payment method
            const allowedPaymentMethods: unknown = plan.allowedPaymentMethods as string[];

            return allowedPaymentMethods.includes(paymentMethodType) || allowedPaymentMethods.includes("*");
        } catch (error) {
            logger.error(`Error checking payment method availability: ${(error as Error).message}`, {
                merchantId,
                paymentMethodType,
                error
            });

            return false;
        }
    }

    /**
     * Get available payment methods for a merchant
     * @param merchantId Merchant ID
     * @returns Array of available payment method types
     */
    public async getAvailablePaymentMethods(merchantId: string): Promise<PaymentMethodType[]> {
        try {
            // Get merchant with subscription
            const merchant: unknown = await prisma.merchant.findUnique({
                where: { id: merchantId },
                include: { subscription: true }
            });

            if (!merchant || !merchant.subscription) {
                return [];
            }

            // Get subscription plan
            const plan: unknown = await prisma.subscriptionPlan.findUnique({
                where: { id: merchant.subscription.planId }
            });

            if (!plan) {
                return [];
            }

            // Return allowed payment methods
            return plan.allowedPaymentMethods as PaymentMethodType[];
        } catch (error) {
            logger.error(`Error getting available payment methods: ${(error as Error).message}`, {
                merchantId,
                error
            });

            return [];
        }
    }

    /**
     * Get subscription capabilities for a merchant
     * @param merchantId Merchant ID
     * @returns Array of subscription capabilities
     */
    public async getSubscriptionCapabilities(merchantId: string): Promise<SubscriptionCapability[]> {
        try {
            // Get merchant with subscription
            const merchant: unknown = await prisma.merchant.findUnique({
                where: { id: merchantId },
                include: { subscription: true }
            });

            if (!merchant || !merchant.subscription) {
                return [];
            }

            // Get subscription plan
            const plan: unknown = await prisma.subscriptionPlan.findUnique({
                where: { id: merchant.subscription.planId }
            });

            if (!plan) {
                return [];
            }

            const capabilities: SubscriptionCapability[] = [
                {
                    name: "max_transactions_per_day",
                    description: "Maximum number of transactions per day",
                    value: plan.maxTransactionsPerDay ?? 0
                },
                {
                    name: "max_transaction_amount",
                    description: "Maximum transaction amount",
                    value: plan.maxTransactionAmount ?? 0
                },
                {
                    name: "fee_percentage",
                    description: "Transaction fee percentage",
                    value: plan.feePercentage ?? 0
                },
                {
                    name: "has_custom_branding",
                    description: "Custom branding available",
                    value: plan.hasCustomBranding ?? false
                },
                {
                    name: "has_api_access",
                    description: "API access available",
                    value: plan.hasApiAccess ?? false
                },
                {
                    name: "has_advanced_analytics",
                    description: "Advanced analytics available",
                    value: plan.hasAdvancedAnalytics ?? false
                },
                {
                    name: "has_priority_support",
                    description: "Priority support available",
                    value: plan.hasPrioritySupport ?? false
                }
            ];

            return capabilities;
        } catch (error) {
            logger.error(`Error getting subscription capabilities: ${(error as Error).message}`, {
                merchantId,
                error
            });

            return [];
        }
    }

    /**
     * Check if a merchant has a specific capability
     * @param merchantId Merchant ID
     * @param capabilityName Capability name
     * @returns Boolean indicating if merchant has the capability
     */
    public async hasCapability(merchantId: string, capabilityName: string): Promise<boolean> {
        try {
            const capabilities: unknown = await this.getSubscriptionCapabilities(merchantId);
            const capability: unknown = capabilities.find(cap => cap.name === capabilityName);

            if (!capability) {
                return false;
            }

            return capability.value === true || (typeof capability.value === "number" && capability.value > 0);
        } catch (error) {
            logger.error(`Error checking subscription capability: ${(error as Error).message}`, {
                merchantId,
                capabilityName,
                error
            });

            return false;
        }
    }

    /**
     * Get capability value for a merchant
     * @param merchantId Merchant ID
     * @param capabilityName Capability name
     * @returns Capability value or null if not found
     */
    public async getCapabilityValue(merchantId: string, capabilityName: string): Promise<boolean | number | string | null> {
        try {
            const capabilities: unknown = await this.getSubscriptionCapabilities(merchantId);
            const capability: unknown = capabilities.find(cap => cap.name === capabilityName);

            if (!capability) {
                return null;
            }

            return capability.value;
        } catch (error) {
            logger.error(`Error getting subscription capability value: ${(error as Error).message}`, {
                merchantId,
                capabilityName,
                error
            });

            return null;
        }
    }

    /**
     * Update merchant subscription
     * @param merchantId Merchant ID
     * @param planId Subscription plan ID
     * @returns Boolean indicating if update was successful
     */
    public async updateMerchantSubscription(merchantId: string, planId: string): Promise<boolean> {
        try {
            // Check if merchant exists
            const merchant: unknown = await prisma.merchant.findUnique({
                where: { id: merchantId }
            });

            if (!merchant) {
                return false;
            }

            // Check if plan exists
            const plan: unknown = await prisma.subscriptionPlan.findUnique({
                where: { id: planId }
            });

            if (!plan) {
                return false;
            }

            // Update or create subscription
            await prisma.subscription.upsert({
                where: { merchantId },
                update: {
                    planId,
                    startDate: new Date(),
                    status: "ACTIVE"
                },
                create: {
                    merchantId,
                    planId,
                    startDate: new Date(),
                    status: "ACTIVE"
                }
            });

            return true;
        } catch (error) {
            logger.error(`Error updating merchant subscription: ${(error as Error).message}`, {
                merchantId,
                planId,
                error
            });

            return false;
        }
    }
}