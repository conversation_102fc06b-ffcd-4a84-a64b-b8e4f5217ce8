a8be6e17444922d9e210d5e832e94a24
"use strict";
/**
 * Unit Tests for Identity Verification Service
 *
 * Comprehensive test suite covering all functionality of the IdentityVerificationService
 */
Object.defineProperty(exports, "__esModule", { value: true });
const globals_1 = require("@jest/globals");
// Mock dependencies
globals_1.jest.mock('@prisma/client');
globals_1.jest.mock('ethers');
const IdentityVerificationService_1 = require("../core/IdentityVerificationService");
const IdentityVerificationError_1 = require("../core/IdentityVerificationError");
(0, globals_1.describe)('IdentityVerificationService', () => {
    let service;
    let mockPrisma;
    (0, globals_1.beforeEach)(() => {
        // Create mock Prisma client
        mockPrisma = {
            identityVerification: {
                create: globals_1.jest.fn(),
                findUnique: globals_1.jest.fn(),
                findMany: globals_1.jest.fn(),
                update: globals_1.jest.fn(),
                delete: globals_1.jest.fn(),
                count: globals_1.jest.fn(),
            },
            user: {
                findUnique: globals_1.jest.fn(),
                create: globals_1.jest.fn(),
                update: globals_1.jest.fn(),
            },
            merchant: {
                findUnique: globals_1.jest.fn(),
            },
        };
        // Initialize service with mock
        service = new IdentityVerificationService_1.IdentityVerificationService(mockPrisma);
    });
    (0, globals_1.afterEach)(() => {
        globals_1.jest.clearAllMocks();
    });
    (0, globals_1.describe)('verifyEthereumSignature', () => {
        const validVerificationData = {
            address: testUtils.mockEthereumAddress(),
            message: 'Verify identity for AmazingPay',
            signature: '0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890ab1c',
            userId: testUtils.mockUUID(),
            merchantId: testUtils.mockUUID(),
        };
        (0, globals_1.it)('should successfully verify a valid Ethereum signature', async () => {
            // Arrange
            const mockUser = {
                id: validVerificationData.userId,
                email: '<EMAIL>',
                role: 'USER',
            };
            const mockMerchant = {
                id: validVerificationData.merchantId,
                name: 'Test Merchant',
            };
            const mockVerificationResult = {
                id: testUtils.mockUUID(),
                userId: validVerificationData.userId,
                merchantId: validVerificationData.merchantId,
                method: 'ethereum_signature',
                status: 'verified',
                confidence: 0.95,
                address: validVerificationData.address,
                createdAt: new Date(),
            };
            // Mock Prisma calls
            mockPrisma.user.findUnique.mockResolvedValue(mockUser);
            mockPrisma.merchant.findUnique.mockResolvedValue(mockMerchant);
            mockPrisma.identityVerification.create.mockResolvedValue(mockVerificationResult);
            // Mock ethers verification
            const { ethers } = require('ethers');
            ethers.utils.verifyMessage.mockReturnValue(validVerificationData.address);
            ethers.utils.isAddress.mockReturnValue(true);
            // Act
            const result = await service.verifyEthereumSignature(validVerificationData);
            // Assert
            (0, globals_1.expect)(result).toBeDefined();
            (0, globals_1.expect)(result.success).toBe(true);
            (0, globals_1.expect)(result.verificationId).toBe(mockVerificationResult.id);
            (0, globals_1.expect)(result.method).toBe('ethereum_signature');
            (0, globals_1.expect)(result.confidence).toBe(0.95);
            // Verify Prisma calls
            (0, globals_1.expect)(mockPrisma.user.findUnique).toHaveBeenCalledWith({
                where: { id: validVerificationData.userId },
            });
            (0, globals_1.expect)(mockPrisma.merchant.findUnique).toHaveBeenCalledWith({
                where: { id: validVerificationData.merchantId },
            });
            (0, globals_1.expect)(mockPrisma.identityVerification.create).toHaveBeenCalledWith({
                data: globals_1.expect.objectContaining({
                    userId: validVerificationData.userId,
                    merchantId: validVerificationData.merchantId,
                    method: 'ethereum_signature',
                    status: 'verified',
                    address: validVerificationData.address,
                }),
            });
        });
        (0, globals_1.it)('should throw error for invalid Ethereum address', async () => {
            // Arrange
            const invalidData = {
                ...validVerificationData,
                address: 'invalid-address',
            };
            const { ethers } = require('ethers');
            ethers.utils.isAddress.mockReturnValue(false);
            // Act & Assert
            await (0, globals_1.expect)(service.verifyEthereumSignature(invalidData))
                .rejects
                .toThrow(IdentityVerificationError_1.IdentityVerificationError);
            // Verify no database calls were made
            (0, globals_1.expect)(mockPrisma.identityVerification.create).not.toHaveBeenCalled();
        });
        (0, globals_1.it)('should throw error for invalid signature', async () => {
            // Arrange
            const mockUser = { id: validVerificationData.userId };
            const mockMerchant = { id: validVerificationData.merchantId };
            mockPrisma.user.findUnique.mockResolvedValue(mockUser);
            mockPrisma.merchant.findUnique.mockResolvedValue(mockMerchant);
            const { ethers } = require('ethers');
            ethers.utils.isAddress.mockReturnValue(true);
            ethers.utils.verifyMessage.mockReturnValue('0xdifferentaddress');
            // Act & Assert
            await (0, globals_1.expect)(service.verifyEthereumSignature(validVerificationData))
                .rejects
                .toThrow(IdentityVerificationError_1.IdentityVerificationError);
            (0, globals_1.expect)(mockPrisma.identityVerification.create).not.toHaveBeenCalled();
        });
        (0, globals_1.it)('should throw error when user not found', async () => {
            // Arrange
            mockPrisma.user.findUnique.mockResolvedValue(null);
            const { ethers } = require('ethers');
            ethers.utils.isAddress.mockReturnValue(true);
            // Act & Assert
            await (0, globals_1.expect)(service.verifyEthereumSignature(validVerificationData))
                .rejects
                .toThrow(IdentityVerificationError_1.IdentityVerificationError);
            (0, globals_1.expect)(mockPrisma.identityVerification.create).not.toHaveBeenCalled();
        });
        (0, globals_1.it)('should throw error when merchant not found', async () => {
            // Arrange
            const mockUser = { id: validVerificationData.userId };
            mockPrisma.user.findUnique.mockResolvedValue(mockUser);
            mockPrisma.merchant.findUnique.mockResolvedValue(null);
            const { ethers } = require('ethers');
            ethers.utils.isAddress.mockReturnValue(true);
            // Act & Assert
            await (0, globals_1.expect)(service.verifyEthereumSignature(validVerificationData))
                .rejects
                .toThrow(IdentityVerificationError_1.IdentityVerificationError);
            (0, globals_1.expect)(mockPrisma.identityVerification.create).not.toHaveBeenCalled();
        });
        (0, globals_1.it)('should handle database errors gracefully', async () => {
            // Arrange
            const mockUser = { id: validVerificationData.userId };
            const mockMerchant = { id: validVerificationData.merchantId };
            mockPrisma.user.findUnique.mockResolvedValue(mockUser);
            mockPrisma.merchant.findUnique.mockResolvedValue(mockMerchant);
            mockPrisma.identityVerification.create.mockRejectedValue(new Error('Database error'));
            const { ethers } = require('ethers');
            ethers.utils.isAddress.mockReturnValue(true);
            ethers.utils.verifyMessage.mockReturnValue(validVerificationData.address);
            // Act & Assert
            await (0, globals_1.expect)(service.verifyEthereumSignature(validVerificationData))
                .rejects
                .toThrow(IdentityVerificationError_1.IdentityVerificationError);
        });
    });
    (0, globals_1.describe)('getVerificationById', () => {
        (0, globals_1.it)('should return verification details for valid ID', async () => {
            // Arrange
            const verificationId = testUtils.mockUUID();
            const mockVerification = {
                id: verificationId,
                userId: testUtils.mockUUID(),
                merchantId: testUtils.mockUUID(),
                method: 'ethereum_signature',
                status: 'verified',
                confidence: 0.95,
                address: testUtils.mockEthereumAddress(),
                createdAt: new Date(),
                updatedAt: new Date(),
            };
            mockPrisma.identityVerification.findUnique.mockResolvedValue(mockVerification);
            // Act
            const result = await service.getVerificationById(verificationId);
            // Assert
            (0, globals_1.expect)(result).toEqual(mockVerification);
            (0, globals_1.expect)(mockPrisma.identityVerification.findUnique).toHaveBeenCalledWith({
                where: { id: verificationId },
            });
        });
        (0, globals_1.it)('should throw error for non-existent verification', async () => {
            // Arrange
            const verificationId = testUtils.mockUUID();
            mockPrisma.identityVerification.findUnique.mockResolvedValue(null);
            // Act & Assert
            await (0, globals_1.expect)(service.getVerificationById(verificationId))
                .rejects
                .toThrow(IdentityVerificationError_1.IdentityVerificationError);
        });
    });
    (0, globals_1.describe)('getUserVerifications', () => {
        (0, globals_1.it)('should return user verifications with pagination', async () => {
            // Arrange
            const userId = testUtils.mockUUID();
            const mockVerifications = [
                {
                    id: testUtils.mockUUID(),
                    userId,
                    method: 'ethereum_signature',
                    status: 'verified',
                    createdAt: new Date(),
                },
                {
                    id: testUtils.mockUUID(),
                    userId,
                    method: 'ethereum_signature',
                    status: 'verified',
                    createdAt: new Date(),
                },
            ];
            mockPrisma.identityVerification.findMany.mockResolvedValue(mockVerifications);
            mockPrisma.identityVerification.count.mockResolvedValue(2);
            // Act
            const result = await service.getUserVerifications(userId, { page: 1, limit: 10 });
            // Assert
            (0, globals_1.expect)(result.verifications).toEqual(mockVerifications);
            (0, globals_1.expect)(result.total).toBe(2);
            (0, globals_1.expect)(result.page).toBe(1);
            (0, globals_1.expect)(result.limit).toBe(10);
            (0, globals_1.expect)(mockPrisma.identityVerification.findMany).toHaveBeenCalledWith({
                where: { userId },
                orderBy: { createdAt: 'desc' },
                skip: 0,
                take: 10,
            });
        });
        (0, globals_1.it)('should handle empty results', async () => {
            // Arrange
            const userId = testUtils.mockUUID();
            mockPrisma.identityVerification.findMany.mockResolvedValue([]);
            mockPrisma.identityVerification.count.mockResolvedValue(0);
            // Act
            const result = await service.getUserVerifications(userId);
            // Assert
            (0, globals_1.expect)(result.verifications).toEqual([]);
            (0, globals_1.expect)(result.total).toBe(0);
        });
    });
    (0, globals_1.describe)('updateVerificationStatus', () => {
        (0, globals_1.it)('should successfully update verification status', async () => {
            // Arrange
            const verificationId = testUtils.mockUUID();
            const newStatus = 'rejected';
            const reason = 'Invalid signature detected';
            const mockUpdatedVerification = {
                id: verificationId,
                status: newStatus,
                reason,
                updatedAt: new Date(),
            };
            mockPrisma.identityVerification.update.mockResolvedValue(mockUpdatedVerification);
            // Act
            const result = await service.updateVerificationStatus(verificationId, newStatus, reason);
            // Assert
            (0, globals_1.expect)(result).toEqual(mockUpdatedVerification);
            (0, globals_1.expect)(mockPrisma.identityVerification.update).toHaveBeenCalledWith({
                where: { id: verificationId },
                data: {
                    status: newStatus,
                    reason,
                    updatedAt: globals_1.expect.any(Date),
                },
            });
        });
        (0, globals_1.it)('should throw error for invalid verification ID', async () => {
            // Arrange
            const verificationId = testUtils.mockUUID();
            mockPrisma.identityVerification.update.mockRejectedValue(new Error('Record not found'));
            // Act & Assert
            await (0, globals_1.expect)(service.updateVerificationStatus(verificationId, 'rejected'))
                .rejects
                .toThrow(IdentityVerificationError_1.IdentityVerificationError);
        });
    });
    (0, globals_1.describe)('getVerificationStatistics', () => {
        (0, globals_1.it)('should return verification statistics', async () => {
            // Arrange
            const merchantId = testUtils.mockUUID();
            const dateFrom = new Date('2024-01-01');
            const dateTo = new Date('2024-01-31');
            mockPrisma.identityVerification.count
                .mockResolvedValueOnce(100) // total
                .mockResolvedValueOnce(85) // verified
                .mockResolvedValueOnce(10) // pending
                .mockResolvedValueOnce(5); // rejected
            // Act
            const result = await service.getVerificationStatistics(merchantId, dateFrom, dateTo);
            // Assert
            (0, globals_1.expect)(result).toEqual({
                total: 100,
                verified: 85,
                pending: 10,
                rejected: 5,
                verificationRate: 85,
            });
            // Verify database calls
            (0, globals_1.expect)(mockPrisma.identityVerification.count).toHaveBeenCalledTimes(4);
        });
        (0, globals_1.it)('should handle zero verifications', async () => {
            // Arrange
            mockPrisma.identityVerification.count.mockResolvedValue(0);
            // Act
            const result = await service.getVerificationStatistics();
            // Assert
            (0, globals_1.expect)(result.total).toBe(0);
            (0, globals_1.expect)(result.verificationRate).toBe(0);
        });
    });
    (0, globals_1.describe)('Error Handling', () => {
        (0, globals_1.it)('should handle network errors gracefully', async () => {
            // Arrange
            mockPrisma.user.findUnique.mockRejectedValue(new Error('Network error'));
            // Act & Assert
            await (0, globals_1.expect)(service.verifyEthereumSignature(validVerificationData))
                .rejects
                .toThrow(IdentityVerificationError_1.IdentityVerificationError);
        });
        (0, globals_1.it)('should handle timeout errors', async () => {
            // Arrange
            mockPrisma.user.findUnique.mockImplementation(() => new Promise((_, reject) => setTimeout(() => reject(new Error('Timeout')), 100)));
            // Act & Assert
            await (0, globals_1.expect)(service.verifyEthereumSignature(validVerificationData))
                .rejects
                .toThrow(IdentityVerificationError_1.IdentityVerificationError);
        });
    });
    (0, globals_1.describe)('Input Validation', () => {
        (0, globals_1.it)('should validate required fields', async () => {
            // Test missing address
            await (0, globals_1.expect)(service.verifyEthereumSignature({
                address: '',
                message: 'test',
                signature: '0xtest',
                userId: testUtils.mockUUID(),
                merchantId: testUtils.mockUUID(),
            })).rejects.toThrow();
            // Test missing message
            await (0, globals_1.expect)(service.verifyEthereumSignature({
                address: testUtils.mockEthereumAddress(),
                message: '',
                signature: '0xtest',
                userId: testUtils.mockUUID(),
                merchantId: testUtils.mockUUID(),
            })).rejects.toThrow();
            // Test missing signature
            await (0, globals_1.expect)(service.verifyEthereumSignature({
                address: testUtils.mockEthereumAddress(),
                message: 'test',
                signature: '',
                userId: testUtils.mockUUID(),
                merchantId: testUtils.mockUUID(),
            })).rejects.toThrow();
        });
        (0, globals_1.it)('should validate UUID format', async () => {
            await (0, globals_1.expect)(service.verifyEthereumSignature({
                address: testUtils.mockEthereumAddress(),
                message: 'test',
                signature: '0xtest',
                userId: 'invalid-uuid',
                merchantId: testUtils.mockUUID(),
            })).rejects.toThrow();
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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