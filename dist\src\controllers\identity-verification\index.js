"use strict";
/**
 * Identity Verification Controller Module
 *
 * Centralized exports for the identity verification controller system.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = exports.IdentityVerificationResponseMapper = exports.IdentityVerificationValidationService = exports.IdentityVerificationAuthService = exports.IdentityVerificationController = void 0;
// Main controller export
var IdentityVerificationController_1 = require("./IdentityVerificationController");
Object.defineProperty(exports, "IdentityVerificationController", { enumerable: true, get: function () { return IdentityVerificationController_1.IdentityVerificationController; } });
// Service exports
var IdentityVerificationAuthService_1 = require("./services/IdentityVerificationAuthService");
Object.defineProperty(exports, "IdentityVerificationAuthService", { enumerable: true, get: function () { return IdentityVerificationAuthService_1.IdentityVerificationAuthService; } });
var IdentityVerificationValidationService_1 = require("./services/IdentityVerificationValidationService");
Object.defineProperty(exports, "IdentityVerificationValidationService", { enumerable: true, get: function () { return IdentityVerificationValidationService_1.IdentityVerificationValidationService; } });
// Mapper exports
var IdentityVerificationResponseMapper_1 = require("./mappers/IdentityVerificationResponseMapper");
Object.defineProperty(exports, "IdentityVerificationResponseMapper", { enumerable: true, get: function () { return IdentityVerificationResponseMapper_1.IdentityVerificationResponseMapper; } });
// Type exports
__exportStar(require("./types/IdentityVerificationControllerTypes"), exports);
// Default export - main controller class
var IdentityVerificationController_2 = require("./IdentityVerificationController");
Object.defineProperty(exports, "default", { enumerable: true, get: function () { return IdentityVerificationController_2.IdentityVerificationController; } });
//# sourceMappingURL=index.js.map