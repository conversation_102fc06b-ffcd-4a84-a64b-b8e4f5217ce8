import { BaseController } from "./base.controller";
declare global {
    namespace Express {
        interface Request {
            user?: {
                id: string;
                role: string;
                merchantId?: string;
            };
        }
    }
}
/**
 * SmsController
 * Controller for handling SMS operations
 */
export declare class SmsController extends BaseController {
    constructor();
    /**
     * Test the SMS service by sending a test SMS
     */
    testSmsService: any;
    /**
     * Send a custom SMS
     */
    sendCustomSms: any;
    /**
     * Get admin phone numbers
     */
    getAdminPhoneNumbers: any;
}
declare const _default: SmsController;
export default _default;
//# sourceMappingURL=sms.controller.d.ts.map