/**
 * Database Type Definitions
 * 
 * This file contains type definitions for database operations.
 */

import { PaginationParams, UUID } from './index';

// Generic database client interface
export interface DatabaseClient {
  connect(): Promise<void>;
  disconnect(): Promise<void>;
  query<T = any>(query: string, params?: any[]): Promise<QueryResult<T>>;
}

// Query result interface
export interface QueryResult<T> {
  rows: T[];
  rowCount: number;
}

// Base repository interface
export interface Repository<T, ID = UUID> {
  findById(id: ID): Promise<T | null>;
  findAll(options?: FindOptions): Promise<T[]>;
  findOne(filter: Partial<T>): Promise<T | null>;
  create(data: Omit<T, 'id' | 'createdAt' | 'updatedAt'>): Promise<T>;
  update(id: ID, data: Partial<T>): Promise<T | null>;
  delete(id: ID): Promise<boolean>;
  count(filter?: Partial<T>): Promise<number>;
}

// Find options interface
export interface FindOptions extends PaginationParams {
  where?: Record<string, any>;
  orderBy?: string;
  orderDirection?: 'asc' | 'desc';
  include?: string[];
}

// Transaction options
export interface TransactionOptions {
  isolationLevel?: IsolationLevel;
  timeout?: number;
}

// Isolation levels
export enum IsolationLevel {
  READ_UNCOMMITTED = 'READ UNCOMMITTED',
  READ_COMMITTED = 'READ COMMITTED',
  REPEATABLE_READ = 'REPEATABLE READ',
  SERIALIZABLE = 'SERIALIZABLE'
}

// Database connection config
export interface DatabaseConfig {
  host: string;
  port: number;
  database: string;
  user: string;
  password: string;
  ssl?: boolean;
  connectionLimit?: number;
}

// Migration interface
export interface Migration {
  up(): Promise<void>;
  down(): Promise<void>;
}

// Database verifier interfaces
export interface DatabaseVerifier {
  connect(): Promise<void>;
  tableExists(tableName: string): Promise<boolean>;
  columnExists(tableName: string, columnName: string): Promise<boolean>;
  foreignKeyExists(
    tableName: string,
    columnName: string,
    referencedTable: string,
    referencedColumn?: string
  ): Promise<boolean>;
  tableHasData(tableName: string): Promise<boolean>;
  verifySchema(): Promise<VerificationResult>;
  close(): Promise<void>;
}

export interface VerificationResult {
  success: boolean;
  missingTables: string[];
  missingColumns: { table: string; columns: string[] }[];
  missingForeignKeys: { table: string; 
    column: string; 
    referencedTable: string; 
    referencedColumn: string 
  }[];
  message?: string;
}

// Export all types
export default {
  DatabaseClient,
  QueryResult,
  Repository,
  FindOptions,
  TransactionOptions,
  IsolationLevel,
  DatabaseConfig,
  Migration,
  DatabaseVerifier,
  VerificationResult
};
