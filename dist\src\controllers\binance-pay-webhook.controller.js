"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.handleBinancePayWebhook = void 0;
const transaction_service_1 = require("../services/transaction.service");
const binance_pay_service_1 = require("../services/binance-pay.service");
const error_middleware_1 = require("../middlewares/error.middleware");
const database_1 = __importDefault(require("../config/database"));
// Handle Binance Pay webhook
exports.handleBinancePayWebhook = (0, error_middleware_1.asyncHandler)(async (req, res) => {
    // Validate webhook data
    if (!req.body || !req.body.data || !req.body.timestamp || !req.body.nonce) {
        throw new error_middleware_1.AppError({
            message: "Invalid webhook data",
            type: ErrorType.VALIDATION,
            code: ErrorCode.INVALID_INPUT
        });
    }
    const { data, timestamp, nonce } = req.body;
    // Validate webhook signature
    const signature = req.headers["binancepay-signature"];
    if (!signature) {
        throw new error_middleware_1.AppError({
            message: "Missing webhook signature",
            type: ErrorType.VALIDATION,
            code: ErrorCode.INVALID_INPUT
        });
    }
    // Get transaction by merchant trade number
    const merchantTradeNo = data.merchantTradeNo;
    if (!merchantTradeNo) {
        throw new error_middleware_1.AppError({
            message: "Missing merchant trade number",
            type: ErrorType.VALIDATION,
            code: ErrorCode.INVALID_INPUT
        });
    }
    const transaction = await database_1.default.transaction.findFirst({
        where: { reference: merchantTradeNo },
        include: { paymentMethod: true
        }
    });
    if (!transaction) {
        throw new error_middleware_1.AppError({
            message: "Transaction not found",
            type: ErrorType.NOT_FOUND,
            code: ErrorCode.RESOURCE_NOT_FOUND
        });
    }
    // Get API secret from payment method config
    if (!transaction.paymentMethod || !transaction.paymentMethod.config) {
        throw new error_middleware_1.AppError({
            message: "Payment method configuration not found",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
    }
    const config = transaction.paymentMethod.config;
    const apiSecret = config.apiSecret;
    if (!apiSecret) {
        throw new error_middleware_1.AppError({
            message: "Missing API secret",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
    }
    // Verify webhook signature
    const isValid = binance_pay_service_1.BinancePayService.verifyWebhookSignature(req.body, signature, apiSecret);
    if (!isValid) {
        throw new error_middleware_1.AppError({
            message: "Invalid webhook signature",
            type: ErrorType.AUTHENTICATION,
            code: ErrorCode.INVALID_CREDENTIALS
        });
    }
    // Process webhook based on status
    const status = data.status;
    if (status === "PAY_SUCCESS" || status === "PAID") {
        // Update transaction status
        await transaction_service_1.TransactionService.updateTransactionStatus(transaction.id, "COMPLETED", {
            verificationResult: data,
            metadata: { verifiedAt: new Date().toISOString(),
                verificationMethod: "binance_pay",
                automatic: true,
                webhookTimestamp: timestamp
            }
        });
    }
    else if (status === "PAY_CLOSED" || status === "PAY_REFUND" || status === "CANCELED") {
        // Update transaction status
        await transaction_service_1.TransactionService.updateTransactionStatus(transaction.id, "FAILED", {
            verificationResult: data,
            metadata: { failedAt: new Date().toISOString(),
                verificationMethod: "binance_pay",
                reason: `Payment ${status.toLowerCase()}`,
                automatic: true,
                webhookTimestamp: timestamp
            }
        });
    }
    // Acknowledge webhook
    res.status(200).json({ success: true });
});
//# sourceMappingURL=binance-pay-webhook.controller.js.map