// jscpd:ignore-file
import { logger } from "../utils/logger";
import prisma from "../lib/prisma";
import { Merchant } from '../types';
import { Alert, AlertStatus, AlertSeverity, AlertType } from '../types/alert.types';


/**
 * Alert analytics service
 */
export class AlertAnalyticsService {
    /**
   * Get alert count by status
   * @param startDate Start date
   * @param endDate End date
   * @param merchantId Merchant ID (optional)
   * @returns Alert count by status
   */
    public async getAlertCountByStatus(
        startDate: Date,
        endDate: Date,
        merchantId?: string: unknown
    ): Promise<{ status: string; count: number }[]> {
        try {
            // Build where clause
            const where: unknown = {
                createdAt: { gte: startDate,
                    lte: endDate
                }
            };

            // Add merchant filter
            if (merchantId) {
                where.merchantId = merchantId;
            }

            // Get alert count by status
            const result: unknown = await prisma.$queryRaw`
        SELECT status, COUNT(*) as count
        FROM "Alert"
        WHERE "createdAt" >= ${startDate} AND "createdAt" <= ${endDate}
        ${merchantId ? prisma.sql`AND "merchantId" = ${merchantId}` : prisma.sql``}
        GROUP BY status
        ORDER BY count DESC
      `;

            return result as { status: string; count: number }[];
        } catch (error) {
            logger.error("Error getting alert count by status", { error, startDate, endDate, merchantId });
            return [];
        }
    }

    /**
   * Get alert count by severity
   * @param startDate Start date
   * @param endDate End date
   * @param merchantId Merchant ID (optional)
   * @returns Alert count by severity
   */
    public async getAlertCountBySeverity(
        startDate: Date,
        endDate: Date,
        merchantId?: string
    ): Promise<{ severity: string; count: number }[]> {
        try {
            // Build where clause
            const where: unknown = {
                createdAt: { gte: startDate,
                    lte: endDate
                }
            };

            // Add merchant filter
            if (merchantId) {
                where.merchantId = merchantId;
            }

            // Get alert count by severity
            const result: unknown = await prisma.$queryRaw`
        SELECT severity, COUNT(*) as count
        FROM "Alert"
        WHERE "createdAt" >= ${startDate} AND "createdAt" <= ${endDate}
        ${merchantId ? prisma.sql`AND "merchantId" = ${merchantId}` : prisma.sql``}
        GROUP BY severity
        ORDER BY
          CASE
            WHEN severity = 'critical' THEN 1
            WHEN severity = 'error' THEN 2
            WHEN severity = 'warning' THEN 3
            WHEN severity = 'info' THEN 4
            ELSE 5
          END
      `;

            return result as { severity: string; count: number }[];
        } catch (error) {
            logger.error("Error getting alert count by severity", { error, startDate, endDate, merchantId });
            return [];
        }
    }

    /**
   * Get alert count by type
   * @param startDate Start date
   * @param endDate End date
   * @param merchantId Merchant ID (optional)
   * @returns Alert count by type
   */
    public async getAlertCountByType(
        startDate: Date,
        endDate: Date,
        merchantId?: string
    ): Promise<{ type: string; count: number }[]> {
        try {
            // Build where clause
            const where: unknown = {
                createdAt: { gte: startDate,
                    lte: endDate
                }
            };

            // Add merchant filter
            if (merchantId) {
                where.merchantId = merchantId;
            }

            // Get alert count by type
            const result: unknown = await prisma.$queryRaw`
        SELECT type, COUNT(*) as count
        FROM "Alert"
        WHERE "createdAt" >= ${startDate} AND "createdAt" <= ${endDate}
        ${merchantId ? prisma.sql`AND "merchantId" = ${merchantId}` : prisma.sql``}
        GROUP BY type
        ORDER BY count DESC
      `;

            return result as { type: string; count: number }[];
        } catch (error) {
            logger.error("Error getting alert count by type", { error, startDate, endDate, merchantId });
            return [];
        }
    }

    /**
   * Get alert count by day
   * @param startDate Start date
   * @param endDate End date
   * @param merchantId Merchant ID (optional)
   * @returns Alert count by day
   */
    public async getAlertCountByDay(
        startDate: Date,
        endDate: Date,
        merchantId?: string
    ): Promise<{ date: string; count: number }[]> {
        try {
            // Build where clause
            const where: unknown = {
                createdAt: { gte: startDate,
                    lte: endDate
                }
            };

            // Add merchant filter
            if (merchantId) {
                where.merchantId = merchantId;
            }

            // Get alert count by day
            const result: unknown = await prisma.$queryRaw`
        SELECT
          DATE_TRUNC('day', "createdAt") as date,
          COUNT(*) as count
        FROM "Alert"
        WHERE "createdAt" >= ${startDate} AND "createdAt" <= ${endDate}
        ${merchantId ? prisma.sql`AND "merchantId" = ${merchantId}` : prisma.sql``}
        GROUP BY DATE_TRUNC('day', "createdAt")
        ORDER BY date ASC
      `;

            return (result as { date: Date; count: number }[]).map(item => ({
                date: item.date.toISOString().split("T")[0],
                count: Number(item.count)
            }));
        } catch (error) {
            logger.error("Error getting alert count by day", { error, startDate, endDate, merchantId });
            return [];
        }
    }

    /**
   * Get alert count by hour
   * @param date Date
   * @param merchantId Merchant ID (optional)
   * @returns Alert count by hour
   */
    public async getAlertCountByHour(
        date: Date,
        merchantId?: string
    ): Promise<{ hour: number; count: number }[]> {
        try {
            // Calculate start and end date
            const startDate: Date =new Date(date);
            startDate.setHours(0, 0, 0, 0);

            const endDate: Date =new Date(date);
            endDate.setHours(23, 59, 59, 999);

            // Build where clause
            const where: unknown = {
                createdAt: { gte: startDate,
                    lte: endDate
                }
            };

            // Add merchant filter
            if (merchantId) {
                where.merchantId = merchantId;
            }

            // Get alert count by hour
            const result: unknown = await prisma.$queryRaw`
        SELECT
          EXTRACT(HOUR FROM "createdAt") as hour,
          COUNT(*) as count
        FROM "Alert"
        WHERE "createdAt" >= ${startDate} AND "createdAt" <= ${endDate}
        ${merchantId ? prisma.sql`AND "merchantId" = ${merchantId}` : prisma.sql``}
        GROUP BY EXTRACT(HOUR FROM "createdAt")
        ORDER BY hour ASC
      `;

            // Fill in missing hours
            const hourCounts: unknown = new Array(24).fill(0).map(((_, i)) => ({
                hour: i,
                count: 0
            }));

            (result as { hour: number; count: number }[]).forEach((item)) => {
                hourCounts[item.hour].count = Number(item.count);
            });

            return hourCounts;
        } catch (error) {
            logger.error("Error getting alert count by hour", { error, date, merchantId });
            return [];
        }
    }

    /**
   * Get top merchants by alert count
   * @param startDate Start date
   * @param endDate End date
   * @param limit Limit
   * @returns Top merchants by alert count
   */
    public async getTopMerchantsByAlertCount(
        startDate: Date,
        endDate: Date,
        limit: number = 10
    ): Promise<{ merchantId: string; merchantName: string; count: number }[]> {
        try {
            // Get top merchants by alert count
            const result: unknown = await prisma.$queryRaw`
        SELECT
          a."merchantId",
          m.name as "merchantName",
          COUNT(*) as count
        FROM "Alert" a
        LEFT JOIN "Merchant" m ON a."merchantId" = m.id
        WHERE
          a."createdAt" >= ${startDate}
          AND a."createdAt" <= ${endDate}
          AND a."merchantId" IS NOT NULL
        GROUP BY a."merchantId", m.name
        ORDER BY count DESC
        LIMIT ${limit}
      `;

            return result as { merchantId: string; merchantName: string; count: number }[];
        } catch (error) {
            logger.error("Error getting top merchants by alert count", { error, startDate, endDate, limit });
            return [];
        }
    }

    /**
   * Get alert resolution time statistics
   * @param startDate Start date
   * @param endDate End date
   * @param merchantId Merchant ID (optional)
   * @returns Alert resolution time statistics
   */
    public async getAlertResolutionTimeStats(
        startDate: Date,
        endDate: Date,
        merchantId?: string
    ): Promise<{
    averageResolutionTimeMinutes: number;
    medianResolutionTimeMinutes: number;
    resolvedCount: number;
    unresolvedCount: number;
  }> {
        try {
            // Build where clause
            const where: unknown = {
                createdAt: { gte: startDate,
                    lte: endDate
                }
            };

            // Add merchant filter
            if (merchantId) {
                where.merchantId = merchantId;
            }

            // Get resolved alerts
            const resolvedAlerts: unknown = await prisma.alert.findMany({
                where: {
                    ...where,
                    status: AlertStatus.RESOLVED,
                    resolvedAt: { not: null }
                },
                select: { createdAt: true,
                    resolvedAt: true
                }
            });

            // Get unresolved alerts count
            const unresolvedCount: unknown = await prisma.alert.count({
                where: {
                    ...where,
                    status: { not: AlertStatus.RESOLVED }
                }
            });

            // Calculate resolution times
            const resolutionTimes: unknown = resolvedAlerts.map((alert)) => {
                const createdAt: Date =new Date(alert.createdAt).getTime();
                const resolvedAt: Date =new Date(alert.resolvedAt!).getTime();
                return (resolvedAt - createdAt) / (1000 * 60); // Convert to minutes
            });

            // Calculate average resolution time
            const averageResolutionTimeMinutes: unknown = resolutionTimes.length > 0
                ? resolutionTimes.reduce((sum, time) => sum + time, 0) / resolutionTimes.length
                : 0;

            // Calculate median resolution time
            let medianResolutionTimeMinutes: number =0;
            if (resolutionTimes.length > 0) {
                const sortedTimes: unknown = [...resolutionTimes].sort((a, b) => a - b);
                const midIndex: unknown = Math.floor(sortedTimes.length / 2);

                if (sortedTimes.length % 2 === 0) {
                    medianResolutionTimeMinutes = (sortedTimes[midIndex - 1] + sortedTimes[midIndex]) / 2;
                } else {
                    medianResolutionTimeMinutes = sortedTimes[midIndex];
                }
            }

            return {
                averageResolutionTimeMinutes,
                medianResolutionTimeMinutes,
                resolvedCount: resolvedAlerts.length,
                unresolvedCount
            };
        } catch (error) {
            logger.error("Error getting alert resolution time stats", { error, startDate, endDate, merchantId });
            return {
                averageResolutionTimeMinutes: 0,
                medianResolutionTimeMinutes: 0,
                resolvedCount: 0,
                unresolvedCount: 0
            };
        }
    }

    /**
   * Get alert trends
   * @param days Number of days
   * @param merchantId Merchant ID (optional)
   * @returns Alert trends
   */
    public async getAlertTrends(
        days: number = 30,
        merchantId?: string
    ): Promise<{
    previousPeriod: { count: number; criticalCount: number; errorCount: number };
    currentPeriod: { count: number; criticalCount: number; errorCount: number };
    percentageChange: number;
    criticalPercentageChange: number;
    errorPercentageChange: number;
  }> {
        try {
            // Calculate date ranges
            const now: Date =new Date();
            const currentPeriodEnd: Date =new Date(now);
            const currentPeriodStart: Date =new Date(now);
            currentPeriodStart.setDate(currentPeriodStart.getDate() - days);

            const previousPeriodEnd: Date =new Date(currentPeriodStart);
            previousPeriodEnd.setDate(previousPeriodEnd.getDate() - 1);
            const previousPeriodStart: Date =new Date(previousPeriodEnd);
            previousPeriodStart.setDate(previousPeriodStart.getDate() - days);

            // Build where clauses
            const currentPeriodWhere: unknown = {
                createdAt: { gte: currentPeriodStart,
                    lte: currentPeriodEnd
                }
            };

            const previousPeriodWhere: unknown = {
                createdAt: { gte: previousPeriodStart,
                    lte: previousPeriodEnd
                }
            };

            // Add merchant filter
            if (merchantId) {
                currentPeriodWhere.merchantId = merchantId;
                previousPeriodWhere.merchantId = merchantId;
            }

            // Get current period counts
            const currentPeriodCount: unknown = await prisma.alert.count({
                where: currentPeriodWhere
            });

            const currentPeriodCriticalCount: unknown = await prisma.alert.count({
                where: {
                    ...currentPeriodWhere,
                    severity: AlertSeverity.CRITICAL
                }
            });

            const currentPeriodErrorCount: unknown = await prisma.alert.count({
                where: {
                    ...currentPeriodWhere,
                    severity: AlertSeverity.ERROR
                }
            });

            // Get previous period counts
            const previousPeriodCount: unknown = await prisma.alert.count({
                where: previousPeriodWhere
            });

            const previousPeriodCriticalCount: unknown = await prisma.alert.count({
                where: {
                    ...previousPeriodWhere,
                    severity: AlertSeverity.CRITICAL
                }
            });

            const previousPeriodErrorCount: unknown = await prisma.alert.count({
                where: {
                    ...previousPeriodWhere,
                    severity: AlertSeverity.ERROR
                }
            });

            // Calculate percentage changes
            const percentageChange: unknown = previousPeriodCount > 0
                ? ((currentPeriodCount - previousPeriodCount) / previousPeriodCount) * 100
                : currentPeriodCount > 0 ? 100 : 0;

            const criticalPercentageChange = previousPeriodCriticalCount > 0
                ? ((currentPeriodCriticalCount - previousPeriodCriticalCount) / previousPeriodCriticalCount) * 100
                : currentPeriodCriticalCount > 0 ? 100 : 0;

            const errorPercentageChange = previousPeriodErrorCount > 0
                ? ((currentPeriodErrorCount - previousPeriodErrorCount) / previousPeriodErrorCount) * 100
                : currentPeriodErrorCount > 0 ? 100 : 0;

            return {
                previousPeriod: { count: previousPeriodCount,
                    criticalCount: previousPeriodCriticalCount,
                    errorCount: previousPeriodErrorCount
                },
                currentPeriod: { count: currentPeriodCount,
                    criticalCount: currentPeriodCriticalCount,
                    errorCount: currentPeriodErrorCount
                },
                percentageChange,
                criticalPercentageChange,
                errorPercentageChange
            };
        } catch (error) {
            logger.error("Error getting alert trends", { error, days, merchantId });
            return {
                previousPeriod: { count: 0, criticalCount: 0, errorCount: 0 },
                currentPeriod: { count: 0, criticalCount: 0, errorCount: 0 },
                percentageChange: 0,
                criticalPercentageChange: 0,
                errorPercentageChange: 0
            };
        }
    }
}
