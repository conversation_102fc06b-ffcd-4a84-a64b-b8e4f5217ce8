{"file": "F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts", "mappings": ";AAAA;;;;GAIG;;;AAEH,2CAIwB;AAQxB,2EAAwE;AACxE,4FAAyF;AACzF,gDAA6C;AAE7C;;GAEG;AACH,MAAa,2BAA2B;IAKtC,YAAY,MAAoB,EAAE,MAAoC;QACpE,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,MAAM,GAAG;YACZ,cAAc,EACZ,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,8CAA8C;YAChF,cAAc,EAAE;gBACd,uCAA8B,CAAC,kBAAkB;gBACjD,uCAA8B,CAAC,OAAO;gBACtC,uCAA8B,CAAC,MAAM;gBACrC,uCAA8B,CAAC,GAAG;gBAClC,uCAA8B,CAAC,UAAU;gBACzC,uCAA8B,CAAC,SAAS;gBACxC,uCAA8B,CAAC,QAAQ;aACxC;YACD,GAAG,MAAM;SACV,CAAC;QAEF,kCAAkC;QAClC,IAAI,CAAC,6BAA6B,GAAG,IAAI,6DAA6B,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACtF,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,uBAAuB,CAC3B,MAA+B;QAE/B,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,uCAA8B,CAAC,kBAAkB,CAAC,EAAE,CAAC;YAC7E,MAAM,qDAAyB,CAAC,iBAAiB,CAC/C,gDAAgD,CACjD,CAAC;QACJ,CAAC;QAED,OAAO,MAAM,IAAI,CAAC,6BAA6B,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IACjE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB,CAAC,EAAU;QAClC,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,UAAU,CAAC;gBACrE,KAAK,EAAE,EAAE,EAAE,EAAE;gBACb,OAAO,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;aAC1B,CAAC,CAAC;YAEH,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,MAAM,qDAAyB,CAAC,oBAAoB,EAAE,CAAC;YACzD,CAAC;YAED,OAAO,YAAY,CAAC;QACtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,qDAAyB,EAAE,CAAC;gBAC/C,MAAM,KAAK,CAAC;YACd,CAAC;YAED,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YACzD,MAAM,qDAAyB,CAAC,aAAa,CAAC,0CAA0C,CAAC,CAAC;QAC5F,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAAC,UAA+B,EAAE;QACtD,IAAI,CAAC;YACH,MAAM,KAAK,GAAQ,EAAE,CAAC;YAEtB,IAAI,OAAO,CAAC,MAAM;gBAAE,KAAK,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;YAClD,IAAI,OAAO,CAAC,UAAU;gBAAE,KAAK,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;YAC9D,IAAI,OAAO,CAAC,MAAM;gBAAE,KAAK,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;YAClD,IAAI,OAAO,CAAC,MAAM;gBAAE,KAAK,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;YAElD,IAAI,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;gBACvC,KAAK,CAAC,SAAS,GAAG,EAAE,CAAC;gBACrB,IAAI,OAAO,CAAC,QAAQ;oBAAE,KAAK,CAAC,SAAS,CAAC,GAAG,GAAG,OAAO,CAAC,QAAQ,CAAC;gBAC7D,IAAI,OAAO,CAAC,MAAM;oBAAE,KAAK,CAAC,SAAS,CAAC,GAAG,GAAG,OAAO,CAAC,MAAM,CAAC;YAC3D,CAAC;YAED,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,QAAQ,CAAC;gBACrD,KAAK;gBACL,OAAO,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;gBACzB,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;gBAC9B,IAAI,EAAE,OAAO,CAAC,KAAK,IAAI,EAAE;gBACzB,IAAI,EAAE,OAAO,CAAC,MAAM,IAAI,CAAC;aAC1B,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACpD,MAAM,qDAAyB,CAAC,aAAa,CAAC,2CAA2C,CAAC,CAAC;QAC7F,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,uBAAuB,CAAC,MAAc;QAC1C,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,qDAAyB,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,CAAC;QAC3E,CAAC;QAED,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;IACjD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,2BAA2B,CAAC,UAAkB;QAClD,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,MAAM,qDAAyB,CAAC,iBAAiB,CAAC,yBAAyB,CAAC,CAAC;QAC/E,CAAC;QAED,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,EAAE,UAAU,EAAE,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB,CAAC,UAA+B,EAAE;QAC1D,IAAI,CAAC;YACH,MAAM,KAAK,GAAQ,EAAE,CAAC;YAEtB,IAAI,OAAO,CAAC,MAAM;gBAAE,KAAK,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;YAClD,IAAI,OAAO,CAAC,UAAU;gBAAE,KAAK,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;YAC9D,IAAI,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;gBACvC,KAAK,CAAC,SAAS,GAAG,EAAE,CAAC;gBACrB,IAAI,OAAO,CAAC,QAAQ;oBAAE,KAAK,CAAC,SAAS,CAAC,GAAG,GAAG,OAAO,CAAC,QAAQ,CAAC;gBAC7D,IAAI,OAAO,CAAC,MAAM;oBAAE,KAAK,CAAC,SAAS,CAAC,GAAG,GAAG,OAAO,CAAC,MAAM,CAAC;YAC3D,CAAC;YAED,MAAM,CACJ,kBAAkB,EAClB,uBAAuB,EACvB,mBAAmB,EACnB,oBAAoB,EACpB,qBAAqB,EACtB,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBACpB,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,CAAC;gBACjD,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,KAAK,CAAC;oBACrC,KAAK,EAAE,EAAE,GAAG,KAAK,EAAE,MAAM,EAAE,uCAA8B,CAAC,QAAQ,EAAE;iBACrE,CAAC;gBACF,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,KAAK,CAAC;oBACrC,KAAK,EAAE,EAAE,GAAG,KAAK,EAAE,MAAM,EAAE,uCAA8B,CAAC,QAAQ,EAAE;iBACrE,CAAC;gBACF,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,KAAK,CAAC;oBACrC,KAAK,EAAE,EAAE,GAAG,KAAK,EAAE,MAAM,EAAE,uCAA8B,CAAC,OAAO,EAAE;iBACpE,CAAC;gBACF,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,OAAO,CAAC;oBACvC,EAAE,EAAE,CAAC,QAAQ,CAAC;oBACd,KAAK;oBACL,MAAM,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;iBACzB,CAAC;aACH,CAAC,CAAC;YAEH,MAAM,WAAW,GAA2B,EAAE,CAAC;YAC/C,qBAAqB,CAAC,OAAO,CAAC,CAAC,IAAS,EAAE,EAAE;gBAC1C,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;YAChD,CAAC,CAAC,CAAC;YAEH,mDAAmD;YACnD,MAAM,uBAAuB,GAAG,IAAI,CAAC,CAAC,oBAAoB;YAE1D,OAAO;gBACL,kBAAkB;gBAClB,uBAAuB;gBACvB,mBAAmB;gBACnB,oBAAoB;gBACpB,qBAAqB,EAAE,WAAW;gBAClC,uBAAuB;aACxB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YACzD,MAAM,qDAAyB,CAAC,aAAa,CAAC,4CAA4C,CAAC,CAAC;QAC9F,CAAC;IACH,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,MAAsC;QAC5D,OAAO,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,OAAO,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,MAAmC;QAC9C,IAAI,CAAC,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,MAAM,EAAE,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW;QACf,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,4BAA4B;QAC5B,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;YAChD,IAAI,CAAC;gBACH,QAAQ,MAAM,EAAE,CAAC;oBACf,KAAK,uCAA8B,CAAC,kBAAkB;wBACpD,2BAA2B;wBAC3B,OAAO,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;wBACnC,MAAM;oBACR,+BAA+B;oBAC/B;wBACE,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACzB,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,KAAK,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;YACxF,CAAC;QACH,CAAC;QAED,OAAO;YACL,MAAM,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU;YACpD,OAAO;YACP,MAAM;SACP,CAAC;IACJ,CAAC;CACF;AAvOD,kEAuOC", "names": [], "sources": ["F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts"], "sourcesContent": ["/**\n * Identity Verification Service\n *\n * Main orchestrator for all identity verification methods.\n */\n\nimport {\n  PrismaClient,\n  IdentityVerificationMethodEnum,\n  IdentityVerificationStatusEnum,\n} from '@prisma/client';\nimport {\n  IdentityVerificationResult,\n  VerificationFilters,\n  VerificationStats,\n  VerificationConfig,\n  EthereumSignatureParams,\n} from './IdentityVerificationTypes';\nimport { IdentityVerificationError } from './IdentityVerificationError';\nimport { EthereumSignatureVerification } from '../methods/EthereumSignatureVerification';\nimport { logger } from '../../../lib/logger';\n\n/**\n * Main identity verification service\n */\nexport class IdentityVerificationService {\n  private prisma: PrismaClient;\n  private config: VerificationConfig;\n  private ethereumSignatureVerification: EthereumSignatureVerification;\n\n  constructor(prisma: PrismaClient, config?: Partial<VerificationConfig>) {\n    this.prisma = prisma;\n    this.config = {\n      ethereumRpcUrl:\n        process.env.ETHEREUM_RPC_URL || 'https://mainnet.infura.io/v3/your-infura-key',\n      enabledMethods: [\n        IdentityVerificationMethodEnum.ETHEREUM_SIGNATURE,\n        IdentityVerificationMethodEnum.ERC1484,\n        IdentityVerificationMethodEnum.ERC725,\n        IdentityVerificationMethodEnum.ENS,\n        IdentityVerificationMethodEnum.POLYGON_ID,\n        IdentityVerificationMethodEnum.WORLDCOIN,\n        IdentityVerificationMethodEnum.BRIGHTID,\n      ],\n      ...config,\n    };\n\n    // Initialize verification methods\n    this.ethereumSignatureVerification = new EthereumSignatureVerification(this.prisma);\n  }\n\n  /**\n   * Verify identity using Ethereum signature\n   */\n  async verifyEthereumSignature(\n    params: EthereumSignatureParams\n  ): Promise<IdentityVerificationResult> {\n    if (!this.isMethodEnabled(IdentityVerificationMethodEnum.ETHEREUM_SIGNATURE)) {\n      throw IdentityVerificationError.invalidParameters(\n        'Ethereum signature verification is not enabled'\n      );\n    }\n\n    return await this.ethereumSignatureVerification.verify(params);\n  }\n\n  /**\n   * Get verification by ID\n   */\n  async getVerificationById(id: string) {\n    try {\n      const verification = await this.prisma.identityVerification.findUnique({\n        where: { id },\n        include: { claims: true },\n      });\n\n      if (!verification) {\n        throw IdentityVerificationError.verificationNotFound();\n      }\n\n      return verification;\n    } catch (error) {\n      if (error instanceof IdentityVerificationError) {\n        throw error;\n      }\n\n      logger.error('Error getting verification by ID:', error);\n      throw IdentityVerificationError.internalError('Failed to retrieve identity verification');\n    }\n  }\n\n  /**\n   * Get verifications with filters\n   */\n  async getVerifications(filters: VerificationFilters = {}) {\n    try {\n      const where: any = {};\n\n      if (filters.userId) where.userId = filters.userId;\n      if (filters.merchantId) where.merchantId = filters.merchantId;\n      if (filters.method) where.method = filters.method;\n      if (filters.status) where.status = filters.status;\n\n      if (filters.dateFrom || filters.dateTo) {\n        where.createdAt = {};\n        if (filters.dateFrom) where.createdAt.gte = filters.dateFrom;\n        if (filters.dateTo) where.createdAt.lte = filters.dateTo;\n      }\n\n      return await this.prisma.identityVerification.findMany({\n        where,\n        include: { claims: true },\n        orderBy: { createdAt: 'desc' },\n        take: filters.limit || 50,\n        skip: filters.offset || 0,\n      });\n    } catch (error) {\n      logger.error('Error getting verifications:', error);\n      throw IdentityVerificationError.internalError('Failed to retrieve identity verifications');\n    }\n  }\n\n  /**\n   * Get verifications for user\n   */\n  async getVerificationsForUser(userId: string) {\n    if (!userId) {\n      throw IdentityVerificationError.invalidParameters('User ID is required');\n    }\n\n    return await this.getVerifications({ userId });\n  }\n\n  /**\n   * Get verifications for merchant\n   */\n  async getVerificationsForMerchant(merchantId: string) {\n    if (!merchantId) {\n      throw IdentityVerificationError.invalidParameters('Merchant ID is required');\n    }\n\n    return await this.getVerifications({ merchantId });\n  }\n\n  /**\n   * Get verification statistics\n   */\n  async getVerificationStats(filters: VerificationFilters = {}): Promise<VerificationStats> {\n    try {\n      const where: any = {};\n\n      if (filters.userId) where.userId = filters.userId;\n      if (filters.merchantId) where.merchantId = filters.merchantId;\n      if (filters.dateFrom || filters.dateTo) {\n        where.createdAt = {};\n        if (filters.dateFrom) where.createdAt.gte = filters.dateFrom;\n        if (filters.dateTo) where.createdAt.lte = filters.dateTo;\n      }\n\n      const [\n        totalVerifications,\n        successfulVerifications,\n        failedVerifications,\n        pendingVerifications,\n        verificationsByMethod,\n      ] = await Promise.all([\n        this.prisma.identityVerification.count({ where }),\n        this.prisma.identityVerification.count({\n          where: { ...where, status: IdentityVerificationStatusEnum.VERIFIED },\n        }),\n        this.prisma.identityVerification.count({\n          where: { ...where, status: IdentityVerificationStatusEnum.REJECTED },\n        }),\n        this.prisma.identityVerification.count({\n          where: { ...where, status: IdentityVerificationStatusEnum.PENDING },\n        }),\n        this.prisma.identityVerification.groupBy({\n          by: ['method'],\n          where,\n          _count: { method: true },\n        }),\n      ]);\n\n      const methodStats: Record<string, number> = {};\n      verificationsByMethod.forEach((item: any) => {\n        methodStats[item.method] = item._count.method;\n      });\n\n      // Calculate average verification time (simplified)\n      const averageVerificationTime = 5000; // 5 seconds average\n\n      return {\n        totalVerifications,\n        successfulVerifications,\n        failedVerifications,\n        pendingVerifications,\n        verificationsByMethod: methodStats,\n        averageVerificationTime,\n      };\n    } catch (error) {\n      logger.error('Error getting verification stats:', error);\n      throw IdentityVerificationError.internalError('Failed to retrieve verification statistics');\n    }\n  }\n\n  /**\n   * Check if verification method is enabled\n   */\n  private isMethodEnabled(method: IdentityVerificationMethodEnum): boolean {\n    return this.config.enabledMethods.includes(method);\n  }\n\n  /**\n   * Get enabled verification methods\n   */\n  getEnabledMethods(): IdentityVerificationMethodEnum[] {\n    return [...this.config.enabledMethods];\n  }\n\n  /**\n   * Update configuration\n   */\n  updateConfig(config: Partial<VerificationConfig>): void {\n    this.config = { ...this.config, ...config };\n  }\n\n  /**\n   * Health check for verification service\n   */\n  async healthCheck(): Promise<{ status: string; methods: string[]; errors: string[] }> {\n    const errors: string[] = [];\n    const methods: string[] = [];\n\n    // Check each enabled method\n    for (const method of this.config.enabledMethods) {\n      try {\n        switch (method) {\n          case IdentityVerificationMethodEnum.ETHEREUM_SIGNATURE:\n            // Test Ethereum connection\n            methods.push('ethereum_signature');\n            break;\n          // Add other method checks here\n          default:\n            methods.push(method);\n        }\n      } catch (error) {\n        errors.push(`${method}: ${error instanceof Error ? error.message : 'Unknown error'}`);\n      }\n    }\n\n    return {\n      status: errors.length === 0 ? 'healthy' : 'degraded',\n      methods,\n      errors,\n    };\n  }\n}\n"], "version": 3}