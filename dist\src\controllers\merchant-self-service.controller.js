"use strict";
// jscpd:ignore-file
/**
 * Merchant Self-Service Controller
 *
 * This controller handles API requests related to merchant self-service tools.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.MerchantSelfServiceController = void 0;
const base_controller_1 = require("./base.controller");
const merchant_self_service_service_1 = require("../services/merchant-self-service.service");
const logger_1 = require("../utils/logger");
/**
 * Merchant self-service controller
 */
class MerchantSelfServiceController extends base_controller_1.BaseController {
    constructor() {
        super();
        /**
       * Create API key
       */
        this.createApiKey = this.asyncHandler(async (req, res) => {
            try {
                const { merchantId } = req.params;
                const { name, permissions, expiresAt } = req.body;
                // Validate required fields
                if (!name || !permissions || !Array.isArray(permissions)) {
                    return res.badRequest("Name and permissions array are required");
                }
                // Validate permissions
                const validPermissions = Object.values(merchant_self_service_service_1.ApiKeyPermission);
                const invalidPermissions = permissions.filter(p => !validPermissions.includes(p));
                if (invalidPermissions.length > 0) {
                    return res.badRequest(`Invalid permissions: ${invalidPermissions.join(", ")}. Valid permissions are: ${validPermissions.join(", ")}`);
                }
                // Create API key
                const apiKey = await this.merchantSelfServiceService.createApiKey(merchantId, name, permissions, expiresAt ? new Date(expiresAt) : undefined);
                return res.success("API key created", apiKey);
            }
            catch (error) {
                logger_1.logger.error("Error creating API key:", error);
                return res.serverError("Failed to create API key");
            }
        });
        /**
       * Get merchant API keys
       */
        this.getMerchantApiKeys = this.asyncHandler(async (req, res) => {
            try {
                const { merchantId } = req.params;
                // Get API keys
                const apiKeys = await this.merchantSelfServiceService.getMerchantApiKeys(merchantId);
                return res.success("Merchant API keys retrieved", apiKeys);
            }
            catch (error) {
                logger_1.logger.error("Error getting merchant API keys:", error);
                return res.serverError("Failed to get merchant API keys");
            }
        });
        /**
       * Delete API key
       */
        this.deleteApiKey = this.asyncHandler(async (req, res) => {
            try {
                const { apiKeyId } = req.params;
                // Delete API key
                const apiKey = await this.merchantSelfServiceService.deleteApiKey(apiKeyId);
                return res.success("API key deleted", apiKey);
            }
            catch (error) {
                logger_1.logger.error("Error deleting API key:", error);
                return res.serverError("Failed to delete API key");
            }
        });
        /**
       * Create webhook
       */
        this.createWebhook = this.asyncHandler(async (req, res) => {
            try {
                const { merchantId } = req.params;
                const { url, events } = req.body;
                // Validate required fields
                if (!url || !events || !Array.isArray(events)) {
                    return res.badRequest("URL and events array are required");
                }
                // Validate URL
                try {
                    new URL(url);
                }
                catch (error) {
                    return res.badRequest("Invalid URL");
                }
                // Validate events
                const webhook = await this.merchantSelfServiceService.createWebhook(merchantId, url, events);
                return res.success("Webhook created", webhook);
            }
            catch (error) {
                logger_1.logger.error("Error creating webhook:", error);
                return res.serverError("Failed to create webhook");
            }
        });
        /**
       * Get merchant webhooks
       */
        this.getMerchantWebhooks = this.asyncHandler(async (req, res) => {
            try {
                const { merchantId } = req.params;
                // Get webhooks
                const webhooks = await this.merchantSelfServiceService.getMerchantWebhooks(merchantId);
                return res.success("Merchant webhooks retrieved", webhooks);
            }
            catch (error) {
                logger_1.logger.error("Error getting merchant webhooks:", error);
                return res.serverError("Failed to get merchant webhooks");
            }
        });
        /**
       * Update webhook
       */
        this.updateWebhook = this.asyncHandler(async (req, res) => {
            try {
                const { webhookId } = req.params;
                const { url, events, isActive } = req.body;
                // Validate URL if provided
                if (url) {
                    try {
                        new URL(url);
                    }
                    catch (error) {
                        return res.badRequest("Invalid URL");
                    }
                }
                // Validate events if provided
                if (events) {
                    if (!Array.isArray(events)) {
                        return res.badRequest("Events must be an array");
                    }
                }
                // Update webhook
                const webhook = await this.merchantSelfServiceService.updateWebhook(webhookId, url, events, isActive);
                return res.success("Webhook updated", webhook);
            }
            catch (error) {
                logger_1.logger.error("Error updating webhook:", error);
                return res.serverError("Failed to update webhook");
            }
        });
        /**
       * Delete webhook
       */
        this.deleteWebhook = this.asyncHandler(async (req, res) => {
            try {
                const { webhookId } = req.params;
                // Delete webhook
                const webhook = await this.merchantSelfServiceService.deleteWebhook(webhookId);
                return res.success("Webhook deleted", webhook);
            }
            catch (error) {
                logger_1.logger.error("Error deleting webhook:", error);
                return res.serverError("Failed to delete webhook");
            }
        });
        /**
       * Set notification preference
       */
        this.setNotificationPreference = this.asyncHandler(async (req, res) => {
            try {
                const { merchantId } = req.params;
                const { channel, eventType, enabled } = req.body;
                // Validate required fields
                if (!channel || !eventType || enabled === undefined) {
                    return res.badRequest("Channel, eventType, and enabled are required");
                }
                // Validate channel
                if (!Object.values(merchant_self_service_service_1.NotificationChannel).includes(channel)) {
                    return res.badRequest(`Invalid channel. Must be one of: ${Object.values(merchant_self_service_service_1.NotificationChannel).join(", ")}`);
                }
                // Validate event type
                if (!Object.values(merchant_self_service_service_1.WebhookEventType).includes(eventType)) {
                    return res.badRequest(`Invalid event type. Must be one of: ${Object.values(merchant_self_service_service_1.WebhookEventType).join(", ")}`);
                }
                // Set notification preference
                const preference = await this.merchantSelfServiceService.setNotificationPreference(merchantId, channel, eventType, enabled);
                return res.success("Notification preference set", preference);
            }
            catch (error) {
                logger_1.logger.error("Error setting notification preference:", error);
                return res.serverError("Failed to set notification preference");
            }
        });
        /**
       * Get merchant notification preferences
       */
        this.getMerchantNotificationPreferences = this.asyncHandler(async (req, res) => {
            try {
                const { merchantId } = req.params;
                // Get notification preferences
                const preferences = await this.merchantSelfServiceService.getMerchantNotificationPreferences(merchantId);
                return res.success("Merchant notification preferences retrieved", preferences);
            }
            catch (error) {
                logger_1.logger.error("Error getting merchant notification preferences:", error);
                return res.serverError("Failed to get merchant notification preferences");
            }
        });
        this.merchantSelfServiceService = new merchant_self_service_service_1.MerchantSelfServiceService();
    }
}
exports.MerchantSelfServiceController = MerchantSelfServiceController;
//# sourceMappingURL=merchant-self-service.controller.js.map