// jscpd:ignore-file
import { Router } from "express";
import { <PERSON><PERSON><PERSON>ontroller } from "../controllers/refactored/alert.controller";
import { authenticate } from '../middlewares/auth';
import { Alert } from '../types';
import { <PERSON>ertController } from "../controllers/refactored/alert.controller";
import { authenticate } from '../middlewares/auth';
import { Alert } from '../types';


// Create controller instance
const alertControllerInstance: any =new AlertController();
const router: any =Router();

// Alert routes
router.get("/", authenticate, alertControllerInstance.getAlerts);
router.get("/count", authenticate, alertControllerInstance.getAlertCount);
router.get("/:id", authenticate, alertControllerInstance.getAlert);
router.put("/:id/status", authenticate, alertControllerInstance.updateAlertStatus);
router.post("/test", authenticate, alertControllerInstance.createTestAlert);

export default router;
