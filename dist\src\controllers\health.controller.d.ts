import { Request, Response } from 'express';
export declare class HealthController {
    /**
     * Basic health check
     */
    healthCheck: (req: Request, res: Response) => Promise<void>;
    /**
     * Detailed health check including dependencies
     */
    detailedHealthCheck: (req: Request, res: Response) => Promise<void>;
    /**
     * Get reporting system metrics
     */
    getMetrics: (req: Request, res: Response) => Promise<void>;
    /**
     * Trigger cleanup of old reports
     */
    cleanupReports: (req: Request, res: Response) => Promise<void>;
    /**
     * Get system information
     */
    getSystemInfo: (req: Request, res: Response) => Promise<void>;
    /**
     * Check database connectivity
     */
    private checkDatabase;
    /**
     * Check file system access
     */
    private checkFileSystem;
    /**
     * Check reporting system health
     */
    private checkReportingSystem;
    /**
     * Check memory usage
     */
    private checkMemoryUsage;
    /**
     * Extract result from Promise.allSettled
     */
    private getCheckResult;
}
//# sourceMappingURL=health.controller.d.ts.map