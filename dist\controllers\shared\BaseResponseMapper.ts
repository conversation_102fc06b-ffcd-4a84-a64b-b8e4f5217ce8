/**
 * Base Response Mapper
 * 
 * Centralized response handling to eliminate code duplication across controllers.
 * Provides consistent API responses with proper error handling and logging.
 */

import { Response } from 'express';
import { AppError } from '../../core/errors/AppError';
import { logger } from '../../utils/logger';

export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  message: string;
  pagination?: PaginationInfo;
  timestamp: Date;
  requestId: string;
}

export interface APIErrorResponse {
  success: false;
  error: {
    message: string;
    code?: string;
    type: string;
    details?: any;
  };
  timestamp: Date;
  requestId: string;
}

export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

/**
 * Base Response Mapper - Eliminates duplication across all controllers
 */
export class BaseResponseMapper {
  /**
   * Send successful response
   */
  static sendSuccess<T>(
    res: Response,
    data: T,
    message: string = 'Operation completed successfully',
    statusCode: number = 200,
    pagination?: PaginationInfo
  ): void {
    const response: APIResponse<T> = {
      success: true,
      data,
      message,
      pagination,
      timestamp: new Date(),
      requestId: res.locals.requestId ?? 'unknown',
    };

    res.status(statusCode).json(response);
  }

  /**
   * Send error response
   */
  static sendError(res: Response, error: Error | AppError, statusCode?: number): void {
    let errorResponse: APIErrorResponse;

    if (error instanceof AppError) {
      errorResponse = {
        success: false,
        error: {
          message: error.message,
          code: error.code,
          type: error.type,
          details: error.details,
        },
        timestamp: new Date(),
        requestId: res.locals.requestId ?? 'unknown',
      };

      statusCode = statusCode ?? error.statusCode ?? 400;
    } else {
      errorResponse = {
        success: false,
        error: {
          message: error.message,
          type: 'INTERNAL',
        },
        timestamp: new Date(),
        requestId: res.locals.requestId ?? 'unknown',
      };

      statusCode = statusCode ?? 500;
    }

    // Log error for monitoring
    logger.error('API Error Response', {
      error: error.message,
      stack: error.stack,
      requestId: res.locals.requestId,
      statusCode,
    });

    res.status(statusCode).json(errorResponse);
  }

  /**
   * Send paginated response
   */
  static sendPaginated<T>(
    res: Response,
    data: T[],
    total: number,
    page: number,
    limit: number,
    message: string = 'Data retrieved successfully'
  ): void {
    const totalPages = Math.ceil(total / limit);
    const pagination: PaginationInfo = {
      page,
      limit,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };

    this.sendSuccess(res, data, message, 200, pagination);
  }

  /**
   * Send created response
   */
  static sendCreated<T>(res: Response, data: T, message: string = 'Resource created successfully'): void {
    this.sendSuccess(res, data, message, 201);
  }

  /**
   * Send updated response
   */
  static sendUpdated<T>(res: Response, data: T, message: string = 'Resource updated successfully'): void {
    this.sendSuccess(res, data, message, 200);
  }

  /**
   * Send deleted response
   */
  static sendDeleted(res: Response, message: string = 'Resource deleted successfully'): void {
    this.sendSuccess(res, null, message, 200);
  }

  /**
   * Send not found response
   */
  static sendNotFound(res: Response, message: string = 'Resource not found'): void {
    const error = new AppError({
      message,
      statusCode: 404,
      type: 'NOT_FOUND',
    });
    this.sendError(res, error);
  }

  /**
   * Send validation error response
   */
  static sendValidationError(res: Response, details: any, message: string = 'Validation failed'): void {
    const error = new AppError({
      message,
      statusCode: 400,
      type: 'VALIDATION_ERROR',
      details,
    });
    this.sendError(res, error);
  }

  /**
   * Send unauthorized response
   */
  static sendUnauthorized(res: Response, message: string = 'Unauthorized access'): void {
    const error = new AppError({
      message,
      statusCode: 401,
      type: 'UNAUTHORIZED',
    });
    this.sendError(res, error);
  }

  /**
   * Send forbidden response
   */
  static sendForbidden(res: Response, message: string = 'Access forbidden'): void {
    const error = new AppError({
      message,
      statusCode: 403,
      type: 'FORBIDDEN',
    });
    this.sendError(res, error);
  }

  /**
   * Send conflict response
   */
  static sendConflict(res: Response, message: string = 'Resource conflict'): void {
    const error = new AppError({
      message,
      statusCode: 409,
      type: 'CONFLICT',
    });
    this.sendError(res, error);
  }

  /**
   * Send rate limit response
   */
  static sendRateLimit(res: Response, message: string = 'Rate limit exceeded'): void {
    const error = new AppError({
      message,
      statusCode: 429,
      type: 'RATE_LIMIT',
    });
    this.sendError(res, error);
  }

  /**
   * Send internal server error response
   */
  static sendInternalError(res: Response, message: string = 'Internal server error'): void {
    const error = new AppError({
      message,
      statusCode: 500,
      type: 'INTERNAL_ERROR',
    });
    this.sendError(res, error);
  }

  /**
   * Send service unavailable response
   */
  static sendServiceUnavailable(res: Response, message: string = 'Service temporarily unavailable'): void {
    const error = new AppError({
      message,
      statusCode: 503,
      type: 'SERVICE_UNAVAILABLE',
    });
    this.sendError(res, error);
  }

  /**
   * Create pagination info
   */
  static createPagination(page: number, limit: number, total: number): PaginationInfo {
    const totalPages = Math.ceil(total / limit);
    return {
      page,
      limit,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  /**
   * Handle async controller method with automatic error handling
   */
  static async handleAsync(
    res: Response,
    asyncFn: () => Promise<any>,
    successMessage?: string,
    successStatusCode?: number
  ): Promise<void> {
    try {
      const result = await asyncFn();
      this.sendSuccess(res, result, successMessage, successStatusCode);
    } catch (error) {
      this.sendError(res, error as Error);
    }
  }
}
