import { Request, Response } from "express";
import { AppError } from "../../utils/appError";
import { ServiceError } from "../../utils/errors/ServiceError";
export declare class BaseController {
    constructor();
    /**
     * Async handler for controller methods
     * @param fn Function to handle
     * @returns Express handler
     */
    protected asyncHandler(fn: (req: Request, res: Response) => Promise<any>): any;
    /**
     * Check if user is authorized
     * @param req Express request
     * @throws AppError if user is not authorized
     */
    protected checkAuthorization(req: Request): {
        userRole: string;
        userId: string;
        merchantId?: string;
    };
    /**
     * Check if user is admin
     * @param req Express request
     * @throws AppError if user is not admin
     */
    protected checkAdminRole(req: Request): {
        userId: string;
    };
    /**
     * Check if user is merchant
     * @param req Express request
     * @throws AppError if user is not merchant
     */
    protected checkMerchantRole(req: Request): {
        userId: string;
        merchantId: string;
    };
    /**
     * Parse date range from request
     * @param req Express request
     * @param startDateField Start date field name
     * @param endDateField End date field name
     * @returns Date range
     */
    protected parseDateRange(req: Request, startDateField?: string, endDateField?: string): {
        startDate: Date;
        endDate: Date;
    };
    /**
     * Parse pagination parameters from request
     * @param req Express request
     * @returns Pagination parameters
     */
    protected parsePagination(req: Request): {
        page: number;
        limit: number;
        offset: number;
    };
    /**
     * Validate required fields
     * @param req Express request
     * @param fields Required fields
     * @throws AppError if any required field is missing
     */
    protected validateRequiredFields(req: Request, fields: string[]): void;
    /**
     * Validate enum value
     * @param value Value to validate
     * @param enumType Enum type
     * @param fieldName Field name for error message
     * @throws AppError if value is not in enum
     */
    protected validateEnum<T extends object>(value: any, enumType: T, fieldName: string): void;
    /**
     * Send success response
     * @param res Express response
     * @param data Response data
     * @param statusCode HTTP status code
     */
    protected sendSuccess(res: Response, data: any, statusCode?: number): Response;
    /**
     * Send message response
     * @param res Express response
     * @param message Response message
     * @param statusCode HTTP status code
     */
    protected sendMessage(res: Response, message: string, statusCode?: number): Response;
    /**
     * Send paginated success response
     * @param res Express response
     * @param data Response data
     * @param total Total number of items
     * @param page Current page
     * @param limit Items per page
     * @param statusCode HTTP status code
     */
    protected sendPaginatedSuccess(res: Response, data: any, total: number, page: number, limit: number, statusCode?: number): Response;
    /**
     * Handle error
     * @param error Error
     * @param res Response
     */
    protected handleError(error: any, res: Response): Response;
    /**
     * Send error response
     * @param res Express response
     * @param error Error
     * @returns Response
     */
    protected sendError(res: Response, error: Error | AppError | ServiceError | string, statusCode?: number): Response;
    /**
     * Send not found response
     * @param res Express response
     * @param resource Resource name
     * @param id Resource ID
     * @returns Response
     */
    protected sendNotFound(res: Response, resource: string, id: string): Response;
    /**
     * Send validation error response
     * @param res Express response
     * @param errors Validation errors
     * @returns Response
     */
    protected sendValidationError(res: Response, errors: Record<string, string[]>): Response;
}
//# sourceMappingURL=BaseController.d.ts.map