{"version": 3, "file": "BaseResponseMapper.js", "sourceRoot": "", "sources": ["../../../../src/controllers/shared/BaseResponseMapper.ts"], "names": [], "mappings": ";AAAA;;;;;GAKG;;;AAGH,yDAAsD;AACtD,+CAA4C;AAgC5C;;GAEG;AACH,MAAa,kBAAkB;IAC7B;;OAEG;IACH,MAAM,CAAC,WAAW,CAChB,GAAa,EACb,IAAO,EACP,UAAkB,kCAAkC,EACpD,aAAqB,GAAG,EACxB,UAA2B;QAE3B,MAAM,QAAQ,GAAmB;YAC/B,OAAO,EAAE,IAAI;YACb,IAAI;YACJ,OAAO;YACP,UAAU;YACV,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,SAAS,EAAE,GAAG,CAAC,MAAM,CAAC,SAAS,IAAI,SAAS;SAC7C,CAAC;QAEF,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,SAAS,CAAC,GAAa,EAAE,KAAuB,EAAE,UAAmB;QAC1E,IAAI,aAA+B,CAAC;QAEpC,IAAI,KAAK,YAAY,mBAAQ,EAAE,CAAC;YAC9B,aAAa,GAAG;gBACd,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE;oBACL,OAAO,EAAE,KAAK,CAAC,OAAO;oBACtB,IAAI,EAAE,KAAK,CAAC,IAAI;oBAChB,IAAI,EAAE,KAAK,CAAC,IAAI;oBAChB,OAAO,EAAE,KAAK,CAAC,OAAO;iBACvB;gBACD,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,SAAS,EAAE,GAAG,CAAC,MAAM,CAAC,SAAS,IAAI,SAAS;aAC7C,CAAC;YAEF,UAAU,GAAG,UAAU,IAAI,KAAK,CAAC,UAAU,IAAI,GAAG,CAAC;QACrD,CAAC;aAAM,CAAC;YACN,aAAa,GAAG;gBACd,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE;oBACL,OAAO,EAAE,KAAK,CAAC,OAAO;oBACtB,IAAI,EAAE,UAAU;iBACjB;gBACD,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,SAAS,EAAE,GAAG,CAAC,MAAM,CAAC,SAAS,IAAI,SAAS;aAC7C,CAAC;YAEF,UAAU,GAAG,UAAU,IAAI,GAAG,CAAC;QACjC,CAAC;QAED,2BAA2B;QAC3B,eAAM,CAAC,KAAK,CAAC,oBAAoB,EAAE;YACjC,KAAK,EAAE,KAAK,CAAC,OAAO;YACpB,KAAK,EAAE,KAAK,CAAC,KAAK;YAClB,SAAS,EAAE,GAAG,CAAC,MAAM,CAAC,SAAS;YAC/B,UAAU;SACX,CAAC,CAAC;QAEH,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,aAAa,CAClB,GAAa,EACb,IAAS,EACT,KAAa,EACb,IAAY,EACZ,KAAa,EACb,UAAkB,6BAA6B;QAE/C,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;QAC5C,MAAM,UAAU,GAAmB;YACjC,IAAI;YACJ,KAAK;YACL,KAAK;YACL,UAAU;YACV,OAAO,EAAE,IAAI,GAAG,UAAU;YAC1B,OAAO,EAAE,IAAI,GAAG,CAAC;SAClB,CAAC;QAEF,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC;IACxD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,WAAW,CAAI,GAAa,EAAE,IAAO,EAAE,UAAkB,+BAA+B;QAC7F,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,WAAW,CAAI,GAAa,EAAE,IAAO,EAAE,UAAkB,+BAA+B;QAC7F,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,WAAW,CAAC,GAAa,EAAE,UAAkB,+BAA+B;QACjF,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,YAAY,CAAC,GAAa,EAAE,UAAkB,oBAAoB;QACvE,MAAM,KAAK,GAAG,IAAI,mBAAQ,CAAC;YACzB,OAAO;YACP,UAAU,EAAE,GAAG;YACf,IAAI,EAAE,WAAW;SAClB,CAAC,CAAC;QACH,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,mBAAmB,CAAC,GAAa,EAAE,OAAY,EAAE,UAAkB,mBAAmB;QAC3F,MAAM,KAAK,GAAG,IAAI,mBAAQ,CAAC;YACzB,OAAO;YACP,UAAU,EAAE,GAAG;YACf,IAAI,EAAE,kBAAkB;YACxB,OAAO;SACR,CAAC,CAAC;QACH,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,gBAAgB,CAAC,GAAa,EAAE,UAAkB,qBAAqB;QAC5E,MAAM,KAAK,GAAG,IAAI,mBAAQ,CAAC;YACzB,OAAO;YACP,UAAU,EAAE,GAAG;YACf,IAAI,EAAE,cAAc;SACrB,CAAC,CAAC;QACH,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,aAAa,CAAC,GAAa,EAAE,UAAkB,kBAAkB;QACtE,MAAM,KAAK,GAAG,IAAI,mBAAQ,CAAC;YACzB,OAAO;YACP,UAAU,EAAE,GAAG;YACf,IAAI,EAAE,WAAW;SAClB,CAAC,CAAC;QACH,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,YAAY,CAAC,GAAa,EAAE,UAAkB,mBAAmB;QACtE,MAAM,KAAK,GAAG,IAAI,mBAAQ,CAAC;YACzB,OAAO;YACP,UAAU,EAAE,GAAG;YACf,IAAI,EAAE,UAAU;SACjB,CAAC,CAAC;QACH,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,aAAa,CAAC,GAAa,EAAE,UAAkB,qBAAqB;QACzE,MAAM,KAAK,GAAG,IAAI,mBAAQ,CAAC;YACzB,OAAO;YACP,UAAU,EAAE,GAAG;YACf,IAAI,EAAE,YAAY;SACnB,CAAC,CAAC;QACH,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,iBAAiB,CAAC,GAAa,EAAE,UAAkB,uBAAuB;QAC/E,MAAM,KAAK,GAAG,IAAI,mBAAQ,CAAC;YACzB,OAAO;YACP,UAAU,EAAE,GAAG;YACf,IAAI,EAAE,gBAAgB;SACvB,CAAC,CAAC;QACH,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,sBAAsB,CAAC,GAAa,EAAE,UAAkB,iCAAiC;QAC9F,MAAM,KAAK,GAAG,IAAI,mBAAQ,CAAC;YACzB,OAAO;YACP,UAAU,EAAE,GAAG;YACf,IAAI,EAAE,qBAAqB;SAC5B,CAAC,CAAC;QACH,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,gBAAgB,CAAC,IAAY,EAAE,KAAa,EAAE,KAAa;QAChE,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;QAC5C,OAAO;YACL,IAAI;YACJ,KAAK;YACL,KAAK;YACL,UAAU;YACV,OAAO,EAAE,IAAI,GAAG,UAAU;YAC1B,OAAO,EAAE,IAAI,GAAG,CAAC;SAClB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,WAAW,CACtB,GAAa,EACb,OAA2B,EAC3B,cAAuB,EACvB,iBAA0B;QAE1B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,OAAO,EAAE,CAAC;YAC/B,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,MAAM,EAAE,cAAc,EAAE,iBAAiB,CAAC,CAAC;QACnE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,KAAc,CAAC,CAAC;QACtC,CAAC;IACH,CAAC;CACF;AAjPD,gDAiPC"}