// jscpd:ignore-file
/**
 * Security Middleware
 *
 * This middleware provides additional security features for the API.
 */

import { Request, Response, NextFunction, RequestHandler } from 'express';
import { logger } from '../lib/logger';
import { createBadRequestError, createForbiddenError } from './error.middleware';
import { validateCsrfToken, extractCsrfToken } from '../utils/csrf';
import { isProduction, isDemo } from '../utils/environment-validator';

// Extend the Express Request type to include user property
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role: string;
        merchantId?: string;
      };
    }
  }
}

/**
 * Content Security Policy middleware
 * Sets CSP headers to prevent XSS attacks
 */
export const contentSecurityPolicy: RequestHandler = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  // Set Content-Security-Policy header
  res.setHeader(
    'Content-Security-Policy',
    "default-src 'self'; " +
      "script-src 'self' 'unsafe-inline'; " +
      "style-src 'self' 'unsafe-inline'; " +
      "img-src 'self' data: blob:; " +
      "font-src 'self' data:; " +
      "connect-src 'self' https://api.amazingpayme.com; " +
      "frame-src 'self'; " +
      "object-src 'none'; " +
      "base-uri 'self'; " +
      "form-action 'self';"
  );

  next();
};

/**
 * CSRF Protection middleware
 * Validates CSRF tokens for mutating requests
 */

export const csrfProtection: RequestHandler = (req: Request, res: Response, next: NextFunction) => {
  // Skip CSRF check for GET, HEAD, OPTIONS requests
  if (['GET', 'HEAD', 'OPTIONS'].includes(req.method)) {
    return next();
  }

  // Skip CSRF check for API endpoints that use JWT authentication
  if (req.path.startsWith('/api/') && req.headers.authorization) {
    return next();
  }

  // Extract CSRF token
  const csrfToken = extractCsrfToken(req);

  if (!csrfToken) {
    logger.warn('CSRF token missing', {
      ip: req.ip,
      path: req.path,
      method: req.method,
      requestId: req.requestId,
    });

    return next(createForbiddenError('CSRF token missing', 'CSRF_TOKEN_MISSING'));
  }

  // Get user ID from session or request
  const userId = req.user?.id ?? 'anonymous';

  // Validate CSRF token
  if (!validateCsrfToken(csrfToken, userId)) {
    logger.warn('Invalid CSRF token', {
      ip: req.ip,
      path: req.path,
      method: req.method,
      requestId: req.requestId,
      userId,
    });

    return next(createForbiddenError('Invalid CSRF token', 'INVALID_CSRF_TOKEN'));
  }

  next();
};

/**
 * HTTP Strict Transport Security middleware
 * Forces browsers to use HTTPS
 */
export const hsts: RequestHandler = (req: Request, res: Response, next: NextFunction) => {
  // Set Strict-Transport-Security header
  res.setHeader('Strict-Transport-Security', 'max-age = 31536000; includeSubDomains; preload');

  next();
};

/**
 * X-Content-Type-Options middleware
 * Prevents MIME type sniffing
 */
export const noSniff: RequestHandler = (req: Request, res: Response, next: NextFunction) => {
  // Set X-Content-Type-Options header
  res.setHeader('X-Content-Type-Options', 'nosniff');

  next();
};

/**
 * X-Frame-Options middleware
 * Prevents clickjacking
 */
export const frameGuard: RequestHandler = (req: Request, res: Response, next: NextFunction) => {
  // Set X-Frame-Options header
  res.setHeader('X-Frame-Options', 'SAMEORIGIN');

  next();
};

/**
 * X-XSS-Protection middleware
 * Enables browser XSS filtering
 */
export const xssFilter: RequestHandler = (req: Request, res: Response, next: NextFunction) => {
  // Set X-XSS-Protection header
  res.setHeader('X-XSS-Protection', '1; mode = block');

  next();
};

/**
 * Referrer-Policy middleware
 * Controls the Referer header
 */
export const referrerPolicy: RequestHandler = (req: Request, res: Response, next: NextFunction) => {
  // Set Referrer-Policy header
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');

  next();
};

/**
 * Permissions-Policy middleware
 * Restricts browser features
 */
export const permissionsPolicy: RequestHandler = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  // Set Permissions-Policy header
  res.setHeader(
    'Permissions-Policy',
    'camera=(), microphone=(), geolocation=(), interest-cohort=()'
  );

  next();
};

/**
 * Cache Control middleware
 * Sets cache control headers to prevent sensitive information caching
 */
export const cacheControl: RequestHandler = (req: Request, res: Response, next: NextFunction) => {
  // Set Cache-Control header for API routes
  if (req.path.startsWith('/api/')) {
    // No caching for API routes by default
    res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');
  }

  next();
};

/**
 * SQL Injection Protection middleware
 * Basic protection against SQL injection attacks
 */
export const sqlInjectionProtection: RequestHandler = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  // Check for common SQL injection patterns in query parameters
  const sqlInjectionPattern =
    /('|"|;|--|\/\*|\*\/|@@|@|char|nchar|varchar|nvarchar|alter|begin|cast|create|cursor|declare|delete|drop|end|exec|execute|fetch|insert|kill|open|select|sys|sysobjects|syscolumns|table|update|union|waitfor)/i;

  // Check query parameters
  const queryParams = req.query;
  for (const key in queryParams) {
    if (
      typeof queryParams[key] === 'string' &&
      sqlInjectionPattern.test(queryParams[key] as string)
    ) {
      logger.warn('Possible SQL injection attempt detected in query parameters', {
        ip: req.ip,
        path: req.path,
        method: req.method,
        requestId: req.requestId,
        parameter: key,
        value: queryParams[key],
      });

      return next(createBadRequestError('Invalid input', 'INVALID_INPUT'));
    }
  }

  next();
};

/**
 * Environment Banner middleware
 * Adds environment information to response headers
 */
export const environmentBanner: RequestHandler = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  // Add environment information to response headers
  if (isDemo()) {
    res.setHeader('X-Environment', 'demo');
    res.setHeader('X-Demo-Mode', 'true');
  } else if (!isProduction()) {
    res.setHeader('X-Environment', process.env.NODE_ENV ?? 'development');
  }

  next();
};

/**
 * Request Validation middleware
 * Validates request parameters for common security issues
 */
export const requestValidation: RequestHandler = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  // Check for overly large payloads
  const contentLength = parseInt((req.headers['content-length'] as string) || '0', 10);
  if (contentLength > 1024 * 1024) {
    // 1MB
    logger.warn('Request payload too large', {
      ip: req.ip,
      path: req.path,
      method: req.method,
      requestId: req.requestId,
      contentLength,
    });

    return next(createBadRequestError('Request payload too large', 'PAYLOAD_TOO_LARGE'));
  }

  next();
};

/**
 * Apply all security middleware
 */
export const securityMiddleware: RequestHandler[] = [
  contentSecurityPolicy,
  csrfProtection,
  hsts,
  noSniff,
  frameGuard,
  xssFilter,
  referrerPolicy,
  permissionsPolicy,
  cacheControl,
  sqlInjectionProtection,
  environmentBanner,
  requestValidation,
];

export default securityMiddleware;
