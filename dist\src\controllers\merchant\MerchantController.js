"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MerchantController = void 0;
const asyncHandler_1 = require("../../middleware/asyncHandler");
const CrudController_1 = require("../base/CrudController");
const appError_1 = require("../../utils/appError");
const ServiceFactory_1 = require("../../factories/ServiceFactory");
/**
 * Merchant controller
 */
class MerchantController extends CrudController_1.CrudController {
    constructor() {
        const serviceFactory = ServiceFactory_1.ServiceFactory.getInstance();
        const service = serviceFactory.getMerchantService();
        super(service, "Merchant");
        /**
         * Get merchant statistics
         * @route GET /api/merchants/:id/stats
         */
        this.getStats = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Check authentication
                const { userRole, userId, merchantId } = this.checkAuthorization(req);
                // Get merchant ID
                const id = req.params.id;
                // Check if user is authorized to access this merchant
                if (userRole !== "ADMIN" && merchantId !== id) {
                    throw new appError_1.AppError({
                        message: "Unauthorized",
                        type: ErrorType.AUTHORIZATION,
                        code: ErrorCode.FORBIDDEN
                    });
                }
                // Get date range
                const { startDate, endDate } = this.parseDateRange(req);
                // Get statistics
                const stats = await this.merchantService.getMerchantStats(id, {
                    startDate,
                    endDate
                });
                // Return response
                return this.sendSuccess(res, stats);
            }
            catch (error) {
                this.handleError(error, res);
            }
        });
        /**
         * Verify merchant
         * @route POST /api/merchants/:id/verify
         */
        this.verifyMerchant = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Check if user is admin
                this.checkAdminRole(req);
                // Get merchant ID
                const id = req.params.id;
                // Verify merchant
                const merchant = await this.merchantService.verifyMerchant(id);
                // Return response
                return this.sendSuccess(res, merchant);
            }
            catch (error) {
                this.handleError(error, res);
            }
        });
        /**
         * Suspend merchant
         * @route POST /api/merchants/:id/suspend
         */
        this.suspendMerchant = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Check if user is admin
                this.checkAdminRole(req);
                // Get merchant ID
                const id = req.params.id;
                // Validate required fields
                this.validateRequiredFields(req, ["reason"]);
                // Get reason
                const { reason } = req.body;
                // Suspend merchant
                const merchant = await this.merchantService.suspendMerchant(id, reason);
                // Return response
                return this.sendSuccess(res, merchant);
            }
            catch (error) {
                this.handleError(error, res);
            }
        });
        this.merchantService = service;
        // Set required fields
        this.requiredCreateFields = ["name", "email"];
        this.requiredUpdateFields = [];
    }
    /**
     * Get all merchants
     * @param page Page number
     * @param limit Items per page
     * @param offset Offset
     * @param filters Filters
     * @returns Merchants and total count
     */
    async getAllEntities(page, limit, offset, filters) {
        return this.merchantService.getMerchants({
            limit,
            offset,
            ...filters
        });
    }
    /**
     * Get merchant by ID
     * @param id Merchant ID
     * @returns Merchant
     */
    async getEntityById(id) {
        const merchant = await this.merchantService.getMerchantById(id);
        if (!merchant) {
            throw new appError_1.AppError(`Merchant with ID ${id} not found`, 404);
        }
        return merchant;
    }
    /**
     * Create merchant
     * @param data Merchant data
     * @returns Created merchant
     */
    async createEntity(data) {
        return this.merchantService.createMerchant(data);
    }
    /**
     * Update merchant
     * @param id Merchant ID
     * @param data Merchant data
     * @returns Updated merchant
     */
    async updateEntity(id, data) {
        return this.merchantService.updateMerchant(id, data);
    }
    /**
     * Delete merchant
     * @param id Merchant ID
     */
    async deleteEntity(id) {
        await this.merchantService.deleteMerchant(id);
    }
    /**
     * Parse filters from request
     * @param req Request
     * @returns Filters
     */
    parseFilters(req) {
        const { page, limit, sortBy, sortOrder, search, status } = req.query;
        return {
            search: search,
            status: status
        };
    }
    /**
     * Validate create input
     * @param req Request
     */
    validateCreateInput(req) {
        const { email } = req.body;
        // Validate email format
        if (email && !this.isValidEmail(email)) {
            throw new appError_1.AppError({
                message: "Invalid email format",
                type: ErrorType.VALIDATION,
                code: ErrorCode.INVALID_INPUT
            });
        }
    }
    /**
     * Validate update input
     * @param req Request
     */
    validateUpdateInput(req) {
        const { email, status } = req.body;
        // Validate email format
        if (email && !this.isValidEmail(email)) {
            throw new appError_1.AppError({
                message: "Invalid email format",
                type: ErrorType.VALIDATION,
                code: ErrorCode.INVALID_INPUT
            });
        }
        // Validate status
        if (status && !["PENDING", "ACTIVE", "SUSPENDED"].includes(status)) {
            throw new appError_1.AppError({
                message: "Invalid status",
                type: ErrorType.VALIDATION,
                code: ErrorCode.INVALID_INPUT
            });
        }
    }
    /**
     * Validate email format
     * @param email Email
     * @returns Whether email is valid
     */
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
}
exports.MerchantController = MerchantController;
exports.default = MerchantController;
//# sourceMappingURL=MerchantController.js.map