// jscpd:ignore-file
/**
 * Enhanced Verification Routes
 * 
 * Routes for enhanced verification operations.
 */

import { Router } from "express";
import { body, param } from "express-validator";
import { validate } from "../middlewares/validation.middleware";
import { enhancedAuthenticate, requirePermission } from "../middlewares/enhanced-auth.middleware";
import { auditLog } from "../middlewares/audit.middleware";
import enhancedVerificationController from "../controllers/enhanced-verification.controller";
import { body, param } from "express-validator";
import { validate } from "../middlewares/validation.middleware";
import { enhancedAuthenticate, requirePermission } from "../middlewares/enhanced-auth.middleware";
import { auditLog } from "../middlewares/audit.middleware";

const router: any =Router();

// Public routes (no authentication required)
router.post(
    "/verify",
    validate([
        body("transactionId").notEmpty(),
        body("merchantId").notEmpty(),
        body("paymentMethodId").notEmpty(),
        body("paymentMethodType").notEmpty(),
        body("amount").isNumeric(),
        body("currency").notEmpty(),
        body("verificationData").notEmpty(),
        body("verificationData.verificationMethod").notEmpty()
    ]),
    enhancedVerificationController.verifyPayment
);

// Routes requiring authentication
router.use(enhancedAuthenticate);

// Get verification methods for a payment method
router.get(
    "/methods/payment-method/:paymentMethodType",
    requirePermission("verification_methods", "view"),
    validate([
        param("paymentMethodType").notEmpty()
    ]),
    enhancedVerificationController.getVerificationMethodsForPaymentMethod
);

// Get all verification methods
router.get(
    "/methods",
    requirePermission("verification_methods", "view"),
    enhancedVerificationController.getAllVerificationMethods
);

// Get verification method by type
router.get(
    "/methods/:type",
    requirePermission("verification_methods", "view"),
    validate([
        param("type").notEmpty()
    ]),
    enhancedVerificationController.getVerificationMethodByType
);

export default router;
