/**
 * Service Error
 * 
 * This module provides a specialized error class for service-level errors.
 */
import { AppError, ErrorType, ErrorCode } from './AppError';

/**
 * Service Error
 */
export class ServiceError extends AppError {
  constructor(options: { message: string;
    code?: ErrorCode;
    statusCode?: number;
    details?: unknown;
    path?: string;
    requestId?: string;
    originalError?: Error;
  }) {
    super({
      message: options.message,
      type: ErrorType.INTERNAL,
      code: options.code ?? ErrorCode.INTERNAL_SERVER_ERROR,
      statusCode: options.statusCode ?? 500,
      details: options.details,
      path: options.path,
      requestId: options.requestId,
      originalError: options.originalError
    });
    
    this.name = 'ServiceError';
  }

  /**
   * Create a validation error
   */
  static validation(message: string, details?: unknown): ServiceError {
    return new ServiceError({
      message,
      code: ErrorCode.INVALID_INPUT,
      statusCode: 400,
      details
    });
  }

  /**
   * Create a not found error
   */
  static notFound(entity: string, id?: string | number): ServiceError {
    const message: unknown = id
      ? `${entity} with ID ${id} not found`
      : `${entity} not found`;

    return new ServiceError({
      message,
      code: ErrorCode.RESOURCE_NOT_FOUND,
      statusCode: 404,
      details: { entity, id }
    });
  }

  /**
   * Create a conflict error
   */
  static conflict(message: string, details?: unknown): ServiceError {
    return new ServiceError({
      message,
      code: ErrorCode.RESOURCE_ALREADY_EXISTS,
      statusCode: 409,
      details
    });
  }

  /**
   * Create a database error
   */
  static database(message: string, originalError?: Error): ServiceError {
    return new ServiceError({
      message: `Database, error: ${message}`,
      code: ErrorCode.DATABASE_ERROR,
      statusCode: 500,
      originalError
    });
  }

  /**
   * Create an external service error
   */
  static external(message: string, originalError?: Error): ServiceError {
    return new ServiceError({
      message: `External service error: ${message}`,
      code: ErrorCode.EXTERNAL_SERVICE_ERROR,
      statusCode: 502,
      originalError
    });
  }

  /**
   * Create a business rule error
   */
  static businessRule(message: string, details?: unknown): ServiceError {
    return new ServiceError({
      message,
      code: ErrorCode.BUSINESS_RULE_VIOLATION,
      statusCode: 400,
      details
    });
  }

  /**
   * Create an unauthorized error
   */
  static unauthorized(message: string = 'Unauthorized'): ServiceError {
    return new ServiceError({
      message,
      code: ErrorCode.INVALID_CREDENTIALS,
      statusCode: 401
    });
  }

  /**
   * Create a forbidden error
   */
  static forbidden(message: string = 'Forbidden'): ServiceError {
    return new ServiceError({
      message,
      code: ErrorCode.INSUFFICIENT_PERMISSIONS,
      statusCode: 403
    });
  }
}
