// jscpd:ignore-file
import prisma from "../lib/prisma";
import { AppError } from '../middlewares/error.middleware';
import { EventEmitter } from "events";
import { TransactionService } from "./transaction.service";
import { Merchant, PaymentMethod, PaymentPage, Transaction } from '../types';
import { EventEmitter } from "events";
import { TransactionService } from "./transaction.service";
import { Merchant, PaymentMethod, PaymentPage, Transaction } from '../types';


// Create a global event emitter for real-time payment page updates
export const paymentPageEvents: any = new EventEmitter();

// Payment page creation data
interface CreatePaymentPageData {
  title: string;
  description?: string;
  slug: string;
  amount: number;
  currency: string;
  merchantId: string;
  isActive?: boolean;
  isTemplate?: boolean;
  logoUrl?: string;
  successUrl?: string;
  cancelUrl?: string;
  allowCustomAmount?: boolean;
  collectCustomerInfo?: boolean;
  redirectAutomatically?: boolean;
  paymentMethodIds: string[];
  expiryMinutes?: number;
  metadata?: Record<string, any>;
}

// Payment page update data
interface UpdatePaymentPageData {
  title?: string;
  description?: string;
  slug?: string;
  amount?: number;
  currency?: string;
  isActive?: boolean;
  isTemplate?: boolean;
  logoUrl?: string;
  successUrl?: string;
  cancelUrl?: string;
  allowCustomAmount?: boolean;
  collectCustomerInfo?: boolean;
  redirectAutomatically?: boolean;
  paymentMethodIds?: string[];
  expiryMinutes?: number;
  metadata?: Record<string, any>;
}

// Payment page service
export const PaymentPageService: any = {
    // Create a new payment page
    async createPaymentPage(data: CreatePaymentPageData) {
        try {
            // Validate merchant
            const merchant: any = await prisma.merchant.findUnique({
                where: { id: data.merchantId }
            });

            if (!merchant) {
                throw new AppError({
            message: "Merchant not found",
            type: ErrorType.NOT_FOUND,
            code: ErrorCode.RESOURCE_NOT_FOUND
        });
            }

            // Check if slug is already in use
            const existingPage: any = await prisma.paymentPage.findFirst({
                where: { slug: data.slug,
                    merchantId: data.merchantId
                }
            });

            if (existingPage) {
                throw new AppError(`Payment page with slug '${data.slug}' already exists for this merchant`, 400);
            }

            // Validate payment methods
            for (const paymentMethodId of data.paymentMethodIds) {
                const paymentMethod: any = await prisma.paymentMethod.findUnique({
                    where: { id: paymentMethodId }
                });

                if (!paymentMethod) {
                    throw new AppError(`Payment method with ID ${paymentMethodId} not found`, 404);
                }

                if (paymentMethod.merchantId !== data.merchantId) {
                    throw new AppError(`Payment method with ID ${paymentMethodId} does not belong to this merchant`, 403);
                }

                if (!paymentMethod.isActive) {
                    throw new AppError(`Payment method with ID ${paymentMethodId} is not active`, 400);
                }
            }

            // Create payment page
            const paymentPage: any = await prisma.paymentPage.create({
                data: { name: data.title || "Payment Page",
                    title: data.title,
                    slug: data.slug,
                    amount: data.amount,
                    currency: data.currency,
                    merchantId: data.merchantId,
                    isActive: data.isActive !== undefined ? data.isActive : true,
                    successUrl: data.successUrl,
                    cancelUrl: data.cancelUrl,
                    allowCustomAmount: data.allowCustomAmount !== undefined ? data.allowCustomAmount : false,
                    collectCustomerInfo: data.collectCustomerInfo !== undefined ? data.collectCustomerInfo : true,
                    redirectAutomatically: data.redirectAutomatically !== undefined ? data.redirectAutomatically : true,
                    paymentMethodIds: data.paymentMethodIds,
                    expiryMinutes: data.expiryMinutes || 30,
                    metadata: data.metadata || {},
                    config: { description: data.description,
                        isTemplate: data.isTemplate !== undefined ? data.isTemplate : false,
                        logoUrl: data.logoUrl
                    }
                }
            });

            // Emit payment page created event
            paymentPageEvents.emit("paymentPage.created", paymentPage);

            return paymentPage;
        } catch (error) {
            if (error instanceof AppError) {
                throw error;
            }
            throw new AppError({
            message: "Failed to create payment page",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
        }
    },

    // Get payment page by ID
    async getPaymentPageById(id: string) {
        try {
            const paymentPage: any = await prisma.paymentPage.findUnique({
                where: { id },
                include: { merchant: {
                        select: { id: true,
                            name: true,
                            email: true,
                            businessName: true
                        }
                    }
                }
            });

            if (!paymentPage) {
                return null;
            }

            return paymentPage;
        } catch (error) {
            console.error("Error getting payment page by ID:", error);
            return null;
        }
    },

    // Get payment page by slug
    async getPaymentPageBySlug(slug: string, merchantId: string) {
        try {
            const paymentPage: any = await prisma.paymentPage.findFirst({
                where: {
                    slug,
                    merchantId,
                    isActive: true
                },
                include: { merchant: {
                        select: { id: true,
                            name: true,
                            email: true,
                            businessName: true
                        }
                    }
                }
            });

            if (!paymentPage) {
                return null;
            }

            return paymentPage;
        } catch (error) {
            console.error("Error getting payment page by slug:", error);
            return null;
        }
    },

    async updatePaymentPage(id: string, data: UpdatePaymentPageData) {
    // Get current payment page
        const paymentPage: any = await prisma.paymentPage.findUnique({
            where: { id }
        });

        if (!paymentPage) {
            throw new AppError({
            message: "Payment page not found",
            type: ErrorType.NOT_FOUND,
            code: ErrorCode.RESOURCE_NOT_FOUND
        });
        }

        // Check if slug is already in use (if changing slug)
        if (data.slug && data.slug !== paymentPage.slug) {
            const existingPage: any = await prisma.paymentPage.findFirst({
                where: { slug: data.slug,
                    merchantId: paymentPage.merchantId,
                    id: { not: id }
                }
            });

            if (existingPage) {
                throw new AppError(`Payment page with slug '${data.slug}' already exists for this merchant`, 400);
            }
        }

        // Validate payment methods (if changing payment methods)
        if (data.paymentMethodIds && data.paymentMethodIds.length > 0) {
            for (const paymentMethodId of data.paymentMethodIds) {
                const paymentMethod: any = await prisma.paymentMethod.findUnique({
                    where: { id: paymentMethodId }
                });

                if (!paymentMethod) {
                    throw new AppError(`Payment method with ID ${paymentMethodId} not found`, 404);
                }

                if (paymentMethod.merchantId !== paymentPage.merchantId) {
                    throw new AppError(`Payment method with ID ${paymentMethodId} does not belong to this merchant`, 403);
                }

                if (!paymentMethod.isActive) {
                    throw new AppError(`Payment method with ID ${paymentMethodId} is not active`, 400);
                }
            }
        }

        // Get current config
        const currentConfig: any = paymentPage.config as any || {};

        // Update payment page
        const updatedPaymentPage: any = await prisma.paymentPage.update({
            where: { id },
            data: { name: data.title || paymentPage.name,
                title: data.title,
                slug: data.slug,
                amount: data.amount,
                currency: data.currency,
                isActive: data.isActive,
                successUrl: data.successUrl,
                cancelUrl: data.cancelUrl,
                allowCustomAmount: data.allowCustomAmount,
                collectCustomerInfo: data.collectCustomerInfo,
                redirectAutomatically: data.redirectAutomatically,
                paymentMethodIds: data.paymentMethodIds,
                expiryMinutes: data.expiryMinutes,
                metadata: data.metadata ? { ...(paymentPage.metadata as any || {}), ...data.metadata } : paymentPage.metadata,
                config: {
                    ...currentConfig,
                    description: data.description !== undefined ? data.description : currentConfig.description,
                    isTemplate: data.isTemplate !== undefined ? data.isTemplate : currentConfig.isTemplate,
                    logoUrl: data.logoUrl !== undefined ? data.logoUrl : currentConfig.logoUrl
                }
            },
            include: { merchant: {
                    select: { id: true,
                        name: true,
                        email: true,
                        businessName: true
                    }
                }
            }
        });

        // Emit payment page updated event
        paymentPageEvents.emit("paymentPage.updated", updatedPaymentPage);

        return updatedPaymentPage;
    },

    // Delete payment page
    async deletePaymentPage(id: string) {
    // Check if payment page exists
        const paymentPage: any = await prisma.paymentPage.findUnique({
            where: { id }
        });

        if (!paymentPage) {
            throw new AppError({
            message: "Payment page not found",
            type: ErrorType.NOT_FOUND,
            code: ErrorCode.RESOURCE_NOT_FOUND
        });
        }

        // Check if there are any transactions using this payment page
        const transactionCount: any = await prisma.transaction.count({
            where: { paymentPageId: id }
        });

        if (transactionCount > 0) {
            throw new AppError(`Cannot delete payment page with ${transactionCount} associated transactions`, 400);
        }

        // Delete payment page
        await prisma.paymentPage.delete({
            where: { id }
        });

        // Emit payment page deleted event
        paymentPageEvents.emit("paymentPage.deleted", { ...paymentPage });

        return { id, message: "Payment page deleted successfully" };
    },

    // Get merchant payment pages
    async getMerchantPaymentPages(merchantId: string) {
    // Validate merchant
        const merchant: any = await prisma.merchant.findUnique({
            where: { id: merchantId }
        });

        if (!merchant) {
            throw new AppError({
            message: "Merchant not found",
            type: ErrorType.NOT_FOUND,
            code: ErrorCode.RESOURCE_NOT_FOUND
        });
        }

        // Get payment pages
        const paymentPages: any = await prisma.paymentPage.findMany({
            where: { merchantId },
            orderBy: { createdAt: "desc" }
        });

        return paymentPages;
    },

    // Create transaction from payment page
    async createTransactionFromPaymentPage(paymentPageId: string, data: {
    amount?: number;
    paymentMethodId: string;
    customerEmail?: string;
    customerName?: string;
    customerPhone?: string;
    metadata?: Record<string, any>;
  }) {
    // Get payment page
        const paymentPage: any = await prisma.paymentPage.findUnique({
            where: { id: paymentPageId },
            include: { merchant: true
            }
        });

        if (!paymentPage) {
            throw new AppError({
            message: "Payment page not found",
            type: ErrorType.NOT_FOUND,
            code: ErrorCode.RESOURCE_NOT_FOUND
        });
        }

        if (!paymentPage.isActive) {
            throw new AppError({
            message: "Payment page is not active",
            type: ErrorType.VALIDATION,
            code: ErrorCode.INVALID_INPUT
        });
        }

        // Check if payment method is allowed for this payment page
        if (!paymentPage.paymentMethodIds.includes(data.paymentMethodId)) {
            throw new AppError({
            message: "Payment method is not allowed for this payment page",
            type: ErrorType.VALIDATION,
            code: ErrorCode.INVALID_INPUT
        });
        }

        // Validate payment method
        const paymentMethod: any = await prisma.paymentMethod.findUnique({
            where: { id: data.paymentMethodId }
        });

        if (!paymentMethod) {
            throw new AppError({
            message: "Payment method not found",
            type: ErrorType.NOT_FOUND,
            code: ErrorCode.RESOURCE_NOT_FOUND
        });
        }

        if (!paymentMethod.isActive) {
            throw new AppError({
            message: "Payment method is not active",
            type: ErrorType.VALIDATION,
            code: ErrorCode.INVALID_INPUT
        });
        }

        // Validate amount
        const amount: any = data.amount || paymentPage.amount;
        if (!paymentPage.allowCustomAmount && data.amount && data.amount !== paymentPage.amount) {
            throw new AppError({
            message: "Custom amount is not allowed for this payment page",
            type: ErrorType.VALIDATION,
            code: ErrorCode.INVALID_INPUT
        });
        }

        // Calculate expiry time
        const expiresAt: Date =new Date();
        expiresAt.setMinutes(expiresAt.getMinutes() + paymentPage.expiryMinutes);

        // Create transaction
        const transaction: any = await TransactionService.createTransaction({
            amount: amount || 0,
            currency: paymentPage.currency || "USD",
            paymentMethodId: data.paymentMethodId,
            merchantId: paymentPage.merchantId,
            customerEmail: data.customerEmail,
            customerName: data.customerName,
            customerPhone: data.customerPhone,
            metadata: {
                ...data.metadata,
                paymentPageId,
                paymentPageTitle: paymentPage.title || "Payment Page"
            },
            callbackUrl: paymentPage.merchant.callbackUrl || undefined,
            successUrl: paymentPage.successUrl || undefined,
            cancelUrl: paymentPage.cancelUrl || undefined,
            paymentPageId,
            expiresAt
        });

        return transaction;
    }
};