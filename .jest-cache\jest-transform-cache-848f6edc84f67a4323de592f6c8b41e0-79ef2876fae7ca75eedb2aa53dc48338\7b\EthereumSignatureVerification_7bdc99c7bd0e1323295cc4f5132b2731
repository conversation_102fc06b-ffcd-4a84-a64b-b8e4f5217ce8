********************************
"use strict";
/**
 * Ethereum Signature Verification Method
 *
 * Handles verification of Ethereum message signatures.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.EthereumSignatureVerification = void 0;
const client_1 = require("@prisma/client");
const IdentityVerificationError_1 = require("../core/IdentityVerificationError");
const BlockchainUtils_1 = require("../utils/BlockchainUtils");
const logger_1 = require("../../../lib/logger");
/**
 * Ethereum signature verification implementation
 */
class EthereumSignatureVerification {
    constructor(prisma) {
        this.prisma = prisma;
    }
    /**
     * Get verification method name
     */
    getName() {
        return "ethereum_signature";
    }
    /**
     * Verify Ethereum signature
     */
    async verify(params) {
        try {
            // Validate parameters
            this.validateParams(params);
            const { address, message, signature, userId, merchantId } = params;
            // Normalize address
            const normalizedAddress = BlockchainUtils_1.BlockchainUtils.normalizeAddress(address);
            // Verify signature
            const isValidSignature = BlockchainUtils_1.BlockchainUtils.verifySignature(message, signature, normalizedAddress);
            if (!isValidSignature) {
                logger_1.logger.warn(`Ethereum signature verification failed for address: ${normalizedAddress}`);
                return {
                    success: false,
                    method: client_1.IdentityVerificationMethodEnum.ETHEREUM_SIGNATURE,
                    status: client_1.IdentityVerificationStatusEnum.REJECTED,
                    message: "Signature verification failed",
                    error: "Invalid signature"
                };
            }
            // Recover address from signature for additional verification
            const recoveredAddress = BlockchainUtils_1.BlockchainUtils.recoverAddress(message, signature);
            // Create verification record
            const verification = await this.createVerificationRecord({
                userId,
                merchantId,
                address: normalizedAddress,
                message,
                signature,
                recoveredAddress
            });
            logger_1.logger.info(`Ethereum signature verified successfully for address: ${normalizedAddress}`);
            return {
                success: true,
                method: client_1.IdentityVerificationMethodEnum.ETHEREUM_SIGNATURE,
                status: client_1.IdentityVerificationStatusEnum.VERIFIED,
                message: "Ethereum signature verified successfully",
                data: {
                    address: normalizedAddress,
                    message,
                    recoveredAddress
                },
                verificationId: verification.id
            };
        }
        catch (error) {
            logger_1.logger.error("Error verifying Ethereum signature:", error);
            if (error instanceof IdentityVerificationError_1.IdentityVerificationError) {
                return {
                    success: false,
                    method: client_1.IdentityVerificationMethodEnum.ETHEREUM_SIGNATURE,
                    status: client_1.IdentityVerificationStatusEnum.REJECTED,
                    message: error.message,
                    error: error.code
                };
            }
            return {
                success: false,
                method: client_1.IdentityVerificationMethodEnum.ETHEREUM_SIGNATURE,
                status: client_1.IdentityVerificationStatusEnum.REJECTED,
                message: "Error verifying Ethereum signature",
                error: error instanceof Error ? error.message : "Unknown error"
            };
        }
    }
    /**
     * Validate verification parameters
     */
    validateParams(params) {
        if (!params.address) {
            throw IdentityVerificationError_1.IdentityVerificationError.invalidParameters("Address is required");
        }
        if (!params.message) {
            throw IdentityVerificationError_1.IdentityVerificationError.invalidParameters("Message is required");
        }
        if (!params.signature) {
            throw IdentityVerificationError_1.IdentityVerificationError.invalidParameters("Signature is required");
        }
        if (!BlockchainUtils_1.BlockchainUtils.isValidAddress(params.address)) {
            throw IdentityVerificationError_1.IdentityVerificationError.invalidAddress("Invalid Ethereum address format");
        }
        // Validate signature format (should start with 0x and be 132 characters long)
        if (!params.signature.startsWith('0x') || params.signature.length !== 132) {
            throw IdentityVerificationError_1.IdentityVerificationError.invalidSignature("Invalid signature format");
        }
    }
    /**
     * Create verification record in database
     */
    async createVerificationRecord(data) {
        try {
            return await this.prisma.identityVerification.create({
                data: {
                    userId: data.userId,
                    merchantId: data.merchantId,
                    method: client_1.IdentityVerificationMethodEnum.ETHEREUM_SIGNATURE,
                    status: client_1.IdentityVerificationStatusEnum.VERIFIED,
                    address: data.address,
                    verificationData: {
                        message: data.message,
                        signature: data.signature,
                        recoveredAddress: data.recoveredAddress,
                        verifiedAt: new Date().toISOString()
                    },
                    verifiedAt: new Date()
                }
            });
        }
        catch (error) {
            logger_1.logger.error("Error creating verification record:", error);
            throw IdentityVerificationError_1.IdentityVerificationError.internalError("Failed to create verification record");
        }
    }
    /**
     * Generate verification message
     */
    static generateVerificationMessage(address, nonce) {
        const timestamp = new Date().toISOString();
        const nonceValue = nonce || Math.random().toString(36).substring(2, 15);
        return `Please sign this message to verify your identity:

Address: ${address}
Timestamp: ${timestamp}
Nonce: ${nonceValue}

This signature will be used for identity verification purposes only.`;
    }
    /**
     * Validate message format
     */
    static isValidMessage(message) {
        return message.includes("Please sign this message to verify your identity") &&
            message.includes("Address:") &&
            message.includes("Timestamp:") &&
            message.includes("Nonce:");
    }
}
exports.EthereumSignatureVerification = EthereumSignatureVerification;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJmaWxlIjoiRjpcXEFtYXppbmcgcGF5IGZsb3dcXHNyY1xcc2VydmljZXNcXGlkZW50aXR5LXZlcmlmaWNhdGlvblxcbWV0aG9kc1xcRXRoZXJldW1TaWduYXR1cmVWZXJpZmljYXRpb24udHMiLCJtYXBwaW5ncyI6IjtBQUFBOzs7O0dBSUc7OztBQUVILDJDQUE4RztBQUU5RyxpRkFBOEU7QUFDOUUsOERBQTJEO0FBQzNELGdEQUE2QztBQUU3Qzs7R0FFRztBQUNILE1BQWEsNkJBQTZCO0lBR3RDLFlBQVksTUFBb0I7UUFDNUIsSUFBSSxDQUFDLE1BQU0sR0FBRyxNQUFNLENBQUM7SUFDekIsQ0FBQztJQUVEOztPQUVHO0lBQ0gsT0FBTztRQUNILE9BQU8sb0JBQW9CLENBQUM7SUFDaEMsQ0FBQztJQUVEOztPQUVHO0lBQ0gsS0FBSyxDQUFDLE1BQU0sQ0FBQyxNQUErQjtRQUN4QyxJQUFJLENBQUM7WUFDRCxzQkFBc0I7WUFDdEIsSUFBSSxDQUFDLGNBQWMsQ0FBQyxNQUFNLENBQUMsQ0FBQztZQUU1QixNQUFNLEVBQUUsT0FBTyxFQUFFLE9BQU8sRUFBRSxTQUFTLEVBQUUsTUFBTSxFQUFFLFVBQVUsRUFBRSxHQUFHLE1BQU0sQ0FBQztZQUVuRSxvQkFBb0I7WUFDcEIsTUFBTSxpQkFBaUIsR0FBRyxpQ0FBZSxDQUFDLGdCQUFnQixDQUFDLE9BQU8sQ0FBQyxDQUFDO1lBRXBFLG1CQUFtQjtZQUNuQixNQUFNLGdCQUFnQixHQUFHLGlDQUFlLENBQUMsZUFBZSxDQUFDLE9BQU8sRUFBRSxTQUFTLEVBQUUsaUJBQWlCLENBQUMsQ0FBQztZQUVoRyxJQUFJLENBQUMsZ0JBQWdCLEVBQUUsQ0FBQztnQkFDcEIsZUFBTSxDQUFDLElBQUksQ0FBQyx1REFBdUQsaUJBQWlCLEVBQUUsQ0FBQyxDQUFDO2dCQUV4RixPQUFPO29CQUNILE9BQU8sRUFBRSxLQUFLO29CQUNkLE1BQU0sRUFBRSx1Q0FBOEIsQ0FBQyxrQkFBa0I7b0JBQ3pELE1BQU0sRUFBRSx1Q0FBOEIsQ0FBQyxRQUFRO29CQUMvQyxPQUFPLEVBQUUsK0JBQStCO29CQUN4QyxLQUFLLEVBQUUsbUJBQW1CO2lCQUM3QixDQUFDO1lBQ04sQ0FBQztZQUVELDZEQUE2RDtZQUM3RCxNQUFNLGdCQUFnQixHQUFHLGlDQUFlLENBQUMsY0FBYyxDQUFDLE9BQU8sRUFBRSxTQUFTLENBQUMsQ0FBQztZQUU1RSw2QkFBNkI7WUFDN0IsTUFBTSxZQUFZLEdBQUcsTUFBTSxJQUFJLENBQUMsd0JBQXdCLENBQUM7Z0JBQ3JELE1BQU07Z0JBQ04sVUFBVTtnQkFDVixPQUFPLEVBQUUsaUJBQWlCO2dCQUMxQixPQUFPO2dCQUNQLFNBQVM7Z0JBQ1QsZ0JBQWdCO2FBQ25CLENBQUMsQ0FBQztZQUVILGVBQU0sQ0FBQyxJQUFJLENBQUMseURBQXlELGlCQUFpQixFQUFFLENBQUMsQ0FBQztZQUUxRixPQUFPO2dCQUNILE9BQU8sRUFBRSxJQUFJO2dCQUNiLE1BQU0sRUFBRSx1Q0FBOEIsQ0FBQyxrQkFBa0I7Z0JBQ3pELE1BQU0sRUFBRSx1Q0FBOEIsQ0FBQyxRQUFRO2dCQUMvQyxPQUFPLEVBQUUsMENBQTBDO2dCQUNuRCxJQUFJLEVBQUU7b0JBQ0YsT0FBTyxFQUFFLGlCQUFpQjtvQkFDMUIsT0FBTztvQkFDUCxnQkFBZ0I7aUJBQ25CO2dCQUNELGNBQWMsRUFBRSxZQUFZLENBQUMsRUFBRTthQUNsQyxDQUFDO1FBRU4sQ0FBQztRQUFDLE9BQU8sS0FBSyxFQUFFLENBQUM7WUFDYixlQUFNLENBQUMsS0FBSyxDQUFDLHFDQUFxQyxFQUFFLEtBQUssQ0FBQyxDQUFDO1lBRTNELElBQUksS0FBSyxZQUFZLHFEQUF5QixFQUFFLENBQUM7Z0JBQzdDLE9BQU87b0JBQ0gsT0FBTyxFQUFFLEtBQUs7b0JBQ2QsTUFBTSxFQUFFLHVDQUE4QixDQUFDLGtCQUFrQjtvQkFDekQsTUFBTSxFQUFFLHVDQUE4QixDQUFDLFFBQVE7b0JBQy9DLE9BQU8sRUFBRSxLQUFLLENBQUMsT0FBTztvQkFDdEIsS0FBSyxFQUFFLEtBQUssQ0FBQyxJQUFJO2lCQUNwQixDQUFDO1lBQ04sQ0FBQztZQUVELE9BQU87Z0JBQ0gsT0FBTyxFQUFFLEtBQUs7Z0JBQ2QsTUFBTSxFQUFFLHVDQUE4QixDQUFDLGtCQUFrQjtnQkFDekQsTUFBTSxFQUFFLHVDQUE4QixDQUFDLFFBQVE7Z0JBQy9DLE9BQU8sRUFBRSxvQ0FBb0M7Z0JBQzdDLEtBQUssRUFBRSxLQUFLLFlBQVksS0FBSyxDQUFDLENBQUMsQ0FBQyxLQUFLLENBQUMsT0FBTyxDQUFDLENBQUMsQ0FBQyxlQUFlO2FBQ2xFLENBQUM7UUFDTixDQUFDO0lBQ0wsQ0FBQztJQUVEOztPQUVHO0lBQ0ssY0FBYyxDQUFDLE1BQStCO1FBQ2xELElBQUksQ0FBQyxNQUFNLENBQUMsT0FBTyxFQUFFLENBQUM7WUFDbEIsTUFBTSxxREFBeUIsQ0FBQyxpQkFBaUIsQ0FBQyxxQkFBcUIsQ0FBQyxDQUFDO1FBQzdFLENBQUM7UUFFRCxJQUFJLENBQUMsTUFBTSxDQUFDLE9BQU8sRUFBRSxDQUFDO1lBQ2xCLE1BQU0scURBQXlCLENBQUMsaUJBQWlCLENBQUMscUJBQXFCLENBQUMsQ0FBQztRQUM3RSxDQUFDO1FBRUQsSUFBSSxDQUFDLE1BQU0sQ0FBQyxTQUFTLEVBQUUsQ0FBQztZQUNwQixNQUFNLHFEQUF5QixDQUFDLGlCQUFpQixDQUFDLHVCQUF1QixDQUFDLENBQUM7UUFDL0UsQ0FBQztRQUVELElBQUksQ0FBQyxpQ0FBZSxDQUFDLGNBQWMsQ0FBQyxNQUFNLENBQUMsT0FBTyxDQUFDLEVBQUUsQ0FBQztZQUNsRCxNQUFNLHFEQUF5QixDQUFDLGNBQWMsQ0FBQyxpQ0FBaUMsQ0FBQyxDQUFDO1FBQ3RGLENBQUM7UUFFRCw4RUFBOEU7UUFDOUUsSUFBSSxDQUFDLE1BQU0sQ0FBQyxTQUFTLENBQUMsVUFBVSxDQUFDLElBQUksQ0FBQyxJQUFJLE1BQU0sQ0FBQyxTQUFTLENBQUMsTUFBTSxLQUFLLEdBQUcsRUFBRSxDQUFDO1lBQ3hFLE1BQU0scURBQXlCLENBQUMsZ0JBQWdCLENBQUMsMEJBQTBCLENBQUMsQ0FBQztRQUNqRixDQUFDO0lBQ0wsQ0FBQztJQUVEOztPQUVHO0lBQ0ssS0FBSyxDQUFDLHdCQUF3QixDQUFDLElBT3RDO1FBQ0csSUFBSSxDQUFDO1lBQ0QsT0FBTyxNQUFNLElBQUksQ0FBQyxNQUFNLENBQUMsb0JBQW9CLENBQUMsTUFBTSxDQUFDO2dCQUNqRCxJQUFJLEVBQUU7b0JBQ0YsTUFBTSxFQUFFLElBQUksQ0FBQyxNQUFNO29CQUNuQixVQUFVLEVBQUUsSUFBSSxDQUFDLFVBQVU7b0JBQzNCLE1BQU0sRUFBRSx1Q0FBOEIsQ0FBQyxrQkFBa0I7b0JBQ3pELE1BQU0sRUFBRSx1Q0FBOEIsQ0FBQyxRQUFRO29CQUMvQyxPQUFPLEVBQUUsSUFBSSxDQUFDLE9BQU87b0JBQ3JCLGdCQUFnQixFQUFFO3dCQUNkLE9BQU8sRUFBRSxJQUFJLENBQUMsT0FBTzt3QkFDckIsU0FBUyxFQUFFLElBQUksQ0FBQyxTQUFTO3dCQUN6QixnQkFBZ0IsRUFBRSxJQUFJLENBQUMsZ0JBQWdCO3dCQUN2QyxVQUFVLEVBQUUsSUFBSSxJQUFJLEVBQUUsQ0FBQyxXQUFXLEVBQUU7cUJBQ3ZDO29CQUNELFVBQVUsRUFBRSxJQUFJLElBQUksRUFBRTtpQkFDekI7YUFDSixDQUFDLENBQUM7UUFDUCxDQUFDO1FBQUMsT0FBTyxLQUFLLEVBQUUsQ0FBQztZQUNiLGVBQU0sQ0FBQyxLQUFLLENBQUMscUNBQXFDLEVBQUUsS0FBSyxDQUFDLENBQUM7WUFDM0QsTUFBTSxxREFBeUIsQ0FBQyxhQUFhLENBQUMsc0NBQXNDLENBQUMsQ0FBQztRQUMxRixDQUFDO0lBQ0wsQ0FBQztJQUVEOztPQUVHO0lBQ0gsTUFBTSxDQUFDLDJCQUEyQixDQUFDLE9BQWUsRUFBRSxLQUFjO1FBQzlELE1BQU0sU0FBUyxHQUFHLElBQUksSUFBSSxFQUFFLENBQUMsV0FBVyxFQUFFLENBQUM7UUFDM0MsTUFBTSxVQUFVLEdBQUcsS0FBSyxJQUFJLElBQUksQ0FBQyxNQUFNLEVBQUUsQ0FBQyxRQUFRLENBQUMsRUFBRSxDQUFDLENBQUMsU0FBUyxDQUFDLENBQUMsRUFBRSxFQUFFLENBQUMsQ0FBQztRQUV4RSxPQUFPOztXQUVKLE9BQU87YUFDTCxTQUFTO1NBQ2IsVUFBVTs7cUVBRWtELENBQUM7SUFDbEUsQ0FBQztJQUVEOztPQUVHO0lBQ0gsTUFBTSxDQUFDLGNBQWMsQ0FBQyxPQUFlO1FBQ2pDLE9BQU8sT0FBTyxDQUFDLFFBQVEsQ0FBQyxrREFBa0QsQ0FBQztZQUNwRSxPQUFPLENBQUMsUUFBUSxDQUFDLFVBQVUsQ0FBQztZQUM1QixPQUFPLENBQUMsUUFBUSxDQUFDLFlBQVksQ0FBQztZQUM5QixPQUFPLENBQUMsUUFBUSxDQUFDLFFBQVEsQ0FBQyxDQUFDO0lBQ3RDLENBQUM7Q0FDSjtBQWxMRCxzRUFrTEMiLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiRjpcXEFtYXppbmcgcGF5IGZsb3dcXHNyY1xcc2VydmljZXNcXGlkZW50aXR5LXZlcmlmaWNhdGlvblxcbWV0aG9kc1xcRXRoZXJldW1TaWduYXR1cmVWZXJpZmljYXRpb24udHMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBFdGhlcmV1bSBTaWduYXR1cmUgVmVyaWZpY2F0aW9uIE1ldGhvZFxuICogXG4gKiBIYW5kbGVzIHZlcmlmaWNhdGlvbiBvZiBFdGhlcmV1bSBtZXNzYWdlIHNpZ25hdHVyZXMuXG4gKi9cblxuaW1wb3J0IHsgUHJpc21hQ2xpZW50LCBJZGVudGl0eVZlcmlmaWNhdGlvbk1ldGhvZEVudW0sIElkZW50aXR5VmVyaWZpY2F0aW9uU3RhdHVzRW51bSB9IGZyb20gXCJAcHJpc21hL2NsaWVudFwiO1xuaW1wb3J0IHsgSWRlbnRpdHlWZXJpZmljYXRpb25SZXN1bHQsIEV0aGVyZXVtU2lnbmF0dXJlUGFyYW1zLCBJVmVyaWZpY2F0aW9uTWV0aG9kIH0gZnJvbSBcIi4uL2NvcmUvSWRlbnRpdHlWZXJpZmljYXRpb25UeXBlc1wiO1xuaW1wb3J0IHsgSWRlbnRpdHlWZXJpZmljYXRpb25FcnJvciB9IGZyb20gXCIuLi9jb3JlL0lkZW50aXR5VmVyaWZpY2F0aW9uRXJyb3JcIjtcbmltcG9ydCB7IEJsb2NrY2hhaW5VdGlscyB9IGZyb20gXCIuLi91dGlscy9CbG9ja2NoYWluVXRpbHNcIjtcbmltcG9ydCB7IGxvZ2dlciB9IGZyb20gXCIuLi8uLi8uLi9saWIvbG9nZ2VyXCI7XG5cbi8qKlxuICogRXRoZXJldW0gc2lnbmF0dXJlIHZlcmlmaWNhdGlvbiBpbXBsZW1lbnRhdGlvblxuICovXG5leHBvcnQgY2xhc3MgRXRoZXJldW1TaWduYXR1cmVWZXJpZmljYXRpb24gaW1wbGVtZW50cyBJVmVyaWZpY2F0aW9uTWV0aG9kIHtcbiAgICBwcml2YXRlIHByaXNtYTogUHJpc21hQ2xpZW50O1xuXG4gICAgY29uc3RydWN0b3IocHJpc21hOiBQcmlzbWFDbGllbnQpIHtcbiAgICAgICAgdGhpcy5wcmlzbWEgPSBwcmlzbWE7XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICogR2V0IHZlcmlmaWNhdGlvbiBtZXRob2QgbmFtZVxuICAgICAqL1xuICAgIGdldE5hbWUoKTogc3RyaW5nIHtcbiAgICAgICAgcmV0dXJuIFwiZXRoZXJldW1fc2lnbmF0dXJlXCI7XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICogVmVyaWZ5IEV0aGVyZXVtIHNpZ25hdHVyZVxuICAgICAqL1xuICAgIGFzeW5jIHZlcmlmeShwYXJhbXM6IEV0aGVyZXVtU2lnbmF0dXJlUGFyYW1zKTogUHJvbWlzZTxJZGVudGl0eVZlcmlmaWNhdGlvblJlc3VsdD4ge1xuICAgICAgICB0cnkge1xuICAgICAgICAgICAgLy8gVmFsaWRhdGUgcGFyYW1ldGVyc1xuICAgICAgICAgICAgdGhpcy52YWxpZGF0ZVBhcmFtcyhwYXJhbXMpO1xuXG4gICAgICAgICAgICBjb25zdCB7IGFkZHJlc3MsIG1lc3NhZ2UsIHNpZ25hdHVyZSwgdXNlcklkLCBtZXJjaGFudElkIH0gPSBwYXJhbXM7XG5cbiAgICAgICAgICAgIC8vIE5vcm1hbGl6ZSBhZGRyZXNzXG4gICAgICAgICAgICBjb25zdCBub3JtYWxpemVkQWRkcmVzcyA9IEJsb2NrY2hhaW5VdGlscy5ub3JtYWxpemVBZGRyZXNzKGFkZHJlc3MpO1xuXG4gICAgICAgICAgICAvLyBWZXJpZnkgc2lnbmF0dXJlXG4gICAgICAgICAgICBjb25zdCBpc1ZhbGlkU2lnbmF0dXJlID0gQmxvY2tjaGFpblV0aWxzLnZlcmlmeVNpZ25hdHVyZShtZXNzYWdlLCBzaWduYXR1cmUsIG5vcm1hbGl6ZWRBZGRyZXNzKTtcblxuICAgICAgICAgICAgaWYgKCFpc1ZhbGlkU2lnbmF0dXJlKSB7XG4gICAgICAgICAgICAgICAgbG9nZ2VyLndhcm4oYEV0aGVyZXVtIHNpZ25hdHVyZSB2ZXJpZmljYXRpb24gZmFpbGVkIGZvciBhZGRyZXNzOiAke25vcm1hbGl6ZWRBZGRyZXNzfWApO1xuICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICAgICAgICAgICAgICBtZXRob2Q6IElkZW50aXR5VmVyaWZpY2F0aW9uTWV0aG9kRW51bS5FVEhFUkVVTV9TSUdOQVRVUkUsXG4gICAgICAgICAgICAgICAgICAgIHN0YXR1czogSWRlbnRpdHlWZXJpZmljYXRpb25TdGF0dXNFbnVtLlJFSkVDVEVELFxuICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiBcIlNpZ25hdHVyZSB2ZXJpZmljYXRpb24gZmFpbGVkXCIsXG4gICAgICAgICAgICAgICAgICAgIGVycm9yOiBcIkludmFsaWQgc2lnbmF0dXJlXCJcbiAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAvLyBSZWNvdmVyIGFkZHJlc3MgZnJvbSBzaWduYXR1cmUgZm9yIGFkZGl0aW9uYWwgdmVyaWZpY2F0aW9uXG4gICAgICAgICAgICBjb25zdCByZWNvdmVyZWRBZGRyZXNzID0gQmxvY2tjaGFpblV0aWxzLnJlY292ZXJBZGRyZXNzKG1lc3NhZ2UsIHNpZ25hdHVyZSk7XG5cbiAgICAgICAgICAgIC8vIENyZWF0ZSB2ZXJpZmljYXRpb24gcmVjb3JkXG4gICAgICAgICAgICBjb25zdCB2ZXJpZmljYXRpb24gPSBhd2FpdCB0aGlzLmNyZWF0ZVZlcmlmaWNhdGlvblJlY29yZCh7XG4gICAgICAgICAgICAgICAgdXNlcklkLFxuICAgICAgICAgICAgICAgIG1lcmNoYW50SWQsXG4gICAgICAgICAgICAgICAgYWRkcmVzczogbm9ybWFsaXplZEFkZHJlc3MsXG4gICAgICAgICAgICAgICAgbWVzc2FnZSxcbiAgICAgICAgICAgICAgICBzaWduYXR1cmUsXG4gICAgICAgICAgICAgICAgcmVjb3ZlcmVkQWRkcmVzc1xuICAgICAgICAgICAgfSk7XG5cbiAgICAgICAgICAgIGxvZ2dlci5pbmZvKGBFdGhlcmV1bSBzaWduYXR1cmUgdmVyaWZpZWQgc3VjY2Vzc2Z1bGx5IGZvciBhZGRyZXNzOiAke25vcm1hbGl6ZWRBZGRyZXNzfWApO1xuXG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICAgICAgICAgICAgbWV0aG9kOiBJZGVudGl0eVZlcmlmaWNhdGlvbk1ldGhvZEVudW0uRVRIRVJFVU1fU0lHTkFUVVJFLFxuICAgICAgICAgICAgICAgIHN0YXR1czogSWRlbnRpdHlWZXJpZmljYXRpb25TdGF0dXNFbnVtLlZFUklGSUVELFxuICAgICAgICAgICAgICAgIG1lc3NhZ2U6IFwiRXRoZXJldW0gc2lnbmF0dXJlIHZlcmlmaWVkIHN1Y2Nlc3NmdWxseVwiLFxuICAgICAgICAgICAgICAgIGRhdGE6IHtcbiAgICAgICAgICAgICAgICAgICAgYWRkcmVzczogbm9ybWFsaXplZEFkZHJlc3MsXG4gICAgICAgICAgICAgICAgICAgIG1lc3NhZ2UsXG4gICAgICAgICAgICAgICAgICAgIHJlY292ZXJlZEFkZHJlc3NcbiAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgIHZlcmlmaWNhdGlvbklkOiB2ZXJpZmljYXRpb24uaWRcbiAgICAgICAgICAgIH07XG5cbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgIGxvZ2dlci5lcnJvcihcIkVycm9yIHZlcmlmeWluZyBFdGhlcmV1bSBzaWduYXR1cmU6XCIsIGVycm9yKTtcblxuICAgICAgICAgICAgaWYgKGVycm9yIGluc3RhbmNlb2YgSWRlbnRpdHlWZXJpZmljYXRpb25FcnJvcikge1xuICAgICAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICAgICAgICAgICAgICBtZXRob2Q6IElkZW50aXR5VmVyaWZpY2F0aW9uTWV0aG9kRW51bS5FVEhFUkVVTV9TSUdOQVRVUkUsXG4gICAgICAgICAgICAgICAgICAgIHN0YXR1czogSWRlbnRpdHlWZXJpZmljYXRpb25TdGF0dXNFbnVtLlJFSkVDVEVELFxuICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiBlcnJvci5tZXNzYWdlLFxuICAgICAgICAgICAgICAgICAgICBlcnJvcjogZXJyb3IuY29kZVxuICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgICAgICAgICAgbWV0aG9kOiBJZGVudGl0eVZlcmlmaWNhdGlvbk1ldGhvZEVudW0uRVRIRVJFVU1fU0lHTkFUVVJFLFxuICAgICAgICAgICAgICAgIHN0YXR1czogSWRlbnRpdHlWZXJpZmljYXRpb25TdGF0dXNFbnVtLlJFSkVDVEVELFxuICAgICAgICAgICAgICAgIG1lc3NhZ2U6IFwiRXJyb3IgdmVyaWZ5aW5nIEV0aGVyZXVtIHNpZ25hdHVyZVwiLFxuICAgICAgICAgICAgICAgIGVycm9yOiBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6IFwiVW5rbm93biBlcnJvclwiXG4gICAgICAgICAgICB9O1xuICAgICAgICB9XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICogVmFsaWRhdGUgdmVyaWZpY2F0aW9uIHBhcmFtZXRlcnNcbiAgICAgKi9cbiAgICBwcml2YXRlIHZhbGlkYXRlUGFyYW1zKHBhcmFtczogRXRoZXJldW1TaWduYXR1cmVQYXJhbXMpOiB2b2lkIHtcbiAgICAgICAgaWYgKCFwYXJhbXMuYWRkcmVzcykge1xuICAgICAgICAgICAgdGhyb3cgSWRlbnRpdHlWZXJpZmljYXRpb25FcnJvci5pbnZhbGlkUGFyYW1ldGVycyhcIkFkZHJlc3MgaXMgcmVxdWlyZWRcIik7XG4gICAgICAgIH1cblxuICAgICAgICBpZiAoIXBhcmFtcy5tZXNzYWdlKSB7XG4gICAgICAgICAgICB0aHJvdyBJZGVudGl0eVZlcmlmaWNhdGlvbkVycm9yLmludmFsaWRQYXJhbWV0ZXJzKFwiTWVzc2FnZSBpcyByZXF1aXJlZFwiKTtcbiAgICAgICAgfVxuXG4gICAgICAgIGlmICghcGFyYW1zLnNpZ25hdHVyZSkge1xuICAgICAgICAgICAgdGhyb3cgSWRlbnRpdHlWZXJpZmljYXRpb25FcnJvci5pbnZhbGlkUGFyYW1ldGVycyhcIlNpZ25hdHVyZSBpcyByZXF1aXJlZFwiKTtcbiAgICAgICAgfVxuXG4gICAgICAgIGlmICghQmxvY2tjaGFpblV0aWxzLmlzVmFsaWRBZGRyZXNzKHBhcmFtcy5hZGRyZXNzKSkge1xuICAgICAgICAgICAgdGhyb3cgSWRlbnRpdHlWZXJpZmljYXRpb25FcnJvci5pbnZhbGlkQWRkcmVzcyhcIkludmFsaWQgRXRoZXJldW0gYWRkcmVzcyBmb3JtYXRcIik7XG4gICAgICAgIH1cblxuICAgICAgICAvLyBWYWxpZGF0ZSBzaWduYXR1cmUgZm9ybWF0IChzaG91bGQgc3RhcnQgd2l0aCAweCBhbmQgYmUgMTMyIGNoYXJhY3RlcnMgbG9uZylcbiAgICAgICAgaWYgKCFwYXJhbXMuc2lnbmF0dXJlLnN0YXJ0c1dpdGgoJzB4JykgfHwgcGFyYW1zLnNpZ25hdHVyZS5sZW5ndGggIT09IDEzMikge1xuICAgICAgICAgICAgdGhyb3cgSWRlbnRpdHlWZXJpZmljYXRpb25FcnJvci5pbnZhbGlkU2lnbmF0dXJlKFwiSW52YWxpZCBzaWduYXR1cmUgZm9ybWF0XCIpO1xuICAgICAgICB9XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICogQ3JlYXRlIHZlcmlmaWNhdGlvbiByZWNvcmQgaW4gZGF0YWJhc2VcbiAgICAgKi9cbiAgICBwcml2YXRlIGFzeW5jIGNyZWF0ZVZlcmlmaWNhdGlvblJlY29yZChkYXRhOiB7XG4gICAgICAgIHVzZXJJZD86IHN0cmluZztcbiAgICAgICAgbWVyY2hhbnRJZD86IHN0cmluZztcbiAgICAgICAgYWRkcmVzczogc3RyaW5nO1xuICAgICAgICBtZXNzYWdlOiBzdHJpbmc7XG4gICAgICAgIHNpZ25hdHVyZTogc3RyaW5nO1xuICAgICAgICByZWNvdmVyZWRBZGRyZXNzOiBzdHJpbmc7XG4gICAgfSkge1xuICAgICAgICB0cnkge1xuICAgICAgICAgICAgcmV0dXJuIGF3YWl0IHRoaXMucHJpc21hLmlkZW50aXR5VmVyaWZpY2F0aW9uLmNyZWF0ZSh7XG4gICAgICAgICAgICAgICAgZGF0YToge1xuICAgICAgICAgICAgICAgICAgICB1c2VySWQ6IGRhdGEudXNlcklkLFxuICAgICAgICAgICAgICAgICAgICBtZXJjaGFudElkOiBkYXRhLm1lcmNoYW50SWQsXG4gICAgICAgICAgICAgICAgICAgIG1ldGhvZDogSWRlbnRpdHlWZXJpZmljYXRpb25NZXRob2RFbnVtLkVUSEVSRVVNX1NJR05BVFVSRSxcbiAgICAgICAgICAgICAgICAgICAgc3RhdHVzOiBJZGVudGl0eVZlcmlmaWNhdGlvblN0YXR1c0VudW0uVkVSSUZJRUQsXG4gICAgICAgICAgICAgICAgICAgIGFkZHJlc3M6IGRhdGEuYWRkcmVzcyxcbiAgICAgICAgICAgICAgICAgICAgdmVyaWZpY2F0aW9uRGF0YToge1xuICAgICAgICAgICAgICAgICAgICAgICAgbWVzc2FnZTogZGF0YS5tZXNzYWdlLFxuICAgICAgICAgICAgICAgICAgICAgICAgc2lnbmF0dXJlOiBkYXRhLnNpZ25hdHVyZSxcbiAgICAgICAgICAgICAgICAgICAgICAgIHJlY292ZXJlZEFkZHJlc3M6IGRhdGEucmVjb3ZlcmVkQWRkcmVzcyxcbiAgICAgICAgICAgICAgICAgICAgICAgIHZlcmlmaWVkQXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgICAgICB2ZXJpZmllZEF0OiBuZXcgRGF0ZSgpXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSk7XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICBsb2dnZXIuZXJyb3IoXCJFcnJvciBjcmVhdGluZyB2ZXJpZmljYXRpb24gcmVjb3JkOlwiLCBlcnJvcik7XG4gICAgICAgICAgICB0aHJvdyBJZGVudGl0eVZlcmlmaWNhdGlvbkVycm9yLmludGVybmFsRXJyb3IoXCJGYWlsZWQgdG8gY3JlYXRlIHZlcmlmaWNhdGlvbiByZWNvcmRcIik7XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiBHZW5lcmF0ZSB2ZXJpZmljYXRpb24gbWVzc2FnZVxuICAgICAqL1xuICAgIHN0YXRpYyBnZW5lcmF0ZVZlcmlmaWNhdGlvbk1lc3NhZ2UoYWRkcmVzczogc3RyaW5nLCBub25jZT86IHN0cmluZyk6IHN0cmluZyB7XG4gICAgICAgIGNvbnN0IHRpbWVzdGFtcCA9IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKTtcbiAgICAgICAgY29uc3Qgbm9uY2VWYWx1ZSA9IG5vbmNlIHx8IE1hdGgucmFuZG9tKCkudG9TdHJpbmcoMzYpLnN1YnN0cmluZygyLCAxNSk7XG4gICAgICAgIFxuICAgICAgICByZXR1cm4gYFBsZWFzZSBzaWduIHRoaXMgbWVzc2FnZSB0byB2ZXJpZnkgeW91ciBpZGVudGl0eTpcblxuQWRkcmVzczogJHthZGRyZXNzfVxuVGltZXN0YW1wOiAke3RpbWVzdGFtcH1cbk5vbmNlOiAke25vbmNlVmFsdWV9XG5cblRoaXMgc2lnbmF0dXJlIHdpbGwgYmUgdXNlZCBmb3IgaWRlbnRpdHkgdmVyaWZpY2F0aW9uIHB1cnBvc2VzIG9ubHkuYDtcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiBWYWxpZGF0ZSBtZXNzYWdlIGZvcm1hdFxuICAgICAqL1xuICAgIHN0YXRpYyBpc1ZhbGlkTWVzc2FnZShtZXNzYWdlOiBzdHJpbmcpOiBib29sZWFuIHtcbiAgICAgICAgcmV0dXJuIG1lc3NhZ2UuaW5jbHVkZXMoXCJQbGVhc2Ugc2lnbiB0aGlzIG1lc3NhZ2UgdG8gdmVyaWZ5IHlvdXIgaWRlbnRpdHlcIikgJiZcbiAgICAgICAgICAgICAgIG1lc3NhZ2UuaW5jbHVkZXMoXCJBZGRyZXNzOlwiKSAmJlxuICAgICAgICAgICAgICAgbWVzc2FnZS5pbmNsdWRlcyhcIlRpbWVzdGFtcDpcIikgJiZcbiAgICAgICAgICAgICAgIG1lc3NhZ2UuaW5jbHVkZXMoXCJOb25jZTpcIik7XG4gICAgfVxufVxuIl0sInZlcnNpb24iOjN9