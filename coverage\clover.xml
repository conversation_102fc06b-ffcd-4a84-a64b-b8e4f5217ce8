<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1748138620703" clover="3.2.0">
  <project timestamp="1748138620703" name="All files">
    <metrics statements="40" coveredstatements="0" conditionals="16" coveredconditionals="0" methods="2" coveredmethods="0" elements="58" coveredelements="0" complexity="0" loc="40" ncloc="40" packages="3" files="5" classes="5"/>
    <package name="config">
      <metrics statements="1" coveredstatements="0" conditionals="16" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="env.config.ts" path="F:\Amazing pay flow\src\config\env.config.ts">
        <metrics statements="1" coveredstatements="0" conditionals="16" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
      </file>
    </package>
    <package name="routes">
      <metrics statements="39" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="advanced-report.routes.ts" path="F:\Amazing pay flow\src\routes\advanced-report.routes.ts">
        <metrics statements="19" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
      </file>
      <file name="dashboard.routes.ts" path="F:\Amazing pay flow\src\routes\dashboard.routes.ts">
        <metrics statements="17" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
      </file>
      <file name="fee-management-test.routes.ts" path="F:\Amazing pay flow\src\routes\fee-management-test.routes.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
      </file>
    </package>
    <package name="utils">
      <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="appError.ts" path="F:\Amazing pay flow\src\utils\appError.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
  </project>
</coverage>
