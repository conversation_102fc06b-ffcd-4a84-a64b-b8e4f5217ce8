/**
 * Role template interface
 */
export interface RoleTemplate {
    name: string;
    type: string;
    description: string;
    permissions: string[];
    isSystem?: boolean;
}
/**
 * Super Admin role template
 */
export declare const SUPER_ADMIN_TEMPLATE: RoleTemplate;
/**
 * Admin role template
 */
export declare const ADMIN_TEMPLATE: RoleTemplate;
/**
 * Financial Admin role template
 */
export declare const FINANCIAL_ADMIN_TEMPLATE: RoleTemplate;
/**
 * Merchant Admin role template
 */
export declare const MERCHANT_ADMIN_TEMPLATE: RoleTemplate;
/**
 * Security Admin role template
 */
export declare const SECURITY_ADMIN_TEMPLATE: RoleTemplate;
/**
 * Support Admin role template
 */
export declare const SUPPORT_ADMIN_TEMPLATE: RoleTemplate;
/**
 * Compliance Officer role template
 */
export declare const COMPLIANCE_OFFICER_TEMPLATE: RoleTemplate;
/**
 * Analytics Manager role template
 */
export declare const ANALYTICS_MANAGER_TEMPLATE: RoleTemplate;
/**
 * System Administrator role template
 */
export declare const SYSTEM_ADMINISTRATOR_TEMPLATE: RoleTemplate;
/**
 * Auditor role template
 */
export declare const AUDITOR_TEMPLATE: RoleTemplate;
/**
 * All role templates
 */
export declare const ROLE_TEMPLATES: Record<string, RoleTemplate>;
//# sourceMappingURL=RoleTemplates.d.ts.map