// jscpd:ignore-file
/**
 * Enhanced Verification Controller
 *
 * Handles verification operations using the new verification service.
 */

import { Request, Response, NextFunction } from 'express';
import { PrismaClient } from '@prisma/client';
import { VerificationService } from '../services/verification/VerificationService';
import { VerificationStrategyFactory } from '../factories/verification/VerificationStrategyFactory';
import { VerificationPluginManager } from '../plugins/verification/VerificationPluginManager';
import BinanceVerificationPlugin from '../plugins/verification/BinanceVerificationPlugin';
import { LoggingPreProcessor } from '../services/verification/processors/LoggingPreProcessor';
import { NotificationPostProcessor } from '../services/verification/processors/NotificationPostProcessor';
import { logger } from '../lib/logger';
import { AppError } from '../middlewares/error.middleware';
import { PrismaClient } from '@prisma/client';
import { VerificationService } from '../services/verification/VerificationService';
import { VerificationStrategyFactory } from '../factories/verification/VerificationStrategyFactory';
import { VerificationPluginManager } from '../plugins/verification/VerificationPluginManager';
import { LoggingPreProcessor } from '../services/verification/processors/LoggingPreProcessor';
import { NotificationPostProcessor } from '../services/verification/processors/NotificationPostProcessor';
import { logger } from '../lib/logger';
import { AppError } from '../middlewares/error.middleware';

const prisma: any = new PrismaClient();
const verificationService: any = new VerificationService(prisma);
const verificationPluginManager: any =
  VerificationPluginManager.getInstance(verificationService);

// Register pre-processors
verificationService.registerPreProcessor(new LoggingPreProcessor());

// Register post-processors
verificationService.registerPostProcessor(new NotificationPostProcessor(prisma));

// Register plugins
verificationPluginManager.registerPlugin(BinanceVerificationPlugin);

/**
 * Verify a payment
 */
export const verifyPayment: any = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const {
      transactionId,
      merchantId,
      paymentMethodId,
      paymentMethodType,
      amount,
      currency,
      verificationData,
    } = req.body;

    // Validate required fields
    if (
      !transactionId ||
      !merchantId ||
      !paymentMethodId ||
      !paymentMethodType ||
      !amount ||
      !currency ||
      !verificationData
    ) {
      return next(
        new AppError({
          message: 'Missing required fields',
          type: ErrorType.VALIDATION,
          code: ErrorCode.MISSING_REQUIRED_FIELD,
        })
      );
    }

    // Validate verification data
    if (!verificationData.verificationMethod) {
      return next(
        new AppError({
          message: 'Verification method is required',
          type: ErrorType.VALIDATION,
          code: ErrorCode.MISSING_REQUIRED_FIELD,
        })
      );
    }

    // Check if the verification method exists
    const strategyFactory: any = VerificationStrategyFactory.getInstance();
    if (!strategyFactory.hasStrategy(verificationData.verificationMethod)) {
      return next(
        new AppError(`Unsupported verification method: ${verificationData.verificationMethod}`, 400)
      );
    }

    // Verify the payment
    const result: any = await verificationService.verify({
      verificationMethod: verificationData.verificationMethod,
      transactionId,
      merchantId,
      paymentMethodId,
      paymentMethodType,
      amount,
      currency,
      verificationData,
    });

    // Return the result
    res.json({
      verified: result.success,
      status: result.success ? 'success' : 'failed',
      message: result.message,
      details: result.details,
      timestamp: result.timestamp,
    });
  } catch (error) {
    logger.error('Verification error:', error);
    next(
      new AppError({
        message: 'Verification failed',
        type: ErrorType.INTERNAL,
        code: ErrorCode.INTERNAL_SERVER_ERROR,
      })
    );
  }
};

/**
 * Get verification methods for a payment method
 */
export const getVerificationMethodsForPaymentMethod: any = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { paymentMethodType } = req.params;

    if (!paymentMethodType) {
      return next(
        new AppError({
          message: 'Payment method type is required',
          type: ErrorType.VALIDATION,
          code: ErrorCode.MISSING_REQUIRED_FIELD,
        })
      );
    }

    // Get verification methods for the payment method
    const verificationMethods: any =
      verificationService.getVerificationMethodsForPaymentMethod(paymentMethodType);

    // Map to a simpler format
    const methods: any = verificationMethods.map((method) => ({
      type: method.getType(),
      displayName: method.getDisplayName(),
      description: method.getDescription(),
      requiredFields: method.getRequiredFields(),
      enabled: method.isEnabled(),
    }));

    res.json({
      paymentMethodType,
      verificationMethods: methods,
    });
  } catch (error) {
    logger.error('Error getting verification methods:', error);
    next(
      new AppError({
        message: 'Failed to get verification methods',
        type: ErrorType.INTERNAL,
        code: ErrorCode.INTERNAL_SERVER_ERROR,
      })
    );
  }
};

/**
 * Get all verification methods
 */
export const getAllVerificationMethods: any = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    // Get all verification methods
    const verificationMethods: any = verificationService.getAllVerificationMethods();

    // Map to a simpler format
    const methods: any = verificationMethods.map((method) => ({
      type: method.getType(),
      displayName: method.getDisplayName(),
      description: method.getDescription(),
      supportedPaymentMethods: method.getSupportedPaymentMethods(),
      requiredFields: method.getRequiredFields(),
      enabled: method.isEnabled(),
    }));

    res.json({
      verificationMethods: methods,
    });
  } catch (error) {
    logger.error('Error getting all verification methods:', error);
    next(
      new AppError({
        message: 'Failed to get verification methods',
        type: ErrorType.INTERNAL,
        code: ErrorCode.INTERNAL_SERVER_ERROR,
      })
    );
  }
};

/**
 * Get verification method by type
 */
export const getVerificationMethodByType = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { type } = req.params;

    if (!type) {
      return next(
        new AppError({
          message: 'Verification method type is required',
          type: ErrorType.VALIDATION,
          code: ErrorCode.MISSING_REQUIRED_FIELD,
        })
      );
    }

    // Get the verification method
    const strategyFactory = VerificationStrategyFactory.getInstance();

    if (!strategyFactory.hasStrategy(type)) {
      return next(new AppError(`Verification method not found: ${type}`, 404));
    }

    const method: any = strategyFactory.getStrategy(type);

    // Map to a simpler format
    const methodData: any = {
      type: method.getType(),
      displayName: method.getDisplayName(),
      description: method.getDescription(),
      supportedPaymentMethods: method.getSupportedPaymentMethods(),
      requiredFields: method.getRequiredFields(),
      enabled: method.isEnabled(),
      configuration: method.getConfiguration(),
    };

    res.json({
      verificationMethod: methodData,
    });
  } catch (error) {
    logger.error('Error getting verification method:', error);
    next(
      new AppError({
        message: 'Failed to get verification method',
        type: ErrorType.INTERNAL,
        code: ErrorCode.INTERNAL_SERVER_ERROR,
      })
    );
  }
};

export default {
  verifyPayment,
  getVerificationMethodsForPaymentMethod,
  getAllVerificationMethods,
  getVerificationMethodByType,
};
