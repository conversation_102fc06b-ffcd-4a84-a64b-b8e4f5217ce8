import { Request, Response, NextFunction } from 'express';
import request from "supertest";
/**
 * Route test helper
 * This class helps with testing routes
 */
export declare class RouteTestHelper {
    private app;
    private routeRegistry;
    private routeVersionManager;
    private routeMonitor;
    /**
     * Create a new route test helper
     * @param app Express application
     */
    constructor(app: Application);
    /**
     * Create a mock router
     * @param key Router key
     * @param path Router path
     * @param handler Request handler
     * @returns Express router
     */
    createMockRouter(key: string, path: string, handler: (req: Request, res: Response) => void): Router;
    /**
     * Create a mock versioned router
     * @param version Version
     * @param key Router key
     * @param path Router path
     * @param handler Request handler
     * @returns Express router
     */
    createMockVersionedRouter(version: string, key: string, path: string, handler: (req: Request, res: Response) => void): Router;
    /**
     * Create a mock middleware
     * @param name Middleware name
     * @param handler Middleware handler
     * @returns Express middleware
     */
    createMockMiddleware(name: string, handler: (req: Request, res: Response, next: NextFunction) => void): (req: Request, res: Response, next: NextFunction) => void;
    /**
     * Test a route
     * @param method HTTP method
     * @param path Route path
     * @param expectedStatus Expected status code
     * @param expectedBody Expected response body
     * @param token Authentication token
     * @param body Request body
     * @returns Test result
     */
    testRoute(method: "get" | "post" | "put" | "delete" | "patch", path: string, expectedStatus: number, expectedBody?: unknown, token?: string, body?: unknown): Promise<request.Response>;
    /**
     * Test a route with authentication
     * @param method HTTP method
     * @param path Route path
     * @param token Authentication token
     * @param expectedStatus Expected status code
     * @param expectedBody Expected response body
     * @param body Request body
     * @returns Test result
     */
    testAuthenticatedRoute(method: "get" | "post" | "put" | "delete" | "patch", path: string, token: string, expectedStatus: number, expectedBody?: unknown, body?: unknown): Promise<request.Response>;
    /**
     * Test a route without authentication
     * @param method HTTP method
     * @param path Route path
     * @param expectedStatus Expected status code
     * @param expectedBody Expected response body
     * @param body Request body
     * @returns Test result
     */
    testUnauthenticatedRoute(method: "get" | "post" | "put" | "delete" | "patch", path: string, expectedStatus: number, expectedBody?: unknown, body?: unknown): Promise<request.Response>;
    /**
     * Reset route metrics
     */
    resetRouteMetrics(): void;
}
export default RouteTestHelper;
//# sourceMappingURL=RouteTestHelper.d.ts.map