/**
 * Test Types and Interfaces
 *
 * Centralized type definitions for the test utility system.
 */

import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
// import { BaseController } from '../../../core/BaseController';
// import { BaseService } from '../../../core/BaseService';
// import { BaseRepository } from '../../../core/BaseRepository';

// Placeholder types for missing base classes
type BaseController = unknown;
type BaseService = unknown;
type BaseRepository = unknown;

/**
 * Mock request interface
 */
export interface MockRequest extends Partial<Request> {
  params?: unknown;
  query?: unknown;
  body?: unknown;
  headers?: unknown;
  user?: unknown;
  session?: unknown;
  cookies?: unknown;
  ip?: string;
  method?: string;
  url?: string;
  originalUrl?: string;
  path?: string;
  protocol?: string;
  secure?: boolean;
  xhr?: boolean;
}

/**
 * Mock response interface
 */
export interface MockResponse extends Partial<Response> {
  status?: jest.Mock;
  json?: jest.Mock;
  send?: jest.Mock;
  end?: jest.Mock;
  redirect?: jest.Mock;
  cookie?: jest.Mock;
  clearCookie?: jest.Mock;
  locals?: unknown;
  statusCode?: number;
  headersSent?: boolean;
}

/**
 * Mock next (...args: any[]) => any type
 */
export type MockNext = jest.Mock;

/**
 * Base test options interface
 */
export interface BaseTestOptions {
  description?: string;
  setup?: () => void | Promise<any>;
  cleanup?: () => void | Promise<any>;
  beforeEach?: () => void | Promise<any>;
  afterEach?: () => void | Promise<any>;
  timeout?: number;
  skip?: boolean;
  only?: boolean;
}

/**
 * Controller test options
 */
export interface ControllerTestOptions extends BaseTestOptions {
  req?: MockRequest;
  res?: MockResponse;
  next?: MockNext;
  expectedStatus?: number;
  expectedResponse?: unknown;
  expectedError?: unknown;
  controllerSetup?: (controller: BaseController) => void | Promise<any>;
  controllerCleanup?: (controller: BaseController) => void | Promise<any>;
  validateResponse?: (res: MockResponse) => void | Promise<any>;
  validateRequest?: (req: MockRequest) => void | Promise<any>;
}

/**
 * Service test options
 */
export interface ServiceTestOptions extends BaseTestOptions {
  args?: unknown[];
  expectedResult?: unknown;
  expectedError?: unknown;
  serviceSetup?: (service: BaseService) => void | Promise<any>;
  serviceCleanup?: (service: BaseService) => void | Promise<any>;
  mockDependencies?: Record<string, unknown>;
  validateResult?: (result: any) => void | Promise<any>;
  mockMethods?: Record<string, jest.Mock>;
}

/**
 * Repository test options
 */
export interface RepositoryTestOptions extends ServiceTestOptions {
  mockPrisma?: PrismaClient;
  mockTransaction?: boolean;
  mockTransactionResult?: unknown;
  repositorySetup?: (repository: BaseRepository) => void | Promise<any>;
  repositoryCleanup?: (repository: BaseRepository) => void | Promise<any>;
  mockQueries?: Record<string, unknown>;
  validateQuery?: (query: any) => void | Promise<any>;
}

/**
 * Middleware test options
 */
export interface MiddlewareTestOptions extends BaseTestOptions {
  req?: MockRequest;
  res?: MockResponse;
  next?: MockNext;
  expectedStatus?: number;
  expectedResponse?: unknown;
  expectedError?: unknown;
  middlewareSetup?: (req: MockRequest, res: MockResponse, next: MockNext) => void | Promise<any>;
  middlewareCleanup?: (req: MockRequest, res: MockResponse, next: MockNext) => void | Promise<any>;
  expectNextCalled?: boolean;
  expectNextCalledWith?: unknown;
}

/**
 * Validator test options
 */
export interface ValidatorTestOptions extends BaseTestOptions {
  req?: MockRequest;
  res?: MockResponse;
  next?: MockNext;
  expectedStatus?: number;
  expectedResponse?: unknown;
  expectedError?: unknown;
  args?: unknown[];
  validatorSetup?: () => void | Promise<any>;
  validatorCleanup?: () => void | Promise<any>;
  expectValidationError?: boolean;
  expectedValidationMessage?: string;
}

/**
 * Utility test options
 */
export interface UtilityTestOptions extends BaseTestOptions {
  args?: unknown[];
  expectedResult?: unknown;
  expectedError?: unknown;
  utilitySetup?: () => void | Promise<any>;
  utilityCleanup?: () => void | Promise<any>;
  mockGlobals?: Record<string, unknown>;
  restoreGlobals?: boolean;
}

/**
 * API test options
 */
export interface ApiTestOptions extends BaseTestOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  url?: string;
  headers?: Record<string, string>;
  body?: unknown;
  query?: Record<string, unknown>;
  expectedStatus?: number;
  expectedResponse?: unknown;
  expectedError?: unknown;
  auth?: {
    type: 'bearer' | 'basic' | 'api-key';
    token?: string;
    username?: string;
    password?: string;
    apiKey?: string;
  };
}

/**
 * Database test options
 */
export interface DatabaseTestOptions extends BaseTestOptions {
  seedData?: unknown[];
  cleanupData?: boolean;
  useTransaction?: boolean;
  isolateTest?: boolean;
  mockPrisma?: boolean;
  expectedQueries?: string[];
  validateData?: (data: any) => void | Promise<any>;
}

/**
 * Performance test options
 */
export interface PerformanceTestOptions extends BaseTestOptions {
  maxExecutionTime?: number;
  memoryLimit?: number;
  iterations?: number;
  warmupIterations?: number;
  measureMemory?: boolean;
  measureCpu?: boolean;
}

/**
 * Test result interface
 */
export interface TestResult {
  success: boolean;
  result?: unknown;
  error?: Error;
  executionTime?: number;
  memoryUsage?: number;
  metadata?: Record<string, unknown>;
}

/**
 * Test suite configuration
 */
export interface TestSuiteConfig {
  name: string;
  description?: string;
  setup?: () => void | Promise<any>;
  teardown?: () => void | Promise<any>;
  beforeEach?: () => void | Promise<any>;
  afterEach?: () => void | Promise<any>;
  timeout?: number;
  parallel?: boolean;
  retries?: number;
}

/**
 * Mock factory options
 */
export interface MockFactoryOptions {
  partial?: boolean;
  deep?: boolean;
  freeze?: boolean;
  seal?: boolean;
  overrides?: Record<string, unknown>;
}

/**
 * Data generator options
 */
export interface DataGeneratorOptions {
  count?: number;
  seed?: string | number;
  locale?: string;
  unique?: boolean;
  format?: string;
  constraints?: Record<string, unknown>;
}

/**
 * Test environment configuration
 */
export interface TestEnvironmentConfig {
  database?: {
    url: string;
    reset: boolean;
    seed: boolean;
  };
  redis?: {
    url: string;
    flush: boolean;
  };
  external?: {
    mockApis: boolean;
    mockServices: boolean;
  };
  logging?: {
    level: string;
    silent: boolean;
  };
}

/**
 * Test assertion helpers type
 */
export interface TestAssertions {
  toBeValidUUID: (received: string) => jest.CustomMatcherResult;
  toBeValidEmail: (received: string) => jest.CustomMatcherResult;
  toBeValidDate: (received: unknown) => jest.CustomMatcherResult;
  toBeValidUrl: (received: string) => jest.CustomMatcherResult;
  toHaveValidStructure: (received: unknown, structure: unknown) => jest.CustomMatcherResult;
  toMatchApiResponse: (received: unknown, expected: unknown) => jest.CustomMatcherResult;
  toBeWithinRange: (received: number, min: number, max: number) => jest.CustomMatcherResult;
  toHaveBeenCalledWithValidArgs: (
    received: jest.Mock,
    validator: (...args: any[]) => any
  ) => jest.CustomMatcherResult;
}

/**
 * Test context interface
 */
export interface TestContext {
  testName: string;
  suiteName: string;
  startTime: number;
  endTime?: number;
  metadata: any;
  mocks: Record<string, jest.Mock>;
  cleanup: (() => void | Promise<any>)[];
}

/**
 * Test runner configuration
 */
export interface TestRunnerConfig {
  parallel?: boolean;
  maxConcurrency?: number;
  timeout?: number;
  retries?: number;
  bail?: boolean;
  verbose?: boolean;
  coverage?: boolean;
  reporters?: string[];
}

/**
 * Mock data templates
 */
export interface MockDataTemplates {
  user: unknown;
  merchant: unknown;
  transaction: any;
  payment: unknown;
  subscription: unknown;
  notification: unknown;
  webhook: unknown;
  alert: unknown;
  audit: unknown;
  setting: unknown;
}

/**
 * Test fixture interface
 */
export interface TestFixture {
  name: string;
  data: any;
  dependencies?: string[];
  setup?: () => void | Promise<any>;
  teardown?: () => void | Promise<any>;
}

/**
 * Test scenario interface
 */
export interface TestScenario {
  name: string;
  description?: string;
  steps: TestStep[];
  setup?: () => void | Promise<any>;
  teardown?: () => void | Promise<any>;
  expectedOutcome?: unknown;
}

/**
 * Test step interface
 */
export interface TestStep {
  name: string;
  action: () => void | Promise<any>;
  validation?: () => void | Promise<any>;
  timeout?: number;
  retries?: number;
}

/**
 * Test error types
 */
export enum TestErrorType {
  SETUP_ERROR = 'SETUP_ERROR',
  EXECUTION_ERROR = 'EXECUTION_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  CLEANUP_ERROR = 'CLEANUP_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  ASSERTION_ERROR = 'ASSERTION_ERROR',
}

/**
 * Test error class
 */
export class TestError extends Error {
  type: TestErrorType;
  context?: TestContext;
  originalError?: Error;

  constructor(message: string, type: TestErrorType, context?: TestContext, originalError?: Error) {
    super(message);
    this.name = 'TestError';
    this.type = type;
    this.context = context;
    this.originalError = originalError;
  }
}
