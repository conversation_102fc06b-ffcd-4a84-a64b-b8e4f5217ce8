{"version": 3, "file": "log-rotation.js", "sourceRoot": "", "sources": ["../../../src/utils/log-rotation.ts"], "names": [], "mappings": ";AAAA,oBAAoB;AACpB;;;;;GAKG;;;;;;AAEH,4CAAoB;AACpB,gDAAwB;AACxB,uCAAyD;AACzD,0CAAuC;AAEvC,uDAAuD;AAKvD,gBAAgB;AAChB,MAAM,gBAAgB,GAAW,EAAE,CAAC,CAAC,mCAAmC;AAExE,0CAA0C;AAC1C,MAAM,eAAe,GAAY,GAAW,EAAE;IAC5C,MAAM,GAAG,GAAG,IAAA,4BAAc,GAAE,CAAC;IAC7B,OAAO,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;AAC/C,CAAC,CAAC;AACF,MAAM,SAAS,GAAY,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,YAAY,CAAC,CAAC;AAE7E;;;GAGG;AACI,MAAM,cAAc,GAAY,KAAK,IAAmB,EAAE;IAC/D,IAAI,CAAC;QACH,yCAAyC;QACzC,MAAM,YAAY,GAAY,eAAe,EAAE,CAAC;QAEhD,+BAA+B;QAC/B,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;YACjC,eAAM,CAAC,IAAI,CAAC,iBAAiB,YAAY,8BAA8B,CAAC,CAAC;YACzE,YAAE,CAAC,SAAS,CAAC,YAAY,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;YAChD,OAAO;QACT,CAAC;QAED,sCAAsC;QACtC,MAAM,KAAK,GAAY,YAAE,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;QAEpD,wBAAwB;QACxB,MAAM,UAAU,GAAY,IAAA,kBAAO,EAAC,IAAI,IAAI,EAAE,EAAE,gBAAgB,CAAC,CAAC;QAElE,0CAA0C;QAC1C,MAAM,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,IAAY,EAAE,EAAE;YAChD,8BAA8B;YAC9B,MAAM,SAAS,GACb,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAEjF,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,OAAO,KAAK,CAAC;YACf,CAAC;YAED,2DAA2D;YAC3D,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;YAElD,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,OAAO,KAAK,CAAC;YACf,CAAC;YAED,2BAA2B;YAC3B,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;YAExC,0CAA0C;YAC1C,OAAO,QAAQ,GAAG,UAAU,CAAC;QAC/B,CAAC,CAAC,CAAC;QAEH,uBAAuB;QACvB,KAAK,MAAM,IAAI,IAAI,WAAW,EAAE,CAAC;YAC/B,MAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;YAC/C,YAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YACxB,eAAM,CAAC,IAAI,CAAC,yBAAyB,IAAI,EAAE,CAAC,CAAC;QAC/C,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,kCAAkC,WAAW,CAAC,MAAM,iBAAiB,CAAC,CAAC;IACrF,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;IACpD,CAAC;AACH,CAAC,CAAC;AArDW,QAAA,cAAc,kBAqDzB;AAEF;;;;GAIG;AACI,MAAM,eAAe,GAAY,KAAK,EAAE,OAAe,CAAC,EAAiB,EAAE;IAChF,kDAAkD;IAClD,6DAA6D;IAC7D,eAAM,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;AACrD,CAAC,CAAC;AAJW,QAAA,eAAe,mBAI1B;AAEF;;GAEG;AACI,MAAM,mBAAmB,GAAY,GAAmB,EAAE;IAC/D,0CAA0C;IAC1C,IAAA,sBAAc,GAAE,CAAC;IAEjB,iDAAiD;IACjD,MAAM,GAAG,GAAS,IAAI,IAAI,EAAE,CAAC;IAC7B,MAAM,QAAQ,GAAS,IAAI,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,EAAE,GAAG,CAAC,QAAQ,EAAE,EAAE,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAC/F,MAAM,iBAAiB,GAAY,QAAQ,CAAC,OAAO,EAAE,GAAG,GAAG,CAAC,OAAO,EAAE,CAAC;IAEtE,yBAAyB;IACzB,MAAM,KAAK,GAAY,WAAW,CAAC,GAAG,EAAE;QACtC,IAAA,sBAAc,GAAE,CAAC;IACnB,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,WAAW;IAEpC,qCAAqC;IACrC,UAAU,CAAC,GAAG,EAAE;QACd,IAAA,sBAAc,GAAE,CAAC;IACnB,CAAC,EAAE,iBAAiB,CAAC,CAAC;IAEtB,OAAO,KAAK,CAAC;AACf,CAAC,CAAC;AApBW,QAAA,mBAAmB,uBAoB9B;AAEF,kBAAe;IACb,cAAc,EAAd,sBAAc;IACd,eAAe,EAAf,uBAAe;IACf,mBAAmB,EAAnB,2BAAmB;CACpB,CAAC"}