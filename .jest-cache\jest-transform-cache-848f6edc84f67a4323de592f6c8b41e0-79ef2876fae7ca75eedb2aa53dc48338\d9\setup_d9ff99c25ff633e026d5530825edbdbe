0e9abb905f62223c8b6e1d4bb350b9b0
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
// Jest setup file
const dotenv_1 = __importDefault(require("dotenv"));
// Load environment variables for testing
dotenv_1.default.config();
// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret';
process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/test';
// Set timeout for all tests
jest.setTimeout(30000);
// Global beforeAll hook
beforeAll(async () => {
    console.log('Starting test suite');
});
// Global afterAll hook
afterAll(async () => {
    console.log('Test suite completed');
});
// Mock console.error to avoid cluttering test output
const originalConsoleError = console.error;
console.error = (...args) => {
    // Check if this is a test-related error that we want to see
    if (args[0] &&
        typeof args[0] === 'string' &&
        (args[0].includes('FAIL') || args[0].includes('ERROR'))) {
        originalConsoleError(...args);
    }
    // Otherwise suppress the error in test output
};
// Restore console.error after tests
afterAll(() => {
    console.error = originalConsoleError;
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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