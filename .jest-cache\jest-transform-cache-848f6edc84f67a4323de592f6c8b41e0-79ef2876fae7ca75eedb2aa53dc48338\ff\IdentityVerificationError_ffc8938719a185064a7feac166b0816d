174bf6acc5d61185e6a8dcd14c132cfb
"use strict";
/**
 * Identity Verification Error Classes
 *
 * Custom error handling for identity verification operations.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.IdentityVerificationError = void 0;
const app_error_1 = require("../../../utils/app-error");
const IdentityVerificationTypes_1 = require("./IdentityVerificationTypes");
/**
 * Custom error class for identity verification errors
 */
class IdentityVerificationError extends app_error_1.AppError {
    constructor(message, code, statusCode = 400) {
        super(message, statusCode);
        this.code = code;
        this.name = "IdentityVerificationError";
    }
    /**
     * Create an invalid signature error
     */
    static invalidSignature(message = "Invalid signature") {
        return new IdentityVerificationError(message, IdentityVerificationTypes_1.IdentityVerificationErrorCode.INVALID_SIGNATURE, 400);
    }
    /**
     * Create an invalid address error
     */
    static invalidAddress(message = "Invalid address") {
        return new IdentityVerificationError(message, IdentityVerificationTypes_1.IdentityVerificationErrorCode.INVALID_ADDRESS, 400);
    }
    /**
     * Create an invalid proof error
     */
    static invalidProof(message = "Invalid proof") {
        return new IdentityVerificationError(message, IdentityVerificationTypes_1.IdentityVerificationErrorCode.INVALID_PROOF, 400);
    }
    /**
     * Create a verification failed error
     */
    static verificationFailed(message = "Verification failed") {
        return new IdentityVerificationError(message, IdentityVerificationTypes_1.IdentityVerificationErrorCode.VERIFICATION_FAILED, 400);
    }
    /**
     * Create a verification not found error
     */
    static verificationNotFound(message = "Verification not found") {
        return new IdentityVerificationError(message, IdentityVerificationTypes_1.IdentityVerificationErrorCode.VERIFICATION_NOT_FOUND, 404);
    }
    /**
     * Create a claim not found error
     */
    static claimNotFound(message = "Claim not found") {
        return new IdentityVerificationError(message, IdentityVerificationTypes_1.IdentityVerificationErrorCode.CLAIM_NOT_FOUND, 404);
    }
    /**
     * Create an internal error
     */
    static internalError(message = "Internal error") {
        return new IdentityVerificationError(message, IdentityVerificationTypes_1.IdentityVerificationErrorCode.INTERNAL_ERROR, 500);
    }
    /**
     * Create a provider error
     */
    static providerError(message = "Provider error") {
        return new IdentityVerificationError(message, IdentityVerificationTypes_1.IdentityVerificationErrorCode.PROVIDER_ERROR, 502);
    }
    /**
     * Create an invalid parameters error
     */
    static invalidParameters(message = "Invalid parameters") {
        return new IdentityVerificationError(message, IdentityVerificationTypes_1.IdentityVerificationErrorCode.INVALID_PARAMETERS, 400);
    }
    /**
     * Create an unauthorized error
     */
    static unauthorized(message = "Unauthorized") {
        return new IdentityVerificationError(message, IdentityVerificationTypes_1.IdentityVerificationErrorCode.UNAUTHORIZED, 401);
    }
    /**
     * Wrap an unknown error
     */
    static fromError(error, defaultMessage = "Unknown error") {
        if (error instanceof IdentityVerificationError) {
            return error;
        }
        const message = error instanceof Error ? error.message : defaultMessage;
        return new IdentityVerificationError(message, IdentityVerificationTypes_1.IdentityVerificationErrorCode.INTERNAL_ERROR, 500);
    }
}
exports.IdentityVerificationError = IdentityVerificationError;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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