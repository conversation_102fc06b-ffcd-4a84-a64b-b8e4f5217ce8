import { PrismaClient } from '@prisma/client';
// jscpd:ignore-file
import {
  PrismaClient,
  IdentityVerificationMethodEnum,
  IdentityVerificationStatusEnum,
} from '@prisma/client';
import { AppError } from '../utils/app-error';
import { User } from '../types';
import { AppError } from '../utils/app-error';
import { User } from '../types';

const prisma: unknown = new PrismaClient();

// Error codes for MFA
export enum MFAErrorCode {
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  INVALID_PARAMETERS = 'INVALID_PARAMETERS',
  VERIFICATION_NOT_FOUND = 'VERIFICATION_NOT_FOUND',
  VERIFICATION_FAILED = 'VERIFICATION_FAILED',
  INSUFFICIENT_FACTORS = 'INSUFFICIENT_FACTORS',
}

// Custom error class for MFA
export class MFAError extends AppError {
  code: MFAErrorCode;

  constructor(message: string, code: MFAErrorCode, statusCode: number) {
    super(message, statusCode);
    this.code = code;
  }
}

export class MultiFactorAuthService {
  /**
   * Get user's verification methods
   * @param userId User ID
   */
  async getUserVerificationMethods(userId: string): Promise<unknown> {
    try {
      // Get all verified identity verifications for the user
      const verifications: unknown = await prisma.identityVerification.findMany({
        where: {
          userId,
          status: IdentityVerificationStatusEnum.VERIFIED,
        },
        orderBy: { verifiedAt: 'desc' },
      });

      // Group verifications by method
      const methodsMap: unknown = new Map<string, unknown>();

      (verifications as any).forEach((verification: any) => {
        if (!methodsMap.has(verification.method)) {
          methodsMap.set(verification.method, {
            method: verification.method,
            count: 0,
            verifications: [],
          });
        }

        const methodData = methodsMap.get(verification.method);
        methodData.count += 1;
        methodData.verifications.push({
          id: verification.id,
          address: verification.address,
          verifiedAt: verification.verifiedAt,
          verificationData: verification.verificationData,
        });
      });

      // Convert map to array
      const methods: unknown = Array.from(methodsMap.values());

      // Calculate MFA status
      const mfaStatus: unknown = this.calculateMFAStatus(methods);

      return {
        methods,
        mfaStatus,
      };
    } catch (error) {
      console.error('Error getting user verification methods:', error);
      throw new MFAError(
        'Failed to get user verification methods',
        MFAErrorCode.INTERNAL_ERROR,
        500
      );
    }
  }

  /**
   * Calculate MFA status based on verification methods
   * @param methods Verification methods
   */
  private calculateMFAStatus(methods: unknown[]) {
    // Count unique verification methods
    const uniqueMethods: unknown = methods.length;

    // Define security levels
    let securityLevel: string = 'none';
    let requiredMethods: number = 2; // Default required methods for MFA

    if (uniqueMethods === 0) {
      securityLevel = 'none';
    } else if (uniqueMethods === 1) {
      securityLevel = 'basic';
    } else if (uniqueMethods === 2) {
      securityLevel = 'medium';
    } else if (uniqueMethods >= 3) {
      securityLevel = 'high';
    }

    // Check if user has biometric verification (Worldcoin)
    const hasBiometric: unknown = methods.some(
      (m) => m.method === IdentityVerificationMethodEnum.WORLDCOIN
    );

    // Check if user has zero-knowledge proof verification (Polygon ID)
    const hasZKP: unknown = methods.some(
      (m) => m.method === IdentityVerificationMethodEnum.POLYGON_ID
    );

    // Check if user has wallet verification
    const hasWallet: unknown = methods.some(
      (m) =>
        m.method === IdentityVerificationMethodEnum.SELF_SOVEREIGN ||
        m.method === IdentityVerificationMethodEnum.ENS ||
        m.method === IdentityVerificationMethodEnum.UNSTOPPABLE_DOMAINS
    );

    // Calculate MFA status
    const isMFAEnabled: unknown = uniqueMethods >= requiredMethods;

    return {
      securityLevel,
      uniqueMethods,
      requiredMethods,
      isMFAEnabled,
      hasBiometric,
      hasZKP,
      hasWallet,
    };
  }

  /**
   * Enable MFA for a user
   * @param userId User ID
   * @param verificationIds Verification IDs to use for MFA
   */
  async enableMFA(userId: string, verificationIds: string[]): Promise<unknown> {
    try {
      // Validate parameters
      if (!verificationIds || verificationIds.length < 2) {
        throw new MFAError(
          'At least two verification methods are required for MFA',
          MFAErrorCode.INSUFFICIENT_FACTORS,
          400
        );
      }

      // Check if all verifications exist and belong to the user
      const verifications: unknown = await prisma.identityVerification.findMany({
        where: {
          id: { in: verificationIds },
          userId,
          status: IdentityVerificationStatusEnum.VERIFIED,
        },
      });

      if (verifications.length !== verificationIds.length) {
        throw new MFAError(
          'One or more verification methods are invalid or do not belong to the user',
          MFAErrorCode.VERIFICATION_NOT_FOUND,
          400
        );
      }

      // Check if verifications use different methods
      const methods: unknown = new Set(verifications.map((v) => v.method));
      if (methods.size < 2) {
        throw new MFAError(
          'MFA requires at least two different verification methods',
          MFAErrorCode.INSUFFICIENT_FACTORS,
          400
        );
      }

      // Update user's MFA settings
      await prisma.user.update({
        where: { id: userId },
        data: { mfaEnabled: true, mfaVerificationIds: verificationIds, mfaEnabledAt: new Date() },
      });

      // Get updated MFA status
      const mfaStatus: unknown = await this.getUserVerificationMethods(userId);

      return {
        success: true,
        message: 'MFA enabled successfully',
        mfaStatus,
      };
    } catch (error) {
      if (error instanceof MFAError) {
        throw error;
      }

      console.error('Error enabling MFA:', error);
      throw new MFAError('Failed to enable MFA', MFAErrorCode.INTERNAL_ERROR, 500);
    }
  }

  /**
   * Disable MFA for a user
   * @param userId User ID
   */
  async disableMFA(userId: string): Promise<unknown> {
    try {
      // Update user's MFA settings
      await prisma.user.update({
        where: { id: userId },
        data: { mfaEnabled: false, mfaVerificationIds: [], mfaEnabledAt: null },
      });

      return {
        success: true,
        message: 'MFA disabled successfully',
      };
    } catch (error) {
      console.error('Error disabling MFA:', error);
      throw new MFAError('Failed to disable MFA', MFAErrorCode.INTERNAL_ERROR, 500);
    }
  }
}
