import { Request, Response, NextFunction } from 'express';
import { ServiceError } from '../services/base.service';
import { PaginationInfo } from '../middlewares/apiResponseMiddleware';
/**
 * Base controller class
 */
export declare class BaseController {
    /**
     * Handle async route handler
     * @param fn Async route handler function
     * @returns Express route handler
     */
    protected asyncHandler(fn: (req: Request, res: Response, next: NextFunction) => Promise<any>): (req: Request, res: Response, next: NextFunction) => void;
    /**
     * Send a success response
     * @param res Express response object
     * @param data Response data
     * @param message Success message
     * @param meta Response metadata
     */
    protected sendSuccess<T>(res: Response, data?: T, message?: string, meta?: Record<string, any>): void;
    /**
     * Send a paginated response
     * @param res Express response object
     * @param data Response data
     * @param pagination Pagination information
     * @param message Success message
     * @param meta Response metadata
     */
    protected sendPaginated<T>(res: Response, data: T, pagination: PaginationInfo, message?: string, meta?: Record<string, any>): void;
    /**
     * Send an error response
     * @param res Express response object
     * @param error Service error
     */
    protected sendError(res: Response, error: ServiceError): void;
    /**
     * Create pagination info from request query parameters
     * @param req Express request object
     * @param total Total number of items
     * @returns Pagination information
     */
    protected createPaginationInfo(req: Request, total: number): PaginationInfo;
    /**
     * Get pagination parameters from request query parameters
     * @param req Express request object
     * @returns Pagination parameters
     */
    protected getPaginationParams(req: Request): {
        skip: number;
        take: number;
        page: number;
        limit: number;
    };
    /**
     * Get sort parameters from request query parameters
     * @param req Express request object
     * @param defaultField Default sort field
     * @param defaultOrder Default sort order
     * @returns Sort parameters
     */
    protected getSortParams(req: Request, defaultField?: string, defaultOrder?: 'asc' | 'desc'): {
        field: string;
        order: 'asc' | 'desc';
    };
    /**
     * Get filter parameters from request query parameters
     * @param req Express request object
     * @param allowedFields Allowed filter fields
     * @returns Filter parameters
     */
    protected getFilterParams(req: Request, allowedFields: string[]): Record<string, any>;
    /**
     * Get search parameters from request query parameters
     * @param req Express request object
     * @param searchFields Search fields
     * @returns Search parameters
     */
    protected getSearchParams(req: Request, searchFields: string[]): Record<string, any> | null;
}
//# sourceMappingURL=base.controller.d.ts.map