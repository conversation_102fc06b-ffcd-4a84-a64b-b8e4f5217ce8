"use strict";
/**
 * Fraud Detection Response Mapper
 *
 * Handles response formatting for fraud detection operations.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.FraudDetectionResponseMapper = void 0;
const AppError_1 = require("../../../utils/errors/AppError");
/**
 * Response mapper for fraud detection operations
 */
class FraudDetectionResponseMapper {
    /**
     * Send success response
     */
    static sendSuccess(res, data, message, statusCode = 200, pagination) {
        const response = {
            success: true,
            data,
            message,
            pagination,
            timestamp: new Date(),
            requestId: res.locals.requestId ?? 'unknown',
        };
        res.status(statusCode).json(response);
    }
    /**
     * Send error response
     */
    static sendError(res, error, statusCode) {
        let errorResponse;
        if (error instanceof AppError_1.AppError) {
            errorResponse = {
                success: false,
                error: {
                    message: error.message,
                    code: error.code,
                    type: error.type,
                    details: error.details,
                },
                timestamp: new Date(),
                requestId: res.locals.requestId ?? 'unknown',
            };
            statusCode = statusCode ?? error.statusCode ?? 400;
        }
        else {
            errorResponse = {
                success: false,
                error: {
                    message: error.message || 'Internal server error',
                    code: 'INTERNAL_SERVER_ERROR',
                    type: 'INTERNAL',
                },
                timestamp: new Date(),
                requestId: res.locals.requestId ?? 'unknown',
            };
            statusCode = statusCode ?? 500;
        }
        res.status(statusCode).json(errorResponse);
    }
    /**
     * Send risk assessment response
     */
    static sendRiskAssessment(res, assessment, message) {
        this.sendSuccess(res, assessment, message ?? 'Risk assessment completed successfully');
    }
    /**
     * Send transaction risk assessment response
     */
    static sendTransactionRiskAssessment(res, assessment, message) {
        this.sendSuccess(res, assessment, message ?? 'Transaction risk assessment retrieved successfully');
    }
    /**
     * Send fraud configuration response
     */
    static sendFraudConfig(res, config, message) {
        this.sendSuccess(res, config, message ?? 'Fraud detection configuration retrieved successfully');
    }
    /**
     * Send fraud configuration updated response
     */
    static sendFraudConfigUpdated(res, config) {
        this.sendSuccess(res, config, 'Fraud detection configuration updated successfully');
    }
    /**
     * Send flagged transactions list response
     */
    static sendFlaggedTransactionsList(res, transactions, total, page = 1, limit = 10) {
        const totalPages = Math.ceil(total / limit);
        this.sendSuccess(res, transactions, `Retrieved ${transactions.length} flagged transactions`, 200, {
            page,
            limit,
            total,
            totalPages,
        });
    }
    /**
     * Send fraud statistics response
     */
    static sendFraudStatistics(res, statistics) {
        this.sendSuccess(res, statistics, 'Fraud detection statistics retrieved successfully');
    }
    /**
     * Send validation error response
     */
    static sendValidationError(res, errors, message = 'Validation failed') {
        const error = new AppError_1.AppError({
            message,
            type: 'VALIDATION',
            code: 'INVALID_INPUT',
            details: { errors },
        });
        this.sendError(res, error, 400);
    }
    /**
     * Send authorization error response
     */
    static sendAuthorizationError(res, message = 'Access denied', requiredRole) {
        const error = new AppError_1.AppError({
            message,
            type: 'AUTHENTICATION',
            code: 'INVALID_CREDENTIALS',
            details: { requiredRole },
        });
        this.sendError(res, error, 403);
    }
    /**
     * Send not found error response
     */
    static sendNotFoundError(res, resource = 'Resource') {
        const error = new AppError_1.AppError({
            message: `${resource} not found`,
            type: 'NOT_FOUND',
            code: 'RESOURCE_NOT_FOUND',
        });
        this.sendError(res, error, 404);
    }
    /**
     * Send internal server error response
     */
    static sendInternalServerError(res, message = 'Internal server error') {
        const error = new AppError_1.AppError({
            message,
            type: 'INTERNAL',
            code: 'INTERNAL_SERVER_ERROR',
        });
        this.sendError(res, error, 500);
    }
    /**
     * Handle async controller method
     */
    static asyncHandler(fn) {
        return (req, res, next) => {
            Promise.resolve(fn(req, res, next)).catch((error) => next(error));
        };
    }
    /**
     * Set response headers for API
     */
    static setApiHeaders(res) {
        res.setHeader('Content-Type', 'application/json');
        res.setHeader('X-API-Version', '1.0');
        res.setHeader('X-Response-Time', Date.now());
    }
    /**
     * Format risk level for display
     */
    static formatRiskLevel(level) {
        switch (level) {
            case 'LOW':
                return 'Low Risk';
            case 'MEDIUM':
                return 'Medium Risk';
            case 'HIGH':
                return 'High Risk';
            case 'CRITICAL':
                return 'Critical Risk';
            default:
                return 'Unknown Risk';
        }
    }
    /**
     * Format risk score for display
     */
    static formatRiskScore(score) {
        return `${score.toFixed(1)}%`;
    }
    /**
     * Format percentage for display
     */
    static formatPercentage(value) {
        return `${value.toFixed(2)}%`;
    }
    /**
     * Format currency amount
     */
    static formatCurrency(amount, currency = 'USD') {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: currency,
        }).format(amount);
    }
    /**
     * Format date for display
     */
    static formatDate(date) {
        return new Intl.DateTimeFormat('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
        }).format(date);
    }
    /**
     * Create summary statistics
     */
    static createSummaryStats(statistics) {
        return {
            overview: {
                totalAssessments: statistics.totalAssessments,
                flaggedTransactions: statistics.flaggedCount,
                blockedTransactions: statistics.blockedCount,
                flaggedRate: this.formatPercentage(statistics.flaggedRate),
                blockedRate: this.formatPercentage(statistics.blockedRate),
            },
            riskDistribution: {
                low: statistics.levelCounts.LOW,
                medium: statistics.levelCounts.MEDIUM,
                high: statistics.levelCounts.HIGH,
                critical: statistics.levelCounts.CRITICAL,
            },
            period: {
                start: this.formatDate(statistics.period.start),
                end: this.formatDate(statistics.period.end),
                days: Math.ceil((statistics.period.end.getTime() - statistics.period.start.getTime()) /
                    (1000 * 60 * 60 * 24)),
            },
        };
    }
    /**
     * Transform risk assessment for display
     */
    static transformRiskAssessment(assessment) {
        return {
            ...assessment,
            riskScore: {
                ...assessment.riskScore,
                scoreFormatted: this.formatRiskScore(assessment.riskScore.score),
                levelFormatted: this.formatRiskLevel(assessment.riskScore.level),
            },
            createdAtFormatted: this.formatDate(assessment.createdAt),
        };
    }
    /**
     * Transform flagged transaction for display
     */
    static transformFlaggedTransaction(transaction) {
        return {
            ...transaction,
            scoreFormatted: this.formatRiskScore(transaction.score),
            levelFormatted: this.formatRiskLevel(transaction.level),
            createdAtFormatted: this.formatDate(transaction.createdAt),
            transaction: {
                ...transaction.transaction,
                amountFormatted: this.formatCurrency(transaction.transaction.amount, transaction.transaction.currency),
                createdAtFormatted: this.formatDate(transaction.transaction.createdAt),
            },
        };
    }
}
exports.FraudDetectionResponseMapper = FraudDetectionResponseMapper;
//# sourceMappingURL=FraudDetectionResponseMapper.js.map