/**
 * Identity Verification Controller
 *
 * Modular controller for identity verification operations.
 */
import { BaseController } from '../base.controller';
/**
 * Modular Identity Verification Controller
 */
export declare class IdentityVerificationController extends BaseController {
    private readonly authService;
    private readonly validationService;
    private readonly identityService;
    constructor();
    /**
     * Verify identity using Ethereum signature
     */
    verifyEthereumSignature: any;
    /**
     * Verify identity using ERC-1484
     */
    verifyERC1484Identity: any;
    /**
     * Verify identity using ERC-725
     */
    verifyERC725Identity: any;
    /**
     * Verify identity using ENS
     */
    verifyENS: any;
    /**
     * Verify identity using Polygon ID
     */
    verifyPolygonID: any;
    /**
     * Verify identity using Worldcoin
     */
    verifyWorldcoin: any;
    /**
     * Verify identity using Unstoppable Domains
     */
    verifyUnstoppableDomains: any;
    /**
     * Get verification by ID
     */
    getVerificationById: any;
    /**
     * Get verifications for user
     */
    getVerificationsForUser: any;
    /**
     * Health check endpoint
     */
    healthCheck: any;
}
//# sourceMappingURL=IdentityVerificationController.d.ts.map