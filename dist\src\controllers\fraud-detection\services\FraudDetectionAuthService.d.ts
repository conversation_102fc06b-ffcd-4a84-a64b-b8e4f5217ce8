/**
 * Fraud Detection Authorization Service
 *
 * Handles authorization logic for fraud detection operations.
 */
import { AuthorizationContext, PermissionResult } from '../types/FraudDetectionControllerTypes';
/**
 * Authorization service for fraud detection operations
 */
export declare class FraudDetectionAuthService {
    private readonly adminRoles;
    private readonly merchantRoles;
    private readonly userRoles;
    /**
     * Check if user is authorized for the given action
     */
    checkPermission(context: AuthorizationContext): Promise<PermissionResult>;
    /**
     * Check role-based permissions
     */
    private checkRolePermission;
    /**
     * Check risk assessment permissions
     */
    private checkRiskAssessmentPermission;
    /**
     * Check fraud configuration permissions
     */
    private checkFraudConfigPermission;
    /**
     * Check flagged transactions permissions
     */
    private checkFlaggedTransactionsPermission;
    /**
     * Check fraud statistics permissions
     */
    private checkFraudStatisticsPermission;
    /**
     * Check resource-specific permissions
     */
    private checkResourcePermission;
    /**
     * Require admin role
     */
    requireAdmin(userRole?: string): void;
    /**
     * Require merchant role or higher
     */
    requireMerchant(userRole?: string): void;
    /**
     * Require authenticated user
     */
    requireAuthenticated(userRole?: string): void;
    /**
     * Check if user has specific role
     */
    hasRole(userRole: string, requiredRole: string): boolean;
    /**
     * Get user permissions for a resource
     */
    getUserPermissions(userRole: string, resource: string): string[];
    /**
     * Validate authorization context
     */
    validateAuthorizationContext(context: AuthorizationContext): void;
    /**
     * Create authorization context from request
     */
    createAuthorizationContext(user: any, resource: string, action: string, resourceId?: string): AuthorizationContext;
    /**
     * Handle authorization error
     */
    handleAuthorizationError(result: PermissionResult): never;
    /**
     * Extract merchant context from request
     */
    extractMerchantContext(req: any): {
        merchantId?: number;
    };
}
//# sourceMappingURL=FraudDetectionAuthService.d.ts.map