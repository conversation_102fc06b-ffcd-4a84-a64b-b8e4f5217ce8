// jscpd:ignore-file
import { Request, Response } from "express";
import { Base<PERSON>ontroller } from "./base.controller";
import { asyncHandler } from '../utils/asyncHandler';
import { AppError } from '../utils/errors/AppError';
import { EmailService } from '../services/email.service';
import { BaseController } from "./base.controller";
import { asyncHandler } from '../utils/asyncHandler';
import { AppError } from '../utils/errors/AppError';
import { EmailService } from '../services/email.service';

// Extend the Express Request type to include user property
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role: string;
        merchantId?: string;
      };
    }
  }
}

/**
 * EmailController
 * Controller for handling email operations
 */
export class EmailController extends BaseController {
  constructor() {
    super();
  }

  /**
   * Test the email service by sending a test email
   */
  testEmailService = asyncHandler(async (req: Request, res: Response) => {
    try {
        // Get user role
        const userRole: any =req.user?.role;

        // Check if user is authorized
        if (!userRole || userRole !== "ADMIN") {
            throw new AppError({
            message: "Unauthorized",
            type: ErrorType.AUTHENTICATION,
            code: ErrorCode.INVALID_CREDENTIALS
        });
        }

        // Create email service
        const emailService: any =new EmailService();

        // Test email service
        const success: any =await emailService.testEmailService();

        if (success) {
            return res.status(200).json({
                success: true,
                message: "Test email sent successfully"
            });
        } else {
            throw new AppError({
            message: "Failed to send test email",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
        }
    } catch (error) {
        if (error instanceof AppError) {
            throw error;
        }
        throw new AppError({
            message: "Failed to test email service",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
    }
  });

  /**
   * Send a custom email
   */
  sendCustomEmail = asyncHandler(async (req: Request, res: Response) => {
    try {
        // Get user role
        const userRole: any =req.user?.role;

        // Check if user is authorized
        if (!userRole || userRole !== "ADMIN") {
            throw new AppError({
            message: "Unauthorized",
            type: ErrorType.AUTHENTICATION,
            code: ErrorCode.INVALID_CREDENTIALS
        });
        }

        // Get email data from body
        const { to, subject, html, text } = req.body;

        // Validate required fields
        if (!to || !subject || !html) {
            throw new AppError({
            message: "To, subject, and html are required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
        }

        // Create email service
        const emailService: any =new EmailService();

        // Send email
        const success: any =await emailService.sendEmail(to, subject, html, text);

        if (success) {
            return res.status(200).json({
                success: true,
                message: "Email sent successfully"
            });
        } else {
            throw new AppError({
            message: "Failed to send email",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
        }
    } catch (error) {
        if (error instanceof AppError) {
            throw error;
        }
        throw new AppError({
            message: "Failed to send custom email",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
    }
  });

  /**
   * Get admin email addresses
   */
  getAdminEmails = asyncHandler(async (req: Request, res: Response) => {
    try {
        // Get user role
        const userRole: any =req.user?.role;

        // Check if user is authorized
        if (!userRole || userRole !== "ADMIN") {
            throw new AppError({
            message: "Unauthorized",
            type: ErrorType.AUTHENTICATION,
            code: ErrorCode.INVALID_CREDENTIALS
        });
        }

        // Create email service
        const emailService: any =new EmailService();

        // Get admin emails
        const emails: any =await emailService.getAdminEmails();

        return res.status(200).json({
            success: true,
            data: emails
        });
    } catch (error) {
        if (error instanceof AppError) {
            throw error;
        }
        throw new AppError({
            message: "Failed to get admin emails",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
    }
  });
}

export default new EmailController();
