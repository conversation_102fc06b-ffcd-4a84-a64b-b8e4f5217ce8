/**
 * Blockchain Utilities for Identity Verification
 * 
 * Common blockchain operations and utilities.
 */

import { ethers } from "ethers";

/**
 * ERC-1484 Identity Registry ABI (simplified)
 */
export const ERC1484_ABI = [
    "function getIdentity(uint ein) view returns (address[] memory, address[] memory, address[] memory)",
    "function getEIN(address _address) view returns (uint)",
    "function hasIdentity(address _address) view returns (bool)",
    "function isAssociatedAddressFor(uint ein, address _address) view returns (bool)"
];

/**
 * ERC-725 Identity ABI (simplified)
 */
export const ERC725_ABI = [
    "function getKey(bytes32 _key) view returns (bytes32)",
    "function getKeys(bytes32[] memory _keys) view returns (bytes32[] memory)",
    "function execute(uint256 _operation, address _to, uint256 _value, bytes memory _data) returns (bytes32)"
];

/**
 * Polygon ID Verifier ABI (simplified)
 */
export const POLYGON_ID_VERIFIER_ABI = [
    "function verifyProof(uint256 _circuitId, uint256[] memory _inputs, uint256[2] memory _a, uint256[2][2] memory _b, uint256[2] memory _c, uint256[] memory _signals) view returns (bool)"
];

/**
 * Blockchain utility class
 */
export class BlockchainUtils {
    private static provider: ethers.JsonRpcProvider | null = null;

    /**
     * Get Ethereum provider
     */
    static getProvider(): ethers.JsonRpcProvider {
        if (!this.provider) {
            const rpcUrl = process.env.ETHEREUM_RPC_URL ?? "https://mainnet.infura.io/v3/your-infura-key";
            this.provider = new ethers.JsonRpcProvider(rpcUrl);
        }
        return this.provider;
    }

    /**
     * Validate Ethereum address
     */
    static isValidAddress(address: string): boolean {
        try {
            return ethers.isAddress(address);
        } catch {
            return false;
        }
    }

    /**
     * Normalize Ethereum address
     */
    static normalizeAddress(address: string): string {
        if (!this.isValidAddress(address)) {
            throw new Error("Invalid Ethereum address");
        }
        return ethers.getAddress(address);
    }

    /**
     * Verify message signature
     */
    static verifySignature(message: string, signature: string, expectedAddress: string): boolean {
        try {
            const recoveredAddress = ethers.verifyMessage(message, signature);
            return recoveredAddress.toLowerCase() === expectedAddress.toLowerCase();
        } catch {
            return false;
        }
    }

    /**
     * Recover address from signature
     */
    static recoverAddress(message: string, signature: string): string {
        return ethers.verifyMessage(message, signature);
    }

    /**
     * Create contract instance
     */
    static createContract(address: string, abi: any[]): ethers.Contract {
        const provider = this.getProvider();
        return new ethers.Contract(address, abi, provider);
    }

    /**
     * Get ERC-1484 registry contract
     */
    static getERC1484Contract(registryAddress: string): ethers.Contract {
        return this.createContract(registryAddress, ERC1484_ABI);
    }

    /**
     * Get ERC-725 identity contract
     */
    static getERC725Contract(identityAddress: string): ethers.Contract {
        return this.createContract(identityAddress, ERC725_ABI);
    }

    /**
     * Get Polygon ID verifier contract
     */
    static getPolygonIDContract(verifierAddress: string): ethers.Contract {
        return this.createContract(verifierAddress, POLYGON_ID_VERIFIER_ABI);
    }

    /**
     * Convert string to bytes32
     */
    static stringToBytes32(str: string): string {
        return ethers.keccak256(ethers.toUtf8Bytes(str));
    }

    /**
     * Convert bytes32 to string
     */
    static bytes32ToString(bytes32: string): string {
        return ethers.toUtf8String(bytes32);
    }

    /**
     * Check if transaction is confirmed
     */
    static async isTransactionConfirmed(txHash: string, requiredConfirmations: number = 6): Promise<boolean> {
        try {
            const provider = this.getProvider();
            const receipt = await provider.getTransactionReceipt(txHash);
            
            if (!receipt) {
                return false;
            }

            const currentBlock = await provider.getBlockNumber();
            const confirmations = currentBlock - receipt.blockNumber;
            
            return confirmations >= requiredConfirmations;
        } catch {
            return false;
        }
    }

    /**
     * Get transaction details
     */
    static async getTransactionDetails(txHash: string): Promise<any> {
        const provider = this.getProvider();
        const [transaction, receipt] = await Promise.all([
            provider.getTransaction(txHash),
            provider.getTransactionReceipt(txHash)
        ]);

        return {
            transaction,
            receipt,
            confirmations: receipt ? await provider.getBlockNumber() - receipt.blockNumber : 0
        };
    }

    /**
     * Estimate gas for contract call
     */
    static async estimateGas(contract: ethers.Contract, method: string, params: Record<string, string>[]): Promise<bigint> {
        return await contract[method].estimateGas(...params);
    }

    /**
     * Get current gas price
     */
    static async getGasPrice(): Promise<bigint> {
        const provider = this.getProvider();
        const feeData = await provider.getFeeData();
        return feeData.gasPrice ?? BigInt(0);
    }
}
