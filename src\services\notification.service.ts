// jscpd:ignore-file
import { logger } from '../utils/logger';
import prisma from '../lib/prisma';
import { EmailService } from './email.service';
import { SmsService } from './sms.service';
import { TelegramService } from './telegram.service';
import { PushNotificationService } from './push-notification.service';
import { config } from '../config';
import {
  NotificationEventsService,
  NotificationPriority as EventPriority,
  NotificationChannel as EventChannel,
} from './notification-events.service';
import { User, Merchant } from '../types';

/**
 * Notification channel
 */
export enum NotificationChannel {
  EMAIL = 'email',
  SMS = 'sms',
  TELEGRAM = 'telegram',
  PUSH = 'push',
  DASHBOARD = 'dashboard',
}

/**
 * Notification priority
 */
export enum NotificationPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

/**
 * Notification template
 */
export interface NotificationTemplate {
  id: string;
  name: string;
  description: string;
  subject: string;
  content: string;
  variables: string[];
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Notification options
 */
export interface NotificationOptions {
  userId?: string;
  merchantId?: string;
  channels: NotificationChannel[];
  priority: NotificationPriority;
  subject: string;
  message: string;
  templateId?: string;
  templateData?: Record<string, unknown>;
  metadata?: Record<string, unknown>;
}

/**
 * Notification service
 */
export class NotificationService {
  private emailService: EmailService;
  private smsService: SmsService;
  private telegramService: TelegramService;
  private pushService: PushNotificationService;

  /**
   * Create a new notification service
   */
  constructor() {
    this.emailService = new EmailService();
    this.smsService = new SmsService();
    this.telegramService = new TelegramService();
    this.pushService = new PushNotificationService();
  }

  /**
   * Send notification
   * @param options Notification options
   * @returns Success status
   */
  public async sendNotification(options: NotificationOptions): Promise<boolean> {
    try {
      // Validate options
      if (!options.channels || options.channels.length === 0) {
        logger.error('No notification channels specified');
        return false;
      }

      if (!options.subject || !options.message) {
        logger.error('Notification subject or message is missing');
        return false;
      }

      // Get user and merchant information if needed
      let user: unknown = null;
      let merchant = null;

      if (options.userId) {
        user = await prisma.user.findUnique({
          where: { id: options.userId },
        });

        if (!user) {
          logger.error(`User not found: ${options.userId}`);
          return false;
        }
      }

      if (options.merchantId) {
        merchant = await prisma.merchant.findUnique({
          where: { id: options.merchantId },
          include: { user: true },
        });

        if (!merchant) {
          logger.error(`Merchant not found: ${options.merchantId}`);
          return false;
        }

        // If user is not specified, use merchant's user
        if (!user && merchant.user) {
          user = merchant.user;
        }
      }

      // Process template if specified
      let subject: unknown = options.subject;
      let message: unknown = options.message;

      if (options.templateId && options.templateData) {
        const template: unknown = await this.getTemplate(options.templateId);
        if (template) {
          subject = this.processTemplate(template.subject, options.templateData);
          message = this.processTemplate(template.content, options.templateData);
        }
      }

      // Create notification record
      const notification: unknown = await prisma.notification.create({
        data: {
          userId: user?.id,
          merchantId: merchant?.id,
          channels: options.channels,
          priority: options.priority,
          subject,
          message,
          metadata: options.metadata ?? {},
          status: 'pending',
        },
      });

      // Emit notification created event
      NotificationEventsService.emitNotificationCreated({
        id: notification.id,
        userId: user?.id,
        merchantId: merchant?.id,
        title: subject,
        message,
        type: this.mapNotificationType(options),
        priority: this.mapToPriorityEnum(options.priority),
        channels: this.mapToChannelEnum(options.channels),
        metadata: options.metadata ?? {},
        createdAt: notification.createdAt,
      });

      // Send notifications through each channel
      const results: unknown = await Promise.all(
        options.channels.map((channel) =>
          this.sendThroughChannel(
            channel,
            notification.id,
            subject,
            message,
            user,
            merchant,
            options.priority
          )
        )
      );

      // Update notification status
      const success: unknown = results.some((result) => result);
      await prisma.notification.update({
        where: { id: notification.id },
        data: { status: success ? 'sent' : 'failed', sentAt: success ? new Date() : undefined },
      });

      return success;
    } catch (error) {
      logger.error('Error sending notification', { error, options });
      return false;
    }
  }

  /**
   * Send notification through channel
   * @param channel Notification channel
   * @param notificationId Notification ID
   * @param subject Notification subject
   * @param message Notification message
   * @param user User (optional)
   * @param merchant Merchant (optional)
   * @param priority Notification priority
   * @returns Success status
   */
  private async sendThroughChannel(
    channel: NotificationChannel,
    notificationId: string,
    subject: string,
    message: string,
    user: unknown | null,
    merchant: unknown | null,
    priority: NotificationPriority
  ): Promise<boolean> {
    try {
      // Create notification delivery record
      const delivery: unknown = await prisma.notificationDelivery.create({
        data: {
          notificationId,
          channel,
          status: 'pending',
        },
      });

      let success: boolean = false;

      // Send through appropriate channel
      switch (channel) {
        case NotificationChannel.EMAIL:
          success = await this.sendEmail(subject, message, user, merchant, priority);
          break;
        case NotificationChannel.SMS:
          success = await this.sendSms(message, user, merchant, priority);
          break;
        case NotificationChannel.TELEGRAM:
          success = await this.sendTelegram(subject, message, user, merchant, priority);
          break;
        case NotificationChannel.PUSH:
          success = await this.sendPush(subject, message, user, merchant, priority);
          break;
        case NotificationChannel.DASHBOARD:
          success = true; // Dashboard notifications are always successful as they're just stored
          break;
        default:
          logger.error(`Unsupported notification channel: ${channel}`);
          success = false;
      }

      // Update delivery status
      await prisma.notificationDelivery.update({
        where: { id: delivery.id },
        data: {
          status: success ? 'delivered' : 'failed',
          deliveredAt: success ? new Date() : undefined,
        },
      });

      // Emit notification delivered event
      NotificationEventsService.emitNotificationDelivered(
        notificationId,
        this.mapToChannelEnum([channel])[0],
        success,
        success ? undefined : 'Failed to deliver notification'
      );

      return success;
    } catch (error) {
      logger.error('Error sending notification through channel', {
        error,
        channel,
        notificationId,
      });
      return false;
    }
  }

  /**
   * Send email notification
   * @param subject Email subject
   * @param message Email message
   * @param user User (optional)
   * @param merchant Merchant (optional)
   * @param priority Notification priority
   * @returns Success status
   */
  private async sendEmail(
    subject: string,
    message: string,
    user: unknown | null,
    merchant: unknown | null,
    priority: NotificationPriority
  ): Promise<boolean> {
    try {
      // Get email address
      let email: string = '';

      if (user && user.email) {
        email = user.email;
      } else if (merchant && merchant.email) {
        email = merchant.email;
      }

      if (!email) {
        logger.error('No email address found for notification');
        return false;
      }

      // Send email
      return await this.emailService.sendEmail({
        to: email,
        subject,
        html: message,
        priority: this.mapPriorityToEmailPriority(priority),
      });
    } catch (error) {
      logger.error('Error sending email notification', { error, subject });
      return false;
    }
  }

  /**
   * Send SMS notification
   * @param message SMS message
   * @param user User (optional)
   * @param merchant Merchant (optional)
   * @param priority Notification priority
   * @returns Success status
   */
  private async sendSms(
    message: string,
    user: unknown | null,
    merchant: unknown | null,
    priority: NotificationPriority
  ): Promise<boolean> {
    try {
      // Get phone number
      let phone: string = '';

      if (user && user.phone) {
        phone = user.phone;
      } else if (merchant && merchant.phone) {
        phone = merchant.phone;
      }

      if (!phone) {
        logger.error('No phone number found for notification');
        return false;
      }

      // Send SMS
      return await this.smsService.sendSms(phone, message);
    } catch (error) {
      logger.error('Error sending SMS notification', { error, message });
      return false;
    }
  }

  /**
   * Send Telegram notification
   * @param subject Notification subject
   * @param message Notification message
   * @param user User (optional)
   * @param merchant Merchant (optional)
   * @param priority Notification priority
   * @returns Success status
   */
  private async sendTelegram(
    subject: string,
    message: string,
    user: unknown | null,
    merchant: unknown | null,
    priority: NotificationPriority
  ): Promise<boolean> {
    try {
      // Get Telegram chat ID
      let chatId: string = '';

      // First check user preferences
      if (user) {
        const userPrefs: unknown = await prisma.userNotificationPreference.findFirst({
          where: { userId: user.id, channel: NotificationChannel.TELEGRAM },
        });

        if (userPrefs && userPrefs.channelData && userPrefs.channelData.chatId) {
          chatId = userPrefs.channelData.chatId as string;
        }
      }

      // Then check merchant preferences
      if (!chatId && merchant) {
        const merchantPrefs: unknown = await prisma.merchantNotificationPreference.findFirst({
          where: { merchantId: merchant.id, channel: NotificationChannel.TELEGRAM },
        });

        if (merchantPrefs && merchantPrefs.channelData && merchantPrefs.channelData.chatId) {
          chatId = merchantPrefs.channelData.chatId as string;
        }
      }

      if (!chatId) {
        logger.error('No Telegram chat ID found for notification');
        return false;
      }

      // Format message for Telegram
      const formattedMessage: unknown = `*${subject}*\n\n${message}`;

      // Send Telegram message
      return await this.telegramService.sendMessage(chatId, formattedMessage);
    } catch (error) {
      logger.error('Error sending Telegram notification', { error, subject });
      return false;
    }
  }

  /**
   * Send push notification
   * @param subject Notification subject
   * @param message Notification message
   * @param user User (optional)
   * @param merchant Merchant (optional)
   * @param priority Notification priority
   * @returns Success status
   */
  private async sendPush(
    subject: string,
    message: string,
    user: unknown | null,
    merchant: unknown | null,
    priority: NotificationPriority
  ): Promise<boolean> {
    try {
      let success: boolean = false;

      // Determine icon based on priority
      let icon: string = '/logo.png';
      switch (priority) {
        case NotificationPriority.CRITICAL:
          icon = '/icons/critical.png';
          break;
        case NotificationPriority.HIGH:
          icon = '/icons/high.png';
          break;
        case NotificationPriority.MEDIUM:
          icon = '/icons/medium.png';
          break;
        case NotificationPriority.LOW:
          icon = '/icons/low.png';
          break;
      }

      // Send to user if specified
      if (user && user.id) {
        const userSuccess: unknown = await this.pushService.sendNotificationToUser(
          user.id,
          subject,
          message,
          icon,
          { priority },
          '/'
        );

        if (userSuccess) {
          success = true;
        }
      }

      // Send to merchant if specified
      if (merchant && merchant.id) {
        const merchantSuccess: unknown = await this.pushService.sendNotificationToMerchant(
          merchant.id,
          subject,
          message,
          icon,
          { priority },
          '/'
        );

        if (merchantSuccess) {
          success = true;
        }
      }

      return success;
    } catch (error) {
      logger.error('Error sending push notification', { error, subject });
      return false;
    }
  }

  /**
   * Get notification template
   * @param templateId Template ID
   * @returns Notification template
   */
  private async getTemplate(templateId: string): Promise<NotificationTemplate | null> {
    try {
      const template: unknown = await prisma.notificationTemplate.findUnique({
        where: { id: templateId },
      });

      return template;
    } catch (error) {
      logger.error('Error getting notification template', { error, templateId });
      return null;
    }
  }

  /**
   * Process template
   * @param template Template string
   * @param data Template data
   * @returns Processed template
   */
  private processTemplate(template: string, data: Record<string, unknown>): string {
    let result = template;

    // Replace variables in the format {{variable}}
    for (const [key, value] of Object.entries(data)) {
      const regex: unknown = new RegExp(`{{\\s*${key}\\s*}}`, 'g');
      result = result.replace(regex, String(value));
    }

    return result;
  }

  /**
   * Map notification priority to email priority
   * @param priority Notification priority
   * @returns Email priority
   */
  private mapPriorityToEmailPriority(priority: NotificationPriority): string {
    switch (priority) {
      case NotificationPriority.CRITICAL:
        return 'high';
      case NotificationPriority.HIGH:
        return 'high';
      case NotificationPriority.MEDIUM:
        return 'normal';
      case NotificationPriority.LOW:
        return 'low';
      default:
        return 'normal';
    }
  }

  /**
   * Map notification priority to event priority
   * @param priority Notification priority
   * @returns Event priority
   */
  private mapToPriorityEnum(priority: NotificationPriority): EventPriority {
    switch (priority) {
      case NotificationPriority.CRITICAL:
        return EventPriority.CRITICAL;
      case NotificationPriority.HIGH:
        return EventPriority.HIGH;
      case NotificationPriority.MEDIUM:
        return EventPriority.MEDIUM;
      case NotificationPriority.LOW:
        return EventPriority.LOW;
      default:
        return EventPriority.MEDIUM;
    }
  }

  /**
   * Map notification channels to event channels
   * @param channels Notification channels
   * @returns Event channels
   */
  private mapToChannelEnum(channels: NotificationChannel[]): EventChannel[] {
    return channels.map((channel) => {
      switch (channel) {
        case NotificationChannel.EMAIL:
          return EventChannel.EMAIL;
        case NotificationChannel.SMS:
          return EventChannel.SMS;
        case NotificationChannel.TELEGRAM:
          return EventChannel.TELEGRAM;
        case NotificationChannel.PUSH:
          return EventChannel.PUSH;
        case NotificationChannel.DASHBOARD:
          return EventChannel.DASHBOARD;
        default:
          return EventChannel.DASHBOARD;
      }
    });
  }

  /**
   * Map notification options to notification type
   * @param options Notification options
   * @returns Notification type
   */
  private mapNotificationType(
    options: NotificationOptions
  ): 'transaction' | 'merchant' | 'subscription' | 'system' {
    if (options.metadata?.transactionId) {
      return 'transaction';
    } else if (options.merchantId && !options.userId) {
      return 'merchant';
    } else if (options.metadata?.subscriptionId || options.metadata?.planId) {
      return 'subscription';
    } else {
      return 'system';
    }
  }

  /**
   * Get user notification preferences
   * @param userId User ID
   * @returns User notification preferences
   */
  public async getUserNotificationPreferences(userId: string): Promise<unknown[]> {
    try {
      const preferences: unknown = await prisma.userNotificationPreference.findMany({
        where: { userId },
      });

      return preferences;
    } catch (error) {
      logger.error('Error getting user notification preferences', { error, userId });
      return [];
    }
  }

  /**
   * Update user notification preferences
   * @param userId User ID
   * @param channel Notification channel
   * @param enabled Whether the channel is enabled
   * @param channelData Channel-specific data
   * @returns Success status
   */
  public async updateUserNotificationPreferences(
    userId: string,
    channel: NotificationChannel,
    enabled: boolean,
    channelData?: Record<string, unknown>
  ): Promise<boolean> {
    try {
      // Check if preference exists
      const existingPref: unknown = await prisma.userNotificationPreference.findFirst({
        where: {
          userId,
          channel,
        },
      });

      if (existingPref) {
        // Update existing preference
        await prisma.userNotificationPreference.update({
          where: { id: existingPref.id },
          data: {
            enabled,
            channelData: channelData || existingPref.channelData,
          },
        });
      } else {
        // Create new preference
        await prisma.userNotificationPreference.create({
          data: {
            userId,
            channel,
            enabled,
            channelData: channelData ?? {},
          },
        });
      }

      return true;
    } catch (error) {
      logger.error('Error updating user notification preferences', {
        error,
        userId,
        channel,
      });
      return false;
    }
  }

  /**
   * Get merchant notification preferences
   * @param merchantId Merchant ID
   * @returns Merchant notification preferences
   */
  public async getMerchantNotificationPreferences(merchantId: string): Promise<unknown[]> {
    try {
      const preferences: unknown = await prisma.merchantNotificationPreference.findMany({
        where: { merchantId },
      });

      return preferences;
    } catch (error) {
      logger.error('Error getting merchant notification preferences', { error, merchantId });
      return [];
    }
  }

  /**
   * Update merchant notification preferences
   * @param merchantId Merchant ID
   * @param channel Notification channel
   * @param enabled Whether the channel is enabled
   * @param channelData Channel-specific data
   * @returns Success status
   */
  public async updateMerchantNotificationPreferences(
    merchantId: string,
    channel: NotificationChannel,
    enabled: boolean,
    channelData?: Record<string, unknown>
  ): Promise<boolean> {
    try {
      // Check if preference exists
      const existingPref: unknown = await prisma.merchantNotificationPreference.findFirst({
        where: {
          merchantId,
          channel,
        },
      });

      if (existingPref) {
        // Update existing preference
        await prisma.merchantNotificationPreference.update({
          where: { id: existingPref.id },
          data: {
            enabled,
            channelData: channelData || existingPref.channelData,
          },
        });
      } else {
        // Create new preference
        await prisma.merchantNotificationPreference.create({
          data: {
            merchantId,
            channel,
            enabled,
            channelData: channelData ?? {},
          },
        });
      }

      return true;
    } catch (error) {
      logger.error('Error updating merchant notification preferences', {
        error,
        merchantId,
        channel,
      });
      return false;
    }
  }
}
