"use strict";
/**
 * Fraud Detection Controller
 *
 * Modular controller for fraud detection operations.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.FraudDetectionController = void 0;
const base_controller_1 = require("../base.controller");
const asyncHandler_1 = require("../../utils/asyncHandler");
const prisma = __importStar(require("../../lib/prisma"));
const FraudDetectionAuthService_1 = require("./services/FraudDetectionAuthService");
const FraudDetectionValidationService_1 = require("./services/FraudDetectionValidationService");
const FraudDetectionBusinessService_1 = require("./services/FraudDetectionBusinessService");
const FraudDetectionResponseMapper_1 = require("./mappers/FraudDetectionResponseMapper");
/**
 * Modular Fraud Detection Controller
 */
class FraudDetectionController extends base_controller_1.BaseController {
    constructor() {
        super();
        /**
         * Assess transaction risk
         */
        this.assessTransactionRisk = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Authorization
                const authContext = this.authService.createAuthorizationContext(req.user, 'risk_assessment', 'assess_risk');
                const permission = await this.authService.checkPermission(authContext);
                if (!permission.allowed) {
                    this.authService.handleAuthorizationError(permission);
                }
                // Validation
                const validatedData = this.validationService.validateAssessTransactionRisk(req.body);
                // Business logic
                const assessment = await this.businessService.assessTransactionRisk(validatedData);
                // Response
                FraudDetectionResponseMapper_1.FraudDetectionResponseMapper.sendRiskAssessment(res, assessment);
            }
            catch (error) {
                FraudDetectionResponseMapper_1.FraudDetectionResponseMapper.sendError(res, error);
            }
        });
        /**
         * Get transaction risk assessment
         */
        this.getTransactionRiskAssessment = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Authorization
                const authContext = this.authService.createAuthorizationContext(req.user, 'risk_assessment', 'view_assessment', req.params.transactionId);
                const permission = await this.authService.checkPermission(authContext);
                if (!permission.allowed) {
                    this.authService.handleAuthorizationError(permission);
                }
                // Validation
                const transactionId = this.validationService.validateTransactionId(req.params.transactionId);
                // Business logic
                const assessment = await this.businessService.getTransactionRiskAssessment(transactionId);
                // Response
                FraudDetectionResponseMapper_1.FraudDetectionResponseMapper.sendTransactionRiskAssessment(res, assessment);
            }
            catch (error) {
                FraudDetectionResponseMapper_1.FraudDetectionResponseMapper.sendError(res, error);
            }
        });
        /**
         * Get merchant fraud configuration
         */
        this.getMerchantFraudConfig = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Authorization
                const authContext = this.authService.createAuthorizationContext(req.user, 'fraud_config', 'view_config', req.params.merchantId);
                const permission = await this.authService.checkPermission(authContext);
                if (!permission.allowed) {
                    this.authService.handleAuthorizationError(permission);
                }
                // Validation
                const merchantId = this.validationService.validateMerchantId(req.params.merchantId);
                // Business logic
                const config = await this.businessService.getMerchantFraudConfig(merchantId);
                // Response
                FraudDetectionResponseMapper_1.FraudDetectionResponseMapper.sendFraudConfig(res, config);
            }
            catch (error) {
                FraudDetectionResponseMapper_1.FraudDetectionResponseMapper.sendError(res, error);
            }
        });
        /**
         * Update merchant fraud configuration
         */
        this.updateMerchantFraudConfig = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Authorization
                const authContext = this.authService.createAuthorizationContext(req.user, 'fraud_config', 'update_config', req.params.merchantId);
                const permission = await this.authService.checkPermission(authContext);
                if (!permission.allowed) {
                    this.authService.handleAuthorizationError(permission);
                }
                // Validation
                const merchantId = this.validationService.validateMerchantId(req.params.merchantId);
                const validatedData = this.validationService.validateUpdateFraudConfig(req.body);
                // Business logic
                const config = await this.businessService.updateMerchantFraudConfig(merchantId, validatedData);
                // Response
                FraudDetectionResponseMapper_1.FraudDetectionResponseMapper.sendFraudConfigUpdated(res, config);
            }
            catch (error) {
                FraudDetectionResponseMapper_1.FraudDetectionResponseMapper.sendError(res, error);
            }
        });
        /**
         * Get flagged transactions
         */
        this.getFlaggedTransactions = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Authorization
                const authContext = this.authService.createAuthorizationContext(req.user, 'flagged_transactions', 'view_flagged');
                const permission = await this.authService.checkPermission(authContext);
                if (!permission.allowed) {
                    this.authService.handleAuthorizationError(permission);
                }
                // Validation
                const pagination = this.validationService.validatePaginationParams(req.query);
                // Extract merchant context
                const { merchantId } = this.authService.extractMerchantContext(req);
                // Build filters
                const filters = { merchantId };
                if (req.query.riskLevel)
                    filters.riskLevel = req.query.riskLevel;
                if (req.query.isBlocked !== undefined)
                    filters.isBlocked = req.query.isBlocked === 'true';
                if (req.query.startDate)
                    filters.startDate = new Date(req.query.startDate);
                if (req.query.endDate)
                    filters.endDate = new Date(req.query.endDate);
                if (req.query.minScore)
                    filters.minScore = parseFloat(req.query.minScore);
                if (req.query.maxScore)
                    filters.maxScore = parseFloat(req.query.maxScore);
                // Business logic
                const result = await this.businessService.getFlaggedTransactions(filters, pagination);
                // Response
                FraudDetectionResponseMapper_1.FraudDetectionResponseMapper.sendFlaggedTransactionsList(res, result.transactions, result.total, pagination.page, pagination.limit);
            }
            catch (error) {
                FraudDetectionResponseMapper_1.FraudDetectionResponseMapper.sendError(res, error);
            }
        });
        /**
         * Get fraud statistics
         */
        this.getFraudStatistics = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Authorization
                const authContext = this.authService.createAuthorizationContext(req.user, 'fraud_statistics', 'view_statistics');
                const permission = await this.authService.checkPermission(authContext);
                if (!permission.allowed) {
                    this.authService.handleAuthorizationError(permission);
                }
                // Validation
                const { start, end } = this.validationService.validateDateRange(req.query.startDate, req.query.endDate);
                // Extract merchant context
                const { merchantId } = this.authService.extractMerchantContext(req);
                // Business logic
                const statistics = await this.businessService.getFraudStatistics(merchantId, start, end);
                // Response
                FraudDetectionResponseMapper_1.FraudDetectionResponseMapper.sendFraudStatistics(res, statistics);
            }
            catch (error) {
                FraudDetectionResponseMapper_1.FraudDetectionResponseMapper.sendError(res, error);
            }
        });
        /**
         * Health check endpoint
         */
        this.healthCheck = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                FraudDetectionResponseMapper_1.FraudDetectionResponseMapper.sendSuccess(res, {
                    status: 'healthy',
                    timestamp: new Date(),
                    version: '1.0.0',
                    services: {
                        authorization: 'active',
                        validation: 'active',
                        business: 'active',
                        fraudDetection: 'active',
                        database: 'connected',
                    },
                }, 'Fraud Detection Controller is healthy');
            }
            catch (error) {
                FraudDetectionResponseMapper_1.FraudDetectionResponseMapper.sendError(res, error);
            }
        });
        this.authService = new FraudDetectionAuthService_1.FraudDetectionAuthService();
        this.validationService = new FraudDetectionValidationService_1.FraudDetectionValidationService();
        this.businessService = new FraudDetectionBusinessService_1.FraudDetectionBusinessService(prisma);
    }
}
exports.FraudDetectionController = FraudDetectionController;
//# sourceMappingURL=FraudDetectionController.js.map