"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AlertNotificationController = void 0;
const base_controller_1 = require("./base.controller");
const asyncHandler_1 = require("../utils/asyncHandler");
const AppError_1 = require("../utils/errors/AppError");
/**
 * AlertNotificationController
 * Controller for handling alert notifications
 */
class AlertNotificationController extends base_controller_1.BaseController {
    constructor() {
        super();
        /**
         * Get notifications for a user
         */
        this.getNotifications = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                const userId = req.user?.id;
                if (!userId) {
                    throw new AppError_1.AppError({
                        message: "Unauthorized",
                        type: ErrorType.AUTHENTICATION,
                        code: ErrorCode.INVALID_CREDENTIALS
                    });
                }
                // Return empty array for now as placeholder
                return res.status(200).json({
                    success: true,
                    data: []
                });
            }
            catch (error) {
                if (error instanceof AppError_1.AppError) {
                    throw error;
                }
                throw new AppError_1.AppError({
                    message: "Failed to get notifications",
                    type: ErrorType.INTERNAL,
                    code: ErrorCode.INTERNAL_SERVER_ERROR
                });
            }
        });
    }
}
exports.AlertNotificationController = AlertNotificationController;
exports.default = new AlertNotificationController();
//# sourceMappingURL=alert-notification.controller.js.map