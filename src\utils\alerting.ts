// jscpd:ignore-file
/**
 * Alerting Utility
 *
 * This utility provides (...args: any[]) => anys for sending alerts when critical issues occur.
 */

import { logger } from '../lib/logger';
import { config } from '../config';
import { Alert, AlertSeverity, AlertType, AlertStatus } from '../types/alert.types';

// Using imported AlertSeverity and AlertType from types/alert.types.ts

/**
 * Alert notification channels
 * Using AlertChannel from types/alert.types.ts
 */
import { AlertChannel } from '../types/alert.types';

/**
 * Alert data and notification interfaces are imported from types/alert.types.ts
 */
import { AlertData, AlertNotification } from '../types/alert.types';

// Store recent alerts for reference
const recentAlerts: AlertNotification[] = [];
const MAX_RECENT_ALERTS: number = 100;

/**
 * Generate a unique alert ID
 * @returns Unique alert ID
 */
const generateAlertId: unknown = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substring(2, 15);
};

/**
 * Send an alert via email
 * @param alert Alert data
 * @returns Promise that resolves when the alert is sent
 */
const sendEmailAlert: unknown = async (alert: AlertData): Promise<unknown> => {
  // In a real implementation, you would send an email
  // For now, just log the alert
  logger.info(`[EMAIL ALERT] ${alert.severity.toUpperCase()} - ${(alert as Error).message}`, {
    alert,
  });

  // Simulate sending an email
  await new Promise((resolve) => setTimeout(resolve, 100));
};

/**
 * Send an alert via SMS
 * @param alert Alert data
 * @returns Promise that resolves when the alert is sent
 */
const sendSmsAlert: unknown = async (alert: AlertData): Promise<unknown> => {
  // In a real implementation, you would send an SMS
  // For now, just log the alert
  logger.info(`[SMS ALERT] ${alert.severity.toUpperCase()} - ${(alert as Error).message}`, {
    alert,
  });

  // Simulate sending an SMS
  await new Promise((resolve) => setTimeout(resolve, 100));
};

/**
 * Send an alert via Slack
 * @param alert Alert data
 * @returns Promise that resolves when the alert is sent
 */
const sendSlackAlert: unknown = async (alert: AlertData): Promise<unknown> => {
  // In a real implementation, you would send a Slack message
  // For now, just log the alert
  logger.info(`[SLACK ALERT] ${alert.severity.toUpperCase()} - ${(alert as Error).message}`, {
    alert,
  });

  // Simulate sending a Slack message
  await new Promise((resolve) => setTimeout(resolve, 100));
};

/**
 * Send an alert via Telegram
 * @param alert Alert data
 * @returns Promise that resolves when the alert is sent
 */
const sendTelegramAlert: unknown = async (alert: AlertData): Promise<unknown> => {
  // In a real implementation, you would send a Telegram message
  // For now, just log the alert
  logger.info(`[TELEGRAM ALERT] ${alert.severity.toUpperCase()} - ${(alert as Error).message}`, {
    alert,
  });

  // Simulate sending a Telegram message
  await new Promise((resolve) => setTimeout(resolve, 100));
};

/**
 * Send an alert via webhook
 * @param alert Alert data
 * @returns Promise that resolves when the alert is sent
 */
const sendWebhookAlert: unknown = async (alert: AlertData): Promise<unknown> => {
  // In a real implementation, you would send a webhook request
  // For now, just log the alert
  logger.info(`[WEBHOOK ALERT] ${alert.severity.toUpperCase()} - ${(alert as Error).message}`, {
    alert,
  });

  // Simulate sending a webhook request
  await new Promise((resolve) => setTimeout(resolve, 100));
};

/**
 * Send an alert via the specified channels
 * @param alert Alert data
 * @param channels Alert channels
 * @returns Promise that resolves when the alert is sent
 */
export const sendAlert: unknown = async (
  alert: AlertData,
  channels: AlertChannel[] = [AlertChannel.EMAIL]
): Promise<AlertNotification> => {
  // Create an alert notification
  const notification: AlertNotification = {
    id: generateAlertId(),
    alert,
    channels,
    status: 'pending',
    createdAt: new Date(),
  };

  try {
    // Send the alert via each channel
    const sendPromises = channels.map((channel: unknown) => {
      switch (channel) {
        case AlertChannel.EMAIL:
          return sendEmailAlert(alert);
        case AlertChannel.SMS:
          return sendSmsAlert(alert);
        case AlertChannel.SLACK:
          return sendSlackAlert(alert);
        case AlertChannel.TELEGRAM:
          return sendTelegramAlert(alert);
        case AlertChannel.WEBHOOK:
          return sendWebhookAlert(alert);
        default:
          return Promise.resolve();
      }
    });

    // Wait for all alerts to be sent
    await Promise.all(sendPromises);

    // Update the notification status
    notification.status = 'sent';
    notification.sentAt = new Date();

    // Log the alert
    logger.info(`Alert sent: ${(alert as Error).message}`, {
      alertId: notification.id,
      severity: alert.severity,
      type: alert.type,
      channels,
    });
  } catch (error: any) {
    // Update the notification status
    notification.status = 'failed';
    notification.error = error instanceof Error ? (error as Error).message : 'Unknown error';

    // Log the error
    logger.error(`Failed to send alert: ${(alert as Error).message}`, {
      alertId: notification.id,
      severity: alert.severity,
      type: alert.type,
      channels,
      error,
    });
  }

  // Add to recent alerts
  recentAlerts.unshift(notification);

  // Trim to max size
  if (recentAlerts.length > MAX_RECENT_ALERTS) {
    recentAlerts.pop();
  }

  return notification;
};

/**
 * Get recent alerts
 * @returns Recent alerts
 */
export const getRecentAlerts = (): AlertNotification[] => {
  return [...recentAlerts];
};

/**
 * Clear recent alerts
 */
export const clearRecentAlerts = (): any => {
  recentAlerts.length = 0;
};

/**
 * Send a system alert
 * @param message Alert message
 * @param severity Alert severity
 * @param details Additional details
 * @returns Promise that resolves when the alert is sent
 */
export const sendSystemAlert = async (
  message: string,
  severity: AlertSeverity = AlertSeverity.ERROR,
  details?: unknown
): Promise<AlertNotification> => {
  return sendAlert(
    {
      severity,
      type: AlertType.SYSTEM,
      message,
      details,
      timestamp: new Date(),
    },
    getChannelsForSeverity(severity)
  );
};

/**
 * Send a database alert
 * @param message Alert message
 * @param severity Alert severity
 * @param details Additional details
 * @returns Promise that resolves when the alert is sent
 */
export const sendDatabaseAlert = async (
  message: string,
  severity: AlertSeverity = AlertSeverity.ERROR,
  details?: unknown
): Promise<AlertNotification> => {
  return sendAlert(
    {
      severity,
      type: AlertType.DATABASE,
      message,
      details,
      timestamp: new Date(),
    },
    getChannelsForSeverity(severity)
  );
};

/**
 * Send a security alert
 * @param message Alert message
 * @param severity Alert severity
 * @param details Additional details
 * @returns Promise that resolves when the alert is sent
 */
export const sendSecurityAlert = async (
  message: string,
  severity: AlertSeverity = AlertSeverity.ERROR,
  details?: unknown
): Promise<AlertNotification> => {
  return sendAlert(
    {
      severity,
      type: AlertType.SECURITY,
      message,
      details,
      timestamp: new Date(),
    },
    getChannelsForSeverity(severity)
  );
};

/**
 * Get alert channels for a severity level
 * @param severity Alert severity
 * @returns Alert channels
 */
const getChannelsForSeverity = (severity: AlertSeverity): AlertChannel[] => {
  switch (severity) {
    case AlertSeverity.CRITICAL:
      return [AlertChannel.EMAIL, AlertChannel.SMS, AlertChannel.SLACK, AlertChannel.TELEGRAM];
    case AlertSeverity.ERROR:
      return [AlertChannel.EMAIL, AlertChannel.SLACK, AlertChannel.TELEGRAM];
    case AlertSeverity.WARNING:
      return [AlertChannel.EMAIL, AlertChannel.SLACK];
    case AlertSeverity.INFO:
    default:
      return [AlertChannel.EMAIL];
  }
};

export default {
  sendAlert,
  getRecentAlerts,
  clearRecentAlerts,
  sendSystemAlert,
  sendDatabaseAlert,
  sendSecurityAlert,
  AlertSeverity,
  AlertType,
  AlertChannel,
};
