/**
 * Identity Verification Validation Service
 *
 * Handles input validation for identity verification operations.
 */
import { EthereumSignatureRequest, ERC1484IdentityRequest, ERC725IdentityRequest, ENSVerificationRequest, PolygonIDRequest, WorldcoinRequest, UnstoppableDomainsRequest, BlockchainVerificationRequest, CompleteBlockchainVerificationRequest, AddClaimRequest } from '../types/IdentityVerificationControllerTypes';
/**
 * Validation service for identity verification
 */
export declare class IdentityVerificationValidationService {
    /**
     * Validate Ethereum signature request
     */
    validateEthereumSignature(data: any): EthereumSignatureRequest;
    /**
     * Validate ERC-1484 identity request
     */
    validateERC1484Identity(data: any): ERC1484IdentityRequest;
    /**
     * Validate ERC-725 identity request
     */
    validateERC725Identity(data: any): ERC725IdentityRequest;
    /**
     * Validate ENS verification request
     */
    validateENSVerification(data: any): ENSVerificationRequest;
    /**
     * Validate Polygon ID request
     */
    validatePolygonID(data: any): PolygonIDRequest;
    /**
     * Validate Worldcoin request
     */
    validateWorldcoin(data: any): WorldcoinRequest;
    /**
     * Validate Unstoppable Domains request
     */
    validateUnstoppableDomains(data: any): UnstoppableDomainsRequest;
    /**
     * Validate blockchain verification request
     */
    validateBlockchainVerification(data: any): BlockchainVerificationRequest;
    /**
     * Validate complete blockchain verification request
     */
    validateCompleteBlockchainVerification(data: any): CompleteBlockchainVerificationRequest;
    /**
     * Validate add claim request
     */
    validateAddClaim(data: any): AddClaimRequest;
    /**
     * Validate ID parameter
     */
    validateId(id: any, fieldName?: string): string;
    /**
     * Validate pagination parameters
     */
    validatePaginationParams(query: any): {
        page: number;
        limit: number;
        sortBy?: string;
        sortOrder?: 'asc' | 'desc';
    };
    /**
     * Check if string is a valid Ethereum address
     */
    private isValidEthereumAddress;
    /**
     * Check if string is a valid signature
     */
    private isValidSignature;
    /**
     * Check if string is a valid ENS name
     */
    private isValidENSName;
    /**
     * Check if string is a valid domain
     */
    private isValidDomain;
    /**
     * Check if string is a valid UUID
     */
    private isValidUUID;
}
//# sourceMappingURL=IdentityVerificationValidationService.d.ts.map