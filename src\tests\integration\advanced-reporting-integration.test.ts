import request from 'supertest';
import { PrismaClient } from '@prisma/client';
import app from '../../index';
import jwt from 'jsonwebtoken';
import fs from 'fs';
import path from 'path';

const prisma = new PrismaClient();

describe('Advanced Reporting Integration Tests', () => {
  let authToken: string;
  let adminToken: string;
  let userId: string;
  let adminId: string;
  let merchantId: string;
  let templateId: string;
  let scheduledReportId: string;
  let savedReportId: string;
  let dashboardId: string;

  beforeAll(async () => {
    // Create test users
    const user = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: 'hashedpassword',
        firstName: 'Test',
        lastName: 'Merchant',
        role: 'MERCHANT',
      },
    });

    const admin = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: 'hashedpassword',
        firstName: 'Test',
        lastName: 'Admin',
        role: 'ADMIN',
      },
    });

    userId = user.id;
    adminId = admin.id;

    // Create test merchant
    const merchant = await prisma.merchant.create({
      data: {
        userId: user.id,
        businessName: 'Test Merchant Inc',
        businessType: 'E-commerce',
        country: 'US',
        currency: 'USD',
      },
    });

    merchantId = merchant.id;

    // Create test data
    await createTestData();

    // Generate auth tokens
    authToken = jwt.sign(
      { userId: user.id, email: user.email, role: user.role },
      process.env.JWT_SECRET ?? 'test-secret',
      { expiresIn: '1h' }
    );

    adminToken = jwt.sign(
      { userId: admin.id, email: admin.email, role: admin.role },
      process.env.JWT_SECRET ?? 'test-secret',
      { expiresIn: '1h' }
    );
  });

  afterAll(async () => {
    await cleanupTestData();
    await prisma.$disconnect();
  });

  async function createTestData() {
    // Create test transactions
    await prisma.transaction.createMany({
      data: [
        {
          reference: 'TXN001',
          amount: 100.00,
          currency: 'USD',
          status: 'COMPLETED',
          paymentMethod: 'CARD',
          merchantId,
        },
        {
          reference: 'TXN002',
          amount: 250.00,
          currency: 'USD',
          status: 'COMPLETED',
          paymentMethod: 'BANK_TRANSFER',
          merchantId,
        },
        {
          reference: 'TXN003',
          amount: 75.00,
          currency: 'USD',
          status: 'PENDING',
          paymentMethod: 'CARD',
          merchantId,
        },
      ],
    });
  }

  async function cleanupTestData() {
    // Clean up in reverse order of dependencies
    await prisma.dashboardWidget.deleteMany({});
    await prisma.dashboard.deleteMany({});
    await prisma.reportRun.deleteMany({});
    await prisma.savedReport.deleteMany({});
    await prisma.scheduledReport.deleteMany({});
    await prisma.reportTemplate.deleteMany({});
    await prisma.transaction.deleteMany({ where: { merchantId } });
    await prisma.merchant.deleteMany({ where: { userId } });
    await prisma.user.deleteMany({ where: { id: { in: [userId, adminId] } } });

    // Clean up report files
    const reportsDir = path.join(__dirname, '../../../reports');
    if (fs.existsSync(reportsDir)) {
      const files = fs.readdirSync(reportsDir);
      for (const file of files) {
        if (file.startsWith('test_') || file.includes('integration')) {
          fs.unlinkSync(path.join(reportsDir, file));
        }
      }
    }
  }

  describe('Complete Workflow Integration', () => {
    it('should complete the full advanced reporting workflow', async () => {
      // Step 1: Create a report template
      console.log('Step 1: Creating report template...');
      const templateResponse = await request(app)
        .post('/api/advanced-reports/templates')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          name: 'Integration Test Template',
          description: 'Template for integration testing',
          type: 'TRANSACTION',
          config: {
            columns: ['reference', 'amount', 'currency', 'status'],
            groupBy: ['status'],
            sortBy: 'createdAt',
            sortDirection: 'desc',
          },
        })
        .expect(201);

      expect(templateResponse.body.success).toBe(true);
      templateId = templateResponse.body.data.id;
      console.log(`✅ Template created: ${templateId}`);

      // Step 2: Generate a report using the template
      console.log('Step 2: Generating report...');
      const reportResponse = await request(app)
        .post('/api/advanced-reports/generate')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          templateId,
          format: 'CSV',
          name: 'Integration Test Report',
        })
        .expect(200);

      expect(reportResponse.body.success).toBe(true);
      expect(reportResponse.body.data.rowCount).toBeGreaterThan(0);
      savedReportId = reportResponse.body.data.id;
      console.log(`✅ Report generated: ${savedReportId}`);

      // Step 3: Verify the report was saved
      console.log('Step 3: Verifying saved report...');
      const savedReportResponse = await request(app)
        .get(`/api/advanced-reports/saved/${savedReportId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(savedReportResponse.body.success).toBe(true);
      expect(savedReportResponse.body.data.id).toBe(savedReportId);
      console.log('✅ Saved report verified');

      // Step 4: Download the report
      console.log('Step 4: Downloading report...');
      const downloadResponse = await request(app)
        .get(`/api/advanced-reports/saved/${savedReportId}/download`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(downloadResponse.headers['content-type']).toContain('text/csv');
      expect(downloadResponse.headers['content-disposition']).toContain('attachment');
      console.log('✅ Report downloaded successfully');

      // Step 5: Create a scheduled report
      console.log('Step 5: Creating scheduled report...');
      const scheduledResponse = await request(app)
        .post('/api/advanced-reports/scheduled')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          name: 'Integration Test Scheduled Report',
          templateId,
          schedule: '0 0 * * 1', // Every Monday at midnight
          isActive: true,
          emailRecipients: ['<EMAIL>'],
          parameters: {
            format: 'PDF',
          },
        })
        .expect(201);

      expect(scheduledResponse.body.success).toBe(true);
      scheduledReportId = scheduledResponse.body.data.id;
      console.log(`✅ Scheduled report created: ${scheduledReportId}`);

      // Step 6: Run the scheduled report manually
      console.log('Step 6: Running scheduled report...');
      const runResponse = await request(app)
        .post(`/api/advanced-reports/scheduled/${scheduledReportId}/run`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(runResponse.body.success).toBe(true);
      console.log('✅ Scheduled report executed');

      // Step 7: Create a dashboard
      console.log('Step 7: Creating dashboard...');
      const dashboardResponse = await request(app)
        .post('/api/dashboards')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          name: 'Integration Test Dashboard',
          description: 'Dashboard for integration testing',
          layout: { columns: 2, rows: 2 },
          isPublic: false,
        })
        .expect(201);

      expect(dashboardResponse.body.success).toBe(true);
      dashboardId = dashboardResponse.body.data.id;
      console.log(`✅ Dashboard created: ${dashboardId}`);

      // Step 8: Add a widget to the dashboard
      console.log('Step 8: Adding dashboard widget...');
      const widgetResponse = await request(app)
        .post(`/api/dashboards/${dashboardId}/widgets`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          title: 'Transaction Chart',
          type: 'CHART',
          config: {
            chartType: 'line',
            dataSource: 'transactions',
          },
          width: 2,
          height: 1,
        })
        .expect(201);

      expect(widgetResponse.body.success).toBe(true);
      console.log('✅ Dashboard widget added');

      // Step 9: Check system health
      console.log('Step 9: Checking system health...');
      const healthResponse = await request(app)
        .get('/api/health/reports')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(healthResponse.body.status).toBe('healthy');
      console.log('✅ System health verified');

      // Step 10: Get system metrics
      console.log('Step 10: Getting system metrics...');
      const metricsResponse = await request(app)
        .get('/api/health/metrics')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(metricsResponse.body.success).toBe(true);
      expect(metricsResponse.body.data.systemHealth).toBeDefined();
      expect(metricsResponse.body.data.performanceStats).toBeDefined();
      console.log('✅ System metrics retrieved');

      console.log('🎉 Complete workflow integration test passed!');
    }, 60000); // 60 second timeout for the full workflow
  });

  describe('Performance and Load Testing', () => {
    it('should handle multiple concurrent report generations', async () => {
      console.log('Testing concurrent report generation...');

      const promises = [];
      for (let i = 0; i < 5; i++) {
        promises.push(
          request(app)
            .post('/api/advanced-reports/generate')
            .set('Authorization', `Bearer ${authToken}`)
            .send({
              type: 'TRANSACTION',
              format: 'CSV',
              name: `Concurrent Test Report ${i}`,
            })
        );
      }

      const responses = await Promise.all(promises);
      
      responses.forEach((response, index) => {
        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);
        console.log(`✅ Concurrent report ${index + 1} generated successfully`);
      });

      console.log('✅ Concurrent report generation test passed');
    }, 30000);

    it('should handle large dataset report generation', async () => {
      console.log('Testing large dataset report generation...');

      // Create additional test data
      const largeDataset = [];
      for (let i = 0; i < 100; i++) {
        largeDataset.push({
          reference: `BULK_TXN_${i.toString().padStart(3, '0')}`,
          amount: Math.random() * 1000,
          currency: 'USD',
          status: i % 3 === 0 ? 'COMPLETED' : i % 3 === 1 ? 'PENDING' : 'FAILED',
          paymentMethod: i % 2 === 0 ? 'CARD' : 'BANK_TRANSFER',
          merchantId,
        });
      }

      await prisma.transaction.createMany({
        data: largeDataset,
      });

      const response = await request(app)
        .post('/api/advanced-reports/generate')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          type: 'TRANSACTION',
          format: 'CSV',
          name: 'Large Dataset Test Report',
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.rowCount).toBeGreaterThan(100);
      console.log(`✅ Large dataset report generated with ${response.body.data.rowCount} records`);
    }, 30000);
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle invalid report parameters gracefully', async () => {
      const response = await request(app)
        .post('/api/advanced-reports/generate')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          type: 'INVALID_TYPE',
          format: 'CSV',
        })
        .expect(500);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('Unsupported report type');
    });

    it('should prevent unauthorized access to reports', async () => {
      // Try to access a report without authentication
      await request(app)
        .get('/api/advanced-reports/saved')
        .expect(401);

      // Try to access another user's report
      const otherUserToken = jwt.sign(
        { userId: 'other-user', email: '<EMAIL>', role: 'MERCHANT' },
        process.env.JWT_SECRET ?? 'test-secret',
        { expiresIn: '1h' }
      );

      if (savedReportId) {
        await request(app)
          .get(`/api/advanced-reports/saved/${savedReportId}`)
          .set('Authorization', `Bearer ${otherUserToken}`)
          .expect(404); // Should not find the report
      }
    });

    it('should handle file system errors gracefully', async () => {
      // Try to download a non-existent report
      await request(app)
        .get('/api/advanced-reports/saved/non-existent-id/download')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);
    });
  });

  describe('Admin Functionality', () => {
    it('should allow admin to access all reports', async () => {
      const response = await request(app)
        .get('/api/advanced-reports/saved')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data)).toBe(true);
    });

    it('should allow admin to trigger system cleanup', async () => {
      const response = await request(app)
        .post('/api/health/cleanup')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          maxAgeHours: 1, // Very short for testing
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.deletedFiles).toBeGreaterThanOrEqual(0);
    });
  });
});
