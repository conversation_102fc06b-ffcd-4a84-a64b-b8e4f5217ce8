{"version": 3, "file": "IdentityVerificationController.js", "sourceRoot": "", "sources": ["../../../../src/controllers/identity-verification/IdentityVerificationController.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;;;;AAGH,wDAAoD;AACpD,2DAAwD;AACxD,gFAAmF;AACnF,8DAAsC;AAEtC,gGAA6F;AAC7F,4GAAyG;AACzG,qGAAkG;AAIlG;;GAEG;AACH,MAAa,8BAA+B,SAAQ,gCAAc;IAKhE;QACE,KAAK,EAAE,CAAC;QAMV;;WAEG;QACH,4BAAuB,GAAG,IAAA,2BAAY,EAAC,KAAK,EAAE,GAAyB,EAAE,GAAa,EAAE,EAAE;YACxF,IAAI,CAAC;gBACH,gBAAgB;gBAChB,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,0BAA0B,CAC7D,GAAG,CAAC,IAAI,EACR,cAAc,EACd,QAAQ,CACT,CAAC;gBACF,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;gBACvE,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;oBACxB,IAAI,CAAC,WAAW,CAAC,wBAAwB,CAAC,UAAU,CAAC,CAAC;gBACxD,CAAC;gBAED,aAAa;gBACb,IAAI,CAAC,iBAAiB,CAAC,yBAAyB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBAC3D,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;gBAEzC,iBAAiB;gBACjB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,uBAAuB,CAAC;oBAChE,OAAO,EAAE,aAAa,CAAC,OAAO;oBAC9B,OAAO,EAAE,aAAa,CAAC,OAAO;oBAC9B,SAAS,EAAE,aAAa,CAAC,SAAS;oBAClC,MAAM;oBACN,UAAU;iBACX,CAAC,CAAC;gBAEH,WAAW;gBACX,uEAAkC,CAAC,sBAAsB,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;YACzE,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,uEAAkC,CAAC,SAAS,CAAC,GAAG,EAAE,KAAc,CAAC,CAAC;YACpE,CAAC;QACH,CAAC,CAAC,CAAC;QAEH;;WAEG;QACH,0BAAqB,GAAG,IAAA,2BAAY,EAAC,KAAK,EAAE,GAAyB,EAAE,GAAa,EAAE,EAAE;YACtF,IAAI,CAAC;gBACH,gBAAgB;gBAChB,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,0BAA0B,CAC7D,GAAG,CAAC,IAAI,EACR,cAAc,EACd,QAAQ,CACT,CAAC;gBACF,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;gBACvE,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;oBACxB,IAAI,CAAC,WAAW,CAAC,wBAAwB,CAAC,UAAU,CAAC,CAAC;gBACxD,CAAC;gBAED,aAAa;gBACb,IAAI,CAAC,iBAAiB,CAAC,uBAAuB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBACzD,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;gBAEzC,wDAAwD;gBACxD,MAAM,MAAM,GAAG;oBACb,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,0CAA0C;oBACnD,cAAc,EAAE,aAAa;oBAC7B,MAAM,EAAE,SAAS;iBAClB,CAAC;gBAEF,WAAW;gBACX,uEAAkC,CAAC,sBAAsB,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;YACzE,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,uEAAkC,CAAC,SAAS,CAAC,GAAG,EAAE,KAAc,CAAC,CAAC;YACpE,CAAC;QACH,CAAC,CAAC,CAAC;QAEH;;WAEG;QACH,yBAAoB,GAAG,IAAA,2BAAY,EAAC,KAAK,EAAE,GAAyB,EAAE,GAAa,EAAE,EAAE;YACrF,IAAI,CAAC;gBACH,gBAAgB;gBAChB,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,0BAA0B,CAC7D,GAAG,CAAC,IAAI,EACR,cAAc,EACd,QAAQ,CACT,CAAC;gBACF,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;gBACvE,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;oBACxB,IAAI,CAAC,WAAW,CAAC,wBAAwB,CAAC,UAAU,CAAC,CAAC;gBACxD,CAAC;gBAED,aAAa;gBACb,IAAI,CAAC,iBAAiB,CAAC,sBAAsB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBACxD,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;gBAEzC,uDAAuD;gBACvD,MAAM,MAAM,GAAG;oBACb,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,yCAAyC;oBAClD,cAAc,EAAE,aAAa;oBAC7B,MAAM,EAAE,QAAQ;iBACjB,CAAC;gBAEF,WAAW;gBACX,uEAAkC,CAAC,sBAAsB,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;YACzE,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,uEAAkC,CAAC,SAAS,CAAC,GAAG,EAAE,KAAc,CAAC,CAAC;YACpE,CAAC;QACH,CAAC,CAAC,CAAC;QAEH;;WAEG;QACH,cAAS,GAAG,IAAA,2BAAY,EAAC,KAAK,EAAE,GAAyB,EAAE,GAAa,EAAE,EAAE;YAC1E,IAAI,CAAC;gBACH,gBAAgB;gBAChB,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,0BAA0B,CAC7D,GAAG,CAAC,IAAI,EACR,cAAc,EACd,QAAQ,CACT,CAAC;gBACF,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;gBACvE,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;oBACxB,IAAI,CAAC,WAAW,CAAC,wBAAwB,CAAC,UAAU,CAAC,CAAC;gBACxD,CAAC;gBAED,aAAa;gBACb,IAAI,CAAC,iBAAiB,CAAC,uBAAuB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBACzD,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;gBAEzC,oDAAoD;gBACpD,MAAM,MAAM,GAAG;oBACb,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,sCAAsC;oBAC/C,cAAc,EAAE,aAAa;oBAC7B,MAAM,EAAE,KAAK;iBACd,CAAC;gBAEF,WAAW;gBACX,uEAAkC,CAAC,sBAAsB,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;YACzE,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,uEAAkC,CAAC,SAAS,CAAC,GAAG,EAAE,KAAc,CAAC,CAAC;YACpE,CAAC;QACH,CAAC,CAAC,CAAC;QAEH;;WAEG;QACH,oBAAe,GAAG,IAAA,2BAAY,EAAC,KAAK,EAAE,GAAyB,EAAE,GAAa,EAAE,EAAE;YAChF,IAAI,CAAC;gBACH,gBAAgB;gBAChB,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,0BAA0B,CAC7D,GAAG,CAAC,IAAI,EACR,cAAc,EACd,QAAQ,CACT,CAAC;gBACF,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;gBACvE,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;oBACxB,IAAI,CAAC,WAAW,CAAC,wBAAwB,CAAC,UAAU,CAAC,CAAC;gBACxD,CAAC;gBAED,aAAa;gBACb,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBACnD,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;gBAEzC,2DAA2D;gBAC3D,MAAM,MAAM,GAAG;oBACb,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,6CAA6C;oBACtD,cAAc,EAAE,aAAa;oBAC7B,MAAM,EAAE,WAAW;iBACpB,CAAC;gBAEF,WAAW;gBACX,uEAAkC,CAAC,sBAAsB,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;YACzE,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,uEAAkC,CAAC,SAAS,CAAC,GAAG,EAAE,KAAc,CAAC,CAAC;YACpE,CAAC;QACH,CAAC,CAAC,CAAC;QAEH;;WAEG;QACH,oBAAe,GAAG,IAAA,2BAAY,EAAC,KAAK,EAAE,GAAyB,EAAE,GAAa,EAAE,EAAE;YAChF,IAAI,CAAC;gBACH,gBAAgB;gBAChB,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,0BAA0B,CAC7D,GAAG,CAAC,IAAI,EACR,cAAc,EACd,QAAQ,CACT,CAAC;gBACF,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;gBACvE,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;oBACxB,IAAI,CAAC,WAAW,CAAC,wBAAwB,CAAC,UAAU,CAAC,CAAC;gBACxD,CAAC;gBAED,aAAa;gBACb,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBACnD,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;gBAEzC,0DAA0D;gBAC1D,MAAM,MAAM,GAAG;oBACb,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,4CAA4C;oBACrD,cAAc,EAAE,aAAa;oBAC7B,MAAM,EAAE,WAAW;iBACpB,CAAC;gBAEF,WAAW;gBACX,uEAAkC,CAAC,sBAAsB,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;YACzE,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,uEAAkC,CAAC,SAAS,CAAC,GAAG,EAAE,KAAc,CAAC,CAAC;YACpE,CAAC;QACH,CAAC,CAAC,CAAC;QAEH;;WAEG;QACH,6BAAwB,GAAG,IAAA,2BAAY,EAAC,KAAK,EAAE,GAAyB,EAAE,GAAa,EAAE,EAAE;YACzF,IAAI,CAAC;gBACH,gBAAgB;gBAChB,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,0BAA0B,CAC7D,GAAG,CAAC,IAAI,EACR,cAAc,EACd,QAAQ,CACT,CAAC;gBACF,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;gBACvE,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;oBACxB,IAAI,CAAC,WAAW,CAAC,wBAAwB,CAAC,UAAU,CAAC,CAAC;gBACxD,CAAC;gBAED,aAAa;gBACb,IAAI,CAAC,iBAAiB,CAAC,0BAA0B,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBAC5D,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;gBAEzC,oEAAoE;gBACpE,MAAM,MAAM,GAAG;oBACb,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,sDAAsD;oBAC/D,cAAc,EAAE,aAAa;oBAC7B,MAAM,EAAE,oBAAoB;iBAC7B,CAAC;gBAEF,WAAW;gBACX,uEAAkC,CAAC,sBAAsB,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;YACzE,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,uEAAkC,CAAC,SAAS,CAAC,GAAG,EAAE,KAAc,CAAC,CAAC;YACpE,CAAC;QACH,CAAC,CAAC,CAAC;QAEH;;WAEG;QACH,wBAAmB,GAAG,IAAA,2BAAY,EAAC,KAAK,EAAE,GAAyB,EAAE,GAAa,EAAE,EAAE;YACpF,IAAI,CAAC;gBACH,gBAAgB;gBAChB,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,0BAA0B,CAC7D,GAAG,CAAC,IAAI,EACR,cAAc,EACd,MAAM,EACN,GAAG,CAAC,MAAM,CAAC,EAAE,CACd,CAAC;gBACF,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;gBACvE,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;oBACxB,IAAI,CAAC,WAAW,CAAC,wBAAwB,CAAC,UAAU,CAAC,CAAC;gBACxD,CAAC;gBAED,aAAa;gBACb,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,iBAAiB,CAAC,CAAC;gBAE3F,iBAAiB;gBACjB,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,mBAAmB,CAAC,cAAc,CAAC,CAAC;gBAEpF,WAAW;gBACX,MAAM,uBAAuB,GAC3B,uEAAkC,CAAC,qBAAqB,CAAC,YAAY,CAAC,CAAC;gBACzE,uEAAkC,CAAC,gBAAgB,CAAC,GAAG,EAAE,uBAAuB,CAAC,CAAC;YACpF,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,uEAAkC,CAAC,SAAS,CAAC,GAAG,EAAE,KAAc,CAAC,CAAC;YACpE,CAAC;QACH,CAAC,CAAC,CAAC;QAEH;;WAEG;QACH,4BAAuB,GAAG,IAAA,2BAAY,EAAC,KAAK,EAAE,GAAyB,EAAE,GAAa,EAAE,EAAE;YACxF,IAAI,CAAC;gBACH,gBAAgB;gBAChB,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,0BAA0B,CAC7D,GAAG,CAAC,IAAI,EACR,cAAc,EACd,MAAM,CACP,CAAC;gBACF,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;gBACvE,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;oBACxB,IAAI,CAAC,WAAW,CAAC,wBAAwB,CAAC,UAAU,CAAC,CAAC;gBACxD,CAAC;gBAED,cAAc;gBACd,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;gBAC5D,IAAI,CAAC,MAAM,EAAE,CAAC;oBACZ,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;gBACzC,CAAC;gBAED,iBAAiB;gBACjB,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC;gBAEjF,WAAW;gBACX,MAAM,wBAAwB,GAAG,aAAa,CAAC,GAAG,CAChD,uEAAkC,CAAC,qBAAqB,CACzD,CAAC;gBACF,uEAAkC,CAAC,qBAAqB,CACtD,GAAG,EACH,wBAAwB,EACxB,wBAAwB,CAAC,MAAM,CAChC,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,uEAAkC,CAAC,SAAS,CAAC,GAAG,EAAE,KAAc,CAAC,CAAC;YACpE,CAAC;QACH,CAAC,CAAC,CAAC;QAEH;;WAEG;QACH,gBAAW,GAAG,IAAA,2BAAY,EAAC,KAAK,EAAE,GAAyB,EAAE,GAAa,EAAE,EAAE;YAC5E,IAAI,CAAC;gBACH,uEAAkC,CAAC,WAAW,CAC5C,GAAG,EACH;oBACE,MAAM,EAAE,SAAS;oBACjB,SAAS,EAAE,IAAI,IAAI,EAAE;oBACrB,OAAO,EAAE,OAAO;oBAChB,QAAQ,EAAE;wBACR,aAAa,EAAE,QAAQ;wBACvB,UAAU,EAAE,QAAQ;wBACpB,eAAe,EAAE,QAAQ;qBAC1B;iBACF,EACD,6CAA6C,CAC9C,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,uEAAkC,CAAC,SAAS,CAAC,GAAG,EAAE,KAAc,CAAC,CAAC;YACpE,CAAC;QACH,CAAC,CAAC,CAAC;QAxVD,IAAI,CAAC,WAAW,GAAG,IAAI,iEAA+B,EAAE,CAAC;QACzD,IAAI,CAAC,iBAAiB,GAAG,IAAI,6EAAqC,EAAE,CAAC;QACrE,IAAI,CAAC,eAAe,GAAG,IAAI,mDAA2B,CAAC,gBAAa,CAAC,CAAC;IACxE,CAAC;CAsVF;AAhWD,wEAgWC"}