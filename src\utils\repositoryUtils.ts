import { Transaction } from '../types';
import { Repository } from '../types/database';

// jscpd:ignore-file
/**
 * Repository Utilities
 *
 * This file contains utility functions for repositories.
 */

/**
 * Create a where clause from filters
 * @param filters Filters
 * @returns Where clause
 */
export function createWhereClause(filters: Record<string, unknown> = {}: unknown): Record<string, unknown> {
  const where: Record<string, unknown> = {};

  Object.entries(filters).forEach(([key, value]) => {
    if (value === undefined || value === null) {
      return;
    }

    if (typeof value === 'string' && value.includes('*')) {
      // Handle wildcard search
      where[key] = {
        contains: value.replace(/\*/g, ''),
        mode: 'insensitive',
      };
    } else if (Array.isArray(value)) {
      // Handle array values
      where[key] = {
        in: value,
      };
    } else if (typeof value === 'object' && value.min !== undefined && value.max !== undefined) {
      // Handle range values
      where[key] = {
        gte: value.min,
        lte: value.max,
      };
    } else if (typeof value === 'object' && value.from !== undefined && value.to !== undefined) {
      // Handle date range
      where[key] = {
        gte: new Date(value.from),
        lte: new Date(value.to),
      };
    } else {
      // Handle simple values
      where[key] = value;
    }
  });

  return where;
}

/**
 * Create an order by clause
 * @param sort Sort field
 * @param order Sort order
 * @returns Order by clause
 */
export function createOrderByClause(sort?: string, order?: 'asc' | 'desc'): Record<string, unknown> | undefined {
  if (!sort) {
    return undefined;
  }

  return {
    [sort]: order ?? 'asc',
  };
}

/**
 * Execute a repository method with error handling
 * @param method Repository method
 * @param args Method arguments
 * @returns Method result
 */
export async function executeRepositoryMethod<T>(method: (...args: unknown[]) => Promise<T>, ...args: unknown[]): Promise<T> {
  try {
    return await method(...args);
  } catch (error) {
    console.error('Repository error:', error);
    throw error;
  }
}

/**
 * Execute a transaction
 * @param prisma Prisma client
 * @param callback Transaction callback
 * @returns Transaction result
 */
export async function executeTransaction<T>(prisma: unknown, callback: (tx: unknown) => Promise<T>): Promise<T> {
  try {
    return await prisma.$transaction(callback);
  } catch (error) {
    console.error('Transaction error:', error);
    throw error;
  }
}