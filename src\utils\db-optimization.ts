// jscpd:ignore-file
/**
 * Database Query Optimization Utility
 * Re-exports from shared DatabaseUtils to eliminate duplication
 */

import { DatabaseUtils } from '../utils';
import { PrismaClient } from "@prisma/client";
import { logger } from "../lib/logger";
import prisma from "../lib/prisma";
import { PrismaClient } from "@prisma/client";
import { logger } from "../lib/logger";

/**
 * Query performance metrics
 */
interface QueryMetrics {
  query: string;
  params: Record<string, string>;
  duration: number;
  timestamp: Date;
}

/**
 * Log a slow query
 * @param metrics Query metrics
 */
export const logSlowQuery: unknown =(metrics: QueryMetrics) => {
    DatabaseUtils.logSlowQuery(metrics as any, logger);
};

/**
 * Get recent slow queries
 * @returns Recent slow queries
 */
export const getRecentSlowQueries: unknown =() => {
    return DatabaseUtils.getRecentSlowQueries() as QueryMetrics[];
};

/**
 * Clear recent slow queries
 */
export const clearRecentSlowQueries: unknown =() => {
    DatabaseUtils.clearRecentSlowQueries();
};

/**
 * Set up query performance monitoring
 * @param prismaClient Prisma client instance
 */
export const setupQueryPerformanceMonitoring: unknown =(prismaClient: PrismaClient = prisma) => {
    DatabaseUtils.setupQueryPerformanceMonitoring(prismaClient, logger);
};

/**
 * Optimize a findMany query by adding pagination and limiting fields
 * @param model Model name
 * @param args Query arguments
 * @param defaultPageSize Default page size
 * @param maxPageSize Maximum page size
 * @returns Optimized query arguments
 */
export const optimizeFindManyQuery: unknown =(
    model: string,
    args = {},
    defaultPageSize: number = 20,
    maxPageSize: number = 100
) => {
    return DatabaseUtils.optimizeFindManyQuery(model, args, defaultPageSize, maxPageSize, logger);
};

/**
 * Execute a query with timeout and retry
 * @param queryFn Function that executes the query
 * @param timeout Timeout in milliseconds
 * @param retries Number of retries
 * @returns Query result
 */
export const executeQueryWithTimeoutAndRetry: unknown =async <T>(
    queryFn: () => Promise<T>,
    timeout: number = 5000,
    retries: number = 3
): Promise<T> => {
    return DatabaseUtils.executeQueryWithTimeoutAndRetry(queryFn, timeout, retries, logger);
};

/**
 * Initialize database optimization
 */
export const initializeDatabaseOptimization: unknown =() => {
    DatabaseUtils.initializeDatabaseOptimization(prisma, logger);
};

export default {
    logSlowQuery,
    getRecentSlowQueries,
    clearRecentSlowQueries,
    setupQueryPerformanceMonitoring,
    optimizeFindManyQuery,
    executeQueryWithTimeoutAndRetry,
    initializeDatabaseOptimization
};
