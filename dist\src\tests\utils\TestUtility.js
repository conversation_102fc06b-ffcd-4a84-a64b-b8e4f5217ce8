"use strict";
// jscpd:ignore-file
/**
 * Test Utility Library
 *
 * This file provides utility functions for tests to eliminate duplication in test files.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.createMockRequest = createMockRequest;
exports.createMockResponse = createMockResponse;
exports.createMockNext = createMockNext;
exports.createMockPrismaClient = createMockPrismaClient;
exports.testController = testController;
exports.testService = testService;
exports.testRepository = testRepository;
exports.testControllerSuite = testControllerSuite;
exports.testServiceSuite = testServiceSuite;
exports.testRepositorySuite = testRepositorySuite;
exports.testMiddleware = testMiddleware;
exports.testValidator = testValidator;
exports.testUtility = testUtility;
exports.testMiddlewareSuite = testMiddlewareSuite;
exports.testValidatorSuite = testValidatorSuite;
exports.testUtilitySuite = testUtilitySuite;
exports.testModule = testModule;
const mockModelFactory_1 = require("../shared/test/mockModelFactory");
/**
 * Create a mock request
 * @param options Request options
 * @returns Mock request
 */
function createMockRequest(options = {}) {
    return {
        params: options.params || {},
        query: options.query || {},
        body: options.body || {},
        headers: options.headers || {},
        user: options.user || null,
    };
}
/**
 * Create a mock response
 * @returns Mock response
 */
function createMockResponse() {
    const res = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn().mockReturnThis(),
        send: jest.fn().mockReturnThis(),
        end: jest.fn().mockReturnThis(),
        locals: {},
    };
    return res;
}
/**
 * Create a mock next function
 * @returns Mock next function
 */
function createMockNext() {
    return jest.fn();
}
/**
 * Create a mock database model
 * @returns Mock database model
 */
function createMockModel() {
    return (0, mockModelFactory_1.mockModelFactory)();
}
/**
 * Create a mock Prisma client
 * @returns Mock Prisma client
 */
function createMockPrismaClient() {
    // Define models
    const models = [
        'user',
        'merchant',
        'transaction',
        'paymentMethod',
        'alert',
        'notification',
        'webhook',
        'subscription',
        'payment',
        'verification',
        'audit',
        'setting',
        'role',
        'permission',
    ];
    // Create mock Prisma client
    const mockPrisma = {
        $connect: jest.fn(),
        $disconnect: jest.fn(),
        $transaction: jest.fn((callback) => callback(mockPrisma)),
    };
    // Add models to mock Prisma client
    models.forEach((model) => {
        mockPrisma[model] = createMockModel();
    });
    return mockPrisma;
}
/**
 * Test controller
 * @param controller Controller to test
 * @param method Method to test
 * @param options Test options
 * @returns Test result
 */
async function testController(controller, method, options = {}) {
    const req = options.req || createMockRequest();
    const res = options.res || createMockResponse();
    const next = options.next || createMockNext();
    if (options.beforeEach) {
        options.beforeEach();
    }
    if (options.setup) {
        options.setup(controller);
    }
    try {
        await controller[method](req, res, next);
        if (options.expectedStatus) {
            expect(res.status).toHaveBeenCalledWith(options.expectedStatus);
        }
        if (options.expectedResponse) {
            expect(res.json).toHaveBeenCalledWith(options.expectedResponse);
        }
        if (options.cleanup) {
            options.cleanup(controller);
        }
        if (options.afterEach) {
            options.afterEach();
        }
    }
    catch (error) {
        if (options.expectedError) {
            expect(error).toEqual(options.expectedError);
        }
        else {
            throw error;
        }
    }
    return { req, res, next };
}
/**
 * Test service
 * @param service Service to test
 * @param method Method to test
 * @param options Test options
 * @returns Test result
 */
async function testService(service, method, options = {}) {
    const args = options.args || [];
    if (options.beforeEach) {
        options.beforeEach();
    }
    if (options.setup) {
        options.setup(service);
    }
    // Mock dependencies if provided
    if (options.mockDependencies) {
        Object.entries(options.mockDependencies).forEach(([key, value]) => {
            service[key] = value;
        });
    }
    try {
        const result = await service[method](...args);
        if (options.expectedResult !== undefined) {
            expect(result).toEqual(options.expectedResult);
        }
        if (options.cleanup) {
            options.cleanup(service);
        }
        if (options.afterEach) {
            options.afterEach();
        }
        return result;
    }
    catch (error) {
        if (options.expectedError) {
            expect(error).toEqual(options.expectedError);
        }
        else {
            throw error;
        }
    }
}
/**
 * Test repository
 * @param repository Repository to test
 * @param method Method to test
 * @param options Test options
 * @returns Test result
 */
async function testRepository(repository, method, options = {}) {
    const args = options.args || [];
    const mockPrisma = options.mockPrisma || createMockPrismaClient();
    // Replace the repository's prisma client with the mock
    repository.prisma = mockPrisma;
    if (options.beforeEach) {
        options.beforeEach();
    }
    if (options.setup) {
        options.setup(repository);
    }
    // Mock transaction if needed
    if (options.mockTransaction) {
        mockPrisma.$transaction.mockImplementation((callback) => {
            return Promise.resolve(options.mockTransactionResult || callback(mockPrisma));
        });
    }
    try {
        const result = await repository[method](...args);
        if (options.expectedResult !== undefined) {
            expect(result).toEqual(options.expectedResult);
        }
        if (options.cleanup) {
            options.cleanup(repository);
        }
        if (options.afterEach) {
            options.afterEach();
        }
        return result;
    }
    catch (error) {
        if (options.expectedError) {
            expect(error).toEqual(options.expectedError);
        }
        else {
            throw error;
        }
    }
}
/**
 * Create a test suite for a controller
 * @param name Test suite name
 * @param controllerClass Controller class
 * @param tests Test definitions
 */
function testControllerSuite(name, controllerClass, tests) {
    describe(name, () => {
        let controller;
        beforeEach(() => {
            controller = new controllerClass();
        });
        Object.entries(tests).forEach(([method, test]) => {
            it(test.description || `should test ${method}`, async () => {
                await testController(controller, method, test);
            });
        });
    });
}
/**
 * Create a test suite for a service
 * @param name Test suite name
 * @param serviceClass Service class
 * @param tests Test definitions
 * @param setupFn Setup function
 */
function testServiceSuite(name, serviceClass, tests, setupFn) {
    describe(name, () => {
        let service;
        beforeEach(() => {
            service = new serviceClass();
            if (setupFn) {
                setupFn(service);
            }
        });
        Object.entries(tests).forEach(([method, test]) => {
            it(test.description || `should test ${method}`, async () => {
                await testService(service, method, test);
            });
        });
    });
}
/**
 * Create a test suite for a repository
 * @param name Test suite name
 * @param repositoryClass Repository class
 * @param tests Test definitions
 */
function testRepositorySuite(name, repositoryClass, tests) {
    describe(name, () => {
        let repository;
        beforeEach(() => {
            repository = new repositoryClass();
        });
        Object.entries(tests).forEach(([method, test]) => {
            it(test.description || `should test ${method}`, async () => {
                await testRepository(repository, method, test);
            });
        });
    });
}
/**
 * Test middleware
 * @param middleware Middleware function
 * @param options Test options
 * @returns Test result
 */
async function testMiddleware(middleware, options = {}) {
    const req = options.req || createMockRequest();
    const res = options.res || createMockResponse();
    const next = options.next || createMockNext();
    if (options.beforeEach) {
        options.beforeEach();
    }
    if (options.setup) {
        options.setup(req, res, next);
    }
    try {
        await middleware(req, res, next);
        if (options.expectedStatus) {
            expect(res.status).toHaveBeenCalledWith(options.expectedStatus);
        }
        if (options.expectedResponse) {
            expect(res.json).toHaveBeenCalledWith(options.expectedResponse);
        }
        if (options.cleanup) {
            options.cleanup(req, res, next);
        }
        if (options.afterEach) {
            options.afterEach();
        }
    }
    catch (error) {
        if (options.expectedError) {
            expect(error).toEqual(options.expectedError);
        }
        else {
            throw error;
        }
    }
    return { req, res, next };
}
/**
 * Test validator
 * @param validator Validator function
 * @param options Test options
 * @returns Test result
 */
async function testValidator(validator, options = {}) {
    const args = options.args || [];
    const req = options.req || createMockRequest();
    const res = options.res || createMockResponse();
    const next = options.next || createMockNext();
    if (options.beforeEach) {
        options.beforeEach();
    }
    if (options.setup) {
        options.setup();
    }
    try {
        const result = await validator(req, res, next, ...args);
        if (options.expectedStatus) {
            expect(res.status).toHaveBeenCalledWith(options.expectedStatus);
        }
        if (options.expectedResponse) {
            expect(res.json).toHaveBeenCalledWith(options.expectedResponse);
        }
        if (options.cleanup) {
            options.cleanup();
        }
        if (options.afterEach) {
            options.afterEach();
        }
        return result;
    }
    catch (error) {
        if (options.expectedError) {
            expect(error).toEqual(options.expectedError);
        }
        else {
            throw error;
        }
    }
}
/**
 * Test utility function
 * @param utility Utility function
 * @param options Test options
 * @returns Test result
 */
async function testUtility(utility, options = {}) {
    const args = options.args || [];
    if (options.beforeEach) {
        options.beforeEach();
    }
    if (options.setup) {
        options.setup();
    }
    try {
        const result = await utility(...args);
        if (options.expectedResult !== undefined) {
            expect(result).toEqual(options.expectedResult);
        }
        if (options.cleanup) {
            options.cleanup();
        }
        if (options.afterEach) {
            options.afterEach();
        }
        return result;
    }
    catch (error) {
        if (options.expectedError) {
            expect(error).toEqual(options.expectedError);
        }
        else {
            throw error;
        }
    }
}
/**
 * Create a test suite for middleware
 * @param name Test suite name
 * @param middleware Middleware function
 * @param tests Test definitions
 */
function testMiddlewareSuite(name, middleware, tests) {
    describe(name, () => {
        Object.entries(tests).forEach(([testName, test]) => {
            const testFn = test.skip ? it.skip : test.only ? it.only : it;
            testFn(test.description || testName, async () => {
                if (test.beforeEach) {
                    test.beforeEach();
                }
                await testMiddleware(middleware, test);
                if (test.afterEach) {
                    test.afterEach();
                }
            });
            if (test.timeout) {
                testFn.timeout(test.timeout);
            }
        });
    });
}
/**
 * Create a test suite for validators
 * @param name Test suite name
 * @param validator Validator function
 * @param tests Test definitions
 */
function testValidatorSuite(name, validator, tests) {
    describe(name, () => {
        Object.entries(tests).forEach(([testName, test]) => {
            const testFn = test.skip ? it.skip : test.only ? it.only : it;
            testFn(test.description || testName, async () => {
                if (test.beforeEach) {
                    test.beforeEach();
                }
                await testValidator(validator, test);
                if (test.afterEach) {
                    test.afterEach();
                }
            });
            if (test.timeout) {
                testFn.timeout(test.timeout);
            }
        });
    });
}
/**
 * Create a test suite for utilities
 * @param name Test suite name
 * @param utility Utility function
 * @param tests Test definitions
 */
function testUtilitySuite(name, utility, tests) {
    describe(name, () => {
        Object.entries(tests).forEach(([testName, test]) => {
            const testFn = test.skip ? it.skip : test.only ? it.only : it;
            testFn(test.description || testName, async () => {
                if (test.beforeEach) {
                    test.beforeEach();
                }
                await testUtility(utility, test);
                if (test.afterEach) {
                    test.afterEach();
                }
            });
            if (test.timeout) {
                testFn.timeout(test.timeout);
            }
        });
    });
}
/**
 * Create a complete test suite for a module
 * @param name Module name
 * @param options Test options
 */
function testModule(name, options) {
    describe(`${name} Module`, () => {
        // Test controller
        if (options.controllerClass && options.controllerTests) {
            testControllerSuite(`${name}Controller`, options.controllerClass, options.controllerTests);
        }
        // Test service
        if (options.serviceClass && options.serviceTests) {
            testServiceSuite(`${name}Service`, options.serviceClass, options.serviceTests, options.setupServiceFn);
        }
        // Test repository
        if (options.repositoryClass && options.repositoryTests) {
            testRepositorySuite(`${name}Repository`, options.repositoryClass, options.repositoryTests);
        }
        // Test middleware
        if (options.middleware && options.middlewareTests) {
            testMiddlewareSuite(`${name}Middleware`, options.middleware, options.middlewareTests);
        }
        // Test validators
        if (options.validators && options.validatorTests) {
            Object.entries(options.validators).forEach(([validatorName, validator]) => {
                if (options.validatorTests[validatorName]) {
                    testValidatorSuite(`${validatorName}`, validator, options.validatorTests[validatorName]);
                }
            });
        }
        // Test utilities
        if (options.utilities && options.utilityTests) {
            Object.entries(options.utilities).forEach(([utilityName, utility]) => {
                if (options.utilityTests[utilityName]) {
                    testUtilitySuite(`${utilityName}`, utility, options.utilityTests[utilityName]);
                }
            });
        }
    });
}
//# sourceMappingURL=TestUtility.js.map