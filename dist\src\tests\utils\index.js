"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TestUtils = exports.DatabaseAssertions = exports.AssertionHelpers = exports.setupCustomMatchers = exports.generateMockDataWithRelationships = exports.generateMockArray = exports.generateMockTransaction = exports.generateMockMerchant = exports.generateMockUser = exports.generateRandomString = exports.generateEmail = exports.generateUUID = exports.createApiTestSuite = exports.createPerformanceTestSuite = exports.createIntegrationTestSuite = exports.createRepositoryTestSuite = exports.createServiceTestSuite = exports.createControllerTestSuite = exports.testMiddleware = exports.testRepository = exports.testService = exports.testController = exports.createMockErrorResponse = exports.createMockApiResponse = exports.createMockJwtToken = exports.createMockPrismaClient = exports.createMockNext = exports.createMockResponse = exports.createMockRequest = void 0;
/**
 * Test Utils Module
 *
 * Centralized exports for the test utility system.
 */
// Core exports
__exportStar(require("./core/TestTypes"), exports);
// Factory exports
__exportStar(require("./factories/MockFactories"), exports);
// Runner exports
__exportStar(require("./runners/TestRunners"), exports);
// Suite builder exports
__exportStar(require("./suites/TestSuiteBuilders"), exports);
// Data generator exports
__exportStar(require("./generators/DataGenerators"), exports);
// Assertion exports
__exportStar(require("./assertions/CustomAssertions"), exports);
// Convenience re-exports for common patterns
var MockFactories_1 = require("./factories/MockFactories");
Object.defineProperty(exports, "createMockRequest", { enumerable: true, get: function () { return MockFactories_1.createMockRequest; } });
Object.defineProperty(exports, "createMockResponse", { enumerable: true, get: function () { return MockFactories_1.createMockResponse; } });
Object.defineProperty(exports, "createMockNext", { enumerable: true, get: function () { return MockFactories_1.createMockNext; } });
Object.defineProperty(exports, "createMockPrismaClient", { enumerable: true, get: function () { return MockFactories_1.createMockPrismaClient; } });
Object.defineProperty(exports, "createMockJwtToken", { enumerable: true, get: function () { return MockFactories_1.createMockJwtToken; } });
Object.defineProperty(exports, "createMockApiResponse", { enumerable: true, get: function () { return MockFactories_1.createMockApiResponse; } });
Object.defineProperty(exports, "createMockErrorResponse", { enumerable: true, get: function () { return MockFactories_1.createMockErrorResponse; } });
var TestRunners_1 = require("./runners/TestRunners");
Object.defineProperty(exports, "testController", { enumerable: true, get: function () { return TestRunners_1.testController; } });
Object.defineProperty(exports, "testService", { enumerable: true, get: function () { return TestRunners_1.testService; } });
Object.defineProperty(exports, "testRepository", { enumerable: true, get: function () { return TestRunners_1.testRepository; } });
Object.defineProperty(exports, "testMiddleware", { enumerable: true, get: function () { return TestRunners_1.testMiddleware; } });
var TestSuiteBuilders_1 = require("./suites/TestSuiteBuilders");
Object.defineProperty(exports, "createControllerTestSuite", { enumerable: true, get: function () { return TestSuiteBuilders_1.createControllerTestSuite; } });
Object.defineProperty(exports, "createServiceTestSuite", { enumerable: true, get: function () { return TestSuiteBuilders_1.createServiceTestSuite; } });
Object.defineProperty(exports, "createRepositoryTestSuite", { enumerable: true, get: function () { return TestSuiteBuilders_1.createRepositoryTestSuite; } });
Object.defineProperty(exports, "createIntegrationTestSuite", { enumerable: true, get: function () { return TestSuiteBuilders_1.createIntegrationTestSuite; } });
Object.defineProperty(exports, "createPerformanceTestSuite", { enumerable: true, get: function () { return TestSuiteBuilders_1.createPerformanceTestSuite; } });
Object.defineProperty(exports, "createApiTestSuite", { enumerable: true, get: function () { return TestSuiteBuilders_1.createApiTestSuite; } });
var DataGenerators_1 = require("./generators/DataGenerators");
Object.defineProperty(exports, "generateUUID", { enumerable: true, get: function () { return DataGenerators_1.generateUUID; } });
Object.defineProperty(exports, "generateEmail", { enumerable: true, get: function () { return DataGenerators_1.generateEmail; } });
Object.defineProperty(exports, "generateRandomString", { enumerable: true, get: function () { return DataGenerators_1.generateRandomString; } });
Object.defineProperty(exports, "generateMockUser", { enumerable: true, get: function () { return DataGenerators_1.generateMockUser; } });
Object.defineProperty(exports, "generateMockMerchant", { enumerable: true, get: function () { return DataGenerators_1.generateMockMerchant; } });
Object.defineProperty(exports, "generateMockTransaction", { enumerable: true, get: function () { return DataGenerators_1.generateMockTransaction; } });
Object.defineProperty(exports, "generateMockArray", { enumerable: true, get: function () { return DataGenerators_1.generateMockArray; } });
Object.defineProperty(exports, "generateMockDataWithRelationships", { enumerable: true, get: function () { return DataGenerators_1.generateMockDataWithRelationships; } });
var CustomAssertions_1 = require("./assertions/CustomAssertions");
Object.defineProperty(exports, "setupCustomMatchers", { enumerable: true, get: function () { return CustomAssertions_1.setupCustomMatchers; } });
Object.defineProperty(exports, "AssertionHelpers", { enumerable: true, get: function () { return CustomAssertions_1.AssertionHelpers; } });
Object.defineProperty(exports, "DatabaseAssertions", { enumerable: true, get: function () { return CustomAssertions_1.DatabaseAssertions; } });
// Import functions for internal use
const MockFactories_2 = require("./factories/MockFactories");
const DataGenerators_2 = require("./generators/DataGenerators");
// Default export - main test utility class
class TestUtils {
    /**
     * Setup test environment
     */
    static setup() {
        // setupCustomMatchers(); // Commented out due to type issues
    }
    /**
     * Create a complete test context
     */
    static createTestContext(name) {
        const mockRequest = (0, MockFactories_2.createMockRequest)();
        const mockResponse = (0, MockFactories_2.createMockResponse)();
        const mockNext = (0, MockFactories_2.createMockNext)();
        const mockPrisma = (0, MockFactories_2.createMockPrismaClient)();
        const mockUser = (0, DataGenerators_2.generateMockUser)();
        const mockMerchant = (0, DataGenerators_2.generateMockMerchant)();
        const cleanup = () => {
            jest.clearAllMocks();
        };
        return {
            mockRequest,
            mockResponse,
            mockNext,
            mockPrisma,
            mockUser,
            mockMerchant,
            cleanup,
        };
    }
    /**
     * Create a test database context
     */
    static createDatabaseTestContext() {
        const mockPrisma = (0, MockFactories_2.createMockPrismaClient)();
        const testData = (0, DataGenerators_2.generateMockDataWithRelationships)();
        const seedData = async () => {
            // Mock seeding data
            mockPrisma.user.findMany.mockResolvedValue(testData.users);
            mockPrisma.merchant.findMany.mockResolvedValue(testData.merchants);
            mockPrisma.transaction.findMany.mockResolvedValue(testData.transactions);
            mockPrisma.paymentMethod.findMany.mockResolvedValue(testData.paymentMethods);
        };
        const cleanup = async () => {
            jest.clearAllMocks();
        };
        return {
            mockPrisma,
            seedData,
            cleanup,
        };
    }
    /**
     * Create an API test context
     */
    static createApiTestContext(baseUrl = 'http://localhost:3000') {
        let authToken = null;
        const request = {
            get: (path) => ({
                url: `${baseUrl}${path}`,
                method: 'GET',
                headers: authToken ? { Authorization: `Bearer ${authToken}` } : {},
            }),
            post: (path, data) => ({
                url: `${baseUrl}${path}`,
                method: 'POST',
                data,
                headers: authToken ? { Authorization: `Bearer ${authToken}` } : {},
            }),
            put: (path, data) => ({
                url: `${baseUrl}${path}`,
                method: 'PUT',
                data,
                headers: authToken ? { Authorization: `Bearer ${authToken}` } : {},
            }),
            delete: (path) => ({
                url: `${baseUrl}${path}`,
                method: 'DELETE',
                headers: authToken ? { Authorization: `Bearer ${authToken}` } : {},
            }),
        };
        const authenticate = (token) => {
            authToken = token;
        };
        const expectSuccess = (response) => {
            // expect(response).toMatchApiResponse(response.data); // Custom matcher not available
            expect(response.success).toBe(true);
            expect(response.status).toBeGreaterThanOrEqual(200);
            expect(response.status).toBeLessThan(300);
        };
        const expectError = (response, status = 400) => {
            expect(response.success).toBe(false);
            expect(response.status).toBe(status);
            expect(response.error).toBeDefined();
        };
        return {
            request,
            authenticate,
            expectSuccess,
            expectError,
        };
    }
}
exports.TestUtils = TestUtils;
//# sourceMappingURL=index.js.map