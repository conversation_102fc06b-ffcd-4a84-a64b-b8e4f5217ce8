/**
 * Identity Verification Validation Service
 *
 * Handles input validation for identity verification operations.
 */

import { AppError, ErrorType, ErrorCode } from '../../../utils/errors/AppError';
import {
  EthereumSignatureRequest,
  ERC1484IdentityRequest,
  ERC725IdentityRequest,
  ENSVerificationRequest,
  PolygonIDRequest,
  WorldcoinRequest,
  UnstoppableDomainsRequest,
  BlockchainVerificationRequest,
  CompleteBlockchainVerificationRequest,
  AddClaimRequest,
  ValidationError,
  SupportedNetwork,
} from '../types/IdentityVerificationControllerTypes';

/**
 * Validation service for identity verification
 */
export class IdentityVerificationValidationService {
  /**
   * Validate Ethereum signature request
   */
  validateEthereumSignature(data: any): EthereumSignatureRequest {
    const errors: ValidationError[] = [];

    if (!data.address) {
      errors.push({ field: 'address', message: 'Address is required' });
    } else if (typeof data.address !== 'string' || !this.isValidEthereumAddress(data.address)) {
      errors.push({
        field: 'address',
        message: 'Invalid Ethereum address format',
        value: data.address,
      });
    }

    if (!data.message) {
      errors.push({ field: 'message', message: 'Message is required' });
    } else if (typeof data.message !== 'string' || data.message.trim().length === 0) {
      errors.push({ field: 'message', message: 'Message must be a non-empty string' });
    }

    if (!data.signature) {
      errors.push({ field: 'signature', message: 'Signature is required' });
    } else if (typeof data.signature !== 'string' || !this.isValidSignature(data.signature)) {
      errors.push({
        field: 'signature',
        message: 'Invalid signature format',
        value: data.signature,
      });
    }

    if (errors.length > 0) {
      throw new AppError({
        message: 'Validation failed',
        type: ErrorType.VALIDATION,
        code: ErrorCode.INVALID_INPUT,
        details: { errors },
      });
    }

    return {
      address: data.address.toLowerCase(),
      message: data.message.trim(),
      signature: data.signature,
    };
  }

  /**
   * Validate ERC-1484 identity request
   */
  validateERC1484Identity(data: any): ERC1484IdentityRequest {
    const errors: ValidationError[] = [];

    if (!data.address) {
      errors.push({ field: 'address', message: 'Address is required' });
    } else if (!this.isValidEthereumAddress(data.address)) {
      errors.push({
        field: 'address',
        message: 'Invalid Ethereum address format',
        value: data.address,
      });
    }

    if (!data.ein) {
      errors.push({ field: 'ein', message: 'EIN is required' });
    } else if (typeof data.ein !== 'string' || !/^\d+$/.test(data.ein)) {
      errors.push({ field: 'ein', message: 'EIN must be a numeric string', value: data.ein });
    }

    if (!data.registryAddress) {
      errors.push({ field: 'registryAddress', message: 'Registry address is required' });
    } else if (!this.isValidEthereumAddress(data.registryAddress)) {
      errors.push({
        field: 'registryAddress',
        message: 'Invalid registry address format',
        value: data.registryAddress,
      });
    }

    if (errors.length > 0) {
      throw new AppError({
        message: 'Validation failed',
        type: ErrorType.VALIDATION,
        code: ErrorCode.INVALID_INPUT,
        details: { errors },
      });
    }

    return {
      address: data.address.toLowerCase(),
      ein: data.ein,
      registryAddress: data.registryAddress.toLowerCase(),
    };
  }

  /**
   * Validate ERC-725 identity request
   */
  validateERC725Identity(data: any): ERC725IdentityRequest {
    const errors: ValidationError[] = [];

    if (!data.address) {
      errors.push({ field: 'address', message: 'Address is required' });
    } else if (!this.isValidEthereumAddress(data.address)) {
      errors.push({
        field: 'address',
        message: 'Invalid Ethereum address format',
        value: data.address,
      });
    }

    if (!data.key) {
      errors.push({ field: 'key', message: 'Key is required' });
    } else if (typeof data.key !== 'string' || data.key.trim().length === 0) {
      errors.push({ field: 'key', message: 'Key must be a non-empty string' });
    }

    if (!data.value) {
      errors.push({ field: 'value', message: 'Value is required' });
    } else if (typeof data.value !== 'string' || data.value.trim().length === 0) {
      errors.push({ field: 'value', message: 'Value must be a non-empty string' });
    }

    if (errors.length > 0) {
      throw new AppError({
        message: 'Validation failed',
        type: ErrorType.VALIDATION,
        code: ErrorCode.INVALID_INPUT,
        details: { errors },
      });
    }

    return {
      address: data.address.toLowerCase(),
      key: data.key.trim(),
      value: data.value.trim(),
    };
  }

  /**
   * Validate ENS verification request
   */
  validateENSVerification(data: any): ENSVerificationRequest {
    const errors: ValidationError[] = [];

    if (!data.ensName) {
      errors.push({ field: 'ensName', message: 'ENS name is required' });
    } else if (!this.isValidENSName(data.ensName)) {
      errors.push({ field: 'ensName', message: 'Invalid ENS name format', value: data.ensName });
    }

    if (!data.address) {
      errors.push({ field: 'address', message: 'Address is required' });
    } else if (!this.isValidEthereumAddress(data.address)) {
      errors.push({
        field: 'address',
        message: 'Invalid Ethereum address format',
        value: data.address,
      });
    }

    if (errors.length > 0) {
      throw new AppError({
        message: 'Validation failed',
        type: ErrorType.VALIDATION,
        code: ErrorCode.INVALID_INPUT,
        details: { errors },
      });
    }

    return {
      ensName: data.ensName.toLowerCase(),
      address: data.address.toLowerCase(),
    };
  }

  /**
   * Validate Polygon ID request
   */
  validatePolygonID(data: any): PolygonIDRequest {
    const errors: ValidationError[] = [];

    if (!data.address) {
      errors.push({ field: 'address', message: 'Address is required' });
    } else if (!this.isValidEthereumAddress(data.address)) {
      errors.push({ field: 'address', message: 'Invalid address format', value: data.address });
    }

    if (!data.proof) {
      errors.push({ field: 'proof', message: 'Proof is required' });
    } else if (typeof data.proof !== 'object') {
      errors.push({ field: 'proof', message: 'Proof must be an object' });
    }

    if (errors.length > 0) {
      throw new AppError({
        message: 'Validation failed',
        type: ErrorType.VALIDATION,
        code: ErrorCode.INVALID_INPUT,
        details: { errors },
      });
    }

    return {
      address: data.address.toLowerCase(),
      proof: data.proof,
    };
  }

  /**
   * Validate Worldcoin request
   */
  validateWorldcoin(data: any): WorldcoinRequest {
    const errors: ValidationError[] = [];

    if (!data.address) {
      errors.push({ field: 'address', message: 'Address is required' });
    } else if (!this.isValidEthereumAddress(data.address)) {
      errors.push({ field: 'address', message: 'Invalid address format', value: data.address });
    }

    if (!data.nullifier) {
      errors.push({ field: 'nullifier', message: 'Nullifier is required' });
    } else if (typeof data.nullifier !== 'string' || data.nullifier.trim().length === 0) {
      errors.push({ field: 'nullifier', message: 'Nullifier must be a non-empty string' });
    }

    if (!data.proof) {
      errors.push({ field: 'proof', message: 'Proof is required' });
    } else if (typeof data.proof !== 'object') {
      errors.push({ field: 'proof', message: 'Proof must be an object' });
    }

    if (errors.length > 0) {
      throw new AppError({
        message: 'Validation failed',
        type: ErrorType.VALIDATION,
        code: ErrorCode.INVALID_INPUT,
        details: { errors },
      });
    }

    return {
      address: data.address.toLowerCase(),
      nullifier: data.nullifier.trim(),
      proof: data.proof,
    };
  }

  /**
   * Validate Unstoppable Domains request
   */
  validateUnstoppableDomains(data: any): UnstoppableDomainsRequest {
    const errors: ValidationError[] = [];

    if (!data.domain) {
      errors.push({ field: 'domain', message: 'Domain is required' });
    } else if (!this.isValidDomain(data.domain)) {
      errors.push({ field: 'domain', message: 'Invalid domain format', value: data.domain });
    }

    if (!data.address) {
      errors.push({ field: 'address', message: 'Address is required' });
    } else if (!this.isValidEthereumAddress(data.address)) {
      errors.push({ field: 'address', message: 'Invalid address format', value: data.address });
    }

    if (errors.length > 0) {
      throw new AppError({
        message: 'Validation failed',
        type: ErrorType.VALIDATION,
        code: ErrorCode.INVALID_INPUT,
        details: { errors },
      });
    }

    return {
      domain: data.domain.toLowerCase(),
      address: data.address.toLowerCase(),
    };
  }

  /**
   * Validate blockchain verification request
   */
  validateBlockchainVerification(data: any): BlockchainVerificationRequest {
    const errors: ValidationError[] = [];

    if (!data.walletAddress) {
      errors.push({ field: 'walletAddress', message: 'Wallet address is required' });
    } else if (!this.isValidEthereumAddress(data.walletAddress)) {
      errors.push({
        field: 'walletAddress',
        message: 'Invalid wallet address format',
        value: data.walletAddress,
      });
    }

    if (!data.network) {
      errors.push({ field: 'network', message: 'Network is required' });
    } else if (!Object.values(SupportedNetwork).includes(data.network)) {
      errors.push({
        field: 'network',
        message: `Invalid network. Supported networks: ${Object.values(SupportedNetwork).join(
          ', '
        )}`,
        value: data.network,
      });
    }

    if (errors.length > 0) {
      throw new AppError({
        message: 'Validation failed',
        type: ErrorType.VALIDATION,
        code: ErrorCode.INVALID_INPUT,
        details: { errors },
      });
    }

    return {
      walletAddress: data.walletAddress.toLowerCase(),
      network: data.network,
    };
  }

  /**
   * Validate complete blockchain verification request
   */
  validateCompleteBlockchainVerification(data: any): CompleteBlockchainVerificationRequest {
    const errors: ValidationError[] = [];

    if (!data.requestId) {
      errors.push({ field: 'requestId', message: 'Request ID is required' });
    } else if (!this.isValidUUID(data.requestId)) {
      errors.push({
        field: 'requestId',
        message: 'Invalid request ID format',
        value: data.requestId,
      });
    }

    if (!data.signature) {
      errors.push({ field: 'signature', message: 'Signature is required' });
    } else if (!this.isValidSignature(data.signature)) {
      errors.push({
        field: 'signature',
        message: 'Invalid signature format',
        value: data.signature,
      });
    }

    if (errors.length > 0) {
      throw new AppError({
        message: 'Validation failed',
        type: ErrorType.VALIDATION,
        code: ErrorCode.INVALID_INPUT,
        details: { errors },
      });
    }

    return {
      requestId: data.requestId,
      signature: data.signature,
    };
  }

  /**
   * Validate add claim request
   */
  validateAddClaim(data: any): AddClaimRequest {
    const errors: ValidationError[] = [];

    if (!data.verificationId) {
      errors.push({ field: 'verificationId', message: 'Verification ID is required' });
    } else if (!this.isValidUUID(data.verificationId)) {
      errors.push({
        field: 'verificationId',
        message: 'Invalid verification ID format',
        value: data.verificationId,
      });
    }

    if (!data.type) {
      errors.push({ field: 'type', message: 'Type is required' });
    } else if (typeof data.type !== 'string' || data.type.trim().length === 0) {
      errors.push({ field: 'type', message: 'Type must be a non-empty string' });
    }

    if (!data.value) {
      errors.push({ field: 'value', message: 'Value is required' });
    } else if (typeof data.value !== 'string' || data.value.trim().length === 0) {
      errors.push({ field: 'value', message: 'Value must be a non-empty string' });
    }

    if (!data.issuer) {
      errors.push({ field: 'issuer', message: 'Issuer is required' });
    } else if (typeof data.issuer !== 'string' || data.issuer.trim().length === 0) {
      errors.push({ field: 'issuer', message: 'Issuer must be a non-empty string' });
    }

    if (errors.length > 0) {
      throw new AppError({
        message: 'Validation failed',
        type: ErrorType.VALIDATION,
        code: ErrorCode.INVALID_INPUT,
        details: { errors },
      });
    }

    return {
      verificationId: data.verificationId,
      type: data.type.trim(),
      value: data.value.trim(),
      issuer: data.issuer.trim(),
    };
  }

  /**
   * Validate ID parameter
   */
  validateId(id: any, fieldName: string = 'id'): string {
    if (!id) {
      throw new AppError({
        message: `${fieldName} is required`,
        type: ErrorType.VALIDATION,
        code: ErrorCode.MISSING_REQUIRED_FIELD,
      });
    }

    if (!this.isValidUUID(id)) {
      throw new AppError({
        message: `${fieldName} must be a valid UUID`,
        type: ErrorType.VALIDATION,
        code: ErrorCode.INVALID_INPUT,
      });
    }

    return id;
  }

  /**
   * Validate pagination parameters
   */
  validatePaginationParams(query: any): {
    page: number;
    limit: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  } {
    const page = query.page ? parseInt(query.page, 10) : 1;
    const limit = query.limit ? parseInt(query.limit, 10) : 10;

    if (isNaN(page) || page < 1) {
      throw new AppError({
        message: 'Page must be a positive integer',
        type: ErrorType.VALIDATION,
        code: ErrorCode.INVALID_INPUT,
      });
    }

    if (isNaN(limit) || limit < 1 || limit > 100) {
      throw new AppError({
        message: 'Limit must be between 1 and 100',
        type: ErrorType.VALIDATION,
        code: ErrorCode.INVALID_INPUT,
      });
    }

    const result: any = { page, limit };

    if (query.sortBy) {
      const validSortFields = ['createdAt', 'updatedAt', 'status', 'type'];
      if (!validSortFields.includes(query.sortBy)) {
        throw new AppError({
          message: `Invalid sort field. Must be one of: ${validSortFields.join(', ')}`,
          type: ErrorType.VALIDATION,
          code: ErrorCode.INVALID_INPUT,
        });
      }
      result.sortBy = query.sortBy;
    }

    if (query.sortOrder) {
      if (!['asc', 'desc'].includes(query.sortOrder)) {
        throw new AppError({
          message: 'Sort order must be either "asc" or "desc"',
          type: ErrorType.VALIDATION,
          code: ErrorCode.INVALID_INPUT,
        });
      }
      result.sortOrder = query.sortOrder;
    }

    return result;
  }

  /**
   * Check if string is a valid Ethereum address
   */
  private isValidEthereumAddress(address: string): boolean {
    return /^0x[a-fA-F0-9]{40}$/.test(address);
  }

  /**
   * Check if string is a valid signature
   */
  private isValidSignature(signature: string): boolean {
    return /^0x[a-fA-F0-9]{130}$/.test(signature);
  }

  /**
   * Check if string is a valid ENS name
   */
  private isValidENSName(ensName: string): boolean {
    return /^[a-z0-9-]+\.eth$/.test(ensName);
  }

  /**
   * Check if string is a valid domain
   */
  private isValidDomain(domain: string): boolean {
    return /^[a-z0-9-]+\.[a-z]{2,}$/.test(domain);
  }

  /**
   * Check if string is a valid UUID
   */
  private isValidUUID(uuid: string): boolean {
    return /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(uuid);
  }
}
