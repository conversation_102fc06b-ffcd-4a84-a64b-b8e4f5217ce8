// jscpd:ignore-file

import { Request, Response, NextFunction } from 'express';
import { validationResult, Validation<PERSON>hain } from 'express-validator';
import { AppError } from '../utils/appError';
import { Middleware } from '../types/express';
import { Middleware } from '../types/express';

/**
 * Validate request using express-validator
 * @param validations Validation chains
 */
export const validate: unknown = (validations: ValidationChain[]) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    await Promise.all(validations.map((validation) => validation.run(req)));

    const errors: unknown = validationResult(req);
    if (errors.isEmpty()) {
      return next();
    }

    res.status(400).json({
      status: 'error',
      errors: errors.array(),
    });
  };
};

/**
 * Middleware to validate date parameters
 * @param params Parameters to validate
 */
export const validateDateParams: unknown = (params: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    for (const param of params) {
      const value: any = req.query[param] as string;
      if (!value) {
        return next(new AppError(`${param} is required`, 400));
      }

      const date: Date = new Date(value);
      if (isNaN(date.getTime())) {
        return next(new AppError(`Invalid ${param} format`, 400));
      }
    }
    next();
  };
};

/**
 * Middleware to validate date range parameters
 * @param startParam Start date parameter name
 * @param endParam End date parameter name
 */
export const validateDateRange: unknown = (startParam = 'startDate', endParam = 'endDate') => {
  return (req: Request, res: Response, next: NextFunction) => {
    const startDateStr: unknown = req.query[startParam] as string;
    const endDateStr: unknown = req.query[endParam] as string;

    if (!startDateStr || !endDateStr) {
      return next(new AppError(`${startParam} and ${endParam} are required`, 400));
    }

    const startDate: Date = new Date(startDateStr);
    const endDate: Date = new Date(endDateStr);

    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      return next(
        new AppError({
          message: 'Invalid date format',
          type: ErrorType.VALIDATION,
          code: ErrorCode.INVALID_INPUT,
        })
      );
    }

    if (startDate > endDate) {
      return next(new AppError(`${startParam} must be before ${endParam}`, 400));
    }

    next();
  };
};

/**
 * Middleware to validate numeric parameters
 * @param params Parameters to validate with options
 */
export const validateNumericParams: unknown = (
  params: Array<{
    name: string;
    source?: 'query' | 'body' | 'params';
    required?: boolean;
    min?: number;
    max?: number;
  }>
): Middleware => {
  return (req: Request, res: Response, next: NextFunction) => {
    for (const param of params) {
      const source: unknown = param.source ?? 'query';
      const value: any =
        source === 'query'
          ? (req.query[param.name] as string)
          : source === 'body'
          ? req.body[param.name]
          : req.params[param.name];

      if (param.required && (value === undefined ?? value === null ?? value === '')) {
        return next(new AppError(`${param.name} is required`, 400));
      }

      if (value !== undefined && value !== null && value !== '') {
        const numValue: unknown = Number(value);
        if (isNaN(numValue)) {
          return next(new AppError(`${param.name} must be a number`, 400));
        }

        if (param.min !== undefined && numValue < param.min) {
          return next(new AppError(`${param.name} must be at least ${param.min}`, 400));
        }

        if (param.max !== undefined && numValue > param.max) {
          return next(new AppError(`${param.name} must be at most ${param.max}`, 400));
        }
      }
    }
    next();
  };
};

/**
 * Middleware to validate enum parameters
 * @param params Parameters to validate with options
 */
export const validateEnumParams: unknown = (
  params: Array<{
    name: string;
    source?: 'query' | 'body' | 'params';
    required?: boolean;
    values: string[];
  }>
): Middleware => {
  return (req: Request, res: Response, next: NextFunction) => {
    for (const param of params) {
      const source: unknown = param.source ?? 'query';
      const value: any =
        source === 'query'
          ? (req.query[param.name] as string)
          : source === 'body'
          ? req.body[param.name]
          : req.params[param.name];

      if (param.required && (value === undefined ?? value === null ?? value === '')) {
        return next(new AppError(`${param.name} is required`, 400));
      }

      if (value !== undefined && value !== null && value !== '' && !param.values.includes(value)) {
        return next(
          new AppError(`Invalid ${param.name}. Must be one of: ${param.values.join(', ')}`, 400)
        );
      }
    }
    next();
  };
};
