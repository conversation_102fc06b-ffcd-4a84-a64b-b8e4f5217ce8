// jscpd:ignore-file
import { Request, Response, NextFunction } from 'express';
import request from "supertest";
import { RouteRegistry } from "../../core/RouteRegistry";
import { RouteVersionManager } from "../../core/RouteVersionManager";
import { RouteMonitor } from "../../core/RouteMonitor";
import { logger } from "../../lib/logger";
import { Middleware } from '../types/express';
import { RouteRegistry } from "../../core/RouteRegistry";
import { RouteVersionManager } from "../../core/RouteVersionManager";
import { RouteMonitor } from "../../core/RouteMonitor";
import { logger } from "../../lib/logger";
import { Middleware } from '../types/express';


/**
 * Route test helper
 * This class helps with testing routes
 */
export class RouteTestHelper {
  private app: Application;
  private routeRegistry: RouteRegistry;
  private routeVersionManager: RouteVersionManager;
  private routeMonitor: RouteMonitor;
  
  /**
   * Create a new route test helper
   * @param app Express application
   */
  constructor(app: Application) {
    this.app = app;
    this.routeRegistry = RouteRegistry.getInstance();
    this.routeVersionManager = RouteVersionManager.getInstance();
    this.routeMonitor = RouteMonitor.getInstance();
  }
  
  /**
   * Create a mock router
   * @param key Router key
   * @param path Router path
   * @param handler Request handler
   * @returns Express router
   */
  public createMockRouter(
    key: string,
    path: string,
    handler: (req: Request, res: Response) => void
  ): Router {
    const router = Router();
    
    // Add handler
    router.get("/", handler);
    
    // Register router
    this.routeRegistry.register(key, router, { path });
    
    return router;
  }
  
  /**
   * Create a mock versioned router
   * @param version Version
   * @param key Router key
   * @param path Router path
   * @param handler Request handler
   * @returns Express router
   */
  public createMockVersionedRouter(
    version: string,
    key: string,
    path: string,
    handler: (req: Request, res: Response) => void
  ): Router {
    const router = Router();
    
    // Add handler
    router.get("/", handler);
    
    // Register router
    this.routeVersionManager.registerVersionedRoute(
      version,
      key,
      router,
      { path }
    );
    
    return router;
  }
  
  /**
   * Create a mock middleware
   * @param name Middleware name
   * @param handler Middleware handler
   * @returns Express middleware
   */
  public createMockMiddleware(
    name: string,
    handler: (req: Request, res: Response, next: NextFunction) => void
  ): (req: Request, res: Response, next: NextFunction) => void {
    return (req: Request, res: Response, next: NextFunction) => {
      logger.debug(`Mock middleware ${name} called`);
      handler(req, res, next);
    };
  }
  
  /**
   * Test a route
   * @param method HTTP method
   * @param path Route path
   * @param expectedStatus Expected status code
   * @param expectedBody Expected response body
   * @param token Authentication token
   * @param body Request body
   * @returns Test result
   */
  public async testRoute(
    method: "get" | "post" | "put" | "delete" | "patch",
    path: string,
    expectedStatus: number,
    expectedBody?: unknown,
    token?: string,
    body?: unknown
  ): Promise<request.Response> {
    // Create request
    let req: Request =request(this.app)[method](path);
    
    // Add token if provided
    if (token) {
      req = req.set("Authorization", `Bearer ${token}`);
    }
    
    // Add body if provided
    if (body) {
      req = req.send(body);
    }
    
    // Send request
    const res: Response =await req;
    
    // Check status
    expect(res.status).toBe(expectedStatus);
    
    // Check body if provided
    if (expectedBody) {
      expect(res.body).toMatchObject(expectedBody);
    }
    
    return res;
  }
  
  /**
   * Test a route with authentication
   * @param method HTTP method
   * @param path Route path
   * @param token Authentication token
   * @param expectedStatus Expected status code
   * @param expectedBody Expected response body
   * @param body Request body
   * @returns Test result
   */
  public async testAuthenticatedRoute(
    method: "get" | "post" | "put" | "delete" | "patch",
    path: string,
    token: string,
    expectedStatus: number,
    expectedBody?: unknown,
    body?: unknown
  ): Promise<request.Response> {
    return this.testRoute(method, path, expectedStatus, expectedBody, token, body);
  }
  
  /**
   * Test a route without authentication
   * @param method HTTP method
   * @param path Route path
   * @param expectedStatus Expected status code
   * @param expectedBody Expected response body
   * @param body Request body
   * @returns Test result
   */
  public async testUnauthenticatedRoute(
    method: "get" | "post" | "put" | "delete" | "patch",
    path: string,
    expectedStatus: number,
    expectedBody?: unknown,
    body?: unknown
  ): Promise<request.Response> {
    return this.testRoute(method, path, expectedStatus, expectedBody, undefined, body);
  }
  
  /**
   * Reset route metrics
   */
  public resetRouteMetrics(): void {
    this.routeMonitor.resetAllMetrics();
  }
}

export default RouteTestHelper;
