// jscpd:ignore-file
/**
 * Feature Flags Middleware
 * 
 * This middleware provides functions for checking feature flags in requests
 * to ensure complete isolation between production and demo environments.
 */

import { Request, Response, NextFunction } from "express";
import { logger } from "../lib/logger";
import { isFeatureEnabled } from "../utils/feature-flags";
import { AppError } from "./error.middleware";
import { Middleware } from '../types/express';
import { logger } from "../lib/logger";
import { isFeatureEnabled } from "../utils/feature-flags";
import { AppError } from "./error.middleware";
import { Middleware } from '../types/express';


/**
 * Check if a feature is enabled
 * @param featureName Feature name
 * @returns Middleware function
 */
export const checkFeatureEnabled: any =(featureName: string) => {
    return (req: Request, res: Response, next: NextFunction): void => {
        try {
            // Check if feature is enabled
            if (!isFeatureEnabled(featureName)) {
                logger.warn(`Feature not enabled: ${featureName}`, {
                    feature: featureName,
                    environment: req.environment,
                    ip: req.ip,
                    path: req.path,
                    method: req.method,
                    requestId: req.requestId
                });
        
                return next(new AppError(`Feature not enabled: ${featureName}`, 403, true));
            }
      
            next();
        } catch (error) {
            logger.error(`Error checking feature flag: ${featureName}`, error);
            next(error);
        }
    };
};

/**
 * Add feature flags to response
 * @param req Express request
 * @param res Express response
 * @param next Express next function
 */
export const addFeatureFlagsToResponse: any =(req: Request, res: Response, next: NextFunction): void => {
    try {
    // Add feature flags to response headers
        res.setHeader("X-Feature-Binance-Payments", isFeatureEnabled("ENABLE_BINANCE_PAYMENTS") ? "true" : "false");
        res.setHeader("X-Feature-Crypto-Payments", isFeatureEnabled("ENABLE_CRYPTO_PAYMENTS") ? "true" : "false");
        res.setHeader("X-Feature-Fiat-Payments", isFeatureEnabled("ENABLE_FIAT_PAYMENTS") ? "true" : "false");
        res.setHeader("X-Feature-2FA", isFeatureEnabled("ENABLE_2FA") ? "true" : "false");
        res.setHeader("X-Feature-Demo-Banner", isFeatureEnabled("ENABLE_DEMO_BANNER") ? "true" : "false");
    
        next();
    } catch (error) {
        logger.error("Error adding feature flags to response", error);
        next(error);
    }
};

/**
 * Check if real payments are enabled
 * @param req Express request
 * @param res Express response
 * @param next Express next function
 */
export const checkRealPaymentsEnabled: any =(req: Request, res: Response, next: NextFunction): void => {
    try {
    // Check if real payments are disabled
        if (isFeatureEnabled("DISABLE_REAL_PAYMENTS")) {
            logger.warn("Real payments are disabled in this environment", {
                environment: req.environment,
                ip: req.ip,
                path: req.path,
                method: req.method,
                requestId: req.requestId
            });
      
            return next(new AppError("Real payments are disabled in this environment", 403, true));
        }
    
        next();
    } catch (error) {
        logger.error("Error checking real payments feature flag", error);
        next(error);
    }
};

export default {
    checkFeatureEnabled,
    addFeatureFlagsToResponse,
    checkRealPaymentsEnabled
};
