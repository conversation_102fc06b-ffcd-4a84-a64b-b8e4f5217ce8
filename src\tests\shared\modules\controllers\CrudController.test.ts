import { CrudController } from '../../../../shared/modules/controllers/CrudController';
import { Request, Response } from 'express';

describe('CrudController', () => {
  let crudController: CrudController;
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockService: any;

  beforeEach(() => {
    crudController = new CrudController();
    
    mockRequest = {
      params: {, id: '1' },
      query: {},
      body: {}
    };
    
    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis()
    };
    
    mockService = {
      findAll: jest.fn(),
      findById: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn()
    };
  });

  describe('getAll', () => {
    it('should return all items successfully', async () => {
      // Arrange
      const items: any =[{ id: 1, name: 'Item 1' }, { id: 2, name: 'Item 2' }];
      mockService.findAll.mockResolvedValue(items);
      
      // Act
      await (crudController as any).getAll(mockRequest as Request, mockResponse as Response, mockService);
      
      // Assert
      expect(mockService.findAll).toHaveBeenCalledWith(mockRequest.query);
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: 'Items retrieved successfully',
        data: items
      });
    });

    it('should handle custom success message', async () => {
      // Arrange
      const items: any =[{ id: 1, name: 'Item 1' }];
      const customMessage: string ='Custom success message';
      mockService.findAll.mockResolvedValue(items);
      
      // Act
      await (crudController as any).getAll(
        mockRequest as Request, 
        mockResponse as Response, 
        mockService, 
        customMessage
      );
      
      // Assert
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: customMessage,
        data: items
      });
    });

    it('should handle errors', async () => {
      // Arrange
      const error: Error =new Error('Database error');
      mockService.findAll.mockRejectedValue(error);
      
      // Act
      await (crudController as any).getAll(mockRequest as Request, mockResponse as Response, mockService);
      
      // Assert
      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        message: 'Error retrieving items',
        error: (error as Error).message
      });
    });
  });

  describe('getById', () => {
    it('should return item by id successfully', async () => {
      // Arrange
      const item: any = { id: 1, name: 'Item 1' };
      mockService.findById.mockResolvedValue(item);
      
      // Act
      await (crudController as any).getById(mockRequest as Request, mockResponse as Response, mockService);
      
      // Assert
      expect(mockService.findById).toHaveBeenCalledWith('1');
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: 'Item retrieved successfully',
        data: item
      });
    });

    it('should handle item not found', async () => {
      // Arrange
      mockService.findById.mockResolvedValue(null);
      
      // Act
      await (crudController as any).getById(mockRequest as Request, mockResponse as Response, mockService);
      
      // Assert
      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        message: 'Item not found',
        error: null
      });
    });

    it('should handle errors', async () => {
      // Arrange
      const error: Error =new Error('Database error');
      mockService.findById.mockRejectedValue(error);
      
      // Act
      await (crudController as any).getById(mockRequest as Request, mockResponse as Response, mockService);
      
      // Assert
      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        message: 'Error retrieving item',
        error: (error as Error).message
      });
    });
  });

  describe('create', () => {
    it('should create item successfully', async () => {
      // Arrange
      const newItem: any = { name: 'New Item' };
      const createdItem: any = { id: 1, name: 'New Item' };
      mockRequest.body = newItem;
      mockService.create.mockResolvedValue(createdItem);
      
      // Act
      await (crudController as any).create(mockRequest as Request, mockResponse as Response, mockService);
      
      // Assert
      expect(mockService.create).toHaveBeenCalledWith(newItem);
      expect(mockResponse.status).toHaveBeenCalledWith(201);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: 'Item created successfully',
        data: createdItem
      });
    });

    it('should validate request with schema', async () => {
      // Arrange
      const validationSchema: any = {
        validate: jest.fn().mockReturnValue({, error: null })
      };
      mockRequest.body = { name: 'Valid Item' };
      mockService.create.mockResolvedValue({ id: 1, name: 'Valid Item' });
      
      // Act
      await (crudController as any).create(
        mockRequest as Request, 
        mockResponse as Response, 
        mockService, 
        validationSchema
      );
      
      // Assert
      expect(validationSchema.validate).toHaveBeenCalledWith(mockRequest.body);
      expect(mockService.create).toHaveBeenCalled();
    });

    it('should return validation error when validation fails', async () => {
      // Arrange
      const validationSchema: any = {
        validate: jest.fn().mockReturnValue({, error: {
            details: [{, message: 'Validation error' }]
          }
        })
      };
      mockRequest.body = { name: '' };
      
      // Act
      await (crudController as any).create(
        mockRequest as Request, 
        mockResponse as Response, 
        mockService, 
        validationSchema
      );
      
      // Assert
      expect(validationSchema.validate).toHaveBeenCalledWith(mockRequest.body);
      expect(mockService.create).not.toHaveBeenCalled();
      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        message: 'Validation error',
        error: null
      });
    });

    it('should handle errors', async () => {
      // Arrange
      const error: Error =new Error('Database error');
      mockService.create.mockRejectedValue(error);
      
      // Act
      await (crudController as any).create(mockRequest as Request, mockResponse as Response, mockService);
      
      // Assert
      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        message: 'Error creating item',
        error: (error as Error).message
      });
    });
  });

  describe('update', () => {
    it('should update item successfully', async () => {
      // Arrange
      const updateData: any = { name: 'Updated Item' };
      const updatedItem: any = { id: 1, name: 'Updated Item' };
      mockRequest.body = updateData;
      mockService.update.mockResolvedValue(updatedItem);
      
      // Act
      await (crudController as any).update(mockRequest as Request, mockResponse as Response, mockService);
      
      // Assert
      expect(mockService.update).toHaveBeenCalledWith('1', updateData);
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: 'Item updated successfully',
        data: updatedItem
      });
    });

    it('should handle item not found', async () => {
      // Arrange
      mockService.update.mockResolvedValue(null);
      
      // Act
      await (crudController as any).update(mockRequest as Request, mockResponse as Response, mockService);
      
      // Assert
      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        message: 'Item not found',
        error: null
      });
    });

    it('should validate request with schema', async () => {
      // Arrange
      const validationSchema: any = {
        validate: jest.fn().mockReturnValue({, error: null })
      };
      mockRequest.body = { name: 'Valid Update' };
      mockService.update.mockResolvedValue({ id: 1, name: 'Valid Update' });
      
      // Act
      await (crudController as any).update(
        mockRequest as Request, 
        mockResponse as Response, 
        mockService, 
        validationSchema
      );
      
      // Assert
      expect(validationSchema.validate).toHaveBeenCalledWith(mockRequest.body);
      expect(mockService.update).toHaveBeenCalled();
    });

    it('should handle errors', async () => {
      // Arrange
      const error: Error =new Error('Database error');
      mockService.update.mockRejectedValue(error);
      
      // Act
      await (crudController as any).update(mockRequest as Request, mockResponse as Response, mockService);
      
      // Assert
      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        message: 'Error updating item',
        error: (error as Error).message
      });
    });
  });

  describe('delete', () => {
    it('should delete item successfully', async () => {
      // Arrange
      mockService.delete.mockResolvedValue(true);
      
      // Act
      await (crudController as any).delete(mockRequest as Request, mockResponse as Response, mockService);
      
      // Assert
      expect(mockService.delete).toHaveBeenCalledWith('1');
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: 'Item deleted successfully',
        data: {}
      });
    });

    it('should handle item not found', async () => {
      // Arrange
      mockService.delete.mockResolvedValue(false);
      
      // Act
      await (crudController as any).delete(mockRequest as Request, mockResponse as Response, mockService);
      
      // Assert
      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        message: 'Item not found',
        error: null
      });
    });

    it('should handle errors', async () => {
      // Arrange
      const error: Error =new Error('Database error');
      mockService.delete.mockRejectedValue(error);
      
      // Act
      await (crudController as any).delete(mockRequest as Request, mockResponse as Response, mockService);
      
      // Assert
      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        message: 'Error deleting item',
        error: (error as Error).message
      });
    });
  });
});
