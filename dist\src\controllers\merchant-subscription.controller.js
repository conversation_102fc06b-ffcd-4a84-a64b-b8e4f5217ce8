"use strict";
// jscpd:ignore-file
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const subscription_service_1 = __importDefault(require("../services/subscription.service"));
const logger_1 = require("../utils/logger");
class MerchantSubscriptionController {
    async subscribeMerchant(req, res) {
        try {
            const { merchantId } = req.params;
            const { planId, paymentMethodId } = req.body;
            // Authorization check - ensure user can only subscribe their own merchant account
            if (req.user?.role !== "admin" && req.user?.merchantId !== merchantId) {
                return res.status(403).json({
                    status: "error",
                    message: "You are not authorized to manage this merchant subscription"
                });
            }
            const result = await subscription_service_1.default.subscribeMerchant(merchantId, planId, paymentMethodId);
            return res.status(200).json({
                status: "success",
                data: result,
                message: "Subscription updated successfully"
            });
        }
        catch (error) {
            logger_1.logger.error("Subscription error:", error);
            return res.status(500).json({
                status: "error",
                message: error.message || "Failed to update subscription"
            });
        }
    }
    async cancelSubscription(req, res) {
        try {
            const { merchantId } = req.params;
            // Authorization check - ensure user can only cancel their own merchant subscription
            if (req.user?.role !== "admin" && req.user?.merchantId !== merchantId) {
                return res.status(403).json({
                    status: "error",
                    message: "You are not authorized to manage this merchant subscription"
                });
            }
            const result = await subscription_service_1.default.cancelSubscription(merchantId);
            return res.status(200).json({
                status: "success",
                data: result,
                message: "Subscription cancelled successfully. Services will remain active until the end of the billing period."
            });
        }
        catch (error) {
            logger_1.logger.error("Subscription cancellation error:", error);
            return res.status(500).json({
                status: "error",
                message: error.message || "Failed to cancel subscription"
            });
        }
    }
    async getSubscriptionStatus(req, res) {
        try {
            const { merchantId } = req.params;
            // Authorization check - ensure user can only view their own merchant subscription
            if (req.user?.role !== "admin" && req.user?.merchantId !== merchantId) {
                return res.status(403).json({
                    status: "error",
                    message: "You are not authorized to view this merchant subscription"
                });
            }
            // Find merchant to get their subscription details
            const merchant = await subscription_service_1.default.getMerchantSubscriptionStatus(merchantId);
            if (!merchant) {
                return res.status(404).json({
                    status: "error",
                    message: "Merchant not found"
                });
            }
            // Get current plan details
            const currentPlan = merchant.currentPlan ?
                await subscription_service_1.default.getPlanById(merchant.currentPlan) : null;
            return res.status(200).json({
                status: "success",
                data: { currentPlanId: merchant.currentPlan || null,
                    currentPlan: currentPlan || null,
                    expiryDate: merchant.planExpiryDate || null,
                    isActive: merchant.currentPlan &&
                        merchant.planExpiryDate &&
                        new Date(merchant.planExpiryDate) > new Date(),
                    daysRemaining: merchant.planExpiryDate ?
                        Math.max(0, Math.ceil((new Date(merchant.planExpiryDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))) : 0
                }
            });
        }
        catch (error) {
            logger_1.logger.error("Subscription status error:", error);
            return res.status(500).json({
                status: "error",
                message: error.message || "Failed to retrieve subscription status"
            });
        }
    }
}
exports.default = new MerchantSubscriptionController();
//# sourceMappingURL=merchant-subscription.controller.js.map