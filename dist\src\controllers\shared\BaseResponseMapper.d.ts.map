{"version": 3, "file": "BaseResponseMapper.d.ts", "sourceRoot": "", "sources": ["../../../../src/controllers/shared/BaseResponseMapper.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,EAAE,QAAQ,EAAE,MAAM,SAAS,CAAC;AACnC,OAAO,EAAE,QAAQ,EAAE,MAAM,4BAA4B,CAAC;AAGtD,MAAM,WAAW,WAAW,CAAC,CAAC,GAAG,GAAG;IAClC,OAAO,EAAE,OAAO,CAAC;IACjB,IAAI,CAAC,EAAE,CAAC,CAAC;IACT,OAAO,EAAE,MAAM,CAAC;IAChB,UAAU,CAAC,EAAE,cAAc,CAAC;IAC5B,SAAS,EAAE,IAAI,CAAC;IAChB,SAAS,EAAE,MAAM,CAAC;CACnB;AAED,MAAM,WAAW,gBAAgB;IAC/B,OAAO,EAAE,KAAK,CAAC;IACf,KAAK,EAAE;QACL,OAAO,EAAE,MAAM,CAAC;QAChB,IAAI,CAAC,EAAE,MAAM,CAAC;QACd,IAAI,EAAE,MAAM,CAAC;QACb,OAAO,CAAC,EAAE,GAAG,CAAC;KACf,CAAC;IACF,SAAS,EAAE,IAAI,CAAC;IAChB,SAAS,EAAE,MAAM,CAAC;CACnB;AAED,MAAM,WAAW,cAAc;IAC7B,IAAI,EAAE,MAAM,CAAC;IACb,KAAK,EAAE,MAAM,CAAC;IACd,KAAK,EAAE,MAAM,CAAC;IACd,UAAU,EAAE,MAAM,CAAC;IACnB,OAAO,EAAE,OAAO,CAAC;IACjB,OAAO,EAAE,OAAO,CAAC;CAClB;AAED;;GAEG;AACH,qBAAa,kBAAkB;IAC7B;;OAEG;IACH,MAAM,CAAC,WAAW,CAAC,CAAC,EAClB,GAAG,EAAE,QAAQ,EACb,IAAI,EAAE,CAAC,EACP,OAAO,GAAE,MAA2C,EACpD,UAAU,GAAE,MAAY,EACxB,UAAU,CAAC,EAAE,cAAc,GAC1B,IAAI;IAaP;;OAEG;IACH,MAAM,CAAC,SAAS,CAAC,GAAG,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,GAAG,QAAQ,EAAE,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI;IA0CnF;;OAEG;IACH,MAAM,CAAC,aAAa,CAAC,CAAC,EACpB,GAAG,EAAE,QAAQ,EACb,IAAI,EAAE,CAAC,EAAE,EACT,KAAK,EAAE,MAAM,EACb,IAAI,EAAE,MAAM,EACZ,KAAK,EAAE,MAAM,EACb,OAAO,GAAE,MAAsC,GAC9C,IAAI;IAcP;;OAEG;IACH,MAAM,CAAC,WAAW,CAAC,CAAC,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,EAAE,OAAO,GAAE,MAAwC,GAAG,IAAI;IAItG;;OAEG;IACH,MAAM,CAAC,WAAW,CAAC,CAAC,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,EAAE,OAAO,GAAE,MAAwC,GAAG,IAAI;IAItG;;OAEG;IACH,MAAM,CAAC,WAAW,CAAC,GAAG,EAAE,QAAQ,EAAE,OAAO,GAAE,MAAwC,GAAG,IAAI;IAI1F;;OAEG;IACH,MAAM,CAAC,YAAY,CAAC,GAAG,EAAE,QAAQ,EAAE,OAAO,GAAE,MAA6B,GAAG,IAAI;IAShF;;OAEG;IACH,MAAM,CAAC,mBAAmB,CAAC,GAAG,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,GAAE,MAA4B,GAAG,IAAI;IAUpG;;OAEG;IACH,MAAM,CAAC,gBAAgB,CAAC,GAAG,EAAE,QAAQ,EAAE,OAAO,GAAE,MAA8B,GAAG,IAAI;IASrF;;OAEG;IACH,MAAM,CAAC,aAAa,CAAC,GAAG,EAAE,QAAQ,EAAE,OAAO,GAAE,MAA2B,GAAG,IAAI;IAS/E;;OAEG;IACH,MAAM,CAAC,YAAY,CAAC,GAAG,EAAE,QAAQ,EAAE,OAAO,GAAE,MAA4B,GAAG,IAAI;IAS/E;;OAEG;IACH,MAAM,CAAC,aAAa,CAAC,GAAG,EAAE,QAAQ,EAAE,OAAO,GAAE,MAA8B,GAAG,IAAI;IASlF;;OAEG;IACH,MAAM,CAAC,iBAAiB,CAAC,GAAG,EAAE,QAAQ,EAAE,OAAO,GAAE,MAAgC,GAAG,IAAI;IASxF;;OAEG;IACH,MAAM,CAAC,sBAAsB,CAAC,GAAG,EAAE,QAAQ,EAAE,OAAO,GAAE,MAA0C,GAAG,IAAI;IASvG;;OAEG;IACH,MAAM,CAAC,gBAAgB,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,GAAG,cAAc;IAYnF;;OAEG;WACU,WAAW,CACtB,GAAG,EAAE,QAAQ,EACb,OAAO,EAAE,MAAM,OAAO,CAAC,GAAG,CAAC,EAC3B,cAAc,CAAC,EAAE,MAAM,EACvB,iBAAiB,CAAC,EAAE,MAAM,GACzB,OAAO,CAAC,IAAI,CAAC;CAQjB"}