{"file": "F:\\Amazing pay flow\\src\\tests\\dashboard.controller.test.ts", "mappings": ";;;;;AAAA,mCAAyE;AACzE,0DAAgC;AAChC,sDAA8B;AAC9B,8EAA0E;AAC1E,4FAAuF;AAEvF,cAAc;AACd,MAAM,UAAU,GAAG;IACjB,SAAS,EAAE;QACT,QAAQ,EAAE,WAAE,CAAC,EAAE,EAAE;QACjB,UAAU,EAAE,WAAE,CAAC,EAAE,EAAE;QACnB,MAAM,EAAE,WAAE,CAAC,EAAE,EAAE;QACf,MAAM,EAAE,WAAE,CAAC,EAAE,EAAE;QACf,MAAM,EAAE,WAAE,CAAC,EAAE,EAAE;KAChB;IACD,eAAe,EAAE;QACf,QAAQ,EAAE,WAAE,CAAC,EAAE,EAAE;QACjB,UAAU,EAAE,WAAE,CAAC,EAAE,EAAE;QACnB,SAAS,EAAE,WAAE,CAAC,EAAE,EAAE;QAClB,MAAM,EAAE,WAAE,CAAC,EAAE,EAAE;QACf,MAAM,EAAE,WAAE,CAAC,EAAE,EAAE;QACf,MAAM,EAAE,WAAE,CAAC,EAAE,EAAE;QACf,UAAU,EAAE,WAAE,CAAC,EAAE,EAAE;KACpB;CACF,CAAC;AAEF,WAAE,CAAC,IAAI,CAAC,gBAAgB,EAAE,GAAG,EAAE,CAAC,CAAC;IAC/B,YAAY,EAAE,WAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,UAAU,CAAC;CACtC,CAAC,CAAC,CAAC;AAEJ,MAAM,GAAG,GAAG,IAAA,iBAAO,GAAE,CAAC;AACtB,GAAG,CAAC,GAAG,CAAC,iBAAO,CAAC,IAAI,EAAE,CAAC,CAAC;AAExB,uBAAuB;AACvB,MAAM,kBAAkB,GAAG,CAAC,GAAQ,EAAE,GAAQ,EAAE,IAAS,EAAE,EAAE;IAC3D,GAAG,CAAC,IAAI,GAAG;QACT,EAAE,EAAE,QAAQ;QACZ,IAAI,EAAE,UAAU;QAChB,KAAK,EAAE,kBAAkB;KAC1B,CAAC;IACF,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AAEF,eAAe;AACf,MAAM,mBAAmB,GAAG,IAAI,0CAAmB,EAAE,CAAC;AACtD,MAAM,gBAAgB,GAAG,IAAI,uDAAyB,EAAE,CAAC;AAEzD,GAAG,CAAC,GAAG,CAAC,iBAAiB,EAAE,kBAAkB,EAAE,mBAAmB,CAAC,aAAa,CAAC,CAAC;AAClF,GAAG,CAAC,GAAG,CAAC,qBAAqB,EAAE,kBAAkB,EAAE,mBAAmB,CAAC,gBAAgB,CAAC,CAAC;AACzF,GAAG,CAAC,IAAI,CAAC,iBAAiB,EAAE,kBAAkB,EAAE,mBAAmB,CAAC,eAAe,CAAC,CAAC;AACrF,GAAG,CAAC,GAAG,CAAC,qBAAqB,EAAE,kBAAkB,EAAE,mBAAmB,CAAC,eAAe,CAAC,CAAC;AACxF,GAAG,CAAC,MAAM,CAAC,qBAAqB,EAAE,kBAAkB,EAAE,mBAAmB,CAAC,eAAe,CAAC,CAAC;AAE3F,GAAG,CAAC,GAAG,CAAC,sCAAsC,EAAE,kBAAkB,EAAE,gBAAgB,CAAC,UAAU,CAAC,CAAC;AACjG,GAAG,CAAC,GAAG,CAAC,6BAA6B,EAAE,kBAAkB,EAAE,gBAAgB,CAAC,aAAa,CAAC,CAAC;AAC3F,GAAG,CAAC,IAAI,CAAC,sCAAsC,EAAE,kBAAkB,EAAE,gBAAgB,CAAC,YAAY,CAAC,CAAC;AACpG,GAAG,CAAC,GAAG,CAAC,6BAA6B,EAAE,kBAAkB,EAAE,gBAAgB,CAAC,YAAY,CAAC,CAAC;AAC1F,GAAG,CAAC,MAAM,CAAC,6BAA6B,EAAE,kBAAkB,EAAE,gBAAgB,CAAC,YAAY,CAAC,CAAC;AAC7F,GAAG,CAAC,IAAI,CAAC,8CAA8C,EAAE,kBAAkB,EAAE,gBAAgB,CAAC,cAAc,CAAC,CAAC;AAE9G,IAAA,iBAAQ,EAAC,qBAAqB,EAAE,GAAG,EAAE;IACnC,IAAA,mBAAU,EAAC,GAAG,EAAE;QACd,WAAE,CAAC,aAAa,EAAE,CAAC;IACrB,CAAC,CAAC,CAAC;IAEH,IAAA,iBAAQ,EAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,IAAA,WAAE,EAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,MAAM,cAAc,GAAG;gBACrB;oBACE,EAAE,EAAE,aAAa;oBACjB,IAAI,EAAE,cAAc;oBACpB,WAAW,EAAE,QAAQ;oBACrB,QAAQ,EAAE,KAAK;iBAChB;gBACD;oBACE,EAAE,EAAE,aAAa;oBACjB,IAAI,EAAE,kBAAkB;oBACxB,WAAW,EAAE,QAAQ;oBACrB,QAAQ,EAAE,IAAI;iBACf;aACF,CAAC;YAEF,UAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;YAEhE,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;YAE3D,IAAA,eAAM,EAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAClC,IAAA,eAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,IAAA,eAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YACnD,IAAA,eAAM,EAAC,UAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CAAC;gBACzD,KAAK,EAAE;oBACL,EAAE,EAAE;wBACF,EAAE,WAAW,EAAE,QAAQ,EAAE;wBACzB,EAAE,QAAQ,EAAE,IAAI,EAAE;qBACnB;iBACF;gBACD,OAAO,EAAE;oBACP,SAAS,EAAE,MAAM;iBAClB;aACF,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,iBAAQ,EAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,IAAA,WAAE,EAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,MAAM,aAAa,GAAG;gBACpB,EAAE,EAAE,aAAa;gBACjB,IAAI,EAAE,cAAc;gBACpB,WAAW,EAAE,QAAQ;gBACrB,QAAQ,EAAE,KAAK;gBACf,OAAO,EAAE;oBACP;wBACE,EAAE,EAAE,UAAU;wBACd,KAAK,EAAE,mBAAmB;wBAC1B,IAAI,EAAE,OAAO;wBACb,QAAQ,EAAE,CAAC;qBACZ;iBACF;aACF,CAAC;YAEF,UAAU,CAAC,SAAS,CAAC,UAAU,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;YAEjE,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;YAEvE,IAAA,eAAM,EAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAClC,IAAA,eAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,IAAA,eAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;YAClD,IAAA,eAAM,EAAC,UAAU,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,oBAAoB,CAAC;gBAC3D,KAAK,EAAE,EAAE,EAAE,EAAE,aAAa,EAAE;gBAC5B,OAAO,EAAE;oBACP,OAAO,EAAE;wBACP,OAAO,EAAE;4BACP,QAAQ,EAAE,KAAK;yBAChB;qBACF;iBACF;aACF,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAA,WAAE,EAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;YAC5D,UAAU,CAAC,SAAS,CAAC,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAExD,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;YAExE,IAAA,eAAM,EAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAClC,IAAA,eAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1C,IAAA,eAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,IAAA,WAAE,EAAC,2DAA2D,EAAE,KAAK,IAAI,EAAE;YACzE,MAAM,aAAa,GAAG;gBACpB,EAAE,EAAE,aAAa;gBACjB,IAAI,EAAE,mBAAmB;gBACzB,WAAW,EAAE,QAAQ;gBACrB,QAAQ,EAAE,KAAK;aAChB,CAAC;YAEF,UAAU,CAAC,SAAS,CAAC,UAAU,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;YAEjE,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;YAEvE,IAAA,eAAM,EAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAClC,IAAA,eAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1C,IAAA,eAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,iBAAQ,EAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,IAAA,WAAE,EAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;YAC7C,MAAM,aAAa,GAAG;gBACpB,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,kBAAkB;gBAC/B,MAAM,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE;gBACtB,QAAQ,EAAE,KAAK;aAChB,CAAC;YAEF,MAAM,aAAa,GAAG;gBACpB,EAAE,EAAE,aAAa;gBACjB,GAAG,aAAa;gBAChB,WAAW,EAAE,QAAQ;aACtB,CAAC;YAEF,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;YAE7D,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC;iBAChC,IAAI,CAAC,iBAAiB,CAAC;iBACvB,IAAI,CAAC,aAAa,CAAC,CAAC;YAEvB,IAAA,eAAM,EAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAClC,IAAA,eAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,IAAA,eAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;YAClD,IAAA,eAAM,EAAC,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC;gBACvD,IAAI,EAAE;oBACJ,GAAG,aAAa;oBAChB,WAAW,EAAE,QAAQ;iBACtB;aACF,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,iBAAQ,EAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,IAAA,WAAE,EAAC,2BAA2B,EAAE,KAAK,IAAI,EAAE;YACzC,MAAM,iBAAiB,GAAG;gBACxB,EAAE,EAAE,aAAa;gBACjB,IAAI,EAAE,UAAU;gBAChB,WAAW,EAAE,QAAQ;aACtB,CAAC;YAEF,MAAM,UAAU,GAAG;gBACjB,IAAI,EAAE,cAAc;gBACpB,WAAW,EAAE,qBAAqB;aACnC,CAAC;YAEF,MAAM,gBAAgB,GAAG;gBACvB,GAAG,iBAAiB;gBACpB,GAAG,UAAU;aACd,CAAC;YAEF,UAAU,CAAC,SAAS,CAAC,UAAU,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;YACrE,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;YAEhE,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC;iBAChC,GAAG,CAAC,6BAA6B,CAAC;iBAClC,IAAI,CAAC,UAAU,CAAC,CAAC;YAEpB,IAAA,eAAM,EAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAClC,IAAA,eAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,IAAA,eAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;YACrD,IAAA,eAAM,EAAC,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC;gBACvD,KAAK,EAAE,EAAE,EAAE,EAAE,aAAa,EAAE;gBAC5B,IAAI,EAAE,UAAU;aACjB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAA,WAAE,EAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;YACjE,MAAM,iBAAiB,GAAG;gBACxB,EAAE,EAAE,aAAa;gBACjB,IAAI,EAAE,WAAW;gBACjB,WAAW,EAAE,QAAQ;aACtB,CAAC;YAEF,UAAU,CAAC,SAAS,CAAC,UAAU,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;YAErE,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC;iBAChC,GAAG,CAAC,6BAA6B,CAAC;iBAClC,IAAI,CAAC,EAAE,IAAI,EAAE,cAAc,EAAE,CAAC,CAAC;YAElC,IAAA,eAAM,EAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAClC,IAAA,eAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1C,IAAA,eAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,iBAAQ,EAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,IAAA,WAAE,EAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,MAAM,iBAAiB,GAAG;gBACxB,EAAE,EAAE,aAAa;gBACjB,IAAI,EAAE,WAAW;gBACjB,WAAW,EAAE,QAAQ;aACtB,CAAC;YAEF,UAAU,CAAC,SAAS,CAAC,UAAU,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;YACrE,UAAU,CAAC,eAAe,CAAC,UAAU,CAAC,iBAAiB,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;YACtE,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;YAEjE,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,CAAC,MAAM,CAAC,6BAA6B,CAAC,CAAC;YAE1E,IAAA,eAAM,EAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAClC,IAAA,eAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,IAAA,eAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;YACrE,IAAA,eAAM,EAAC,UAAU,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,oBAAoB,CAAC;gBACjE,KAAK,EAAE,EAAE,WAAW,EAAE,aAAa,EAAE;aACtC,CAAC,CAAC;YACH,IAAA,eAAM,EAAC,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC;gBACvD,KAAK,EAAE,EAAE,EAAE,EAAE,aAAa,EAAE;aAC7B,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,IAAA,iBAAQ,EAAC,2BAA2B,EAAE,GAAG,EAAE;IACzC,IAAA,mBAAU,EAAC,GAAG,EAAE;QACd,WAAE,CAAC,aAAa,EAAE,CAAC;IACrB,CAAC,CAAC,CAAC;IAEH,IAAA,iBAAQ,EAAC,0CAA0C,EAAE,GAAG,EAAE;QACxD,IAAA,WAAE,EAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;YAClD,MAAM,aAAa,GAAG;gBACpB,EAAE,EAAE,aAAa;gBACjB,WAAW,EAAE,QAAQ;gBACrB,QAAQ,EAAE,KAAK;aAChB,CAAC;YAEF,MAAM,WAAW,GAAG;gBAClB;oBACE,EAAE,EAAE,UAAU;oBACd,KAAK,EAAE,cAAc;oBACrB,IAAI,EAAE,OAAO;oBACb,QAAQ,EAAE,CAAC;iBACZ;gBACD;oBACE,EAAE,EAAE,UAAU;oBACd,KAAK,EAAE,cAAc;oBACrB,IAAI,EAAE,OAAO;oBACb,QAAQ,EAAE,CAAC;iBACZ;aACF,CAAC;YAEF,UAAU,CAAC,SAAS,CAAC,UAAU,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;YACjE,UAAU,CAAC,eAAe,CAAC,QAAQ,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;YAEnE,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;YAE/E,IAAA,eAAM,EAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAClC,IAAA,eAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,IAAA,eAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;YAChD,IAAA,eAAM,EAAC,UAAU,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CAAC;gBAC/D,KAAK,EAAE,EAAE,WAAW,EAAE,aAAa,EAAE;gBACrC,OAAO,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE;aAC7B,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,iBAAQ,EAAC,2CAA2C,EAAE,GAAG,EAAE;QACzD,IAAA,WAAE,EAAC,4BAA4B,EAAE,KAAK,IAAI,EAAE;YAC1C,MAAM,aAAa,GAAG;gBACpB,EAAE,EAAE,aAAa;gBACjB,WAAW,EAAE,QAAQ;aACtB,CAAC;YAEF,MAAM,UAAU,GAAG;gBACjB,KAAK,EAAE,YAAY;gBACnB,IAAI,EAAE,OAAO;gBACb,MAAM,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;gBAC7B,KAAK,EAAE,CAAC;gBACR,MAAM,EAAE,CAAC;aACV,CAAC;YAEF,MAAM,UAAU,GAAG;gBACjB,EAAE,EAAE,UAAU;gBACd,WAAW,EAAE,aAAa;gBAC1B,GAAG,UAAU;gBACb,QAAQ,EAAE,CAAC;aACZ,CAAC;YAEF,UAAU,CAAC,SAAS,CAAC,UAAU,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;YACjE,UAAU,CAAC,eAAe,CAAC,SAAS,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAC7D,UAAU,CAAC,eAAe,CAAC,MAAM,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;YAEhE,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC;iBAChC,IAAI,CAAC,qCAAqC,CAAC;iBAC3C,IAAI,CAAC,UAAU,CAAC,CAAC;YAEpB,IAAA,eAAM,EAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAClC,IAAA,eAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,IAAA,eAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YAC/C,IAAA,eAAM,EAAC,UAAU,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC;gBAC7D,IAAI,EAAE;oBACJ,WAAW,EAAE,aAAa;oBAC1B,GAAG,UAAU;oBACb,QAAQ,EAAE,CAAC;iBACZ;aACF,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,iBAAQ,EAAC,mDAAmD,EAAE,GAAG,EAAE;QACjE,IAAA,WAAE,EAAC,wBAAwB,EAAE,KAAK,IAAI,EAAE;YACtC,MAAM,aAAa,GAAG;gBACpB,EAAE,EAAE,aAAa;gBACjB,WAAW,EAAE,QAAQ;aACtB,CAAC;YAEF,MAAM,WAAW,GAAG;gBAClB,EAAE,EAAE,EAAE,UAAU,EAAE,QAAQ,EAAE,CAAC,EAAE;gBAC/B,EAAE,EAAE,EAAE,UAAU,EAAE,QAAQ,EAAE,CAAC,EAAE;aAChC,CAAC;YAEF,UAAU,CAAC,SAAS,CAAC,UAAU,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;YACjE,UAAU,CAAC,eAAe,CAAC,MAAM,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC;YAExD,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC;iBAChC,IAAI,CAAC,6CAA6C,CAAC;iBACnD,IAAI,CAAC,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC,CAAC;YAElC,IAAA,eAAM,EAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAClC,IAAA,eAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,IAAA,eAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;YACrE,IAAA,eAAM,EAAC,UAAU,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["F:\\Amazing pay flow\\src\\tests\\dashboard.controller.test.ts"], "sourcesContent": ["import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';\nimport request from 'supertest';\nimport express from 'express';\nimport { DashboardController } from '../controllers/dashboard.controller';\nimport { DashboardWidgetController } from '../controllers/dashboard-widget.controller';\n\n// Mock Prisma\nconst mockPrisma = {\n  dashboard: {\n    findMany: vi.fn(),\n    findUnique: vi.fn(),\n    create: vi.fn(),\n    update: vi.fn(),\n    delete: vi.fn(),\n  },\n  dashboardWidget: {\n    findMany: vi.fn(),\n    findUnique: vi.fn(),\n    findFirst: vi.fn(),\n    create: vi.fn(),\n    update: vi.fn(),\n    delete: vi.fn(),\n    deleteMany: vi.fn(),\n  },\n};\n\nvi.mock('@prisma/client', () => ({\n  PrismaClient: vi.fn(() => mockPrisma),\n}));\n\nconst app = express();\napp.use(express.json());\n\n// Mock auth middleware\nconst mockAuthMiddleware = (req: any, res: any, next: any) => {\n  req.user = {\n    id: 'user-1',\n    role: 'MERCHANT',\n    email: '<EMAIL>',\n  };\n  next();\n};\n\n// Setup routes\nconst dashboardController = new DashboardController();\nconst widgetController = new DashboardWidgetController();\n\napp.get('/api/dashboards', mockAuthMiddleware, dashboardController.getDashboards);\napp.get('/api/dashboards/:id', mockAuthMiddleware, dashboardController.getDashboardById);\napp.post('/api/dashboards', mockAuthMiddleware, dashboardController.createDashboard);\napp.put('/api/dashboards/:id', mockAuthMiddleware, dashboardController.updateDashboard);\napp.delete('/api/dashboards/:id', mockAuthMiddleware, dashboardController.deleteDashboard);\n\napp.get('/api/dashboards/:dashboardId/widgets', mockAuthMiddleware, widgetController.getWidgets);\napp.get('/api/dashboards/widgets/:id', mockAuthMiddleware, widgetController.getWidgetById);\napp.post('/api/dashboards/:dashboardId/widgets', mockAuthMiddleware, widgetController.createWidget);\napp.put('/api/dashboards/widgets/:id', mockAuthMiddleware, widgetController.updateWidget);\napp.delete('/api/dashboards/widgets/:id', mockAuthMiddleware, widgetController.deleteWidget);\napp.post('/api/dashboards/:dashboardId/widgets/reorder', mockAuthMiddleware, widgetController.reorderWidgets);\n\ndescribe('DashboardController', () => {\n  beforeEach(() => {\n    vi.clearAllMocks();\n  });\n\n  describe('GET /api/dashboards', () => {\n    it('should get dashboards for the current user', async () => {\n      const mockDashboards = [\n        {\n          id: 'dashboard-1',\n          name: 'My Dashboard',\n          createdById: 'user-1',\n          isPublic: false,\n        },\n        {\n          id: 'dashboard-2',\n          name: 'Public Dashboard',\n          createdById: 'user-2',\n          isPublic: true,\n        },\n      ];\n\n      mockPrisma.dashboard.findMany.mockResolvedValue(mockDashboards);\n\n      const response = await request(app).get('/api/dashboards');\n\n      expect(response.status).toBe(200);\n      expect(response.body.success).toBe(true);\n      expect(response.body.data).toEqual(mockDashboards);\n      expect(mockPrisma.dashboard.findMany).toHaveBeenCalledWith({\n        where: {\n          OR: [\n            { createdById: 'user-1' },\n            { isPublic: true },\n          ],\n        },\n        orderBy: {\n          createdAt: 'desc',\n        },\n      });\n    });\n  });\n\n  describe('GET /api/dashboards/:id', () => {\n    it('should get a dashboard by ID with widgets', async () => {\n      const mockDashboard = {\n        id: 'dashboard-1',\n        name: 'My Dashboard',\n        createdById: 'user-1',\n        isPublic: false,\n        widgets: [\n          {\n            id: 'widget-1',\n            title: 'Transaction Chart',\n            type: 'CHART',\n            position: 0,\n          },\n        ],\n      };\n\n      mockPrisma.dashboard.findUnique.mockResolvedValue(mockDashboard);\n\n      const response = await request(app).get('/api/dashboards/dashboard-1');\n\n      expect(response.status).toBe(200);\n      expect(response.body.success).toBe(true);\n      expect(response.body.data).toEqual(mockDashboard);\n      expect(mockPrisma.dashboard.findUnique).toHaveBeenCalledWith({\n        where: { id: 'dashboard-1' },\n        include: {\n          widgets: {\n            orderBy: {\n              position: 'asc',\n            },\n          },\n        },\n      });\n    });\n\n    it('should return 404 for non-existent dashboard', async () => {\n      mockPrisma.dashboard.findUnique.mockResolvedValue(null);\n\n      const response = await request(app).get('/api/dashboards/non-existent');\n\n      expect(response.status).toBe(404);\n      expect(response.body.success).toBe(false);\n      expect(response.body.message).toBe('Dashboard not found');\n    });\n\n    it('should return 403 for private dashboard not owned by user', async () => {\n      const mockDashboard = {\n        id: 'dashboard-1',\n        name: 'Private Dashboard',\n        createdById: 'user-2',\n        isPublic: false,\n      };\n\n      mockPrisma.dashboard.findUnique.mockResolvedValue(mockDashboard);\n\n      const response = await request(app).get('/api/dashboards/dashboard-1');\n\n      expect(response.status).toBe(403);\n      expect(response.body.success).toBe(false);\n      expect(response.body.message).toBe('Access denied');\n    });\n  });\n\n  describe('POST /api/dashboards', () => {\n    it('should create a new dashboard', async () => {\n      const dashboardData = {\n        name: 'New Dashboard',\n        description: 'Test Description',\n        layout: { columns: 2 },\n        isPublic: false,\n      };\n\n      const mockDashboard = {\n        id: 'dashboard-1',\n        ...dashboardData,\n        createdById: 'user-1',\n      };\n\n      mockPrisma.dashboard.create.mockResolvedValue(mockDashboard);\n\n      const response = await request(app)\n        .post('/api/dashboards')\n        .send(dashboardData);\n\n      expect(response.status).toBe(201);\n      expect(response.body.success).toBe(true);\n      expect(response.body.data).toEqual(mockDashboard);\n      expect(mockPrisma.dashboard.create).toHaveBeenCalledWith({\n        data: {\n          ...dashboardData,\n          createdById: 'user-1',\n        },\n      });\n    });\n  });\n\n  describe('PUT /api/dashboards/:id', () => {\n    it('should update a dashboard', async () => {\n      const existingDashboard = {\n        id: 'dashboard-1',\n        name: 'Old Name',\n        createdById: 'user-1',\n      };\n\n      const updateData = {\n        name: 'Updated Name',\n        description: 'Updated Description',\n      };\n\n      const updatedDashboard = {\n        ...existingDashboard,\n        ...updateData,\n      };\n\n      mockPrisma.dashboard.findUnique.mockResolvedValue(existingDashboard);\n      mockPrisma.dashboard.update.mockResolvedValue(updatedDashboard);\n\n      const response = await request(app)\n        .put('/api/dashboards/dashboard-1')\n        .send(updateData);\n\n      expect(response.status).toBe(200);\n      expect(response.body.success).toBe(true);\n      expect(response.body.data).toEqual(updatedDashboard);\n      expect(mockPrisma.dashboard.update).toHaveBeenCalledWith({\n        where: { id: 'dashboard-1' },\n        data: updateData,\n      });\n    });\n\n    it('should return 403 for dashboard not owned by user', async () => {\n      const existingDashboard = {\n        id: 'dashboard-1',\n        name: 'Dashboard',\n        createdById: 'user-2',\n      };\n\n      mockPrisma.dashboard.findUnique.mockResolvedValue(existingDashboard);\n\n      const response = await request(app)\n        .put('/api/dashboards/dashboard-1')\n        .send({ name: 'Updated Name' });\n\n      expect(response.status).toBe(403);\n      expect(response.body.success).toBe(false);\n      expect(response.body.message).toBe('Access denied');\n    });\n  });\n\n  describe('DELETE /api/dashboards/:id', () => {\n    it('should delete a dashboard and its widgets', async () => {\n      const existingDashboard = {\n        id: 'dashboard-1',\n        name: 'Dashboard',\n        createdById: 'user-1',\n      };\n\n      mockPrisma.dashboard.findUnique.mockResolvedValue(existingDashboard);\n      mockPrisma.dashboardWidget.deleteMany.mockResolvedValue({ count: 2 });\n      mockPrisma.dashboard.delete.mockResolvedValue(existingDashboard);\n\n      const response = await request(app).delete('/api/dashboards/dashboard-1');\n\n      expect(response.status).toBe(200);\n      expect(response.body.success).toBe(true);\n      expect(response.body.message).toBe('Dashboard deleted successfully');\n      expect(mockPrisma.dashboardWidget.deleteMany).toHaveBeenCalledWith({\n        where: { dashboardId: 'dashboard-1' },\n      });\n      expect(mockPrisma.dashboard.delete).toHaveBeenCalledWith({\n        where: { id: 'dashboard-1' },\n      });\n    });\n  });\n});\n\ndescribe('DashboardWidgetController', () => {\n  beforeEach(() => {\n    vi.clearAllMocks();\n  });\n\n  describe('GET /api/dashboards/:dashboardId/widgets', () => {\n    it('should get widgets for a dashboard', async () => {\n      const mockDashboard = {\n        id: 'dashboard-1',\n        createdById: 'user-1',\n        isPublic: false,\n      };\n\n      const mockWidgets = [\n        {\n          id: 'widget-1',\n          title: 'Chart Widget',\n          type: 'CHART',\n          position: 0,\n        },\n        {\n          id: 'widget-2',\n          title: 'Table Widget',\n          type: 'TABLE',\n          position: 1,\n        },\n      ];\n\n      mockPrisma.dashboard.findUnique.mockResolvedValue(mockDashboard);\n      mockPrisma.dashboardWidget.findMany.mockResolvedValue(mockWidgets);\n\n      const response = await request(app).get('/api/dashboards/dashboard-1/widgets');\n\n      expect(response.status).toBe(200);\n      expect(response.body.success).toBe(true);\n      expect(response.body.data).toEqual(mockWidgets);\n      expect(mockPrisma.dashboardWidget.findMany).toHaveBeenCalledWith({\n        where: { dashboardId: 'dashboard-1' },\n        orderBy: { position: 'asc' },\n      });\n    });\n  });\n\n  describe('POST /api/dashboards/:dashboardId/widgets', () => {\n    it('should create a new widget', async () => {\n      const mockDashboard = {\n        id: 'dashboard-1',\n        createdById: 'user-1',\n      };\n\n      const widgetData = {\n        title: 'New Widget',\n        type: 'CHART',\n        config: { chartType: 'line' },\n        width: 2,\n        height: 1,\n      };\n\n      const mockWidget = {\n        id: 'widget-1',\n        dashboardId: 'dashboard-1',\n        ...widgetData,\n        position: 0,\n      };\n\n      mockPrisma.dashboard.findUnique.mockResolvedValue(mockDashboard);\n      mockPrisma.dashboardWidget.findFirst.mockResolvedValue(null);\n      mockPrisma.dashboardWidget.create.mockResolvedValue(mockWidget);\n\n      const response = await request(app)\n        .post('/api/dashboards/dashboard-1/widgets')\n        .send(widgetData);\n\n      expect(response.status).toBe(201);\n      expect(response.body.success).toBe(true);\n      expect(response.body.data).toEqual(mockWidget);\n      expect(mockPrisma.dashboardWidget.create).toHaveBeenCalledWith({\n        data: {\n          dashboardId: 'dashboard-1',\n          ...widgetData,\n          position: 0,\n        },\n      });\n    });\n  });\n\n  describe('POST /api/dashboards/:dashboardId/widgets/reorder', () => {\n    it('should reorder widgets', async () => {\n      const mockDashboard = {\n        id: 'dashboard-1',\n        createdById: 'user-1',\n      };\n\n      const widgetsData = [\n        { id: 'widget-1', position: 1 },\n        { id: 'widget-2', position: 0 },\n      ];\n\n      mockPrisma.dashboard.findUnique.mockResolvedValue(mockDashboard);\n      mockPrisma.dashboardWidget.update.mockResolvedValue({});\n\n      const response = await request(app)\n        .post('/api/dashboards/dashboard-1/widgets/reorder')\n        .send({ widgets: widgetsData });\n\n      expect(response.status).toBe(200);\n      expect(response.body.success).toBe(true);\n      expect(response.body.message).toBe('Widgets reordered successfully');\n      expect(mockPrisma.dashboardWidget.update).toHaveBeenCalledTimes(2);\n    });\n  });\n});\n"], "version": 3}