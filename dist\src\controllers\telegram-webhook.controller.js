"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TelegramWebhookController = void 0;
const base_controller_1 = require("./base.controller");
const asyncHandler_1 = require("../utils/asyncHandler");
const AppError_1 = require("../utils/errors/AppError");
const telegram_service_1 = require("../services/telegram.service");
/**
 * TelegramWebhookController
 */
class TelegramWebhookController extends base_controller_1.BaseController {
    constructor() {
        super();
        /**
         * Handle incoming webhook from Telegram
         */
        this.handleWebhook = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Get webhook data
                const update = req.body;
                // Validate webhook data
                if (!update || !update.message) {
                    return res.status(200).json({ success: true });
                }
                // Process webhook data
                const telegramService = new telegram_service_1.TelegramService();
                await telegramService.processUpdate(update);
                // Return success
                return res.status(200).json({ success: true });
            }
            catch (error) {
                console.error("Error handling webhook:", error);
                // Always return 200 to Telegram to prevent retries
                return res.status(200).json({ success: false });
            }
        });
        /**
         * Set webhook URL for Telegram bot
         */
        this.setWebhook = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Get user role
                const userRole = req.user?.role;
                // Check if user is authorized
                if (!userRole || userRole !== "ADMIN") {
                    throw new AppError_1.AppError({
                        message: "Unauthorized",
                        type: ErrorType.AUTHENTICATION,
                        code: ErrorCode.INVALID_CREDENTIALS
                    });
                }
                // Get webhook URL
                const { url } = req.body;
                // Validate webhook URL
                if (!url) {
                    throw new AppError_1.AppError({
                        message: "Webhook URL is required",
                        type: ErrorType.VALIDATION,
                        code: ErrorCode.MISSING_REQUIRED_FIELD
                    });
                }
                // Set webhook
                const telegramService = new telegram_service_1.TelegramService();
                const success = await telegramService.setWebhook(url);
                if (!success) {
                    throw new AppError_1.AppError({
                        message: "Failed to set webhook",
                        type: ErrorType.INTERNAL,
                        code: ErrorCode.INTERNAL_SERVER_ERROR
                    });
                }
                // Return success
                return res.status(200).json({
                    success: true,
                    message: "Webhook set successfully"
                });
            }
            catch (error) {
                if (error instanceof AppError_1.AppError) {
                    throw error;
                }
                throw new AppError_1.AppError({
                    message: "Failed to set webhook",
                    type: ErrorType.INTERNAL,
                    code: ErrorCode.INTERNAL_SERVER_ERROR
                });
            }
        });
        /**
         * Delete webhook for Telegram bot
         */
        this.deleteWebhook = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Get user role
                const userRole = req.user?.role;
                // Check if user is authorized
                if (!userRole || userRole !== "ADMIN") {
                    throw new AppError_1.AppError({
                        message: "Unauthorized",
                        type: ErrorType.AUTHENTICATION,
                        code: ErrorCode.INVALID_CREDENTIALS
                    });
                }
                // Delete webhook
                const telegramService = new telegram_service_1.TelegramService();
                const success = await telegramService.deleteWebhook();
                if (!success) {
                    throw new AppError_1.AppError({
                        message: "Failed to delete webhook",
                        type: ErrorType.INTERNAL,
                        code: ErrorCode.INTERNAL_SERVER_ERROR
                    });
                }
                // Return success
                return res.status(200).json({
                    success: true,
                    message: "Webhook deleted successfully"
                });
            }
            catch (error) {
                if (error instanceof AppError_1.AppError) {
                    throw error;
                }
                throw new AppError_1.AppError({
                    message: "Failed to delete webhook",
                    type: ErrorType.INTERNAL,
                    code: ErrorCode.INTERNAL_SERVER_ERROR
                });
            }
        });
        /**
         * Get webhook info for Telegram bot
         */
        this.getWebhookInfo = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Get user role
                const userRole = req.user?.role;
                // Check if user is authorized
                if (!userRole || userRole !== "ADMIN") {
                    throw new AppError_1.AppError({
                        message: "Unauthorized",
                        type: ErrorType.AUTHENTICATION,
                        code: ErrorCode.INVALID_CREDENTIALS
                    });
                }
                // Get webhook info
                const telegramService = new telegram_service_1.TelegramService();
                const webhookInfo = await telegramService.getWebhookInfo();
                if (!webhookInfo) {
                    throw new AppError_1.AppError({
                        message: "Failed to get webhook info",
                        type: ErrorType.INTERNAL,
                        code: ErrorCode.INTERNAL_SERVER_ERROR
                    });
                }
                // Return webhook info
                return res.status(200).json({
                    success: true,
                    data: webhookInfo
                });
            }
            catch (error) {
                if (error instanceof AppError_1.AppError) {
                    throw error;
                }
                throw new AppError_1.AppError({
                    message: "Failed to get webhook info",
                    type: ErrorType.INTERNAL,
                    code: ErrorCode.INTERNAL_SERVER_ERROR
                });
            }
        });
        /**
         * Get bot info for Telegram bot
         */
        this.getBotInfo = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Get user role
                const userRole = req.user?.role;
                // Check if user is authorized
                if (!userRole || userRole !== "ADMIN") {
                    throw new AppError_1.AppError({
                        message: "Unauthorized",
                        type: ErrorType.AUTHENTICATION,
                        code: ErrorCode.INVALID_CREDENTIALS
                    });
                }
                // Get bot info
                const telegramService = new telegram_service_1.TelegramService();
                const botInfo = await telegramService.getMe();
                if (!botInfo) {
                    throw new AppError_1.AppError({
                        message: "Failed to get bot info",
                        type: ErrorType.INTERNAL,
                        code: ErrorCode.INTERNAL_SERVER_ERROR
                    });
                }
                // Return bot info
                return res.status(200).json({
                    success: true,
                    data: botInfo
                });
            }
            catch (error) {
                if (error instanceof AppError_1.AppError) {
                    throw error;
                }
                throw new AppError_1.AppError({
                    message: "Failed to get bot info",
                    type: ErrorType.INTERNAL,
                    code: ErrorCode.INTERNAL_SERVER_ERROR
                });
            }
        });
        /**
         * Send test message to a Telegram chat
         */
        this.sendTestMessage = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Get user role and ID
                // Check authorization
                const { userRole, userId, merchantId } = this.checkAuthorization(req);
                // Get chat ID
                const { chatId } = req.body;
                // Validate chat ID
                if (!chatId) {
                    throw new AppError_1.AppError({
                        message: "Chat ID is required",
                        type: ErrorType.VALIDATION,
                        code: ErrorCode.MISSING_REQUIRED_FIELD
                    });
                }
                // Send test message
                const telegramService = new telegram_service_1.TelegramService();
                const success = await telegramService.sendMessage(chatId, "*Test Message*\n\nThis is a test message from AmazingPay. If you received this message, your Telegram notifications are working correctly.");
                if (!success) {
                    throw new AppError_1.AppError({
                        message: "Failed to send test message",
                        type: ErrorType.INTERNAL,
                        code: ErrorCode.INTERNAL_SERVER_ERROR
                    });
                }
                // Return success
                return res.status(200).json({
                    success: true,
                    message: "Test message sent successfully"
                });
            }
            catch (error) {
                if (error instanceof AppError_1.AppError) {
                    throw error;
                }
                throw new AppError_1.AppError({
                    message: "Failed to send test message",
                    type: ErrorType.INTERNAL,
                    code: ErrorCode.INTERNAL_SERVER_ERROR
                });
            }
        });
    }
}
exports.TelegramWebhookController = TelegramWebhookController;
exports.default = new TelegramWebhookController();
//# sourceMappingURL=telegram-webhook.controller.js.map