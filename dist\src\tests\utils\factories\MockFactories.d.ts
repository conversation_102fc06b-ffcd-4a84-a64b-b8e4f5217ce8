/**
 * Mock Factories
 *
 * Factory functions for creating mock objects used in tests.
 */
import { PrismaClient } from '@prisma/client';
import { MockRequest, MockResponse, MockNext, MockFactoryOptions } from '../core/TestTypes';
/**
 * Create a mock request object
 */
export declare function createMockRequest(options?: {
    params?: unknown;
    query?: unknown;
    body?: unknown;
    headers?: unknown;
    user?: unknown;
    session?: unknown;
    cookies?: unknown;
    ip?: string;
    method?: string;
    url?: string;
    originalUrl?: string;
    path?: string;
    protocol?: string;
    secure?: boolean;
    xhr?: boolean;
}): MockRequest;
/**
 * Create a mock response object
 */
export declare function createMockResponse(options?: {
    statusCode?: number;
    locals?: unknown;
    headersSent?: boolean;
}): MockResponse;
/**
 * Create a mock next function
 */
export declare function createMockNext(): MockNext;
/**
 * Create a mock database model
 */
export declare function createMockModel(modelName?: string): unknown;
/**
 * Create a mock Prisma client
 */
export declare function createMockPrismaClient(options?: MockFactoryOptions): PrismaClient;
/**
 * Create a mock JWT token
 */
export declare function createMockJwtToken(payload?: Record<string, unknown>, options?: {
    expiresIn?: string;
    issuer?: string;
    audience?: string;
}): string;
/**
 * Create mock API response
 */
export declare function createMockApiResponse(data: unknown, options?: {
    status?: number;
    message?: string;
    success?: boolean;
    pagination?: unknown;
    metadata?: unknown;
}): unknown;
/**
 * Create mock error response
 */
export declare function createMockErrorResponse(error: string | Error, options?: {
    status?: number;
    code?: string;
    details?: unknown;
}): unknown;
/**
 * Create mock file upload
 */
export declare function createMockFileUpload(options?: {
    filename?: string;
    mimetype?: string;
    size?: number;
    buffer?: Buffer;
}): unknown;
/**
 * Create mock WebSocket
 */
export declare function createMockWebSocket(): unknown;
/**
 * Reset all mocks in an object
 */
export declare function resetMocks(obj: Record<string, unknown>): void;
/**
 * Clear all mocks in an object
 */
export declare function clearMocks(obj: Record<string, unknown>): void;
//# sourceMappingURL=MockFactories.d.ts.map