// jscpd:ignore-file
/**
 * System Initializer
 *
 * Service for initializing the system with predefined components.
 */

import { PrismaClient } from "@prisma/client";
import { logger } from "../../lib/logger";
import { container } from "../../lib/DIContainer";
import { moduleRegistry } from "../../lib/ModuleRegistry";
import { eventBus } from "../../lib/EventBus";
import { RBACInitializer } from "../rbac/RBACInitializer";
import { VerificationService } from "../verification/VerificationService";
import { PREDEFINED_VERIFICATION_POLICIES } from "../../config/verification/PredefinedVerificationPolicies";
import { EnhancedPaymentService } from "../payment/EnhancedPaymentService";
import { EnhancedSubscriptionService } from "../enhanced-subscription.service";
import { FeeCalculator } from "../payment/fees/FeeCalculator";
import { PercentageFeeStrategy, TieredFeeStrategy, FixedFeeStrategy } from "../payment/fees/strategies/CommonFeeStrategies";
import { PaymentRouter } from "../payment/routing/PaymentRouter";
import { CountryBasedRule, AmountBasedRule, SuccessRateRule } from "../payment/routing/rules/CommonRoutingRules";
import { OperationalModeService, OperationalMode } from "./OperationalModeService";
import { logger } from "../../lib/logger";
import { container } from "../../lib/DIContainer";
import { moduleRegistry } from "../../lib/ModuleRegistry";
import { eventBus } from "../../lib/EventBus";
import { RBACInitializer } from "../rbac/RBACInitializer";
import { VerificationService } from "../verification/VerificationService";
import { PREDEFINED_VERIFICATION_POLICIES } from "../../config/verification/PredefinedVerificationPolicies";
import { EnhancedPaymentService } from "../payment/EnhancedPaymentService";
import { EnhancedSubscriptionService } from "../enhanced-subscription.service";
import { FeeCalculator } from "../payment/fees/FeeCalculator";
import { PercentageFeeStrategy, TieredFeeStrategy, FixedFeeStrategy } from "../payment/fees/strategies/CommonFeeStrategies";
import { PaymentRouter } from "../payment/routing/PaymentRouter";
import { CountryBasedRule, AmountBasedRule, SuccessRateRule } from "../payment/routing/rules/CommonRoutingRules";
import { OperationalModeService, OperationalMode } from "./OperationalModeService";

/**
 * System initializer service
 */
export class SystemInitializer {
    private prisma: PrismaClient;

    /**
   * Constructor
   *
   * @param prisma Prisma client
   */
    constructor(prisma: PrismaClient) {
        this.prisma = prisma;
    }

    /**
   * Initialize the system
   */
    public async initialize(): Promise<void> {
        logger.info("Initializing system");

        try {
            // Register core services in DI container
            this.registerServices();

            // Register modules in module registry
            this.registerModules();

            // Initialize operational mode service
            await this.initializeOperationalMode();

            // Initialize RBAC system
            await this.initializeRBAC();

            // Initialize verification system
            await this.initializeVerification();

            // Initialize payment system
            await this.initializePayment();

            // Set up event listeners
            this.setupEventListeners();

            logger.info("System initialized successfully");
        } catch (error) {
            logger.error("Error initializing system:", error);
            throw error;
        }
    }

    /**
   * Initialize operational mode service
   */
    private async initializeOperationalMode(): Promise<void> {
        logger.info("Initializing operational mode service");

        try {
            const operationalModeService: any =container.resolve<OperationalModeService>("operationalModeService");
            await operationalModeService.initialize();

            // Set default mode to production
            if (operationalModeService.getCurrentMode() !== OperationalMode.PRODUCTION) {
                await operationalModeService.setOperationalMode(OperationalMode.PRODUCTION, "system");
            }

            // Ensure system is enabled
            if (!operationalModeService.isSystemEnabled()) {
                await operationalModeService.setSystemEnabled(true, "system");
            }

            logger.info(`Operational mode initialized: ${operationalModeService.getCurrentMode()} (${operationalModeService.isSystemEnabled() ? "enabled" : "disabled"})`);
        } catch (error) {
            logger.error("Error initializing operational mode service:", error);
            throw error;
        }
    }

    /**
   * Register core services in DI container
   */
    private registerServices(): void {
        logger.info("Registering core services");

        // Register PrismaClient
        container.registerInstance("prisma", this.prisma);

        // Register operational mode service
        container.register("operationalModeService", () => {
            return new OperationalModeService(this.prisma);
        });

        // Register subscription service
        container.register("subscriptionService", () => {
            return new EnhancedSubscriptionService(this.prisma);
        });

        // Register payment service
        container.register("paymentService", () => {
            const subscriptionService: any =container.resolve<EnhancedSubscriptionService>("subscriptionService");
            return new EnhancedPaymentService(this.prisma, subscriptionService);
        });

        // Register verification service
        container.register("verificationService", () => {
            return new VerificationService(this.prisma);
        });

        // Register fee calculator
        container.register("feeCalculator", () => {
            const calculator: any =new FeeCalculator();

            calculator.addStrategies([
                new PercentageFeeStrategy(this.prisma),
                new TieredFeeStrategy(),
                new FixedFeeStrategy()
            ]);

            return calculator;
        });

        // Register payment router
        container.register("paymentRouter", () => {
            const router: any =new PaymentRouter();

            router.addRules([
                new CountryBasedRule(this.prisma),
                new AmountBasedRule(),
                new SuccessRateRule(this.prisma)
            ]);

            return router;
        });

        logger.info("Core services registered");
    }

    /**
   * Register modules in module registry
   */
    private registerModules(): void {
        logger.info("Registering modules");

        // Register core modules
        moduleRegistry.registerModule("core", {
            enabled: true,
            config: {},
            version: "1.0.0",
            description: "Core system module"
        });

        // Register RBAC module
        moduleRegistry.registerModule("rbac", {
            enabled: true,
            config: {},
            dependencies: ["core"],
            version: "1.0.0",
            description: "Role-based access control module"
        });

        // Register verification module
        moduleRegistry.registerModule("verification", {
            enabled: true,
            config: {},
            dependencies: ["core"],
            version: "1.0.0",
            description: "Verification module"
        });

        // Register payment module
        moduleRegistry.registerModule("payment", {
            enabled: true,
            config: {},
            dependencies: ["core", "verification"],
            version: "1.0.0",
            description: "Payment module"
        });

        // Register subscription module
        moduleRegistry.registerModule("subscription", {
            enabled: true,
            config: {},
            dependencies: ["core", "payment"],
            version: "1.0.0",
            description: "Subscription module"
        });

        logger.info("Modules registered");
    }

    /**
   * Initialize RBAC system
   */
    private async initializeRBAC(): Promise<void> {
        logger.info("Initializing RBAC system");

        const rbacInitializer: any =new RBACInitializer(this.prisma);
        await rbacInitializer.initialize();

        logger.info("RBAC system initialized");
    }

    /**
   * Initialize verification system
   */
    private async initializeVerification(): Promise<void> {
        logger.info("Initializing verification system");

        const verificationService: any =container.resolve<VerificationService>("verificationService");

        // Register predefined verification policies
        for (const policy of PREDEFINED_VERIFICATION_POLICIES) {
            verificationService.registerPolicy(policy);
        }

        logger.info("Verification system initialized");
    }

    /**
   * Initialize payment system
   */
    private async initializePayment(): Promise<void> {
        logger.info("Initializing payment system");

        // Nothing to do here yet

        logger.info("Payment system initialized");
    }

    /**
   * Set up event listeners
   */
    private setupEventListeners(): void {
        logger.info("Setting up event listeners");

        // Listen for verification events
        eventBus.on("verification.completed", async (data) => {
            logger.info(`Verification completed for transaction: ${data.transactionId}`, {
                success: data.success,
                merchantId: data.merchantId
            });

            // Update transaction status in database
            if (data.success) {
                try {
                    await this.prisma.transaction.update({
                        where: {, id: data.transactionId },
                        data: {, verificationStatus: "verified",
                            updatedAt: new Date()
                        }
                    });
                } catch (error) {
                    logger.error(`Error updating transaction status: ${(error as Error).message}`, {
                        transactionId: data.transactionId,
                        error
                    });
                }
            }
        });

        // Listen for payment events
        eventBus.on("payment.processed", async (data) => {
            logger.info(`Payment processed for transaction: ${data.transactionId}`, {
                success: data.success,
                merchantId: data.merchantId
            });
        });

        // Listen for module events
        eventBus.on("module.registered", (data) => {
            logger.info(`Module registered: ${data.name}`, {
                enabled: data.config.enabled,
                version: data.config.version
            });
        });

        logger.info("Event listeners set up");
    }
}
