// jscpd:ignore-file
import { Request, Response, NextFunction } from 'express';
import { BaseController } from "../../core/BaseController";
import { VersionRegistry, VersionStatus } from "../../core/VersionRegistry";
import { logger } from "../../lib/logger";
import { BaseController } from "../../core/BaseController";
import { VersionRegistry, VersionStatus } from "../../core/VersionRegistry";
import { logger } from "../../lib/logger";

/**
 * Version controller
 * This controller handles version-related requests
 */
export class VersionController extends BaseController {
  private versionRegistry: VersionRegistry;
  
  /**
   * Create a new version controller
   */
  constructor() {
    super();
    this.versionRegistry = VersionRegistry.getInstance();
    this.initializeVersions();
  }
  
  /**
   * Initialize versions
   */
  private initializeVersions(): void {
    // Register v1
    if (!this.versionRegistry.hasVersion("v1")) {
      this.versionRegistry.registerVersion(
        "v1",
        VersionStatus.ACTIVE,
        new Date("2023-01-01"),
        undefined,
        "Initial API version"
      );
    }
    
    // Set current version
    this.versionRegistry.setCurrentVersion("v1");
  }
  
  /**
   * Get all versions
   */
  getAllVersions = this.createHandler(async (req: Request, res: Response) => {
    const versions: any =this.versionRegistry.getAllVersions();
    const currentVersion: any =this.versionRegistry.getCurrentVersion();
    
    return this.sendSuccess(res, {
      versions,
      current: currentVersion
    });
  });
  
  /**
   * Get version by name
   */
  getVersionByName = this.createHandler(async (req: Request, res: Response) => {
    const { version } = req.params;
    
    try {
      const versionInfo: any =this.versionRegistry.getVersion(version);
      
      return this.sendSuccess(res, versionInfo);
    } catch (error) {
      return this.sendError(res, 404, `Version not found: ${version}`);
    }
  });
  
  /**
   * Get current version
   */
  getCurrentVersion = this.createHandler(async (req: Request, res: Response) => {
    const currentVersion: any =this.versionRegistry.getCurrentVersion();
    const versionInfo: any =this.versionRegistry.getVersion(currentVersion);
    
    return this.sendSuccess(res, versionInfo);
  });
  
  /**
   * Get active versions
   */
  getActiveVersions = this.createHandler(async (req: Request, res: Response) => {
    const versions: any =this.versionRegistry.getActiveVersions();
    
    return this.sendSuccess(res, {
      versions,
      count: versions.length
    });
  });
  
  /**
   * Get deprecated versions
   */
  getDeprecatedVersions = this.createHandler(async (req: Request, res: Response) => {
    const versions: any =this.versionRegistry.getDeprecatedVersions();
    
    return this.sendSuccess(res, {
      versions,
      count: versions.length
    });
  });
  
  /**
   * Register a new version
   */
  registerVersion = this.createHandler(async (req: Request, res: Response) => {
    // Check authorization
    const { userRole } = this.checkAuthorization(req);
    
    // Only admins can register versions
    if (userRole !== "ADMIN") {
      return this.sendError(res, 403, "Only admins can register versions");
    }
    
    const { version, status, releaseDate, sunsetDate, description } = req.body;
    
    // Validate required fields
    if (!version || !status || !releaseDate) {
      return this.sendError(res, 400, "Missing required fields: version, status, releaseDate");
    }
    
    // Check if version already exists
    if (this.versionRegistry.hasVersion(version)) {
      return this.sendError(res, 409, `Version already exists: ${version}`);
    }
    
    // Register version
    this.versionRegistry.registerVersion(
      version,
      status,
      new Date(releaseDate),
      sunsetDate ? new Date(sunsetDate) : undefined,
      description
    );
    
    // Get version info
    const versionInfo: any =this.versionRegistry.getVersion(version);
    
    return this.sendSuccess(res, versionInfo, 201);
  });
  
  /**
   * Update version status
   */
  updateVersionStatus = this.createHandler(async (req: Request, res: Response) => {
    // Check authorization
    const { userRole } = this.checkAuthorization(req);
    
    // Only admins can update version status
    if (userRole !== "ADMIN") {
      return this.sendError(res, 403, "Only admins can update version status");
    }
    
    const { version } = req.params;
    const { status, sunsetDate } = req.body;
    
    // Validate required fields
    if (!status) {
      return this.sendError(res, 400, "Missing required field: status");
    }
    
    // Check if version exists
    if (!this.versionRegistry.hasVersion(version)) {
      return this.sendError(res, 404, `Version not found: ${version}`);
    }
    
    // Update version status
    this.versionRegistry.updateVersionStatus(
      version,
      status,
      sunsetDate ? new Date(sunsetDate) : undefined
    );
    
    // Get version info
    const versionInfo: any =this.versionRegistry.getVersion(version);
    
    return this.sendSuccess(res, versionInfo);
  });
  
  /**
   * Set current version
   */
  setCurrentVersion = this.createHandler(async (req: Request, res: Response) => {
    // Check authorization
    const { userRole } = this.checkAuthorization(req);
    
    // Only admins can set current version
    if (userRole !== "ADMIN") {
      return this.sendError(res, 403, "Only admins can set current version");
    }
    
    const { version } = req.body;
    
    // Validate required fields
    if (!version) {
      return this.sendError(res, 400, "Missing required field: version");
    }
    
    // Check if version exists
    if (!this.versionRegistry.hasVersion(version)) {
      return this.sendError(res, 404, `Version not found: ${version}`);
    }
    
    // Set current version
    this.versionRegistry.setCurrentVersion(version);
    
    // Get version info
    const versionInfo: any =this.versionRegistry.getVersion(version);
    
    return this.sendSuccess(res, versionInfo);
  });
}

export default VersionController;
