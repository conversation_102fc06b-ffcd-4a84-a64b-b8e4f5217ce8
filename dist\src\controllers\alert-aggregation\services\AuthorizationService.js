"use strict";
/**
 * Authorization Service
 *
 * Handles authorization logic for alert aggregation operations.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthorizationService = void 0;
const AppError_1 = require("../../../utils/errors/AppError");
/**
 * Authorization service for alert aggregation
 */
class AuthorizationService {
    constructor() {
        this.adminRoles = ['ADMIN', 'SUPER_ADMIN'];
        this.managerRoles = ['MANAGER', 'ADMIN', 'SUPER_ADMIN'];
        this.userRoles = ['USER', 'MANAGER', 'ADMIN', 'SUPER_ADMIN'];
    }
    /**
     * Check if user is authorized for the given action
     */
    async checkPermission(context) {
        const { user, resource, action } = context;
        if (!user) {
            return {
                allowed: false,
                reason: 'User not authenticated',
            };
        }
        // Check role-based permissions
        const rolePermission = this.checkRolePermission(user.role, resource, action);
        if (!rolePermission.allowed) {
            return rolePermission;
        }
        // Check resource-specific permissions
        const resourcePermission = await this.checkResourcePermission(context);
        if (!resourcePermission.allowed) {
            return resourcePermission;
        }
        return { allowed: true };
    }
    /**
     * Check role-based permissions
     */
    checkRolePermission(userRole, resource, action) {
        switch (resource) {
            case 'aggregation-rules':
                return this.checkAggregationRulePermission(userRole, action);
            case 'correlation-rules':
                return this.checkCorrelationRulePermission(userRole, action);
            default:
                return {
                    allowed: false,
                    reason: `Unknown resource: ${resource}`,
                };
        }
    }
    /**
     * Check aggregation rule permissions
     */
    checkAggregationRulePermission(userRole, action) {
        switch (action) {
            case 'read':
                if (this.userRoles.includes(userRole)) {
                    return { allowed: true };
                }
                break;
            case 'create':
            case 'update':
            case 'delete':
                if (this.adminRoles.includes(userRole)) {
                    return { allowed: true };
                }
                return {
                    allowed: false,
                    reason: 'Admin role required for write operations',
                    requiredRole: 'ADMIN',
                };
            default:
                return {
                    allowed: false,
                    reason: `Unknown action: ${action}`,
                };
        }
        return {
            allowed: false,
            reason: 'Insufficient permissions',
            requiredRole: 'USER',
        };
    }
    /**
     * Check correlation rule permissions
     */
    checkCorrelationRulePermission(userRole, action) {
        switch (action) {
            case 'read':
                if (this.managerRoles.includes(userRole)) {
                    return { allowed: true };
                }
                return {
                    allowed: false,
                    reason: 'Manager role required for correlation rules',
                    requiredRole: 'MANAGER',
                };
            case 'create':
            case 'update':
            case 'delete':
                if (this.adminRoles.includes(userRole)) {
                    return { allowed: true };
                }
                return {
                    allowed: false,
                    reason: 'Admin role required for write operations',
                    requiredRole: 'ADMIN',
                };
            default:
                return {
                    allowed: false,
                    reason: `Unknown action: ${action}`,
                };
        }
    }
    /**
     * Check resource-specific permissions
     */
    async checkResourcePermission(context) {
        const { user, resource } = context;
        // For now, we don't have resource-specific permissions
        // This could be extended to check ownership, merchant-specific access, etc.
        // Example: Check if user can access merchant-specific resources
        if (user.merchantId && resource.includes('merchant')) {
            // Implement merchant-specific authorization logic here
            return { allowed: true };
        }
        return { allowed: true };
    }
    /**
     * Require admin role
     */
    requireAdmin(userRole) {
        if (!userRole || !this.adminRoles.includes(userRole)) {
            throw new AppError_1.AppError({
                message: 'Admin role required',
                type: AppError_1.ErrorType.AUTHENTICATION,
                code: AppError_1.ErrorCode.INVALID_CREDENTIALS,
            });
        }
    }
    /**
     * Require manager role or higher
     */
    requireManager(userRole) {
        if (!userRole || !this.managerRoles.includes(userRole)) {
            throw new AppError_1.AppError({
                message: 'Manager role required',
                type: AppError_1.ErrorType.AUTHENTICATION,
                code: AppError_1.ErrorCode.INVALID_CREDENTIALS,
            });
        }
    }
    /**
     * Require authenticated user
     */
    requireAuthenticated(userRole) {
        if (!userRole || !this.userRoles.includes(userRole)) {
            throw new AppError_1.AppError({
                message: 'Authentication required',
                type: AppError_1.ErrorType.AUTHENTICATION,
                code: AppError_1.ErrorCode.INVALID_CREDENTIALS,
            });
        }
    }
    /**
     * Check if user has specific role
     */
    hasRole(userRole, requiredRole) {
        const roleHierarchy = {
            USER: 1,
            MANAGER: 2,
            ADMIN: 3,
            SUPER_ADMIN: 4,
        };
        const userLevel = roleHierarchy[userRole] || 0;
        const requiredLevel = roleHierarchy[requiredRole] || 0;
        return userLevel >= requiredLevel;
    }
    /**
     * Get user permissions for a resource
     */
    getUserPermissions(userRole, resource) {
        const permissions = [];
        switch (resource) {
            case 'aggregation-rules':
                if (this.userRoles.includes(userRole)) {
                    permissions.push('read');
                }
                if (this.adminRoles.includes(userRole)) {
                    permissions.push('create', 'update', 'delete');
                }
                break;
            case 'correlation-rules':
                if (this.managerRoles.includes(userRole)) {
                    permissions.push('read');
                }
                if (this.adminRoles.includes(userRole)) {
                    permissions.push('create', 'update', 'delete');
                }
                break;
        }
        return permissions;
    }
    /**
     * Validate authorization context
     */
    validateAuthorizationContext(context) {
        if (!context.user) {
            throw new AppError_1.AppError({
                message: 'User context is required',
                type: AppError_1.ErrorType.AUTHENTICATION,
                code: AppError_1.ErrorCode.INVALID_CREDENTIALS,
            });
        }
        if (!context.resource) {
            throw new AppError_1.AppError({
                message: 'Resource is required',
                type: AppError_1.ErrorType.VALIDATION,
                code: AppError_1.ErrorCode.MISSING_REQUIRED_FIELD,
            });
        }
        if (!context.action) {
            throw new AppError_1.AppError({
                message: 'Action is required',
                type: AppError_1.ErrorType.VALIDATION,
                code: AppError_1.ErrorCode.MISSING_REQUIRED_FIELD,
            });
        }
    }
    /**
     * Create authorization context from request
     */
    createAuthorizationContext(user, resource, action, resourceId) {
        return {
            user: {
                id: user?.id,
                role: user?.role,
                merchantId: user?.merchantId,
            },
            resource,
            action,
            resourceId,
        };
    }
    /**
     * Handle authorization error
     */
    handleAuthorizationError(result) {
        const message = result.reason ?? 'Access denied';
        throw new AppError_1.AppError({
            message,
            type: AppError_1.ErrorType.AUTHENTICATION,
            code: AppError_1.ErrorCode.INVALID_CREDENTIALS,
            details: {
                requiredRole: result.requiredRole,
                requiredPermissions: result.requiredPermissions,
            },
        });
    }
}
exports.AuthorizationService = AuthorizationService;
//# sourceMappingURL=AuthorizationService.js.map