import { Request, Response, NextFunction } from "express";
import { Valida<PERSON><PERSON>hain } from "express-validator";
interface CustomValidationError {
    msg: string;
    param?: string;
    location?: string;
    path?: string;
    [key: string]: unknown;
}
/**
 * Request validator
 * This class provides a centralized way to validate requests
 */
export declare class RequestValidator {
    /**
     * Validate a request
     * @param req Express request
     * @throws AppError if validation fails
     */
    static validate(req: Request): void;
    /**
     * Format validation errors
     * @param errors Validation errors
     * @returns Formatted errors
     */
    static formatErrors(errors: CustomValidationError[]): Record<string, string[]>;
    /**
     * Check if required fields are present
     * @param req Express request
     * @param fields Required fields
     * @throws AppError if required fields are missing
     */
    static checkRequiredFields(req: Request, fields: string[]): void;
    /**
     * Create validation middleware
     * @param validations Validation chains
     * @returns Validation middleware
     */
    static createValidationMiddleware(validations: ValidationChain[]): (req: Request, res: Response, next: NextFunction) => Promise<void>;
}
export default RequestValidator;
//# sourceMappingURL=RequestValidator.d.ts.map