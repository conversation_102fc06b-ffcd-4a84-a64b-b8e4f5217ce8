/**
 * Identity Verification Types and Interfaces
 * 
 * Centralized type definitions for the identity verification system.
 */

import { IdentityVerificationMethodEnum, IdentityVerificationStatusEnum } from "@prisma/client";

/**
 * Error codes for identity verification
 */
export enum IdentityVerificationErrorCode {
  INVALID_SIGNATURE = "INVALID_SIGNATURE",
  INVALID_ADDRESS = "INVALID_ADDRESS",
  INVALID_PROOF = "INVALID_PROOF",
  VERIFICATION_FAILED = "VERIFICATION_FAILED",
  VERIFICATION_NOT_FOUND = "VERIFICATION_NOT_FOUND",
  CLAIM_NOT_FOUND = "CLAIM_NOT_FOUND",
  INTERNAL_ERROR = "INTERNAL_ERROR",
  PROVIDER_ERROR = "PROVIDER_ERROR",
  INVALID_PARAMETERS = "INVALID_PARAMETERS",
  UNAUTHORIZED = "UNAUTHORIZED",
}

/**
 * Identity verification result interface
 */
export interface IdentityVerificationResult {
  success: boolean;
  method: IdentityVerificationMethodEnum;
  status: IdentityVerificationStatusEnum;
  message: string;
  data?: any;
  error?: string;
  verificationId?: string;
}

/**
 * Ethereum signature verification parameters
 */
export interface EthereumSignatureParams {
  address: string;
  message: string;
  signature: string;
  userId?: string;
  merchantId?: string;
}

/**
 * ERC-1484 verification parameters
 */
export interface ERC1484Params {
  address: string;
  ein: string;
  registryAddress: string;
  userId?: string;
  merchantId?: string;
}

/**
 * ERC-725 verification parameters
 */
export interface ERC725Params {
  address: string;
  key: string;
  value: string;
  userId?: string;
  merchantId?: string;
}

/**
 * ENS verification parameters
 */
export interface ENSParams {
  ensName: string;
  address: string;
  userId?: string;
  merchantId?: string;
}

/**
 * Polygon ID verification parameters
 */
export interface PolygonIDParams {
  proof: any;
  publicSignals: any[];
  circuitId: string;
  userId?: string;
  merchantId?: string;
}

/**
 * Worldcoin verification parameters
 */
export interface WorldcoinParams {
  merkleRoot: string;
  nullifierHash: string;
  proof: any;
  userId?: string;
  merchantId?: string;
}

/**
 * BrightID verification parameters
 */
export interface BrightIDParams {
  contextId: string;
  userId?: string;
  merchantId?: string;
}

/**
 * Base verification method interface
 */
export interface IVerificationMethod {
  getName(): string;
  verify(params: any): Promise<IdentityVerificationResult>;
}

/**
 * Verification configuration
 */
export interface VerificationConfig {
  ethereumRpcUrl?: string;
  polygonIdVerifierAddress?: string;
  worldcoinAppId?: string;
  brightIdApiUrl?: string;
  enabledMethods: IdentityVerificationMethodEnum[];
}

/**
 * Verification claim interface
 */
export interface VerificationClaim {
  id: string;
  verificationId: string;
  claimType: string;
  claimValue: any;
  issuer?: string;
  issuedAt: Date;
  expiresAt?: Date;
  verified: boolean;
}

/**
 * Verification statistics
 */
export interface VerificationStats {
  totalVerifications: number;
  successfulVerifications: number;
  failedVerifications: number;
  pendingVerifications: number;
  verificationsByMethod: Record<string, number>;
  averageVerificationTime: number;
}

/**
 * Verification filter options
 */
export interface VerificationFilters {
  userId?: string;
  merchantId?: string;
  method?: IdentityVerificationMethodEnum;
  status?: IdentityVerificationStatusEnum;
  dateFrom?: Date;
  dateTo?: Date;
  limit?: number;
  offset?: number;
}
