"use strict";
// jscpd:ignore-file
/**
 * Production API Configuration
 *
 * This file contains the configuration for third-party APIs in production.
 * It includes settings for API keys, endpoints, and other production-specific options.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.productionApiConfig = exports.initializeApiConfigurations = exports.sentryConfig = exports.emailConfig = exports.binanceApiConfig = void 0;
const logger_1 = require("../../lib/logger");
/**
 * Binance API configuration for production
 */
exports.binanceApiConfig = {
    /**
   * Binance API key
   */
    apiKey: process.env.BINANCE_API_KEY || "",
    /**
   * Binance API secret
   */
    apiSecret: process.env.BINANCE_API_SECRET || "",
    /**
   * Binance API URL
   */
    apiUrl: process.env.BINANCE_API_URL || "https://api.binance.com",
    /**
   * Binance webhook secret for verifying webhook requests
   */
    webhookSecret: process.env.BINANCE_WEBHOOK_SECRET || "",
    /**
   * Request timeout in milliseconds
   */
    timeout: 30000,
    /**
   * Whether to use production endpoints
   */
    production: true,
    /**
   * Retry configuration
   */
    retry: {
        /**
         * Maximum number of retries
         */
        maxRetries: 3,
        /**
     * Delay between retries in milliseconds
     */
        retryDelay: 1000,
        /**
     * Whether to use exponential backoff
     */
        useExponentialBackoff: true
    }
};
/**
 * Email configuration for production
 */
exports.emailConfig = {
    /**
   * SMTP host
   */
    host: process.env.SMTP_HOST || "",
    /**
   * SMTP port
   */
    port: parseInt(process.env.SMTP_PORT || "587", 10),
    /**
   * SMTP username
   */
    user: process.env.SMTP_USER || "",
    /**
   * SMTP password
   */
    password: process.env.SMTP_PASSWORD || "",
    /**
   * Whether to use secure connection (TLS)
   */
    secure: process.env.SMTP_SECURE === "true",
    /**
   * From email address
   */
    from: process.env.EMAIL_FROM || "<EMAIL>",
    /**
   * From name
   */
    fromName: process.env.EMAIL_FROM_NAME || "AmazingPay"
};
/**
 * Sentry configuration for production
 */
exports.sentryConfig = {
    /**
   * Sentry DSN
   */
    dsn: process.env.SENTRY_DSN || "",
    /**
   * Sentry environment
   */
    environment: process.env.SENTRY_ENVIRONMENT || "production",
    /**
   * Traces sample rate
   */
    tracesSampleRate: parseFloat(process.env.SENTRY_TRACES_SAMPLE_RATE || "0.1"),
    /**
   * Whether to enable performance monitoring
   */
    enablePerformanceMonitoring: process.env.ENABLE_PERFORMANCE_MONITORING === "true"
};
/**
 * Initialize third-party API configurations
 * This function validates the configurations and logs warnings for missing values
 */
const initializeApiConfigurations = () => {
    logger_1.logger.info("Initializing production API configurations");
    // Validate Binance API configuration
    if (!exports.binanceApiConfig.apiKey || !exports.binanceApiConfig.apiSecret) {
        logger_1.logger.warn("Binance API credentials are missing. Binance payment methods may not work correctly.");
    }
    else {
        logger_1.logger.info("Binance API configuration initialized successfully");
    }
    // Validate email configuration
    if (!exports.emailConfig.host || !exports.emailConfig.user || !exports.emailConfig.password) {
        logger_1.logger.warn("Email configuration is incomplete. Email notifications may not work correctly.");
    }
    else {
        logger_1.logger.info("Email configuration initialized successfully");
    }
    // Validate Sentry configuration
    if (!exports.sentryConfig.dsn) {
        logger_1.logger.warn("Sentry DSN is missing. Error tracking will not be available.");
    }
    else {
        logger_1.logger.info("Sentry configuration initialized successfully");
    }
};
exports.initializeApiConfigurations = initializeApiConfigurations;
/**
 * Production API configuration
 */
exports.productionApiConfig = {
    binance: exports.binanceApiConfig,
    email: exports.emailConfig,
    sentry: exports.sentryConfig,
    initialize: exports.initializeApiConfigurations
};
exports.default = exports.productionApiConfig;
//# sourceMappingURL=production.api.config.js.map