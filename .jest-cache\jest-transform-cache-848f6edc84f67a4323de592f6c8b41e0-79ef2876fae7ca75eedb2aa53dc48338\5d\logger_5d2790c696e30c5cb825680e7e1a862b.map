{"file": "F:\\Amazing pay flow\\src\\lib\\logger.ts", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,oBAAoB;AACpB,iDAAmC;AACnC,2CAA6B;AAC7B,uCAAyB;AACzB,uCAAgD;AAEhD,0CAA0C;AAC1C,MAAM,UAAU,GAAO,GAAW,EAAE;IAChC,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,aAAa,CAAC;IAClD,MAAM,OAAO,GAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;IAE1D,IAAI,CAAC;QACD,+BAA+B;QAC/B,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;YAC1B,EAAE,CAAC,SAAS,CAAC,OAAO,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QAC/C,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,0EAA0E;QAC1E,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;QACvD,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,MAAM,CAAC,CAAC;IAC5C,CAAC;IAED,OAAO,OAAO,CAAC;AACnB,CAAC,CAAC;AAEF,yBAAyB;AACzB,MAAM,OAAO,GAAO,UAAU,EAAE,CAAC;AAEjC,oBAAoB;AACpB,MAAM,MAAM,GAAQ;IAChB,KAAK,EAAE,CAAC;IACR,IAAI,EAAE,CAAC;IACP,IAAI,EAAE,CAAC;IACP,IAAI,EAAE,CAAC;IACP,KAAK,EAAE,CAAC;CACX,CAAC;AAEF,wCAAwC;AACxC,MAAM,WAAW,GAAO,GAAG,EAAE;IACzB,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,aAAa,CAAC;IAElD,IAAI,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC;QACxB,OAAO,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC;IAC/C,CAAC;IAED,QAAQ,GAAG,EAAE,CAAC;QACd,KAAK,YAAY;YACb,OAAO,MAAM,CAAC;QAClB,KAAK,MAAM;YACP,OAAO,MAAM,CAAC;QAClB,KAAK,aAAa,CAAC;QACnB;YACI,OAAO,OAAO,CAAC;IACnB,CAAC;AACL,CAAC,CAAC;AAEF,+BAA+B;AAC/B,MAAM,MAAM,GAAQ;IAChB,KAAK,EAAE,KAAK;IACZ,IAAI,EAAE,QAAQ;IACd,IAAI,EAAE,OAAO;IACb,IAAI,EAAE,SAAS;IACf,KAAK,EAAE,MAAM;CAChB,CAAC;AAEF,wBAAwB;AACxB,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;AAE1B,4CAA4C;AAC5C,MAAM,aAAa,GAAO,OAAO,CAAC,MAAM,CAAC,OAAO,CAC5C,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,yBAAyB,EAAE,CAAC,EAC/D,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,EACtC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE;IAC3B,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,CAAC;IACpD,MAAM,QAAQ,GAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACnF,OAAO,GAAG,SAAS,IAAI,KAAK,KAAK,OAAO,IAAI,QAAQ,EAAE,CAAC;AAC3D,CAAC,CAAC,CACL,CAAC;AAEF,gDAAgD;AAChD,MAAM,UAAU,GAAO,OAAO,CAAC,MAAM,CAAC,OAAO,CACzC,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,EAC1B,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,CACxB,CAAC;AAEF,oCAAoC;AACpC,MAAM,cAAc,GAAO,CAAC,KAAa,EAAE,EAAE;IACzC,MAAM,IAAI,GAAG,IAAA,iBAAU,EAAC,IAAI,IAAI,EAAE,EAAE,YAAY,CAAC,CAAC;IAClD,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,KAAK,IAAI,IAAI,MAAM,CAAC,CAAC;AACtD,CAAC,CAAC;AAEF,6BAA6B;AAC7B,MAAM,UAAU,GAAO;IACnB,oBAAoB;IACpB,IAAI,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC;QAC3B,MAAM,EAAE,aAAa;KACxB,CAAC;IAEF,2BAA2B;IAC3B,IAAI,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC;QACxB,QAAQ,EAAE,cAAc,CAAC,OAAO,CAAC;QACjC,KAAK,EAAE,OAAO;QACd,MAAM,EAAE,UAAU;QAClB,OAAO,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE,OAAO;QAClC,QAAQ,EAAE,EAAE;KACf,CAAC;IAEF,0BAA0B;IAC1B,IAAI,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC;QACxB,QAAQ,EAAE,cAAc,CAAC,UAAU,CAAC;QACpC,MAAM,EAAE,UAAU;QAClB,OAAO,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE,OAAO;QAClC,QAAQ,EAAE,EAAE;KACf,CAAC;CACL,CAAC;AAEF,oBAAoB;AACP,QAAA,MAAM,GAAO,OAAO,CAAC,YAAY,CAAC;IAC3C,KAAK,EAAE,WAAW,EAAE;IACpB,MAAM;IACN,UAAU;IACV,WAAW,EAAE,KAAK;IAClB,gBAAgB,EAAE,IAAI;IACtB,gBAAgB,EAAE,IAAI;CACzB,CAAC,CAAC;AAEH,yBAAyB;AACzB,cAAM,CAAC,UAAU,CAAC,MAAM,CACpB,IAAI,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC;IACxB,QAAQ,EAAE,cAAc,CAAC,YAAY,CAAC;IACtC,MAAM,EAAE,UAAU;IAClB,OAAO,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE,OAAO;IAClC,QAAQ,EAAE,EAAE;CACf,CAAC,CACL,CAAC;AAEF,yBAAyB;AACzB,cAAM,CAAC,UAAU,CAAC,MAAM,CACpB,IAAI,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC;IACxB,QAAQ,EAAE,cAAc,CAAC,YAAY,CAAC;IACtC,MAAM,EAAE,UAAU;IAClB,OAAO,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE,OAAO;IAClC,QAAQ,EAAE,EAAE;CACf,CAAC,CACL,CAAC;AAEF,+CAA+C;AAClC,QAAA,MAAM,GAAQ;IACvB,KAAK,EAAE,CAAC,OAAe,EAAE,EAAE;QACvB,cAAM,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;IAChC,CAAC;CACJ,CAAC;AAEF,gCAAgC;AACzB,MAAM,YAAY,GAAO,CAAC,SAAiB,EAAE,EAAE;IAClD,OAAO,cAAM,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC;AACvC,CAAC,CAAC;AAFW,QAAA,YAAY,gBAEvB;AAEF,+BAA+B;AACxB,MAAM,YAAY,GAAO,CAAC,OAA4B,EAAE,EAAE;IAC7D,OAAO,cAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AACjC,CAAC,CAAC;AAFW,QAAA,YAAY,gBAEvB;AAEF,0BAA0B;AAC1B,cAAM,CAAC,IAAI,CAAC,kCAAkC,WAAW,EAAE,EAAE,CAAC,CAAC;AAC/D,cAAM,CAAC,IAAI,CAAC,gBAAgB,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,aAAa,EAAE,CAAC,CAAC;AACrE,cAAM,CAAC,IAAI,CAAC,qBAAqB,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,aAAa,EAAE,CAAC,CAAC;AAClF,cAAM,CAAC,IAAI,CAAC,wBAAwB,OAAO,EAAE,CAAC,CAAC", "names": [], "sources": ["F:\\Amazing pay flow\\src\\lib\\logger.ts"], "sourcesContent": ["// jscpd:ignore-file\nimport * as winston from \"winston\";\nimport * as path from \"path\";\nimport * as fs from \"fs\";\nimport { format as formatDate } from \"date-fns\";\n\n// Get environment-specific logs directory\nconst getLogsDir: any =(): string => {\n    const env = process.env.NODE_ENV || \"development\";\n    const logsDir: any =path.join(process.cwd(), \"logs\", env);\n\n    try {\n        // Ensure logs directory exists\n        if (!fs.existsSync(logsDir)) {\n            fs.mkdirSync(logsDir, { recursive: true });\n        }\n    } catch (error) {\n        // Fallback to a temporary directory if we can't create the logs directory\n        console.error('Error creating logs directory:', error);\n        return path.join(process.cwd(), 'temp');\n    }\n\n    return logsDir;\n};\n\n// Get the logs directory\nconst logsDir: any =getLogsDir();\n\n// Define log levels\nconst levels: any = {\n    error: 0,\n    warn: 1,\n    info: 2,\n    http: 3,\n    debug: 4\n};\n\n// Define log level based on environment\nconst getLogLevel: any =() => {\n    const env = process.env.NODE_ENV || \"development\";\n\n    if (process.env.LOG_LEVEL) {\n        return process.env.LOG_LEVEL.toLowerCase();\n    }\n\n    switch (env) {\n    case \"production\":\n        return \"info\";\n    case \"test\":\n        return \"warn\";\n    case \"development\":\n    default:\n        return \"debug\";\n    }\n};\n\n// Define colors for each level\nconst colors: any = {\n    error: \"red\",\n    warn: \"yellow\",\n    info: \"green\",\n    http: \"magenta\",\n    debug: \"blue\"\n};\n\n// Add colors to winston\nwinston.addColors(colors);\n\n// Create a custom format for console output\nconst consoleFormat: any =winston.format.combine(\n    winston.format.timestamp({ format: \"YYYY-MM-DD, HH:mm:ss:ms\" }),\n    winston.format.colorize({ all: true }),\n    winston.format.printf((info) => {\n        const { timestamp, level, message, ...rest } = info;\n        const metaData: any =Object.keys(rest).length ? JSON.stringify(rest, null, 2) : \"\";\n        return `${timestamp} ${level}: ${message} ${metaData}`;\n    })\n);\n\n// Create a custom format for file output (JSON)\nconst fileFormat: any =winston.format.combine(\n    winston.format.timestamp(),\n    winston.format.json()\n);\n\n// Generate date-based log filenames\nconst getLogFileName: any =(level: string) => {\n    const date = formatDate(new Date(), \"yyyy-MM-dd\");\n    return path.join(logsDir, `${level}-${date}.log`);\n};\n\n// Define where to store logs\nconst transports: any =[\n    // Console transport\n    new winston.transports.Console({\n        format: consoleFormat\n    }),\n\n    // Error log file transport\n    new winston.transports.File({\n        filename: getLogFileName(\"error\"),\n        level: \"error\",\n        format: fileFormat,\n        maxsize: 10 * 1024 * 1024, // 10MB\n        maxFiles: 30\n    }),\n\n    // All logs file transport\n    new winston.transports.File({\n        filename: getLogFileName(\"combined\"),\n        format: fileFormat,\n        maxsize: 10 * 1024 * 1024, // 10MB\n        maxFiles: 30\n    })\n];\n\n// Create the logger\nexport const logger: any =winston.createLogger({\n    level: getLogLevel(),\n    levels,\n    transports,\n    exitOnError: false,\n    handleExceptions: true,\n    handleRejections: true\n});\n\n// Add exception handlers\nlogger.exceptions.handle(\n    new winston.transports.File({\n        filename: getLogFileName(\"exceptions\"),\n        format: fileFormat,\n        maxsize: 10 * 1024 * 1024, // 10MB\n        maxFiles: 30\n    })\n);\n\n// Add rejection handlers\nlogger.rejections.handle(\n    new winston.transports.File({\n        filename: getLogFileName(\"rejections\"),\n        format: fileFormat,\n        maxsize: 10 * 1024 * 1024, // 10MB\n        maxFiles: 30\n    })\n);\n\n// Export a stream object for Morgan middleware\nexport const stream: any = {\n    write: (message: string) => {\n        logger.http(message.trim());\n    }\n};\n\n// Add request ID to log context\nexport const addRequestId: any =(requestId: string) => {\n    return logger.child({ requestId });\n};\n\n// Create a logger with context\nexport const createLogger: any =(context: Record<string, any>) => {\n    return logger.child(context);\n};\n\n// Log startup information\nlogger.info(`Logger initialized with level: ${getLogLevel()}`);\nlogger.info(`Environment: ${process.env.NODE_ENV || \"development\"}`);\nlogger.info(`Operational mode: ${process.env.OPERATIONAL_MODE || \"development\"}`);\nlogger.info(`Log files directory: ${logsDir}`);\n"], "version": 3}