// jscpd:ignore-file
import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';
import { createServer } from 'http';
import path from 'path';
import { monitorRequest, initializeMonitoring } from './utils/monitoring';
// import { AlertMonitorService } from './services/alert-monitor.service';
// import { AlertAggregationService } from './services/alert-aggregation.service';
// import { VerificationAlertJob } from './jobs/verification-alert.job';
// import { WebSocketService } from './services/websocket.service';
// import { TransactionMonitorService } from './services/transaction-monitor.service';
import { monitoringMiddleware } from './middlewares/monitoring.middleware';
import { apiResponseMiddleware, errorHandlerMiddleware } from './middlewares/apiResponseMiddleware';
// import { VerificationRealtimeService } from './services/websocket/verification-realtime.service';
import { Middleware } from './types/express';

// Import routes - only the ones we're currently using
import healthRoutes from './routes/health.routes';
import feeManagementTestRoutes from './routes/fee-management-test.routes';

// All monitoring and service imports are already at the top of the file

// Load environment variables
dotenv.config();

// Initialize Prisma client
export const prisma: unknown = new PrismaClient();

// Test database connection
const testDatabaseConnection: unknown = async () => {
  try {
    await prisma.$connect();
  } catch (error) {
    console.error('Database connection failed:', error);
    console.error('Please check your database configuration in .env file');
    process.exit(1);
  }
};

// Create Express app and HTTP server
const app: unknown = express();
const httpServer = createServer(app);
const PORT: unknown = process.env.PORT ?? 3002; // Use port 3002 as default

// Middleware
app.use(
  cors({
    origin: [
      'http://localhost:5173',
      'http://localhost:5174',
      'http://localhost:5175',
      'http://localhost:5176',
      'http://localhost:5177',
      'http://localhost:3000',
      'http://localhost:3002',
      'https://amazingpayme.com',
      '*',
    ],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Site-Domain'],
  })
);
app.use(
  helmet({
    crossOriginResourcePolicy: { policy: 'cross-origin' },
  })
);
app.use(morgan('dev'));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, 'public')));

// Initialize monitoring
const cleanupMonitoring: unknown = initializeMonitoring();

// Add monitoring middleware
app.use(monitorRequest);
app.use(monitoringMiddleware);

// Add API response middleware
app.use(apiResponseMiddleware);

// API routes - temporarily disable most routes to identify the issue
app.use('/api/health', healthRoutes);
app.use('/api/fee-management', feeManagementTestRoutes);

// Temporarily commented out routes to identify the issue
// app.use('/api/auth', authRoutes);
// app.use('/api/users', userRoutes);
// app.use('/api/merchants', merchantRoutes);
// app.use('/api/payment-methods', paymentMethodRoutes);
// app.use('/api/verification-methods', verificationMethodRoutes);
// app.use('/api/transactions', transactionRoutes);
// app.use('/api/payment-pages', paymentPageRoutes);
// app.use('/api/subscriptions', subscriptionRoutes);
// app.use('/api/monitoring', monitoringRoutes);
// app.use('/api/monitoring/websocket', websocketMonitoringRoutes);
// app.use('/api/binance', binanceRoutes);
// app.use('/webhook/binance-pay', binancePayWebhookRoutes);
// app.use('/api/location', locationRoutes);
// app.use('/api/verification', verificationRoutes);
// app.use('/api/webhooks', webhookRoutes);
// app.use('/api/alerts', alertRoutes);
// app.use('/api/alerts', alertAggregationRoutes);
// app.use('/api/alerts', alertAnalyticsRoutes);
// app.use('/api/email', emailRoutes);
// app.use('/api/sms', smsRoutes);
// app.use('/api/telegram', telegramRoutes);
// app.use('/api/notifications', notificationRoutes);
// app.use('/api/push', pushNotificationRoutes);
// app.use('/api/examples', exampleRoutes);
// app.use('/api/payment-verification', paymentVerificationRoutes);
// app.use('/api/fraud-detection', fraudDetectionRoutes);
// app.use('/api/payment-recommendation', paymentRecommendationRoutes);
// app.use('/api/identity-verification', identityVerificationRoutes);
// app.use('/api/risk', enhancedRiskEngineRoutes);
// app.use('/api/merchant-segmentation', merchantSegmentationRoutes);
// app.use('/api/merchant-relationship', merchantRelationshipRoutes);
// app.use('/api/merchant-self-service', merchantSelfServiceRoutes);
// app.use('/api/advanced-reports', advancedReportRoutes);
// app.use('/api/dashboards', dashboardRoutes);

// Static dashboard page
app.use('/dashboard/reports', express.static(path.join(__dirname, 'public/reports')));

// Basic health check endpoint (for load balancers)
app.get('/health', (req, res) => {
  res.success({ status: 'ok' }, 'Server is healthy and running');
});

// Error handling middleware
app.use(errorHandlerMiddleware);

// Initialize WebSocket server (temporarily disabled)
// const io: unknown = WebSocketService.initialize(httpServer);

// Initialize verification WebSocket service (temporarily disabled)
// import verificationWebSocketService from './services/websocket/verificationWebSocketService';
// verificationWebSocketService.initialize(httpServer);

// Initialize verification real-time service (temporarily disabled)
// const verificationRealtimeService: unknown = VerificationRealtimeService.getInstance();
// verificationRealtimeService.initialize(io);

// Initialize WebSocket monitor (temporarily disabled)
// import websocketMonitor from './utils/websocket-monitor';
// websocketMonitor.initialize(io);

// Initialize transaction monitor service (temporarily disabled)
// const transactionMonitor: unknown = TransactionMonitorService.getInstance();
// transactionMonitor.initialize().catch((error) => {
//   console.error('Failed to initialize transaction monitor service:', error);
// });

// Start server
const startServer: unknown = async () => {
  // Test database connection first
  await testDatabaseConnection();

  // Initialize advanced reporting scheduled reports
  try {
    const { AdvancedReportService } = await import('./services/advanced-report.service');
    const reportService = new AdvancedReportService();
    await reportService.initializeScheduledReports();
    console.log('📊 Advanced reporting scheduled reports initialized');
  } catch (error) {
    console.error('Failed to initialize advanced reporting:', error);
  }

  // Log all routes
  app._router.stack.forEach((middleware: unknown) => {
    if (middleware.route) {
      // Routes registered directly on the app
      console.log(`Route: ${middleware.route.path}`);
    } else if (middleware.name === 'router') {
      // Router middleware
      middleware.handle.stack.forEach((handler) => {
        if (handler.route) {
          const path: unknown = handler.route.path;
          const methods: unknown = Object.keys(handler.route.methods);
        }
      });
    }
  });

  return httpServer.listen(PORT, () => {
    console.log(`🚀 Server running on port ${PORT}`);
    console.log(`🔗 API available at http://localhost:${PORT}/api`);
    console.log(`🩺 Health check at http://localhost:${PORT}/health`);

    // Initialize and start alert monitor (temporarily disabled)
    // const alertMonitor: unknown = new AlertMonitorService();
    // alertMonitor.start();
    // (global as unknown).alertMonitor = alertMonitor;

    // Initialize and start alert aggregation service (temporarily disabled)
    // const alertAggregation: unknown = new AlertAggregationService();
    // alertAggregation.start();
    // (global as unknown).alertAggregation = alertAggregation;

    // Initialize and start verification alert job (temporarily disabled)
    // const verificationAlertJob: unknown = new VerificationAlertJob();
    // verificationAlertJob.start();
    // (global as unknown).verificationAlertJob = verificationAlertJob;
  });
};

// Start the server
startServer().catch((error) => {
  console.error('Failed to start server:', error);
  process.exit(1);
});

// Handle graceful shutdown
process.on('SIGTERM', async () => {
  // Close server
  httpServer.close(() => {});

  // Cleanup monitoring
  cleanupMonitoring();

  // Shutdown transaction monitor (temporarily disabled)
  // transactionMonitor.shutdown();

  // Shutdown alert monitor (temporarily disabled)
  // if ((global as unknown).alertMonitor) {
  //   (global as unknown).alertMonitor.stop();
  // }

  // Shutdown alert aggregation service (temporarily disabled)
  // if ((global as unknown).alertAggregation) {
  //   (global as unknown).alertAggregation.stop();
  // }

  // Shutdown verification alert job (temporarily disabled)
  // if ((global as unknown).verificationAlertJob) {
  //   (global as unknown).verificationAlertJob.stop();
  // }

  // Disconnect Prisma
  await prisma.$disconnect();

  process.exit(0);
});

export default app;
