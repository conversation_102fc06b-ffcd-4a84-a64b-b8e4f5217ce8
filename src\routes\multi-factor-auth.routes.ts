// jscpd:ignore-file
import express from "express";
import { authMiddleware as authenticate, authorize } from '../middlewares/auth.middleware';
import {
    getUserVerificationMethods,
    enableMFA,
    disableMFA
} from "../controllers/multi-factor-auth.controller";
import { authMiddleware as authenticate, authorize } from '../middlewares/auth.middleware';

const router: any =express.Router();

// Apply authentication middleware to all routes
router.use(authenticate);

// Get user's verification methods
router.get("/methods", authorize(["USER", "MERCHANT"]), getUserVerificationMethods);

// Enable MFA
router.post("/enable", authorize(["USER", "MERCHANT"]), enableMFA);

// Disable MFA
router.post("/disable", authorize(["USER", "MERCHANT"]), disableMFA);

export default router;
