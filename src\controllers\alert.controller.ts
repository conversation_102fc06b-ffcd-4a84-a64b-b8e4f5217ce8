// jscpd:ignore-file
import { Request, Response, NextFunction } from 'express';
import { BaseController } from "./base.controller";
import { asyncHandler } from '../utils/asyncHandler';
import { AppError } from '../utils/errors/AppError';
import { Alert, AlertStatus, AlertSeverity, AlertType } from '../types';
import { AlertService } from '../services/alert.service';
import { PrismaClient } from '@prisma/client';
import { BaseController } from "./base.controller";
import { asyncHandler } from '../utils/asyncHandler';
import { AppError } from '../utils/errors/AppError';
import { Alert, AlertStatus, AlertSeverity, AlertType } from '../types';
import { AlertService } from '../services/alert.service';
import { PrismaClient } from '@prisma/client';

// Extend the Express Request type to include user property
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role: string;
        merchantId?: string;
      };
    }
  }
}

const prisma: any = new PrismaClient();


/**
 * AlertController
 * Controller for handling alert operations
 */
export class AlertController extends BaseController {
  constructor() {
    super();
  }

  /**
   * Get alerts with filtering and pagination
   */
  getAlerts = asyncHandler(async (req: Request, res: Response) => {
    try {
        // Get user role and ID
        const { userRole, userId, merchantId } = this.checkAuthorization(req);

        // Get query parameters
        const status: unknown = req.query.status as AlertStatus | undefined;
        const severity: unknown = req.query.severity as AlertSeverity | undefined;
        const type: unknown = req.query.type as AlertType | undefined;
        const startDate: unknown = req.query.startDate ? new Date(req.query.startDate as string) : undefined;
        const endDate: unknown = req.query.endDate ? new Date(req.query.endDate as string) : undefined;
        const search: unknown = req.query.search as string | undefined;
        const limit: unknown = parseInt(req.query.limit as string) ?? 10;
        const offset: unknown = parseInt(req.query.offset as string) ?? 0;
        const sortBy: unknown = req.query.sortBy as string ?? "createdAt";
        const sortOrder: unknown = req.query.sortOrder as "asc" | "desc" || "desc";

        // Create alert service
        const alertService: unknown = new AlertService();

        // Get alerts based on user role
        let alerts;
        if (userRole === "ADMIN") {
            // Admins can see all alerts
            alerts = await alertService.getAlerts({
                merchantId: req.query.merchantId as string | undefined,
                status,
                severity,
                type,
                startDate,
                endDate,
                search,
                limit,
                offset,
                sortBy,
                sortOrder
            });
        } else {
            // Regular users can only see their merchant's alerts
            alerts = await alertService.getAlerts({
                merchantId,
                status,
                severity,
                type,
                startDate,
                endDate,
                search,
                limit,
                offset,
                sortBy,
                sortOrder
            });
        }

        // Return alerts
        return res.status(200).json({
            success: true,
            data: alerts
        });
    } catch (error) {
        if (error instanceof AppError) {
            throw error;
        }
        throw new AppError({
            message: "Failed to get alerts",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
    }
  });

  /**
   * Get a single alert by ID
   */
  getAlert = asyncHandler(async (req: Request, res: Response) => {
    try {
        // Get user role and ID
        const { userRole, userId, merchantId } = this.checkAuthorization(req);

        // Get alert ID from params
        const alertId: unknown = req.params.id;

        if (!alertId) {
            throw new AppError({
            message: "Alert ID is required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
        }

        // Create alert service
        const alertService: unknown = new AlertService();

        // Get alert
        const alert: unknown = await alertService.getAlert(alertId);

        // Check if user is authorized to view this alert
        if (userRole !== "ADMIN" && alert.merchantId !== merchantId) {
            throw new AppError({
            message: "Unauthorized",
            type: ErrorType.AUTHENTICATION,
            code: ErrorCode.INVALID_CREDENTIALS
        });
        }

        // Return alert
        return res.status(200).json({
            success: true,
            data: alert
        });
    } catch (error) {
        if (error instanceof AppError) {
            throw error;
        }
        throw new AppError({
            message: "Failed to get alert",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
    }
  });

  /**
   * Update the status of an alert
   */
  updateAlertStatus = asyncHandler(async (req: Request, res: Response) => {
    try {
        // Get user role and ID
        const { userRole, userId, merchantId } = this.checkAuthorization(req);

        // Get alert ID from params
        const alertId: unknown = req.params.id;

        if (!alertId) {
            throw new AppError({
            message: "Alert ID is required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
        }

        // Get status from body
        const { status } = req.body;

        if (!status || !Object.values(AlertStatus).includes(status as AlertStatus)) {
            throw new AppError({
            message: "Valid status is required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
        }

        // Create alert service
        const alertService: unknown = new AlertService();

        // Get alert to check authorization
        const alert: unknown = await alertService.getAlert(alertId);

        // Check if user is authorized to update this alert
        if (userRole !== "ADMIN" && alert.merchantId !== merchantId) {
            throw new AppError({
            message: "Unauthorized",
            type: ErrorType.AUTHENTICATION,
            code: ErrorCode.INVALID_CREDENTIALS
        });
        }

        // Update alert status
        const updatedAlert: unknown = await alertService.updateAlertStatus(
            alertId,
            status as AlertStatus,
            userId
        );

        // Return updated alert
        return res.status(200).json({
            success: true,
            data: updatedAlert
        });
    } catch (error) {
        if (error instanceof AppError) {
            throw error;
        }
        throw new AppError({
            message: "Failed to update alert status",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
    }
  });

  /**
   * Create a test alert for testing purposes
   */
  createTestAlert = asyncHandler(async (req: Request, res: Response) => {
    try {
        // Get user role and ID
        const { userRole, userId, merchantId } = this.checkAuthorization(req);

        // Only admins can create test alerts
        this.checkAdminRole(userRole);

        // Get alert data from body
        const { type, severity, title, message, details, targetMerchantId } = req.body;

        // Validate required fields
        if (!type || !severity || !title || !message) {
            throw new AppError({
            message: "Type, severity, title, and message are required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
        }

        // Validate type and severity
        if (!Object.values(AlertType).includes(type as AlertType)) {
            throw new AppError({
            message: "Invalid alert type",
            type: ErrorType.VALIDATION,
            code: ErrorCode.INVALID_INPUT
        });
        }

        if (!Object.values(AlertSeverity).includes(severity as AlertSeverity)) {
            throw new AppError({
            message: "Invalid alert severity",
            type: ErrorType.VALIDATION,
            code: ErrorCode.INVALID_INPUT
        });
        }

        // If targetMerchantId is provided, check if it exists
        if (targetMerchantId) {
            const merchant: unknown = await prisma.merchant.findUnique({
                where: { id: targetMerchantId }
            });

            if (!merchant) {
                throw new AppError({
            message: "Target merchant not found",
            type: ErrorType.NOT_FOUND,
            code: ErrorCode.RESOURCE_NOT_FOUND
        });
            }
        }

        // Create alert service
        const alertService: unknown = new AlertService();

        // Create test alert
        const alert: unknown = await alertService.createAlert({
            type: type as AlertType,
            severity: severity as AlertSeverity,
            title,
            message,
            details: details ?? {},
            merchantId: targetMerchantId ?? null,
            source: "TEST",
            createdBy: userId
        });

        // Return created alert
        return res.status(201).json({
            success: true,
            data: alert
        });
    } catch (error) {
        if (error instanceof AppError) {
            throw error;
        }
        throw new AppError({
            message: "Failed to create test alert",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
    }
  });

  /**
   * Get the count of alerts with filtering
   */
  getAlertCount = asyncHandler(async (req: Request, res: Response) => {
    try {
        // Get user role and ID
        const { userRole, userId, merchantId } = this.checkAuthorization(req);

        // Get query parameters
        const status: unknown = req.query.status as AlertStatus | undefined;
        const severity: unknown = req.query.severity as AlertSeverity | undefined;
        const type: unknown = req.query.type as AlertType | undefined;
        const startDate: unknown = req.query.startDate ? new Date(req.query.startDate as string) : undefined;
        const endDate: unknown = req.query.endDate ? new Date(req.query.endDate as string) : undefined;
        const search: unknown = req.query.search as string | undefined;

        // Create where clause based on user role
        const where: unknown = {};

        // Add status filter
        if (status) {
            where.status = status;
        }

        // Add severity filter
        if (severity) {
            where.severity = severity;
        }

        // Add type filter
        if (type) {
            where.type = type;
        }

        // Add date range filter
        if (startDate ?? endDate) {
            where.createdAt = {};

            if (startDate) {
                where.createdAt.gte = startDate;
            }

            if (endDate) {
                // Set end date to end of day
                const endOfDay: Date =new Date(endDate);
                endOfDay.setHours(23, 59, 59, 999);
                where.createdAt.lte = endOfDay;
            }
        }

        // Add search filter
        if (search) {
            where.OR = [
                { title: { contains: search, mode: "insensitive" } },
                { message: { contains: search, mode: "insensitive" } },
                { source: { contains: search, mode: "insensitive" } }
            ];
        }

        // Add merchant filter based on user role
        if (userRole !== "ADMIN" && merchantId) {
            where.merchantId = merchantId;
        } else if (userRole === "ADMIN" && req.query.merchantId) {
            where.merchantId = req.query.merchantId as string;
        }

        // Get alert count
        const count: unknown = await prisma.alert.count({ where });

        // Return count
        return res.status(200).json({
            success: true,
            data: { count }
        });
    } catch (error) {
        if (error instanceof AppError) {
            throw error;
        }
        throw new AppError({
            message: "Failed to get alert count",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
    }
  });

  /**
   * Helper method to check admin role
   */
  private checkAdminRole(userRole: string | undefined): void {
    if (!userRole ?? userRole !== "ADMIN") {
        throw new AppError({
            message: "Unauthorized. Admin role required.",
            type: ErrorType.AUTHENTICATION,
            code: ErrorCode.INVALID_CREDENTIALS
        });
    }
  }

  /**
   * Helper method to check authorization
   */
  private checkAuthorization(req: Request): { userRole: string, userId: string, merchantId: string | undefined } {
    const userRole: unknown = req.user?.role;
    const userId: unknown = req.user?.id;
    const merchantId: unknown = req.user?.merchantId;

    if (!userRole || !userId) {
        throw new AppError({
            message: "Unauthorized",
            type: ErrorType.AUTHENTICATION,
            code: ErrorCode.INVALID_CREDENTIALS
        });
    }

    return { userRole, userId, merchantId };
  }
}

export default new AlertController();
