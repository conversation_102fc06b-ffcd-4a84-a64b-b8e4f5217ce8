a79272c6c7f57f6b5562ed035c5b2ef3
"use strict";
/**
 * Identity Verification Service
 *
 * Main orchestrator for all identity verification methods.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.IdentityVerificationService = void 0;
const client_1 = require("@prisma/client");
const IdentityVerificationError_1 = require("./IdentityVerificationError");
const EthereumSignatureVerification_1 = require("../methods/EthereumSignatureVerification");
const logger_1 = require("../../../lib/logger");
/**
 * Main identity verification service
 */
class IdentityVerificationService {
    constructor(prisma, config) {
        this.prisma = prisma;
        this.config = {
            ethereumRpcUrl: process.env.ETHEREUM_RPC_URL || 'https://mainnet.infura.io/v3/your-infura-key',
            enabledMethods: [
                client_1.IdentityVerificationMethodEnum.ETHEREUM_SIGNATURE,
                client_1.IdentityVerificationMethodEnum.ERC1484,
                client_1.IdentityVerificationMethodEnum.ERC725,
                client_1.IdentityVerificationMethodEnum.ENS,
                client_1.IdentityVerificationMethodEnum.POLYGON_ID,
                client_1.IdentityVerificationMethodEnum.WORLDCOIN,
                client_1.IdentityVerificationMethodEnum.BRIGHTID,
            ],
            ...config,
        };
        // Initialize verification methods
        this.ethereumSignatureVerification = new EthereumSignatureVerification_1.EthereumSignatureVerification(this.prisma);
    }
    /**
     * Verify identity using Ethereum signature
     */
    async verifyEthereumSignature(params) {
        if (!this.isMethodEnabled(client_1.IdentityVerificationMethodEnum.ETHEREUM_SIGNATURE)) {
            throw IdentityVerificationError_1.IdentityVerificationError.invalidParameters('Ethereum signature verification is not enabled');
        }
        return await this.ethereumSignatureVerification.verify(params);
    }
    /**
     * Get verification by ID
     */
    async getVerificationById(id) {
        try {
            const verification = await this.prisma.identityVerification.findUnique({
                where: { id },
                include: { claims: true },
            });
            if (!verification) {
                throw IdentityVerificationError_1.IdentityVerificationError.verificationNotFound();
            }
            return verification;
        }
        catch (error) {
            if (error instanceof IdentityVerificationError_1.IdentityVerificationError) {
                throw error;
            }
            logger_1.logger.error('Error getting verification by ID:', error);
            throw IdentityVerificationError_1.IdentityVerificationError.internalError('Failed to retrieve identity verification');
        }
    }
    /**
     * Get verifications with filters
     */
    async getVerifications(filters = {}) {
        try {
            const where = {};
            if (filters.userId)
                where.userId = filters.userId;
            if (filters.merchantId)
                where.merchantId = filters.merchantId;
            if (filters.method)
                where.method = filters.method;
            if (filters.status)
                where.status = filters.status;
            if (filters.dateFrom || filters.dateTo) {
                where.createdAt = {};
                if (filters.dateFrom)
                    where.createdAt.gte = filters.dateFrom;
                if (filters.dateTo)
                    where.createdAt.lte = filters.dateTo;
            }
            return await this.prisma.identityVerification.findMany({
                where,
                include: { claims: true },
                orderBy: { createdAt: 'desc' },
                take: filters.limit || 50,
                skip: filters.offset || 0,
            });
        }
        catch (error) {
            logger_1.logger.error('Error getting verifications:', error);
            throw IdentityVerificationError_1.IdentityVerificationError.internalError('Failed to retrieve identity verifications');
        }
    }
    /**
     * Get verifications for user
     */
    async getVerificationsForUser(userId) {
        if (!userId) {
            throw IdentityVerificationError_1.IdentityVerificationError.invalidParameters('User ID is required');
        }
        return await this.getVerifications({ userId });
    }
    /**
     * Get verifications for merchant
     */
    async getVerificationsForMerchant(merchantId) {
        if (!merchantId) {
            throw IdentityVerificationError_1.IdentityVerificationError.invalidParameters('Merchant ID is required');
        }
        return await this.getVerifications({ merchantId });
    }
    /**
     * Get verification statistics
     */
    async getVerificationStats(filters = {}) {
        try {
            const where = {};
            if (filters.userId)
                where.userId = filters.userId;
            if (filters.merchantId)
                where.merchantId = filters.merchantId;
            if (filters.dateFrom || filters.dateTo) {
                where.createdAt = {};
                if (filters.dateFrom)
                    where.createdAt.gte = filters.dateFrom;
                if (filters.dateTo)
                    where.createdAt.lte = filters.dateTo;
            }
            const [totalVerifications, successfulVerifications, failedVerifications, pendingVerifications, verificationsByMethod,] = await Promise.all([
                this.prisma.identityVerification.count({ where }),
                this.prisma.identityVerification.count({
                    where: { ...where, status: client_1.IdentityVerificationStatusEnum.VERIFIED },
                }),
                this.prisma.identityVerification.count({
                    where: { ...where, status: client_1.IdentityVerificationStatusEnum.REJECTED },
                }),
                this.prisma.identityVerification.count({
                    where: { ...where, status: client_1.IdentityVerificationStatusEnum.PENDING },
                }),
                this.prisma.identityVerification.groupBy({
                    by: ['method'],
                    where,
                    _count: { method: true },
                }),
            ]);
            const methodStats = {};
            verificationsByMethod.forEach((item) => {
                methodStats[item.method] = item._count.method;
            });
            // Calculate average verification time (simplified)
            const averageVerificationTime = 5000; // 5 seconds average
            return {
                totalVerifications,
                successfulVerifications,
                failedVerifications,
                pendingVerifications,
                verificationsByMethod: methodStats,
                averageVerificationTime,
            };
        }
        catch (error) {
            logger_1.logger.error('Error getting verification stats:', error);
            throw IdentityVerificationError_1.IdentityVerificationError.internalError('Failed to retrieve verification statistics');
        }
    }
    /**
     * Check if verification method is enabled
     */
    isMethodEnabled(method) {
        return this.config.enabledMethods.includes(method);
    }
    /**
     * Get enabled verification methods
     */
    getEnabledMethods() {
        return [...this.config.enabledMethods];
    }
    /**
     * Update configuration
     */
    updateConfig(config) {
        this.config = { ...this.config, ...config };
    }
    /**
     * Health check for verification service
     */
    async healthCheck() {
        const errors = [];
        const methods = [];
        // Check each enabled method
        for (const method of this.config.enabledMethods) {
            try {
                switch (method) {
                    case client_1.IdentityVerificationMethodEnum.ETHEREUM_SIGNATURE:
                        // Test Ethereum connection
                        methods.push('ethereum_signature');
                        break;
                    // Add other method checks here
                    default:
                        methods.push(method);
                }
            }
            catch (error) {
                errors.push(`${method}: ${error instanceof Error ? error.message : 'Unknown error'}`);
            }
        }
        return {
            status: errors.length === 0 ? 'healthy' : 'degraded',
            methods,
            errors,
        };
    }
}
exports.IdentityVerificationService = IdentityVerificationService;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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