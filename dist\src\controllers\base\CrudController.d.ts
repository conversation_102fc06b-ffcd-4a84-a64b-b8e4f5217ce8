import { Request } from "express";
import { BaseController } from "./BaseController";
import { BaseService } from "../../services/base.service";
/**
 * Generic CRUD controller with common CRUD operations
 */
export declare abstract class CrudController<T, CreateInput, UpdateInput> extends BaseController {
    protected service: BaseService;
    protected entityName: string;
    protected requiredCreateFields: string[];
    protected requiredUpdateFields: string[];
    constructor(service: BaseService, entityName: string);
    /**
     * Get all entities
     * @route GET /api/{entity}
     */
    getAll: any;
    /**
     * Get entity by ID
     * @route GET /api/{entity}/:id
     */
    getById: any;
    /**
     * Create entity
     * @route POST /api/{entity}
     */
    create: any;
    /**
     * Update entity
     * @route PUT /api/{entity}/:id
     */
    update: any;
    /**
     * Delete entity
     * @route DELETE /api/{entity}/:id
     */
    delete: any;
    /**
     * Parse filters from request
     * @param req Request
     * @returns Filters
     */
    protected parseFilters(req: Request): Record<string, any>;
    /**
     * Get all entities
     * @param page Page number
     * @param limit Items per page
     * @param offset Offset
     * @param filters Filters
     * @returns Entities and total count
     */
    protected abstract getAllEntities(page: number, limit: number, offset: number, filters: Record<string, any>): Promise<{
        data: T[];
        total: number;
    }>;
    /**
     * Get entity by ID
     * @param id Entity ID
     * @returns Entity
     */
    protected abstract getEntityById(id: string): Promise<T>;
    /**
     * Create entity
     * @param data Entity data
     * @returns Created entity
     */
    protected abstract createEntity(data: CreateInput): Promise<T>;
    /**
     * Update entity
     * @param id Entity ID
     * @param data Entity data
     * @returns Updated entity
     */
    protected abstract updateEntity(id: string, data: UpdateInput): Promise<T>;
    /**
     * Delete entity
     * @param id Entity ID
     * @returns Deleted entity
     */
    protected abstract deleteEntity(id: string): Promise<void>;
    /**
     * Validate create input
     * @param req Request
     */
    protected validateCreateInput(req: Request): void;
    /**
     * Validate update input
     * @param req Request
     */
    protected validateUpdateInput(req: Request): void;
}
export default CrudController;
//# sourceMappingURL=CrudController.d.ts.map