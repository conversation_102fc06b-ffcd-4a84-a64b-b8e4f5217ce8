import { PaymentMethodType } from '../types';
/**
 * Encrypt sensitive data in payment method configuration
 * @param config Configuration object
 * @param type Payment method type
 */
export declare function encryptSensitiveData(config: Record<string, unknown>, type: PaymentMethodType): Record<string, unknown>;
/**
 * Decrypt sensitive data in payment method configuration
 * @param config Configuration object
 * @param type Payment method type
 */
export declare function decryptSensitiveData(config: Record<string, unknown>, type: PaymentMethodType): Record<string, unknown>;
/**
 * Encrypt a string
 * @param text Text to encrypt
 */
export declare function encrypt(text: string): string;
/**
 * Decrypt a string
 * @param encryptedText Encrypted text
 */
export declare function decrypt(encryptedText: string): string;
//# sourceMappingURL=encryption.d.ts.map