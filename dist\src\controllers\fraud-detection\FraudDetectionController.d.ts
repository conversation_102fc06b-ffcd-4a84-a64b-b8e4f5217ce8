/**
 * Fraud Detection Controller
 *
 * Modular controller for fraud detection operations.
 */
import { BaseController } from '../base.controller';
/**
 * Modular Fraud Detection Controller
 */
export declare class FraudDetectionController extends BaseController {
    private authService;
    private validationService;
    private businessService;
    constructor();
    /**
     * Assess transaction risk
     */
    assessTransactionRisk: any;
    /**
     * Get transaction risk assessment
     */
    getTransactionRiskAssessment: any;
    /**
     * Get merchant fraud configuration
     */
    getMerchantFraudConfig: any;
    /**
     * Update merchant fraud configuration
     */
    updateMerchantFraudConfig: any;
    /**
     * Get flagged transactions
     */
    getFlaggedTransactions: any;
    /**
     * Get fraud statistics
     */
    getFraudStatistics: any;
    /**
     * Health check endpoint
     */
    healthCheck: any;
}
//# sourceMappingURL=FraudDetectionController.d.ts.map