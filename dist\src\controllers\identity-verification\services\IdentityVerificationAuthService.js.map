{"version": 3, "file": "IdentityVerificationAuthService.js", "sourceRoot": "", "sources": ["../../../../../src/controllers/identity-verification/services/IdentityVerificationAuthService.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;AAEH,6DAAgF;AAChF,sGAIsD;AAEtD;;GAEG;AACH,MAAa,+BAA+B;IAA5C;QACmB,eAAU,GAAG,CAAC,8CAAQ,CAAC,KAAK,CAAC,CAAC;QAC9B,kBAAa,GAAG,CAAC,8CAAQ,CAAC,QAAQ,EAAE,8CAAQ,CAAC,KAAK,CAAC,CAAC;QACpD,cAAS,GAAG,CAAC,8CAAQ,CAAC,IAAI,EAAE,8CAAQ,CAAC,QAAQ,EAAE,8CAAQ,CAAC,KAAK,CAAC,CAAC;IAgVlF,CAAC;IA9UC;;OAEG;IACH,KAAK,CAAC,eAAe,CAAC,OAA6B;QACjD,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;QAE3C,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,wBAAwB;aACjC,CAAC;QACJ,CAAC;QAED,+BAA+B;QAC/B,MAAM,cAAc,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;QAC7E,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;YAC5B,OAAO,cAAc,CAAC;QACxB,CAAC;QAED,sCAAsC;QACtC,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC;QACvE,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;YAChC,OAAO,kBAAkB,CAAC;QAC5B,CAAC;QAED,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;IAC3B,CAAC;IAED;;OAEG;IACK,mBAAmB,CACzB,QAAgB,EAChB,QAAgB,EAChB,MAAc;QAEd,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,cAAc;gBACjB,OAAO,IAAI,CAAC,2BAA2B,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAC5D,KAAK,oBAAoB;gBACvB,OAAO,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YACrD,KAAK,oBAAoB;gBACvB,OAAO,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YACrD;gBACE,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,MAAM,EAAE,qBAAqB,QAAQ,EAAE;iBACxC,CAAC;QACN,CAAC;IACH,CAAC;IAED;;OAEG;IACK,2BAA2B,CAAC,QAAgB,EAAE,MAAc;QAClE,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,QAAQ,CAAC;YACd,KAAK,QAAQ;gBACX,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAoB,CAAC,EAAE,CAAC;oBAClD,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;gBAC3B,CAAC;gBACD,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,MAAM,EAAE,gDAAgD;oBACxD,YAAY,EAAE,MAAM;iBACrB,CAAC;YACJ,KAAK,MAAM;gBACT,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAoB,CAAC,EAAE,CAAC;oBAClD,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;gBAC3B,CAAC;gBACD,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,MAAM,EAAE,8CAA8C;oBACtD,YAAY,EAAE,MAAM;iBACrB,CAAC;YACJ,KAAK,QAAQ,CAAC;YACd,KAAK,QAAQ;gBACX,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAoB,CAAC,EAAE,CAAC;oBACnD,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;gBAC3B,CAAC;gBACD,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,MAAM,EAAE,iDAAiD;oBACzD,YAAY,EAAE,OAAO;iBACtB,CAAC;YACJ;gBACE,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,MAAM,EAAE,mBAAmB,MAAM,EAAE;iBACpC,CAAC;QACN,CAAC;IACH,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,QAAgB,EAAE,MAAc;QAC3D,IAAI,MAAM,KAAK,MAAM,EAAE,CAAC;YACtB,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,QAAoB,CAAC,EAAE,CAAC;gBACtD,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;YAC3B,CAAC;YACD,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,uCAAuC;gBAC/C,YAAY,EAAE,UAAU;aACzB,CAAC;QACJ,CAAC;QAED,OAAO;YACL,OAAO,EAAE,KAAK;YACd,MAAM,EAAE,mBAAmB,MAAM,EAAE;SACpC,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,QAAgB,EAAE,MAAc;QAC3D,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAoB,CAAC,EAAE,CAAC;YACnD,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;QAC3B,CAAC;QACD,OAAO;YACL,OAAO,EAAE,KAAK;YACd,MAAM,EAAE,mDAAmD;YAC3D,YAAY,EAAE,OAAO;SACtB,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,uBAAuB,CAAC,OAA6B;QACjE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;QAE3C,mDAAmD;QACnD,IAAI,QAAQ,KAAK,cAAc,IAAI,MAAM,KAAK,MAAM,EAAE,CAAC;YACrD,yCAAyC;YACzC,8DAA8D;YAC9D,oCAAoC;YACpC,IAAI,IAAI,CAAC,IAAI,KAAK,8CAAQ,CAAC,KAAK,EAAE,CAAC;gBACjC,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;YAC3B,CAAC;YAED,sEAAsE;YACtE,sCAAsC;YACtC,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;QAC3B,CAAC;QAED,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,QAAiB;QAC5B,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAoB,CAAC,EAAE,CAAC;YACjE,MAAM,IAAI,mBAAQ,CAAC;gBACjB,OAAO,EAAE,qBAAqB;gBAC9B,IAAI,EAAE,oBAAS,CAAC,cAAc;gBAC9B,IAAI,EAAE,oBAAS,CAAC,mBAAmB;aACpC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,QAAiB;QAC/B,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,QAAoB,CAAC,EAAE,CAAC;YACpE,MAAM,IAAI,mBAAQ,CAAC;gBACjB,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE,oBAAS,CAAC,cAAc;gBAC9B,IAAI,EAAE,oBAAS,CAAC,mBAAmB;aACpC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,QAAiB;QACpC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAoB,CAAC,EAAE,CAAC;YAChE,MAAM,IAAI,mBAAQ,CAAC;gBACjB,OAAO,EAAE,yBAAyB;gBAClC,IAAI,EAAE,oBAAS,CAAC,cAAc;gBAC9B,IAAI,EAAE,oBAAS,CAAC,mBAAmB;aACpC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,QAAgB,EAAE,YAAoB;QAC5C,MAAM,aAAa,GAA2B;YAC5C,CAAC,8CAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YAClB,CAAC,8CAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YACtB,CAAC,8CAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;SACpB,CAAC;QAEF,MAAM,SAAS,GAAG,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC/C,MAAM,aAAa,GAAG,aAAa,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QAEvD,OAAO,SAAS,IAAI,aAAa,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,QAAgB,EAAE,QAAgB;QACnD,MAAM,WAAW,GAAa,EAAE,CAAC;QAEjC,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,cAAc;gBACjB,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAoB,CAAC,EAAE,CAAC;oBAClD,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;gBAC/C,CAAC;gBACD,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAoB,CAAC,EAAE,CAAC;oBACnD,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;gBACvC,CAAC;gBACD,MAAM;YACR,KAAK,oBAAoB;gBACvB,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,QAAoB,CAAC,EAAE,CAAC;oBACtD,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC3B,CAAC;gBACD,MAAM;YACR,KAAK,oBAAoB;gBACvB,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAoB,CAAC,EAAE,CAAC;oBACnD,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;gBACzD,CAAC;gBACD,MAAM;QACV,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;OAEG;IACH,4BAA4B,CAAC,OAA6B;QACxD,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;YAClB,MAAM,IAAI,mBAAQ,CAAC;gBACjB,OAAO,EAAE,0BAA0B;gBACnC,IAAI,EAAE,oBAAS,CAAC,cAAc;gBAC9B,IAAI,EAAE,oBAAS,CAAC,mBAAmB;aACpC,CAAC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;YACtB,MAAM,IAAI,mBAAQ,CAAC;gBACjB,OAAO,EAAE,sBAAsB;gBAC/B,IAAI,EAAE,oBAAS,CAAC,UAAU;gBAC1B,IAAI,EAAE,oBAAS,CAAC,sBAAsB;aACvC,CAAC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YACpB,MAAM,IAAI,mBAAQ,CAAC;gBACjB,OAAO,EAAE,oBAAoB;gBAC7B,IAAI,EAAE,oBAAS,CAAC,UAAU;gBAC1B,IAAI,EAAE,oBAAS,CAAC,sBAAsB;aACvC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACH,0BAA0B,CACxB,IAAS,EACT,QAAgB,EAChB,MAAc,EACd,UAAmB;QAEnB,OAAO;YACL,IAAI,EAAE;gBACJ,EAAE,EAAE,IAAI,EAAE,EAAE;gBACZ,IAAI,EAAE,IAAI,EAAE,IAAI;gBAChB,UAAU,EAAE,IAAI,EAAE,UAAU;aAC7B;YACD,QAAQ;YACR,MAAM;YACN,UAAU;SACX,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,wBAAwB,CAAC,MAAwB;QAC/C,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,IAAI,eAAe,CAAC;QAEjD,MAAM,IAAI,mBAAQ,CAAC;YACjB,OAAO;YACP,IAAI,EAAE,oBAAS,CAAC,cAAc;YAC9B,IAAI,EAAE,oBAAS,CAAC,mBAAmB;YACnC,OAAO,EAAE;gBACP,YAAY,EAAE,MAAM,CAAC,YAAY;gBACjC,mBAAmB,EAAE,MAAM,CAAC,mBAAmB;aAChD;SACF,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,qBAAqB,CAAC,IAAS,EAAE,YAAiB;QAChD,qCAAqC;QACrC,IAAI,IAAI,CAAC,IAAI,KAAK,8CAAQ,CAAC,KAAK,EAAE,CAAC;YACjC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,2CAA2C;QAC3C,IAAI,IAAI,CAAC,IAAI,KAAK,8CAAQ,CAAC,IAAI,IAAI,YAAY,CAAC,MAAM,KAAK,IAAI,CAAC,EAAE,EAAE,CAAC;YACnE,OAAO,IAAI,CAAC;QACd,CAAC;QAED,gEAAgE;QAChE,IAAI,IAAI,CAAC,IAAI,KAAK,8CAAQ,CAAC,QAAQ,IAAI,YAAY,CAAC,UAAU,KAAK,IAAI,CAAC,EAAE,EAAE,CAAC;YAC3E,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,GAAQ;QACzB,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,IAAI,KAAK,8CAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;QAC1E,MAAM,UAAU,GAAG,GAAG,CAAC,IAAI,EAAE,IAAI,KAAK,8CAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;QAElF,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC;IAChC,CAAC;CACF;AAnVD,0EAmVC"}