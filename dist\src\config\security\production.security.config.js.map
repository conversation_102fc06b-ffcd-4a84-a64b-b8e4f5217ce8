{"version": 3, "file": "production.security.config.js", "sourceRoot": "", "sources": ["../../../../src/config/security/production.security.config.ts"], "names": [], "mappings": ";AAAA,oBAAoB;AACpB;;;;;GAKG;;;AAIH,6CAA0C;AAM1C;;GAEG;AACU,QAAA,SAAS,GAAQ;IAC5B;;OAEG;IACH,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,EAAE;IAEpC;;OAEG;IACH,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,IAAI;IAE7C;;OAEG;IACH,gBAAgB,EAAE,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,IAAI;IAE5D;;OAEG;IACH,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,aAAa,IAAI,OAAO;IAE/C;;OAEG;IACH,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,kBAAkB;IAEpD;;OAEG;IACH,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,kBAAkB;CACzD,CAAC;AAEF;;GAEG;AACU,QAAA,UAAU,GAAgB;IACrC;;OAEG;IACH,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE,KAAK,CAAC,GAAG,CAAC,IAAI;QAC7C,0BAA0B;QAC1B,8BAA8B;QAC9B,gCAAgC;KACjC;IAED;;OAEG;IACH,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE,KAAK,CAAC,GAAG,CAAC,IAAI;QAC/C,KAAK;QACL,MAAM;QACN,KAAK;QACL,OAAO;QACP,MAAM;QACN,QAAQ;KACT;IAED;;OAEG;IACH,iBAAiB,EAAE,OAAO,CAAC,GAAG,CAAC,uBAAuB,KAAK,MAAM;IAEjE;;OAEG;IACH,oBAAoB,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,2BAA2B,IAAI,KAAK,EAAE,EAAE,CAAC;IAEpF;;OAEG;IACH,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB,KAAK,MAAM,IAAI,IAAI;IAE5D;;OAEG;IACH,MAAM,EAAE,KAAK,EAAE,WAAW;CAC3B,CAAC;AAEF;;GAEG;AACU,QAAA,eAAe,GAAqB;IAC/C;;OAEG;IACH,QAAQ,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI,QAAQ,EAAE,EAAE,CAAC,EAAE,aAAa;IAEnF;;OAEG;IACH,GAAG,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,KAAK,EAAE,EAAE,CAAC;IAEtD;;OAEG;IACH,eAAe,EAAE,IAAI;IAErB;;OAEG;IACH,aAAa,EAAE,KAAK;IAEpB;;OAEG;IACH,OAAO,EAAE,EAAG,OAAO,EAAE,KAAK;QACxB,OAAO,EAAE,4CAA4C;KACtD;IAED;;OAEG;IACH,IAAI,EAAE,CAAC,GAAG,EAAE,EAAE;QACZ,gDAAgD;QAChD,IAAI,OAAO,CAAC,GAAG,CAAC,uBAAuB,GAAG,AAAD,IAAI,MAAM,EAAE,CAAC;YACpD,MAAM,UAAU,GAAE,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC,CAAC,4BAA4B;YACpE,OAAO,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QACrC,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;CACF,CAAC;AAEF;;GAEG;AACU,QAAA,sBAAsB,GAAqB;IACtD,GAAG,uBAAe;IAClB,QAAQ,EAAE,OAAO,EAAE,SAAS;IAC5B,GAAG,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,IAAI,EAAE,EAAE,CAAC;IAC7D,OAAO,EAAE,EAAG,OAAO,EAAE,KAAK;QACxB,OAAO,EAAE,oDAAoD;KAC9D;CACF,CAAC;AAEF;;GAEG;AACU,QAAA,2BAA2B,GAAqB;IAC3D,GAAG,uBAAe;IAClB,QAAQ,EAAE,OAAO,EAAE,SAAS;IAC5B,GAAG,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,2BAA2B,IAAI,IAAI,EAAE,EAAE,CAAC;IAClE,OAAO,EAAE,EAAG,OAAO,EAAE,KAAK;QACxB,OAAO,EAAE,yDAAyD;KACnE;CACF,CAAC;AAEF;;GAEG;AACU,QAAA,2BAA2B,GAAQ;IAC9C;;OAEG;IACH,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,8BAA8B,KAAK,MAAM,IAAI,IAAI;IAEtE;;OAEG;IACH,UAAU,EAAE,EAAG,UAAU,EAAE,CAAC,QAAQ,CAAC;QACnC,SAAS,EAAE,CAAC,QAAQ,EAAE,iBAAiB,EAAE,eAAe,EAAE,0BAA0B,EAAE,kCAAkC,CAAC;QACzH,QAAQ,EAAE,CAAC,QAAQ,EAAE,iBAAiB,EAAE,8BAA8B,EAAE,0BAA0B,CAAC;QACnG,MAAM,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,kCAAkC,CAAC;QAC/D,OAAO,EAAE,CAAC,QAAQ,EAAE,2BAA2B,EAAE,OAAO,CAAC;QACzD,UAAU,EAAE,CAAC,QAAQ,EAAE,8BAA8B,EAAE,4BAA4B,EAAE,kCAAkC,CAAC;QACxH,QAAQ,EAAE,CAAC,QAAQ,CAAC;QACpB,SAAS,EAAE,CAAC,QAAQ,CAAC;QACrB,QAAQ,EAAE,CAAC,QAAQ,CAAC;QACpB,WAAW,EAAE,CAAC,QAAQ,CAAC;QACvB,SAAS,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC;QAC9B,QAAQ,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC;QAC7B,UAAU,EAAE,CAAC,QAAQ,CAAC;QACtB,uBAAuB,EAAE,EAAE;KAC5B;IAED;;OAEG;IACH,UAAU,EAAE,KAAK;CAClB,CAAC;AAEF;;GAEG;AACU,QAAA,UAAU,GAAQ;IAC7B;;OAEG;IACH,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,KAAK,MAAM,IAAI,IAAI;IAEnD;;OAEG;IACH,MAAM,EAAE,QAAQ,EAAE,SAAS;IAE3B;;OAEG;IACH,iBAAiB,EAAE,IAAI;IAEvB;;OAEG;IACH,OAAO,EAAE,IAAI;CACd,CAAC;AAEF;;GAEG;AACU,QAAA,aAAa,GAAQ;IAChC;;OAEG;IACH,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc,KAAK,MAAM,IAAI,IAAI;IAErD;;OAEG;IACH,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,iBAAiB,KAAK,MAAM,IAAI,IAAI;IAE1D;;OAEG;IACH,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,QAAQ;IAEnD;;OAEG;IACH,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,aAAa,IAAI,oDAAoD;CAC1F,CAAC;AAEF;;;GAGG;AACI,MAAM,gCAAgC,GAAO,GAAS,EAAE;IAC7D,eAAM,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC;IAE/D,6BAA6B;IAC7B,IAAI,CAAC,iBAAS,CAAC,MAAM,IAAI,iBAAS,CAAC,MAAM,KAAK,sCAAsC,IAAI,iBAAS,CAAC,MAAM,KAAK,iDAAiD,EAAE,CAAC;QAC/J,0HAA0H;QAC1H,eAAM,CAAC,IAAI,CAAC,kHAAkH,CAAC,CAAC;IAClI,CAAC;SAAM,CAAC;QACN,eAAM,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;IAC5D,CAAC;IAED,iCAAiC;IACjC,IAAI,CAAC,qBAAa,CAAC,MAAM,IAAI,qBAAa,CAAC,MAAM,KAAK,0BAA0B,IAAI,qBAAa,CAAC,MAAM,KAAK,oDAAoD,EAAE,CAAC;QAClK,0HAA0H;QAC1H,eAAM,CAAC,IAAI,CAAC,qHAAqH,CAAC,CAAC;IACrI,CAAC;SAAM,CAAC;QACN,eAAM,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAC;IAChE,CAAC;IAED,wBAAwB;IACxB,eAAM,CAAC,IAAI,CAAC,oBAAoB,EAAE;QAChC,IAAI,EAAE,EAAG,MAAM,EAAE,kBAAU,CAAC,MAAM;YAChC,WAAW,EAAE,kBAAU,CAAC,WAAW;SACpC;QACD,SAAS,EAAE,EAAG,QAAQ,EAAE,uBAAe,CAAC,QAAQ;YAC9C,GAAG,EAAE,uBAAe,CAAC,GAAG;YACxB,UAAU,EAAE,8BAAsB,CAAC,GAAG;YACtC,eAAe,EAAE,mCAA2B,CAAC,GAAG;SACjD;QACD,qBAAqB,EAAE,EAAG,OAAO,EAAE,mCAA2B,CAAC,OAAO;YACpE,UAAU,EAAE,mCAA2B,CAAC,UAAU;SACnD;QACD,IAAI,EAAE,EAAG,OAAO,EAAE,kBAAU,CAAC,OAAO;YAClC,MAAM,EAAE,kBAAU,CAAC,MAAM;SAC1B;QACD,OAAO,EAAE,EAAG,MAAM,EAAE,qBAAa,CAAC,MAAM;YACtC,QAAQ,EAAE,qBAAa,CAAC,QAAQ;YAChC,QAAQ,EAAE,qBAAa,CAAC,QAAQ;SACjC;KACF,CAAC,CAAC;AACL,CAAC,CAAC;AAxCW,QAAA,gCAAgC,oCAwC3C;AAEF;;GAEG;AACU,QAAA,wBAAwB,GAAQ;IAC3C,GAAG,EAAE,iBAAS;IACd,IAAI,EAAE,kBAAU;IAChB,SAAS,EAAE,uBAAe;IAC1B,gBAAgB,EAAE,8BAAsB;IACxC,qBAAqB,EAAE,mCAA2B;IAClD,qBAAqB,EAAE,mCAA2B;IAClD,IAAI,EAAE,kBAAU;IAChB,OAAO,EAAE,qBAAa;IACtB,UAAU,EAAE,wCAAgC;CAC7C,CAAC;AAEF,kBAAe,gCAAwB,CAAC"}