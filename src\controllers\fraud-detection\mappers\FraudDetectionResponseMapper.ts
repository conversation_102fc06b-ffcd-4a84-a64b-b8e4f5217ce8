/**
 * Fraud Detection Response Mapper
 *
 * Handles response formatting for fraud detection operations.
 */

import { Request, Response } from 'express';
import {
  SuccessResponse,
  ErrorResponse,
  RiskAssessmentResponse,
  FraudConfigResponse,
  FlaggedTransactionResponse,
  FraudStatisticsResponse,
} from '../types/FraudDetectionControllerTypes';
import { AppError, ErrorType, ErrorCode } from '../../../utils/errors/AppError';

/**
 * Response mapper for fraud detection operations
 */
export class FraudDetectionResponseMapper {
  /**
   * Send success response
   */
  static sendSuccess<T>(
    res: Response,
    data: T,
    message?: string,
    statusCode: number = 200,
    pagination?: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    }
  ): void {
    const response: SuccessResponse<T> = {
      success: true,
      data,
      message,
      pagination,
      timestamp: new Date(),
      requestId: res.locals.requestId ?? 'unknown',
    };

    res.status(statusCode).json(response);
  }

  /**
   * Send error response
   */
  static sendError(res: Response, error: AppError | Error, statusCode?: number): void {
    let errorResponse: ErrorResponse;

    if (error instanceof AppError) {
      errorResponse = {
        success: false,
        error: {
          message: error.message,
          code: error.code,
          type: error.type,
          details: error.details,
        },
        timestamp: new Date(),
        requestId: res.locals.requestId ?? 'unknown',
      };

      statusCode = statusCode ?? error.statusCode ?? 400;
    } else {
      errorResponse = {
        success: false,
        error: {
          message: error.message || 'Internal server error',
          code: 'INTERNAL_SERVER_ERROR',
          type: 'INTERNAL',
        },
        timestamp: new Date(),
        requestId: res.locals.requestId ?? 'unknown',
      };

      statusCode = statusCode ?? 500;
    }

    res.status(statusCode).json(errorResponse);
  }

  /**
   * Send risk assessment response
   */
  static sendRiskAssessment(res: Response, assessment: any, message?: string): void {
    this.sendSuccess(res, assessment, message ?? 'Risk assessment completed successfully');
  }

  /**
   * Send transaction risk assessment response
   */
  static sendTransactionRiskAssessment(
    res: Response,
    assessment: RiskAssessmentResponse,
    message?: string
  ): void {
    this.sendSuccess(
      res,
      assessment,
      message ?? 'Transaction risk assessment retrieved successfully'
    );
  }

  /**
   * Send fraud configuration response
   */
  static sendFraudConfig(res: Response, config: FraudConfigResponse, message?: string): void {
    this.sendSuccess(
      res,
      config,
      message ?? 'Fraud detection configuration retrieved successfully'
    );
  }

  /**
   * Send fraud configuration updated response
   */
  static sendFraudConfigUpdated(res: Response, config: FraudConfigResponse): void {
    this.sendSuccess(res, config, 'Fraud detection configuration updated successfully');
  }

  /**
   * Send flagged transactions list response
   */
  static sendFlaggedTransactionsList(
    res: Response,
    transactions: FlaggedTransactionResponse[],
    total: number,
    page: number = 1,
    limit: number = 10
  ): void {
    const totalPages = Math.ceil(total / limit);

    this.sendSuccess(
      res,
      transactions,
      `Retrieved ${transactions.length} flagged transactions`,
      200,
      {
        page,
        limit,
        total,
        totalPages,
      }
    );
  }

  /**
   * Send fraud statistics response
   */
  static sendFraudStatistics(res: Response, statistics: FraudStatisticsResponse): void {
    this.sendSuccess(res, statistics, 'Fraud detection statistics retrieved successfully');
  }

  /**
   * Send validation error response
   */
  static sendValidationError(
    res: Response,
    errors: any[],
    message: string = 'Validation failed'
  ): void {
    const error = new AppError({
      message,
      type: ErrorType.VALIDATION,
      code: ErrorCode.INVALID_INPUT,
      details: { errors },
    });

    this.sendError(res, error, 400);
  }

  /**
   * Send authorization error response
   */
  static sendAuthorizationError(
    res: Response,
    message: string = 'Access denied',
    requiredRole?: string
  ): void {
    const error = new AppError({
      message,
      type: ErrorType.AUTHORIZATION,
      code: ErrorCode.ACCESS_DENIED,
      details: { requiredRole },
    });

    this.sendError(res, error, 403);
  }

  /**
   * Send not found error response
   */
  static sendNotFoundError(res: Response, resource: string = 'Resource'): void {
    const error = new AppError({
      message: `${resource} not found`,
      type: ErrorType.NOT_FOUND,
      code: ErrorCode.RESOURCE_NOT_FOUND,
    });

    this.sendError(res, error, 404);
  }

  /**
   * Send internal server error response
   */
  static sendInternalServerError(res: Response, message: string = 'Internal server error'): void {
    const error = new AppError({
      message,
      type: ErrorType.INTERNAL,
      code: ErrorCode.INTERNAL_SERVER_ERROR,
    });

    this.sendError(res, error, 500);
  }

  /**
   * Handle async controller method
   */
  static asyncHandler(fn: Function) {
    return (req: Request, res: Response, next: Function) => {
      Promise.resolve(fn(req, res, next)).catch((error) => next(error));
    };
  }

  /**
   * Set response headers for API
   */
  static setApiHeaders(res: Response): void {
    res.setHeader('Content-Type', 'application/json');
    res.setHeader('X-API-Version', '1.0');
    res.setHeader('X-Response-Time', Date.now());
  }

  /**
   * Format risk level for display
   */
  static formatRiskLevel(level: string): string {
    switch (level) {
      case 'LOW':
        return 'Low Risk';
      case 'MEDIUM':
        return 'Medium Risk';
      case 'HIGH':
        return 'High Risk';
      case 'CRITICAL':
        return 'Critical Risk';
      default:
        return 'Unknown Risk';
    }
  }

  /**
   * Format risk score for display
   */
  static formatRiskScore(score: number): string {
    return `${score.toFixed(1)}%`;
  }

  /**
   * Format percentage for display
   */
  static formatPercentage(value: number): string {
    return `${value.toFixed(2)}%`;
  }

  /**
   * Format currency amount
   */
  static formatCurrency(amount: number, currency: string = 'USD'): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  }

  /**
   * Format date for display
   */
  static formatDate(date: Date): string {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  }

  /**
   * Create summary statistics
   */
  static createSummaryStats(statistics: FraudStatisticsResponse): any {
    return {
      overview: {
        totalAssessments: statistics.totalAssessments,
        flaggedTransactions: statistics.flaggedCount,
        blockedTransactions: statistics.blockedCount,
        flaggedRate: this.formatPercentage(statistics.flaggedRate),
        blockedRate: this.formatPercentage(statistics.blockedRate),
      },
      riskDistribution: {
        low: statistics.levelCounts.LOW,
        medium: statistics.levelCounts.MEDIUM,
        high: statistics.levelCounts.HIGH,
        critical: statistics.levelCounts.CRITICAL,
      },
      period: {
        start: this.formatDate(statistics.period.start),
        end: this.formatDate(statistics.period.end),
        days: Math.ceil(
          (statistics.period.end.getTime() - statistics.period.start.getTime()) /
            (1000 * 60 * 60 * 24)
        ),
      },
    };
  }

  /**
   * Transform risk assessment for display
   */
  static transformRiskAssessment(assessment: RiskAssessmentResponse): any {
    return {
      ...assessment,
      riskScore: {
        ...assessment.riskScore,
        scoreFormatted: this.formatRiskScore(assessment.riskScore.score),
        levelFormatted: this.formatRiskLevel(assessment.riskScore.level),
      },
      createdAtFormatted: this.formatDate(assessment.createdAt),
    };
  }

  /**
   * Transform flagged transaction for display
   */
  static transformFlaggedTransaction(transaction: FlaggedTransactionResponse): any {
    return {
      ...transaction,
      scoreFormatted: this.formatRiskScore(transaction.score),
      levelFormatted: this.formatRiskLevel(transaction.level),
      createdAtFormatted: this.formatDate(transaction.createdAt),
      transaction: {
        ...transaction.transaction,
        amountFormatted: this.formatCurrency(
          transaction.transaction.amount,
          transaction.transaction.currency
        ),
        createdAtFormatted: this.formatDate(transaction.transaction.createdAt),
      },
    };
  }
}
