"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.encryptSensitiveData = encryptSensitiveData;
exports.decryptSensitiveData = decryptSensitiveData;
exports.encrypt = encrypt;
exports.decrypt = decrypt;
// jscpd:ignore-file
const crypto_1 = __importDefault(require("crypto"));
const types_1 = require("../types");
// Environment variables for encryption
const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || 'amazingpay-encryption-key-32bytes';
const ENCRYPTION_IV = process.env.ENCRYPTION_IV || 'amazingpay-iv-16b';
/**
 * Encrypt sensitive data in payment method configuration
 * @param config Configuration object
 * @param type Payment method type
 */
function encryptSensitiveData(config, type) {
    if (!config)
        return {};
    const encryptedConfig = { ...config };
    // Encrypt sensitive data based on payment method type
    switch (type) {
        case types_1.PaymentMethodType.BINANCE_PAY:
            if (encryptedConfig.apiSecret) {
                encryptedConfig.apiSecret = encrypt(encryptedConfig.apiSecret);
            }
            break;
        case types_1.PaymentMethodType.BINANCE_C2C:
            // No sensitive data to encrypt
            break;
        case types_1.PaymentMethodType.BINANCE_TRC20:
        case types_1.PaymentMethodType.BINANCE_TRC20_DIRECT:
            if (encryptedConfig.apiSecret) {
                encryptedConfig.apiSecret = encrypt(encryptedConfig.apiSecret);
            }
            break;
        case types_1.PaymentMethodType.CRYPTO_TRANSFER:
            // No sensitive data to encrypt
            break;
        default:
            // For unknown types, encrypt common sensitive fields
            if (encryptedConfig.apiSecret) {
                encryptedConfig.apiSecret = encrypt(encryptedConfig.apiSecret);
            }
            if (encryptedConfig.privateKey) {
                encryptedConfig.privateKey = encrypt(encryptedConfig.privateKey);
            }
            if (encryptedConfig.password) {
                encryptedConfig.password = encrypt(encryptedConfig.password);
            }
    }
    return encryptedConfig;
}
/**
 * Decrypt sensitive data in payment method configuration
 * @param config Configuration object
 * @param type Payment method type
 */
function decryptSensitiveData(config, type) {
    if (!config)
        return {};
    const decryptedConfig = { ...config };
    // Decrypt sensitive data based on payment method type
    switch (type) {
        case types_1.PaymentMethodType.BINANCE_PAY:
            if (decryptedConfig.apiSecret) {
                decryptedConfig.apiSecret = decrypt(decryptedConfig.apiSecret);
            }
            break;
        case types_1.PaymentMethodType.BINANCE_C2C:
            // No sensitive data to decrypt
            break;
        case types_1.PaymentMethodType.BINANCE_TRC20:
        case types_1.PaymentMethodType.BINANCE_TRC20_DIRECT:
            if (decryptedConfig.apiSecret) {
                decryptedConfig.apiSecret = decrypt(decryptedConfig.apiSecret);
            }
            break;
        case types_1.PaymentMethodType.CRYPTO_TRANSFER:
            // No sensitive data to decrypt
            break;
        default:
            // For unknown types, decrypt common sensitive fields
            if (decryptedConfig.apiSecret) {
                decryptedConfig.apiSecret = decrypt(decryptedConfig.apiSecret);
            }
            if (decryptedConfig.privateKey) {
                decryptedConfig.privateKey = decrypt(decryptedConfig.privateKey);
            }
            if (decryptedConfig.password) {
                decryptedConfig.password = decrypt(decryptedConfig.password);
            }
    }
    return decryptedConfig;
}
/**
 * Encrypt a string
 * @param text Text to encrypt
 */
function encrypt(text) {
    try {
        // Create key and iv from environment variables
        const key = crypto_1.default.scryptSync(ENCRYPTION_KEY, 'salt', 32);
        const iv = Buffer.from(ENCRYPTION_IV.slice(0, 16), 'utf8');
        const cipher = crypto_1.default.createCipheriv('aes-256-cbc', key, iv);
        // Encrypt the text
        let encrypted = cipher.update(text, 'utf8', 'hex');
        encrypted += cipher.final('hex');
        return encrypted;
    }
    catch (error) {
        console.error('Encryption error:', error);
        return text; // Return original text on error
    }
}
/**
 * Decrypt a string
 * @param encryptedText Encrypted text
 */
function decrypt(encryptedText) {
    try {
        // Create key and iv from environment variables
        const key = crypto_1.default.scryptSync(ENCRYPTION_KEY, 'salt', 32);
        const iv = Buffer.from(ENCRYPTION_IV.slice(0, 16), 'utf8');
        const decipher = crypto_1.default.createDecipheriv('aes-256-cbc', key, iv);
        // Decrypt the text
        let decrypted = decipher.update(encryptedText, 'hex', 'utf8');
        decrypted += decipher.final('utf8');
        return decrypted;
    }
    catch (error) {
        console.error('Decryption error:', error);
        return encryptedText; // Return encrypted text on error
    }
}
//# sourceMappingURL=encryption.js.map