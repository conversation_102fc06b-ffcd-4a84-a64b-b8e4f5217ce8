"use strict";
/**
 * Transaction Risk Validator
 *
 * Focused validator for transaction risk assessment operations.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.TransactionRiskValidator = void 0;
const AppError_1 = require("../../../utils/errors/AppError");
const BaseValidator_1 = require("./BaseValidator");
/**
 * Validator for transaction risk assessment
 */
class TransactionRiskValidator extends BaseValidator_1.BaseValidator {
    /**
     * Validate transaction risk assessment request
     */
    validateAssessTransactionRisk(data) {
        const errors = [];
        if (!data.transactionId) {
            errors.push({ field: 'transactionId', message: 'Transaction ID is required' });
        }
        else if (typeof data.transactionId !== 'string' || !this.isValidUUID(data.transactionId)) {
            errors.push({ field: 'transactionId', message: 'Invalid transaction ID format', value: data.transactionId });
        }
        if (!data.ipAddress) {
            errors.push({ field: 'ipAddress', message: 'IP address is required' });
        }
        else if (typeof data.ipAddress !== 'string' || !this.isValidIPAddress(data.ipAddress)) {
            errors.push({ field: 'ipAddress', message: 'Invalid IP address format', value: data.ipAddress });
        }
        if (data.userAgent !== undefined && typeof data.userAgent !== 'string') {
            errors.push({ field: 'userAgent', message: 'User agent must be a string', value: data.userAgent });
        }
        if (data.deviceId !== undefined && typeof data.deviceId !== 'string') {
            errors.push({ field: 'deviceId', message: 'Device ID must be a string', value: data.deviceId });
        }
        if (errors.length > 0) {
            throw new AppError_1.AppError({
                message: 'Validation failed',
                type: AppError_1.ErrorType.VALIDATION,
                code: AppError_1.ErrorCode.INVALID_INPUT,
                details: { errors }
            });
        }
        return {
            transactionId: data.transactionId,
            ipAddress: data.ipAddress,
            userAgent: data.userAgent ?? 'Unknown',
            deviceId: data.deviceId ?? 'Unknown'
        };
    }
    /**
     * Validate transaction ID parameter
     */
    validateTransactionId(transactionId) {
        if (!transactionId) {
            throw new AppError_1.AppError({
                message: 'Transaction ID is required',
                type: AppError_1.ErrorType.VALIDATION,
                code: AppError_1.ErrorCode.MISSING_REQUIRED_FIELD
            });
        }
        if (!this.isValidUUID(transactionId)) {
            throw new AppError_1.AppError({
                message: 'Transaction ID must be a valid UUID',
                type: AppError_1.ErrorType.VALIDATION,
                code: AppError_1.ErrorCode.INVALID_INPUT
            });
        }
        return transactionId;
    }
}
exports.TransactionRiskValidator = TransactionRiskValidator;
//# sourceMappingURL=TransactionRiskValidator.js.map