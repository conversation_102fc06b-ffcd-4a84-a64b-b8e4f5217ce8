"use strict";
// jscpd:ignore-file
/**
 * Production Database Configuration
 *
 * This file contains the configuration for the production database.
 * It includes settings for connection pooling, SSL, and other production-specific options.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.getPrismaProductionOptions = exports.getPrismaProductionDatabaseUrl = exports.productionDatabaseConfig = void 0;
const logger_1 = require("../../lib/logger");
/**
 * Production database configuration
 */
exports.productionDatabaseConfig = {
    /**
   * Database connection URL
   * In production, this should be set via environment variables
   */
    url: process.env.DATABASE_URL || "",
    /**
   * Database host
   */
    host: process.env.DB_HOST || "localhost",
    /**
   * Database port
   */
    port: parseInt(process.env.DB_PORT || "5432", 10),
    /**
   * Database username
   */
    username: process.env.DB_USERNAME || "postgres",
    /**
   * Database password
   */
    password: process.env.DB_PASSWORD || "",
    /**
   * Database name
   */
    database: process.env.DB_NAME || "Amazingpay",
    /**
   * Whether to use SSL
   */
    ssl: process.env.DB_SSL === "true",
    /**
   * Connection pool configuration
   */
    pool: {
        /**
         * Minimum number of connections in the pool
         */
        min: parseInt(process.env.DB_CONNECTION_POOL_MIN || "5", 10),
        /**
     * Maximum number of connections in the pool
     */
        max: parseInt(process.env.DB_CONNECTION_POOL_MAX || "20", 10),
        /**
     * Connection idle timeout in milliseconds
     */
        idle: 10000,
        /**
     * Connection acquire timeout in milliseconds
     */
        acquire: 60000
    },
    /**
   * Statement timeout in milliseconds
   */
    statementTimeout: parseInt(process.env.DB_STATEMENT_TIMEOUT || "30000", 10),
    /**
   * Logging configuration
   */
    logging: {
        /**
         * Whether to log queries
         */
        queries: false,
        /**
     * Whether to log errors
     */
        errors: true,
        /**
     * Whether to log warnings
     */
        warnings: true,
        /**
     * Whether to log slow queries
     */
        slowQueries: true,
        /**
     * Threshold for slow queries in milliseconds
     */
        slowQueryThreshold: 1000
    }
};
/**
 * Get the Prisma database URL for production
 * @returns The database URL for Prisma
 */
const getPrismaProductionDatabaseUrl = () => {
    // Use the DATABASE_URL environment variable if available
    if (process.env.DATABASE_URL) {
        return process.env.DATABASE_URL;
    }
    // Otherwise, construct the URL from individual components
    const { username, password, host, port, database, ssl } = exports.productionDatabaseConfig;
    // Construct the database URL
    const url = `postgresql://${username}:${password}@${host}:${port}/${database}?schema=public${ssl ? "&sslmode=require" : ""}`;
    logger_1.logger.info("Using constructed database URL for production");
    return url;
};
exports.getPrismaProductionDatabaseUrl = getPrismaProductionDatabaseUrl;
/**
 * Get the Prisma client options for production
 * @returns The Prisma client options for production
 */
const getPrismaProductionOptions = () => {
    return {
        datasources: { db: {
                url: (0, exports.getPrismaProductionDatabaseUrl)()
            }
        },
        log: ["error"],
        errorFormat: "minimal"
    };
};
exports.getPrismaProductionOptions = getPrismaProductionOptions;
exports.default = exports.productionDatabaseConfig;
//# sourceMappingURL=production.config.js.map