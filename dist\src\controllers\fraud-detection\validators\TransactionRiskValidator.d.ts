/**
 * Transaction Risk Validator
 *
 * Focused validator for transaction risk assessment operations.
 */
import { AssessTransactionRiskRequest } from '../types/FraudDetectionControllerTypes';
import { BaseValidator } from './BaseValidator';
/**
 * Validator for transaction risk assessment
 */
export declare class TransactionRiskValidator extends BaseValidator {
    /**
     * Validate transaction risk assessment request
     */
    validateAssessTransactionRisk(data: any): AssessTransactionRiskRequest;
    /**
     * Validate transaction ID parameter
     */
    validateTransactionId(transactionId: any): string;
}
//# sourceMappingURL=TransactionRiskValidator.d.ts.map