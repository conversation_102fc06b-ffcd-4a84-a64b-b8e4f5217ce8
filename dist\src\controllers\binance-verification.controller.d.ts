import { BaseController } from "./base.controller";
/**
 * BinanceVerificationController
 * Controller for handling Binance verification operations
 */
export declare class BinanceVerificationController extends BaseController {
    constructor();
    /**
     * Verify a TRC20 transaction on the Binance network
     */
    verifyTRC20Transaction: any;
    /**
     * Verify a C2C transaction on the Binance platform
     */
    verifyC2CTransaction: any;
    /**
     * Verify a Pay transaction on the Binance platform
     */
    verifyPayTransaction: any;
    /**
     * Test connection to the Binance API
     */
    testConnection: any;
}
declare const _default: BinanceVerificationController;
export default _default;
//# sourceMappingURL=binance-verification.controller.d.ts.map