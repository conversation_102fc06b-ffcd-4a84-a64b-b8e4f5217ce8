// jscpd:ignore-file
import { User } from '@prisma/client';
import jwt from 'jsonwebtoken';
import { logger } from '../lib/logger';
import { AppError } from '../middlewares/error.middleware';
import { secretsManager } from '../utils/secrets-manager';


// Initialize secrets manager
let JWT_SECRET: string;
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '1d';
const JWT_REFRESH_EXPIRES_IN: any =process.env.JWT_REFRESH_EXPIRES_IN || '7d';

/**
 * Initialize JWT configuration
 * This must be called before using any JWT functions
 */
export const initializeJwtConfig: any =async (): Promise<void> => {
  try {
    // Initialize secrets manager
    await secretsManager.initialize();

    // Get JWT secret
    JWT_SECRET = secretsManager.getJwtSecret();

    if (!JWT_SECRET) {
      throw new Error('JWT secret not found in secrets manager');
    }

    logger.info('JWT configuration initialized successfully');
  } catch (error) {
    logger.error('Failed to initialize JWT configuration:', error);
    throw new Error('Failed to initialize JWT configuration');
  }
};

// Generate access token
export const generateToken: any = async (user) => {
  // Ensure JWT configuration is initialized
  if (!JWT_SECRET) {
    await initializeJwtConfig();
  }

  const payload: any = {
    userId: user.id,
    email: user.email,
    role: user.role || 'MERCHANT',
    type: 'access',
    environment: process.env.NODE_ENV || 'development'
  };

  return jwt.sign(payload, JWT_SECRET as string, {
    expiresIn: JWT_EXPIRES_IN,
    algorithm: 'HS256',
    issuer: `amazingpay-api-${process.env.NODE_ENV || 'development'}`,
    audience: `amazingpay-client-${process.env.NODE_ENV || 'development'}`,
  });
};

// Generate refresh token
export const generateRefreshToken: any = async (user) => {
  // Ensure JWT configuration is initialized
  if (!JWT_SECRET) {
    await initializeJwtConfig();
  }

  const payload: any = {
    userId: user.id,
    type: 'refresh',
    environment: process.env.NODE_ENV || 'development'
  };

  return jwt.sign(payload, JWT_SECRET as string, {
    expiresIn: JWT_REFRESH_EXPIRES_IN,
    algorithm: 'HS256',
    issuer: `amazingpay-api-${process.env.NODE_ENV || 'development'}`,
    audience: `amazingpay-client-${process.env.NODE_ENV || 'development'}`,
  });
};

// Verify JWT token
export const verifyToken: any =async (token: string) => {
  // Ensure JWT configuration is initialized
  if (!JWT_SECRET) {
    await initializeJwtConfig();
  }

  try {
    const decoded: any =jwt.verify(token, JWT_SECRET as string, {
      algorithms: ['HS256'],
      issuer: `amazingpay-api-${process.env.NODE_ENV || 'development'}`,
      audience: `amazingpay-client-${process.env.NODE_ENV || 'development'}`,
    }) as jwt.JwtPayload & { environment: string };

    // Verify that the token was issued for this environment
    if (decoded.environment !== process.env.NODE_ENV) {
      logger.warn(`JWT token environment mismatch: token=${decoded.environment}, current=${process.env.NODE_ENV}`);
      throw new AppError('Invalid token for this environment', 401, true);
    }

    return decoded;
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      throw new AppError('Token has expired', 401);
    } else if (error instanceof jwt.JsonWebTokenError) {
      throw new AppError('Invalid token', 401);
    }
    throw new AppError('Token verification failed', 401);
  }
};

export default {
  JWT_EXPIRES_IN,
  JWT_REFRESH_EXPIRES_IN,
  initializeJwtConfig,
  generateToken,
  generateRefreshToken,
  verifyToken,

  // Get JWT secret (for internal use only)
  getJwtSecret: () => JWT_SECRET,
};
