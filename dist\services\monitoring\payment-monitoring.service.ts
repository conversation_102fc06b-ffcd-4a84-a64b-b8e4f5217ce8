// jscpd:ignore-file
import { PrismaClient } from "@prisma/client";
import { logger } from '../../utils/logger';
import { EventEmitter } from "events";
import { VerificationStatus } from "../../types/verification";
import alertNotificationService from "../notification/alert-notification.service";
import { AlertLevel, Alert } from "../../types/alert.types";


// Create event emitter for monitoring events
export const monitoringEvents: any =new EventEmitter();

// Monitoring event types
export enum MonitoringEventType {
  PAYMENT_CREATED = "payment.created",
  PAYMENT_UPDATED = "payment.updated",
  PAYMENT_VERIFIED = "payment.verified",
  PAYMENT_FAILED = "payment.failed",
  PAYMENT_EXPIRED = "payment.expired",
  VERIFICATION_STARTED = "verification.started",
  VERIFICATION_COMPLETED = "verification.completed",
  VERIFICATION_FAILED = "verification.failed",
  ERROR = "error",
  ALERT = "alert",
}

// Monitoring event
export interface MonitoringEvent {
  type: MonitoringEventType;
  timestamp: string;
  data: Record<string, any>;
  merchantId?: string;
  paymentId?: string;
}

/**
 * Payment monitoring service
 */
class PaymentMonitoringService {
    private static instance: PaymentMonitoringService;
    private prisma: PrismaClient;
    private alerts: Alert[] = [];
    private metrics: {, paymentsCreated: number;
        paymentsVerified: number;
        paymentsFailed: number;
        paymentsExpired: number;
        verificationStarted: number;
        verificationCompleted: number;
        verificationFailed: number;
        errors: number;
        alerts: Record<AlertLevel, number>;
        paymentMethodMetrics: Record<string, {
            created: number;
            verified: number;
            failed: number;
            expired: number;
        }>;
        merchantMetrics: Record<string, {
            created: number;
            verified: number;
            failed: number;
            expired: number;
        }>;
    };
    private constructor() {
        // Initialize service
        this.prisma = new PrismaClient();
        this.setupEventListeners();
        this.initializeMetrics();
    }

    /**
     * Initialize metrics
     */
    private initializeMetrics(): void {
        this.metrics = {
            paymentsCreated: 0,
            paymentsVerified: 0,
            paymentsFailed: 0,
            paymentsExpired: 0,
            verificationStarted: 0,
            verificationCompleted: 0,
            verificationFailed: 0,
            errors: 0,
            alerts: {
                [AlertLevel.INFO]: 0,
                [AlertLevel.WARNING]: 0,
                [AlertLevel.ERROR]: 0,
                [AlertLevel.CRITICAL]: 0
            },
            paymentMethodMetrics: {},
            merchantMetrics: {}
        };
    }

    /**
   * Get singleton instance
   */
    public static getInstance(): PaymentMonitoringService {
        if (!PaymentMonitoringService.instance) {
            PaymentMonitoringService.instance = new PaymentMonitoringService();
        }
        return PaymentMonitoringService.instance;
    }

    /**
   * Set up event listeners
   */
    private setupEventListeners(): void {
    // Payment events
        monitoringEvents.on(MonitoringEventType.PAYMENT_CREATED, this.handlePaymentCreated.bind(this));
        monitoringEvents.on(MonitoringEventType.PAYMENT_UPDATED, this.handlePaymentUpdated.bind(this));
        monitoringEvents.on(MonitoringEventType.PAYMENT_VERIFIED, this.handlePaymentVerified.bind(this));
        monitoringEvents.on(MonitoringEventType.PAYMENT_FAILED, this.handlePaymentFailed.bind(this));
        monitoringEvents.on(MonitoringEventType.PAYMENT_EXPIRED, this.handlePaymentExpired.bind(this));

        // Verification events
        monitoringEvents.on(MonitoringEventType.VERIFICATION_STARTED, this.handleVerificationStarted.bind(this));
        monitoringEvents.on(MonitoringEventType.VERIFICATION_COMPLETED, this.handleVerificationCompleted.bind(this));
        monitoringEvents.on(MonitoringEventType.VERIFICATION_FAILED, this.handleVerificationFailed.bind(this));

        // Error and alert events
        monitoringEvents.on(MonitoringEventType.ERROR, this.handleError.bind(this));
        monitoringEvents.on(MonitoringEventType.ALERT, this.handleAlert.bind(this));
    }

    /**
   * Handle payment created event
   * @param event Monitoring event
   */
    private handlePaymentCreated(event: MonitoringEvent): void {
        this.metrics.paymentsCreated++;

        // Update payment method metrics
        const paymentMethodType: any =event.data.paymentMethodType;
        if (paymentMethodType) {
            if (!this.metrics.paymentMethodMetrics[paymentMethodType]) {
                this.metrics.paymentMethodMetrics[paymentMethodType] = {
                    created: 0,
                    verified: 0,
                    failed: 0,
                    expired: 0
                };
            }
            this.metrics.paymentMethodMetrics[paymentMethodType].created++;
        }

        // Update merchant metrics
        const merchantId: any =event.merchantId;
        if (merchantId) {
            if (!this.metrics.merchantMetrics[merchantId]) {
                this.metrics.merchantMetrics[merchantId] = {
                    created: 0,
                    verified: 0,
                    failed: 0,
                    expired: 0
                };
            }
            this.metrics.merchantMetrics[merchantId].created++;
        }

        // Log event
        logger.info(`Payment created: ${event.paymentId}`, {
            paymentId: event.paymentId,
            merchantId: event.merchantId,
            paymentMethodType,
            amount: event.data.amount,
            currency: event.data.currency
        });
    }

    /**
   * Handle payment updated event
   * @param event Monitoring event
   */
    private handlePaymentUpdated(event: MonitoringEvent): void {
    // Log event
        logger.info(`Payment updated: ${event.paymentId}`, {
            paymentId: event.paymentId,
            merchantId: event.merchantId,
            status: event.data.status
        });
    }

    /**
   * Handle payment verified event
   * @param event Monitoring event
   */
    private handlePaymentVerified(event: MonitoringEvent): void {
        this.metrics.paymentsVerified++;

        // Update payment method metrics
        const paymentMethodType: any =event.data.paymentMethodType;
        if (paymentMethodType && this.metrics.paymentMethodMetrics[paymentMethodType]) {
            this.metrics.paymentMethodMetrics[paymentMethodType].verified++;
        }

        // Update merchant metrics
        const merchantId: any =event.merchantId;
        if (merchantId && this.metrics.merchantMetrics[merchantId]) {
            this.metrics.merchantMetrics[merchantId].verified++;
        }

        // Log event
        logger.info(`Payment verified: ${event.paymentId}`, {
            paymentId: event.paymentId,
            merchantId: event.merchantId,
            paymentMethodType,
            verificationMethod: event.data.verificationMethod
        });
    }

    /**
   * Handle payment failed event
   * @param event Monitoring event
   */
    private handlePaymentFailed(event: MonitoringEvent): void {
        this.metrics.paymentsFailed++;

        // Update payment method metrics
        const paymentMethodType: any =event.data.paymentMethodType;
        if (paymentMethodType && this.metrics.paymentMethodMetrics[paymentMethodType]) {
            this.metrics.paymentMethodMetrics[paymentMethodType].failed++;
        }

        // Update merchant metrics
        const merchantId: any =event.merchantId;
        if (merchantId && this.metrics.merchantMetrics[merchantId]) {
            this.metrics.merchantMetrics[merchantId].failed++;
        }

        // Log event
        logger.warn(`Payment failed: ${event.paymentId}`, {
            paymentId: event.paymentId,
            merchantId: event.merchantId,
            paymentMethodType,
            reason: event.data.reason
        });

        // Create alert if multiple failures for same merchant
        if (merchantId) {
            const merchantFailures: any =this.metrics.merchantMetrics[merchantId]?.failed || 0;
            if (merchantFailures >= 3) {
                this.createAlert(
                    AlertLevel.WARNING,
                    `Multiple payment failures for merchant ${merchantId}`,
                    { merchantId, failures: merchantFailures }
                );
            }
        }
    }

    /**
   * Handle payment expired event
   * @param event Monitoring event
   */
    private handlePaymentExpired(event: MonitoringEvent): void {
        this.metrics.paymentsExpired++;

        // Update payment method metrics
        const paymentMethodType: any =event.data.paymentMethodType;
        if (paymentMethodType && this.metrics.paymentMethodMetrics[paymentMethodType]) {
            this.metrics.paymentMethodMetrics[paymentMethodType].expired++;
        }

        // Update merchant metrics
        const merchantId: any =event.merchantId;
        if (merchantId && this.metrics.merchantMetrics[merchantId]) {
            this.metrics.merchantMetrics[merchantId].expired++;
        }

        // Log event
        logger.warn(`Payment expired: ${event.paymentId}`, {
            paymentId: event.paymentId,
            merchantId: event.merchantId,
            paymentMethodType
        });
    }

    /**
   * Handle verification started event
   * @param event Monitoring event
   */
    private handleVerificationStarted(event: MonitoringEvent): void {
        this.metrics.verificationStarted++;

        // Log event
        logger.info(`Verification started: ${event.paymentId}`, {
            paymentId: event.paymentId,
            merchantId: event.merchantId,
            verificationMethod: event.data.verificationMethod
        });
    }

    /**
   * Handle verification completed event
   * @param event Monitoring event
   */
    private handleVerificationCompleted(event: MonitoringEvent): void {
        this.metrics.verificationCompleted++;

        // Log event
        logger.info(`Verification completed: ${event.paymentId}`, {
            paymentId: event.paymentId,
            merchantId: event.merchantId,
            verificationMethod: event.data.verificationMethod,
            status: event.data.status
        });
    }

    /**
   * Handle verification failed event
   * @param event Monitoring event
   */
    private handleVerificationFailed(event: MonitoringEvent): void {
        this.metrics.verificationFailed++;

        // Log event
        logger.warn(`Verification failed: ${event.paymentId}`, {
            paymentId: event.paymentId,
            merchantId: event.merchantId,
            verificationMethod: event.data.verificationMethod,
            reason: event.data.reason
        });

        // Create alert if multiple verification failures
        if (this.metrics.verificationFailed >= 5) {
            this.createAlert(
                AlertLevel.ERROR,
                "Multiple verification failures detected",
                { failures: this.metrics.verificationFailed }
            );
        }
    }

    /**
   * Handle error event
   * @param event Monitoring event
   */
    private handleError(event: MonitoringEvent): void {
        this.metrics.errors++;

        // Log error
        logger.error(`Error: ${event.data.message}`, {
            paymentId: event.paymentId,
            merchantId: event.merchantId,
            error: event.data.error
        });

        // Create alert for critical errors
        if (event.data.critical) {
            this.createAlert(
                AlertLevel.ERROR,
                event.data.message,
                { paymentId: event.paymentId, merchantId: event.merchantId }
            );
        }
    }

    /**
   * Handle alert event
   * @param event Monitoring event
   */
    private handleAlert(event: MonitoringEvent): void {
        const level = event.data.level || AlertLevel.INFO;
        this.createAlert(level, event.data.message, event.data);
    }

    /**
   * Create alert
   * @param level Alert level
   * @param message Alert message
   * @param data Additional data
   */
    public createAlert(level: AlertLevel, message: string, data?: Record<string, any>): void {
    // Increment alert count
        this.metrics.alerts[level]++;

        // Create alert
        const alert: Alert = {
            level,
            message,
            timestamp: new Date().toISOString(),
            data,
            resolved: false
        };

        // Add to alerts list
        this.alerts.push(alert);

        // Log alert
        switch (level) {
        case AlertLevel.INFO:
            logger.info(`Alert: ${message}`, data);
            break;
        case AlertLevel.WARNING:
            logger.warn(`Alert: ${message}`, data);
            break;
        case AlertLevel.ERROR:
        case AlertLevel.CRITICAL:
            logger.error(`Alert: ${message}`, data);
            break;
        }

        // Send notifications for alerts
        this.sendAlertNotification(level, message, data);
    }

    /**
   * Send alert notification
   * @param level Alert level
   * @param message Alert message
   * @param data Additional data
   */
    private async sendAlertNotification(level: AlertLevel, message: string, data?: Record<string, any>): Promise<void> {
        try {
            // Prepare subject based on alert level
            let subject: string ="Payment System Alert";

            switch (level) {
            case AlertLevel.INFO:
                subject = "Payment System Information";
                break;
            case AlertLevel.WARNING:
                subject = "Payment System Warning";
                break;
            case AlertLevel.ERROR:
                subject = "Payment System Error";
                break;
            case AlertLevel.CRITICAL:
                subject = "CRITICAL: Payment System Alert";
                break;
            }

            // Send notification
            await alertNotificationService.sendAlert({
                level,
                subject,
                message,
                data
            });
        } catch (error) {
            logger.error("Error sending alert notification:", error);
        }
    }

    /**
   * Resolve alert
   * @param index Alert index
   * @param resolution Resolution message
   */
    public resolveAlert(index: number, resolution?: string): void {
        if (index >= 0 && index < this.alerts.length) {
            this.alerts[index].resolved = true;
            this.alerts[index].resolvedAt = new Date().toISOString();

            logger.info(`Alert resolved: ${this.alerts[index].message}`, {
                alertIndex: index,
                resolution
            });
        }
    }

    /**
   * Get all alerts
   * @param includeResolved Whether to include resolved alerts
   */
    public getAlerts(includeResolved: boolean = false): Alert[] {
        if (includeResolved) {
            return this.alerts;
        }
        return this.alerts.filter(alert => !alert.resolved);
    }

    /**
   * Get metrics
   */
    public getMetrics(): Record<string, any> {
        return {
            ...this.metrics,
            timestamp: new Date().toISOString()
        };
    }

    /**
   * Reset metrics
   */
    public resetMetrics(): void {
        this.initializeMetrics();
        logger.info("Monitoring metrics reset");
    }
}

// Export singleton instance
export const paymentMonitoringService: any =PaymentMonitoringService.getInstance();

export default paymentMonitoringService;