{"file": "F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts", "mappings": ";AAAA;;;;GAIG;;;AAEH,wDAAoD;AACpD,2EAA4E;AAE5E;;GAEG;AACH,MAAa,yBAA0B,SAAQ,oBAAQ;IAGnD,YAAY,OAAe,EAAE,IAAmC,EAAE,aAAqB,GAAG;QACtF,KAAK,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;QAC3B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,IAAI,GAAG,2BAA2B,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,gBAAgB,CAAC,UAAkB,mBAAmB;QACzD,OAAO,IAAI,yBAAyB,CAChC,OAAO,EACP,yDAA6B,CAAC,iBAAiB,EAC/C,GAAG,CACN,CAAC;IACN,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,cAAc,CAAC,UAAkB,iBAAiB;QACrD,OAAO,IAAI,yBAAyB,CAChC,OAAO,EACP,yDAA6B,CAAC,eAAe,EAC7C,GAAG,CACN,CAAC;IACN,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,YAAY,CAAC,UAAkB,eAAe;QACjD,OAAO,IAAI,yBAAyB,CAChC,OAAO,EACP,yDAA6B,CAAC,aAAa,EAC3C,GAAG,CACN,CAAC;IACN,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,kBAAkB,CAAC,UAAkB,qBAAqB;QAC7D,OAAO,IAAI,yBAAyB,CAChC,OAAO,EACP,yDAA6B,CAAC,mBAAmB,EACjD,GAAG,CACN,CAAC;IACN,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,oBAAoB,CAAC,UAAkB,wBAAwB;QAClE,OAAO,IAAI,yBAAyB,CAChC,OAAO,EACP,yDAA6B,CAAC,sBAAsB,EACpD,GAAG,CACN,CAAC;IACN,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,aAAa,CAAC,UAAkB,iBAAiB;QACpD,OAAO,IAAI,yBAAyB,CAChC,OAAO,EACP,yDAA6B,CAAC,eAAe,EAC7C,GAAG,CACN,CAAC;IACN,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,aAAa,CAAC,UAAkB,gBAAgB;QACnD,OAAO,IAAI,yBAAyB,CAChC,OAAO,EACP,yDAA6B,CAAC,cAAc,EAC5C,GAAG,CACN,CAAC;IACN,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,aAAa,CAAC,UAAkB,gBAAgB;QACnD,OAAO,IAAI,yBAAyB,CAChC,OAAO,EACP,yDAA6B,CAAC,cAAc,EAC5C,GAAG,CACN,CAAC;IACN,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,iBAAiB,CAAC,UAAkB,oBAAoB;QAC3D,OAAO,IAAI,yBAAyB,CAChC,OAAO,EACP,yDAA6B,CAAC,kBAAkB,EAChD,GAAG,CACN,CAAC;IACN,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,YAAY,CAAC,UAAkB,cAAc;QAChD,OAAO,IAAI,yBAAyB,CAChC,OAAO,EACP,yDAA6B,CAAC,YAAY,EAC1C,GAAG,CACN,CAAC;IACN,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,SAAS,CAAC,KAAU,EAAE,iBAAyB,eAAe;QACjE,IAAI,KAAK,YAAY,yBAAyB,EAAE,CAAC;YAC7C,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,MAAM,OAAO,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,cAAc,CAAC;QACxE,OAAO,IAAI,yBAAyB,CAChC,OAAO,EACP,yDAA6B,CAAC,cAAc,EAC5C,GAAG,CACN,CAAC;IACN,CAAC;CACJ;AAtID,8DAsIC", "names": [], "sources": ["F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts"], "sourcesContent": ["/**\n * Identity Verification Error Classes\n * \n * Custom error handling for identity verification operations.\n */\n\nimport { AppError } from \"../../../utils/app-error\";\nimport { IdentityVerificationErrorCode } from \"./IdentityVerificationTypes\";\n\n/**\n * Custom error class for identity verification errors\n */\nexport class IdentityVerificationError extends AppError {\n    code: IdentityVerificationErrorCode;\n\n    constructor(message: string, code: IdentityVerificationErrorCode, statusCode: number = 400) {\n        super(message, statusCode);\n        this.code = code;\n        this.name = \"IdentityVerificationError\";\n    }\n\n    /**\n     * Create an invalid signature error\n     */\n    static invalidSignature(message: string = \"Invalid signature\"): IdentityVerificationError {\n        return new IdentityVerificationError(\n            message,\n            IdentityVerificationErrorCode.INVALID_SIGNATURE,\n            400\n        );\n    }\n\n    /**\n     * Create an invalid address error\n     */\n    static invalidAddress(message: string = \"Invalid address\"): IdentityVerificationError {\n        return new IdentityVerificationError(\n            message,\n            IdentityVerificationErrorCode.INVALID_ADDRESS,\n            400\n        );\n    }\n\n    /**\n     * Create an invalid proof error\n     */\n    static invalidProof(message: string = \"Invalid proof\"): IdentityVerificationError {\n        return new IdentityVerificationError(\n            message,\n            IdentityVerificationErrorCode.INVALID_PROOF,\n            400\n        );\n    }\n\n    /**\n     * Create a verification failed error\n     */\n    static verificationFailed(message: string = \"Verification failed\"): IdentityVerificationError {\n        return new IdentityVerificationError(\n            message,\n            IdentityVerificationErrorCode.VERIFICATION_FAILED,\n            400\n        );\n    }\n\n    /**\n     * Create a verification not found error\n     */\n    static verificationNotFound(message: string = \"Verification not found\"): IdentityVerificationError {\n        return new IdentityVerificationError(\n            message,\n            IdentityVerificationErrorCode.VERIFICATION_NOT_FOUND,\n            404\n        );\n    }\n\n    /**\n     * Create a claim not found error\n     */\n    static claimNotFound(message: string = \"Claim not found\"): IdentityVerificationError {\n        return new IdentityVerificationError(\n            message,\n            IdentityVerificationErrorCode.CLAIM_NOT_FOUND,\n            404\n        );\n    }\n\n    /**\n     * Create an internal error\n     */\n    static internalError(message: string = \"Internal error\"): IdentityVerificationError {\n        return new IdentityVerificationError(\n            message,\n            IdentityVerificationErrorCode.INTERNAL_ERROR,\n            500\n        );\n    }\n\n    /**\n     * Create a provider error\n     */\n    static providerError(message: string = \"Provider error\"): IdentityVerificationError {\n        return new IdentityVerificationError(\n            message,\n            IdentityVerificationErrorCode.PROVIDER_ERROR,\n            502\n        );\n    }\n\n    /**\n     * Create an invalid parameters error\n     */\n    static invalidParameters(message: string = \"Invalid parameters\"): IdentityVerificationError {\n        return new IdentityVerificationError(\n            message,\n            IdentityVerificationErrorCode.INVALID_PARAMETERS,\n            400\n        );\n    }\n\n    /**\n     * Create an unauthorized error\n     */\n    static unauthorized(message: string = \"Unauthorized\"): IdentityVerificationError {\n        return new IdentityVerificationError(\n            message,\n            IdentityVerificationErrorCode.UNAUTHORIZED,\n            401\n        );\n    }\n\n    /**\n     * Wrap an unknown error\n     */\n    static fromError(error: any, defaultMessage: string = \"Unknown error\"): IdentityVerificationError {\n        if (error instanceof IdentityVerificationError) {\n            return error;\n        }\n\n        const message = error instanceof Error ? error.message : defaultMessage;\n        return new IdentityVerificationError(\n            message,\n            IdentityVerificationErrorCode.INTERNAL_ERROR,\n            500\n        );\n    }\n}\n"], "version": 3}