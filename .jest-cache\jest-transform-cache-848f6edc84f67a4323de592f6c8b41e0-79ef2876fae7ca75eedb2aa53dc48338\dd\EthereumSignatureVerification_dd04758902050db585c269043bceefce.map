{"file": "F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts", "mappings": ";AAAA;;;;GAIG;;;AAEH,2CAA8G;AAE9G,iFAA8E;AAC9E,8DAA2D;AAC3D,gDAA6C;AAE7C;;GAEG;AACH,MAAa,6BAA6B;IAGtC,YAAY,MAAoB;QAC5B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,OAAO;QACH,OAAO,oBAAoB,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAM,CAAC,MAA+B;QACxC,IAAI,CAAC;YACD,sBAAsB;YACtB,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YAE5B,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,MAAM,CAAC;YAEnE,oBAAoB;YACpB,MAAM,iBAAiB,GAAG,iCAAe,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;YAEpE,mBAAmB;YACnB,MAAM,gBAAgB,GAAG,iCAAe,CAAC,eAAe,CAAC,OAAO,EAAE,SAAS,EAAE,iBAAiB,CAAC,CAAC;YAEhG,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACpB,eAAM,CAAC,IAAI,CAAC,uDAAuD,iBAAiB,EAAE,CAAC,CAAC;gBAExF,OAAO;oBACH,OAAO,EAAE,KAAK;oBACd,MAAM,EAAE,uCAA8B,CAAC,kBAAkB;oBACzD,MAAM,EAAE,uCAA8B,CAAC,QAAQ;oBAC/C,OAAO,EAAE,+BAA+B;oBACxC,KAAK,EAAE,mBAAmB;iBAC7B,CAAC;YACN,CAAC;YAED,6DAA6D;YAC7D,MAAM,gBAAgB,GAAG,iCAAe,CAAC,cAAc,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;YAE5E,6BAA6B;YAC7B,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC;gBACrD,MAAM;gBACN,UAAU;gBACV,OAAO,EAAE,iBAAiB;gBAC1B,OAAO;gBACP,SAAS;gBACT,gBAAgB;aACnB,CAAC,CAAC;YAEH,eAAM,CAAC,IAAI,CAAC,yDAAyD,iBAAiB,EAAE,CAAC,CAAC;YAE1F,OAAO;gBACH,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,uCAA8B,CAAC,kBAAkB;gBACzD,MAAM,EAAE,uCAA8B,CAAC,QAAQ;gBAC/C,OAAO,EAAE,0CAA0C;gBACnD,IAAI,EAAE;oBACF,OAAO,EAAE,iBAAiB;oBAC1B,OAAO;oBACP,gBAAgB;iBACnB;gBACD,cAAc,EAAE,YAAY,CAAC,EAAE;aAClC,CAAC;QAEN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;YAE3D,IAAI,KAAK,YAAY,qDAAyB,EAAE,CAAC;gBAC7C,OAAO;oBACH,OAAO,EAAE,KAAK;oBACd,MAAM,EAAE,uCAA8B,CAAC,kBAAkB;oBACzD,MAAM,EAAE,uCAA8B,CAAC,QAAQ;oBAC/C,OAAO,EAAE,KAAK,CAAC,OAAO;oBACtB,KAAK,EAAE,KAAK,CAAC,IAAI;iBACpB,CAAC;YACN,CAAC;YAED,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,uCAA8B,CAAC,kBAAkB;gBACzD,MAAM,EAAE,uCAA8B,CAAC,QAAQ;gBAC/C,OAAO,EAAE,oCAAoC;gBAC7C,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;aAClE,CAAC;QACN,CAAC;IACL,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,MAA+B;QAClD,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YAClB,MAAM,qDAAyB,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,CAAC;QAC7E,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YAClB,MAAM,qDAAyB,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,CAAC;QAC7E,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;YACpB,MAAM,qDAAyB,CAAC,iBAAiB,CAAC,uBAAuB,CAAC,CAAC;QAC/E,CAAC;QAED,IAAI,CAAC,iCAAe,CAAC,cAAc,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC;YAClD,MAAM,qDAAyB,CAAC,cAAc,CAAC,iCAAiC,CAAC,CAAC;QACtF,CAAC;QAED,8EAA8E;QAC9E,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;YACxE,MAAM,qDAAyB,CAAC,gBAAgB,CAAC,0BAA0B,CAAC,CAAC;QACjF,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,wBAAwB,CAAC,IAOtC;QACG,IAAI,CAAC;YACD,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC;gBACjD,IAAI,EAAE;oBACF,MAAM,EAAE,IAAI,CAAC,MAAM;oBACnB,UAAU,EAAE,IAAI,CAAC,UAAU;oBAC3B,MAAM,EAAE,uCAA8B,CAAC,kBAAkB;oBACzD,MAAM,EAAE,uCAA8B,CAAC,QAAQ;oBAC/C,OAAO,EAAE,IAAI,CAAC,OAAO;oBACrB,gBAAgB,EAAE;wBACd,OAAO,EAAE,IAAI,CAAC,OAAO;wBACrB,SAAS,EAAE,IAAI,CAAC,SAAS;wBACzB,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;wBACvC,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;qBACvC;oBACD,UAAU,EAAE,IAAI,IAAI,EAAE;iBACzB;aACJ,CAAC,CAAC;QACP,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;YAC3D,MAAM,qDAAyB,CAAC,aAAa,CAAC,sCAAsC,CAAC,CAAC;QAC1F,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,2BAA2B,CAAC,OAAe,EAAE,KAAc;QAC9D,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAC3C,MAAM,UAAU,GAAG,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAExE,OAAO;;WAEJ,OAAO;aACL,SAAS;SACb,UAAU;;qEAEkD,CAAC;IAClE,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,cAAc,CAAC,OAAe;QACjC,OAAO,OAAO,CAAC,QAAQ,CAAC,kDAAkD,CAAC;YACpE,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC;YAC5B,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC;YAC9B,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IACtC,CAAC;CACJ;AAlLD,sEAkLC", "names": [], "sources": ["F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts"], "sourcesContent": ["/**\n * Ethereum Signature Verification Method\n * \n * Handles verification of Ethereum message signatures.\n */\n\nimport { PrismaClient, IdentityVerificationMethodEnum, IdentityVerificationStatusEnum } from \"@prisma/client\";\nimport { IdentityVerificationResult, EthereumSignatureParams, IVerificationMethod } from \"../core/IdentityVerificationTypes\";\nimport { IdentityVerificationError } from \"../core/IdentityVerificationError\";\nimport { BlockchainUtils } from \"../utils/BlockchainUtils\";\nimport { logger } from \"../../../lib/logger\";\n\n/**\n * Ethereum signature verification implementation\n */\nexport class EthereumSignatureVerification implements IVerificationMethod {\n    private prisma: PrismaClient;\n\n    constructor(prisma: PrismaClient) {\n        this.prisma = prisma;\n    }\n\n    /**\n     * Get verification method name\n     */\n    getName(): string {\n        return \"ethereum_signature\";\n    }\n\n    /**\n     * Verify Ethereum signature\n     */\n    async verify(params: EthereumSignatureParams): Promise<IdentityVerificationResult> {\n        try {\n            // Validate parameters\n            this.validateParams(params);\n\n            const { address, message, signature, userId, merchantId } = params;\n\n            // Normalize address\n            const normalizedAddress = BlockchainUtils.normalizeAddress(address);\n\n            // Verify signature\n            const isValidSignature = BlockchainUtils.verifySignature(message, signature, normalizedAddress);\n\n            if (!isValidSignature) {\n                logger.warn(`Ethereum signature verification failed for address: ${normalizedAddress}`);\n                \n                return {\n                    success: false,\n                    method: IdentityVerificationMethodEnum.ETHEREUM_SIGNATURE,\n                    status: IdentityVerificationStatusEnum.REJECTED,\n                    message: \"Signature verification failed\",\n                    error: \"Invalid signature\"\n                };\n            }\n\n            // Recover address from signature for additional verification\n            const recoveredAddress = BlockchainUtils.recoverAddress(message, signature);\n\n            // Create verification record\n            const verification = await this.createVerificationRecord({\n                userId,\n                merchantId,\n                address: normalizedAddress,\n                message,\n                signature,\n                recoveredAddress\n            });\n\n            logger.info(`Ethereum signature verified successfully for address: ${normalizedAddress}`);\n\n            return {\n                success: true,\n                method: IdentityVerificationMethodEnum.ETHEREUM_SIGNATURE,\n                status: IdentityVerificationStatusEnum.VERIFIED,\n                message: \"Ethereum signature verified successfully\",\n                data: {\n                    address: normalizedAddress,\n                    message,\n                    recoveredAddress\n                },\n                verificationId: verification.id\n            };\n\n        } catch (error) {\n            logger.error(\"Error verifying Ethereum signature:\", error);\n\n            if (error instanceof IdentityVerificationError) {\n                return {\n                    success: false,\n                    method: IdentityVerificationMethodEnum.ETHEREUM_SIGNATURE,\n                    status: IdentityVerificationStatusEnum.REJECTED,\n                    message: error.message,\n                    error: error.code\n                };\n            }\n\n            return {\n                success: false,\n                method: IdentityVerificationMethodEnum.ETHEREUM_SIGNATURE,\n                status: IdentityVerificationStatusEnum.REJECTED,\n                message: \"Error verifying Ethereum signature\",\n                error: error instanceof Error ? error.message : \"Unknown error\"\n            };\n        }\n    }\n\n    /**\n     * Validate verification parameters\n     */\n    private validateParams(params: EthereumSignatureParams): void {\n        if (!params.address) {\n            throw IdentityVerificationError.invalidParameters(\"Address is required\");\n        }\n\n        if (!params.message) {\n            throw IdentityVerificationError.invalidParameters(\"Message is required\");\n        }\n\n        if (!params.signature) {\n            throw IdentityVerificationError.invalidParameters(\"Signature is required\");\n        }\n\n        if (!BlockchainUtils.isValidAddress(params.address)) {\n            throw IdentityVerificationError.invalidAddress(\"Invalid Ethereum address format\");\n        }\n\n        // Validate signature format (should start with 0x and be 132 characters long)\n        if (!params.signature.startsWith('0x') || params.signature.length !== 132) {\n            throw IdentityVerificationError.invalidSignature(\"Invalid signature format\");\n        }\n    }\n\n    /**\n     * Create verification record in database\n     */\n    private async createVerificationRecord(data: {\n        userId?: string;\n        merchantId?: string;\n        address: string;\n        message: string;\n        signature: string;\n        recoveredAddress: string;\n    }) {\n        try {\n            return await this.prisma.identityVerification.create({\n                data: {\n                    userId: data.userId,\n                    merchantId: data.merchantId,\n                    method: IdentityVerificationMethodEnum.ETHEREUM_SIGNATURE,\n                    status: IdentityVerificationStatusEnum.VERIFIED,\n                    address: data.address,\n                    verificationData: {\n                        message: data.message,\n                        signature: data.signature,\n                        recoveredAddress: data.recoveredAddress,\n                        verifiedAt: new Date().toISOString()\n                    },\n                    verifiedAt: new Date()\n                }\n            });\n        } catch (error) {\n            logger.error(\"Error creating verification record:\", error);\n            throw IdentityVerificationError.internalError(\"Failed to create verification record\");\n        }\n    }\n\n    /**\n     * Generate verification message\n     */\n    static generateVerificationMessage(address: string, nonce?: string): string {\n        const timestamp = new Date().toISOString();\n        const nonceValue = nonce || Math.random().toString(36).substring(2, 15);\n        \n        return `Please sign this message to verify your identity:\n\nAddress: ${address}\nTimestamp: ${timestamp}\nNonce: ${nonceValue}\n\nThis signature will be used for identity verification purposes only.`;\n    }\n\n    /**\n     * Validate message format\n     */\n    static isValidMessage(message: string): boolean {\n        return message.includes(\"Please sign this message to verify your identity\") &&\n               message.includes(\"Address:\") &&\n               message.includes(\"Timestamp:\") &&\n               message.includes(\"Nonce:\");\n    }\n}\n"], "version": 3}