{"version": 3, "file": "enhanced-risk-engine.controller.js", "sourceRoot": "", "sources": ["../../../src/controllers/enhanced-risk-engine.controller.ts"], "names": [], "mappings": ";AAAA,oBAAoB;AACpB;;;;GAIG;;;AAGH,uDAAmD;AACnD,2FAA2H;AAC3H,4CAAyC;AACzC,2CAA8C;AAQ9C;;GAEG;AACH,MAAa,4BAA6B,SAAQ,gCAAc;IAI5D;QACI,KAAK,EAAE,CAAC;QAKZ;;WAEG;QACH,0BAAqB,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;YAC5E,IAAI,CAAC;gBACD,MAAM,EAAE,aAAa,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;gBACrC,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;gBAEpD,kBAAkB;gBAClB,MAAM,WAAW,GAAQ,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC;oBAC9D,KAAK,EAAE,EAAE,EAAE,EAAE,aAAa,EAAE;iBAC/B,CAAC,CAAC;gBAEH,IAAI,CAAC,WAAW,EAAE,CAAC;oBACf,OAAO,GAAG,CAAC,QAAQ,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;gBACtD,CAAC;gBAED,eAAe;gBACf,MAAM,QAAQ,GAAQ,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;oBACxD,KAAK,EAAE,EAAE,EAAE,EAAE,WAAW,CAAC,UAAU,EAAE;iBACxC,CAAC,CAAC;gBAEH,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACZ,OAAO,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,WAAW,CAAC,UAAU,CAAC,CAAC;gBAC5D,CAAC;gBAED,0BAA0B;gBAC1B,MAAM,cAAc,GAAQ,MAAM,IAAI,CAAC,yBAAyB,CAAC,qBAAqB,CAClF,WAAW,EACX,SAAS,EACT,SAAS,IAAI,SAAS,EACtB,QAAQ,IAAI,SAAS,EACrB,QAAQ,CACX,CAAC;gBAEF,OAAO,GAAG,CAAC,OAAO,CAAC,6BAA6B,EAAE,cAAc,CAAC,CAAC;YACtE,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;gBACzD,OAAO,GAAG,CAAC,WAAW,CAAC,mCAAmC,CAAC,CAAC;YAChE,CAAC;QACL,CAAC,CAAC,CAAC;QAEH;;WAEG;QACH,iCAA4B,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;YACnF,IAAI,CAAC;gBACD,MAAM,EAAE,aAAa,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;gBAErC,sBAAsB;gBACtB,MAAM,cAAc,GAAQ,MAAM,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,SAAS,CAAC;oBAC3E,KAAK,EAAE,EAAE,aAAa,EAAE;oBACxB,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;iBACjC,CAAC,CAAC;gBAEH,IAAI,CAAC,cAAc,EAAE,CAAC;oBAClB,OAAO,GAAG,CAAC,QAAQ,CAAC,iBAAiB,EAAE,aAAa,CAAC,CAAC;gBAC1D,CAAC;gBAED,kBAAkB;gBAClB,MAAM,mBAAmB,GAAQ;oBAC7B,GAAG,cAAc;oBACjB,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC;oBAC3C,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,cAAc,CAAC;iBAC5D,CAAC;gBAEF,OAAO,GAAG,CAAC,OAAO,CAAC,6BAA6B,EAAE,mBAAmB,CAAC,CAAC;YAC3E,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,eAAM,CAAC,KAAK,CAAC,4CAA4C,EAAE,KAAK,CAAC,CAAC;gBAClE,OAAO,GAAG,CAAC,WAAW,CAAC,2CAA2C,CAAC,CAAC;YACxE,CAAC;QACL,CAAC,CAAC,CAAC;QAEH;;SAEC;QACD,0BAAqB,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;YAC5E,IAAI,CAAC;gBACD,MAAM,EAAE,UAAU,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;gBAElC,yBAAyB;gBACzB,MAAM,UAAU,GAAQ,MAAM,IAAI,CAAC,yBAAyB,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;gBAE/F,IAAI,CAAC,UAAU,EAAE,CAAC;oBACd,OAAO,GAAG,CAAC,OAAO,CAAC,6BAA6B,EAAE;wBAC9C,OAAO,EAAE,4DAA4D;wBACrE,MAAM,EAAE,IAAI,CAAC,yBAAyB,CAAC,eAAe,CAAC;qBAC1D,CAAC,CAAC;gBACP,CAAC;gBAED,OAAO,GAAG,CAAC,OAAO,CAAC,6BAA6B,EAAE,UAAU,CAAC,CAAC;YAClE,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,eAAM,CAAC,KAAK,CAAC,4CAA4C,EAAE,KAAK,CAAC,CAAC;gBAClE,OAAO,GAAG,CAAC,WAAW,CAAC,2CAA2C,CAAC,CAAC;YACxE,CAAC;QACL,CAAC,CAAC,CAAC;QAEH;;SAEC;QACD,6BAAwB,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;YAC/E,IAAI,CAAC;gBACD,MAAM,EAAE,UAAU,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;gBAClC,MAAM,EACF,SAAS,EACT,kBAAkB,EAClB,kBAAkB,EAClB,eAAe,EAClB,GAAG,GAAG,CAAC,IAAI,CAAC;gBAEb,sBAAsB;gBACtB,IAAI,SAAS,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,4CAAa,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;oBACjE,OAAO,GAAG,CAAC,UAAU,CAAC,uCAAuC,MAAM,CAAC,MAAM,CAAC,4CAAa,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAC5G,CAAC;gBAED,+BAA+B;gBAC/B,MAAM,iBAAiB,GAAQ,QAAQ,CAAC,UAAU,CAAC,CAAC;gBAEpD,sCAAsC;gBACtC,MAAM,MAAM,GAAQ,MAAM,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC;oBAC5D,KAAK,EAAE,EAAE,UAAU,EAAE,iBAAiB,EAAE;oBACxC,MAAM,EAAE,EAAE,SAAS,EAAE,SAAS,IAAI,QAAQ;wBACtC,kBAAkB,EAAE,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,SAAS;wBACvF,kBAAkB,EAAE,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,SAAS;wBACvF,eAAe,EAAE,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,SAAS;wBAC9E,SAAS,EAAE,IAAI,IAAI,EAAE;qBACxB;oBACD,MAAM,EAAE,EAAE,UAAU,EAAE,iBAAiB;wBACnC,SAAS,EAAE,SAAS,IAAI,QAAQ;wBAChC,kBAAkB,EAAE,IAAI,CAAC,SAAS,CAAC,kBAAkB,IAAI;4BACrD,qBAAqB,EAAE,CAAC;4BACxB,mBAAmB,EAAE,EAAE;4BACvB,kBAAkB,EAAE,EAAE;4BACtB,eAAe,EAAE,IAAI;4BACrB,aAAa,EAAE,KAAK;4BACpB,YAAY,EAAE,KAAK;4BACnB,wBAAwB,EAAE,CAAC;4BAC3B,eAAe,EAAE,CAAC;4BAClB,iBAAiB,EAAE,CAAC;4BACpB,aAAa,EAAE,CAAC;yBACnB,CAAC;wBACF,kBAAkB,EAAE,IAAI,CAAC,SAAS,CAAC,kBAAkB,IAAI;4BACrD,OAAO,EAAE,IAAI;4BACb,eAAe,EAAE,CAAC;4BAClB,gBAAgB,EAAE,GAAG;yBACxB,CAAC;wBACF,eAAe,EAAE,IAAI,CAAC,SAAS,CAAC,eAAe,IAAI;4BAC/C,OAAO,EAAE,IAAI;4BACb,aAAa,EAAE,GAAG;4BAClB,aAAa,EAAE,OAAO,CAAC,GAAG,CAAC,iBAAiB;yBAC/C,CAAC;wBACF,SAAS,EAAE,IAAI,IAAI,EAAE;wBACrB,SAAS,EAAE,IAAI,IAAI,EAAE;qBACxB;iBACJ,CAAC,CAAC;gBAEH,kBAAkB;gBAClB,MAAM,eAAe,GAAQ;oBACzB,GAAG,MAAM;oBACT,kBAAkB,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,kBAAkB,CAAC;oBACzD,kBAAkB,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,kBAAkB,CAAC;oBACzD,eAAe,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,eAAe,CAAC;iBACtD,CAAC;gBAEF,OAAO,GAAG,CAAC,OAAO,CAAC,qCAAqC,EAAE,eAAe,CAAC,CAAC;YAC/E,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,eAAM,CAAC,KAAK,CAAC,6CAA6C,EAAE,KAAK,CAAC,CAAC;gBACnE,OAAO,GAAG,CAAC,WAAW,CAAC,8CAA8C,CAAC,CAAC;YAC3E,CAAC;QACL,CAAC,CAAC,CAAC;QAEH;;SAEC;QACD,sBAAiB,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;YACxE,IAAI,CAAC;gBACD,MAAM,EAAE,UAAU,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;gBAClC,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC;gBAEzC,cAAc;gBACd,MAAM,eAAe,GAAQ,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,SAAmB,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;gBACzH,MAAM,aAAa,GAAQ,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,OAAiB,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC;gBAE9E,uBAAuB;gBACvB,MAAM,eAAe,GAAQ,MAAM,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,QAAQ,CAAC;oBAC3E,KAAK,EAAE,EAAE,aAAa,EAAE;4BAChB,EAAE,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;gCACvC,KAAK,EAAE;oCACH,UAAU;oCACV,SAAS,EAAE,EAAE,GAAG,EAAE,eAAe;wCAC7B,GAAG,EAAE,aAAa;qCACrB;iCACJ;gCACD,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI;iCACjB;6BACJ,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;yBACzD;qBACJ;iBACJ,CAAC,CAAC;gBAEH,uBAAuB;gBACvB,MAAM,gBAAgB,GAAQ,eAAe,CAAC,MAAM,CAAC;gBACrD,MAAM,kBAAkB,GAAQ,eAAe,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC;gBAClG,MAAM,kBAAkB,GAAQ,eAAe,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC;gBAElG,oCAAoC;gBACpC,MAAM,qBAAqB,GAAQ,eAAe,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,UAAU,EAAE,EAAE;oBAC1E,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;oBACzD,OAAO,GAAG,CAAC;gBACf,CAAC,EAAE,EAA4B,CAAC,CAAC;gBAEjC,4CAA4C;gBAC5C,MAAM,6BAA6B,GAAQ,eAAe,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,UAAU,EAAE,EAAE;oBAClF,GAAG,CAAC,UAAU,CAAC,iBAAiB,CAAC,GAAG,CAAC,GAAG,CAAC,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;oBACjF,OAAO,GAAG,CAAC;gBACf,CAAC,EAAE,EAA4B,CAAC,CAAC;gBAEjC,+BAA+B;gBAC/B,MAAM,gBAAgB,GAAQ,eAAe,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,UAAU,EAAE,EAAE,CAAC,GAAG,GAAG,UAAU,CAAC,KAAK,EAAE,CAAC,CAAC,GAAG,gBAAgB,CAAC;gBAExH,OAAO,GAAG,CAAC,OAAO,CAAC,iBAAiB,EAAE;oBAClC,gBAAgB;oBAChB,kBAAkB;oBAClB,kBAAkB;oBAClB,WAAW,EAAE,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB,GAAG,gBAAgB,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;oBACrF,WAAW,EAAE,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB,GAAG,gBAAgB,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;oBACrF,qBAAqB;oBACrB,6BAA6B;oBAC7B,gBAAgB;oBAChB,MAAM,EAAE,EAAE,SAAS,EAAE,eAAe;wBAChC,OAAO,EAAE,aAAa;qBACzB;iBACJ,CAAC,CAAC;YACP,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;gBACtD,OAAO,GAAG,CAAC,WAAW,CAAC,+BAA+B,CAAC,CAAC;YAC5D,CAAC;QACL,CAAC,CAAC,CAAC;QAjPC,IAAI,CAAC,yBAAyB,GAAG,IAAI,wDAAyB,EAAE,CAAC;QACjE,IAAI,CAAC,MAAM,GAAG,IAAI,qBAAY,EAAE,CAAC;IACrC,CAAC;CAgPJ;AAxPD,oEAwPC"}