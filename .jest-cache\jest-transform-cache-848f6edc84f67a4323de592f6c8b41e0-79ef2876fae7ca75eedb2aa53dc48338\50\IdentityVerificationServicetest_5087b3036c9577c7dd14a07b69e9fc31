7781f08c5eff00d1814d2d951f94db33
"use strict";
/**
 * Unit Tests for Identity Verification Service
 *
 * Comprehensive test suite covering all functionality of the IdentityVerificationService
 */
Object.defineProperty(exports, "__esModule", { value: true });
const globals_1 = require("@jest/globals");
// Mock dependencies
globals_1.jest.mock('@prisma/client');
globals_1.jest.mock('ethers');
const IdentityVerificationService_1 = require("../core/IdentityVerificationService");
const IdentityVerificationError_1 = require("../core/IdentityVerificationError");
(0, globals_1.describe)('IdentityVerificationService', () => {
    let service;
    let mockPrisma;
    (0, globals_1.beforeEach)(() => {
        // Create mock Prisma client
        mockPrisma = {
            identityVerification: {
                create: globals_1.jest.fn(),
                findUnique: globals_1.jest.fn(),
                findMany: globals_1.jest.fn(),
                update: globals_1.jest.fn(),
                delete: globals_1.jest.fn(),
                count: globals_1.jest.fn(),
            },
            user: {
                findUnique: globals_1.jest.fn(),
                create: globals_1.jest.fn(),
                update: globals_1.jest.fn(),
            },
            merchant: {
                findUnique: globals_1.jest.fn(),
            },
        };
        // Initialize service with mock
        service = new IdentityVerificationService_1.IdentityVerificationService(mockPrisma);
    });
    (0, globals_1.afterEach)(() => {
        globals_1.jest.clearAllMocks();
    });
    (0, globals_1.describe)('verifyEthereumSignature', () => {
        const validVerificationData = {
            address: '******************************************',
            message: 'Verify identity for AmazingPay',
            signature: '0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890ab1c',
            userId: 'user-123-456-789',
            merchantId: 'merchant-123-456-789',
        };
        (0, globals_1.it)('should successfully verify a valid Ethereum signature', async () => {
            // Arrange
            const mockUser = {
                id: validVerificationData.userId,
                email: '<EMAIL>',
                role: 'USER',
            };
            const mockMerchant = {
                id: validVerificationData.merchantId,
                name: 'Test Merchant',
            };
            const mockVerificationResult = {
                id: 'verification-123-456-789',
                userId: validVerificationData.userId,
                merchantId: validVerificationData.merchantId,
                method: 'ethereum_signature',
                status: 'verified',
                confidence: 0.95,
                address: validVerificationData.address,
                createdAt: new Date(),
            };
            // Mock Prisma calls
            mockPrisma.user.findUnique.mockResolvedValue(mockUser);
            mockPrisma.merchant.findUnique.mockResolvedValue(mockMerchant);
            mockPrisma.identityVerification.create.mockResolvedValue(mockVerificationResult);
            // Mock ethers verification
            const { ethers } = require('ethers');
            ethers.utils.verifyMessage.mockReturnValue(validVerificationData.address);
            ethers.utils.isAddress.mockReturnValue(true);
            // Act
            const result = await service.verifyEthereumSignature(validVerificationData);
            // Assert
            (0, globals_1.expect)(result).toBeDefined();
            (0, globals_1.expect)(result.success).toBe(true);
            (0, globals_1.expect)(result.verificationId).toBe(mockVerificationResult.id);
            (0, globals_1.expect)(result.method).toBe('ethereum_signature');
            (0, globals_1.expect)(result.confidence).toBe(0.95);
            // Verify Prisma calls
            (0, globals_1.expect)(mockPrisma.user.findUnique).toHaveBeenCalledWith({
                where: { id: validVerificationData.userId },
            });
            (0, globals_1.expect)(mockPrisma.merchant.findUnique).toHaveBeenCalledWith({
                where: { id: validVerificationData.merchantId },
            });
            (0, globals_1.expect)(mockPrisma.identityVerification.create).toHaveBeenCalledWith({
                data: globals_1.expect.objectContaining({
                    userId: validVerificationData.userId,
                    merchantId: validVerificationData.merchantId,
                    method: 'ethereum_signature',
                    status: 'verified',
                    address: validVerificationData.address,
                }),
            });
        });
        (0, globals_1.it)('should throw error for invalid Ethereum address', async () => {
            // Arrange
            const invalidData = {
                ...validVerificationData,
                address: 'invalid-address',
            };
            const { ethers } = require('ethers');
            ethers.utils.isAddress.mockReturnValue(false);
            // Act & Assert
            await (0, globals_1.expect)(service.verifyEthereumSignature(invalidData)).rejects.toThrow(IdentityVerificationError_1.IdentityVerificationError);
            // Verify no database calls were made
            (0, globals_1.expect)(mockPrisma.identityVerification.create).not.toHaveBeenCalled();
        });
        (0, globals_1.it)('should throw error for invalid signature', async () => {
            // Arrange
            const mockUser = { id: validVerificationData.userId };
            const mockMerchant = { id: validVerificationData.merchantId };
            mockPrisma.user.findUnique.mockResolvedValue(mockUser);
            mockPrisma.merchant.findUnique.mockResolvedValue(mockMerchant);
            const { ethers } = require('ethers');
            ethers.utils.isAddress.mockReturnValue(true);
            ethers.utils.verifyMessage.mockReturnValue('0xdifferentaddress');
            // Act & Assert
            await (0, globals_1.expect)(service.verifyEthereumSignature(validVerificationData)).rejects.toThrow(IdentityVerificationError_1.IdentityVerificationError);
            (0, globals_1.expect)(mockPrisma.identityVerification.create).not.toHaveBeenCalled();
        });
        (0, globals_1.it)('should throw error when user not found', async () => {
            // Arrange
            mockPrisma.user.findUnique.mockResolvedValue(null);
            const { ethers } = require('ethers');
            ethers.utils.isAddress.mockReturnValue(true);
            // Act & Assert
            await (0, globals_1.expect)(service.verifyEthereumSignature(validVerificationData)).rejects.toThrow(IdentityVerificationError_1.IdentityVerificationError);
            (0, globals_1.expect)(mockPrisma.identityVerification.create).not.toHaveBeenCalled();
        });
        (0, globals_1.it)('should throw error when merchant not found', async () => {
            // Arrange
            const mockUser = { id: validVerificationData.userId };
            mockPrisma.user.findUnique.mockResolvedValue(mockUser);
            mockPrisma.merchant.findUnique.mockResolvedValue(null);
            const { ethers } = require('ethers');
            ethers.utils.isAddress.mockReturnValue(true);
            // Act & Assert
            await (0, globals_1.expect)(service.verifyEthereumSignature(validVerificationData)).rejects.toThrow(IdentityVerificationError_1.IdentityVerificationError);
            (0, globals_1.expect)(mockPrisma.identityVerification.create).not.toHaveBeenCalled();
        });
        (0, globals_1.it)('should handle database errors gracefully', async () => {
            // Arrange
            const mockUser = { id: validVerificationData.userId };
            const mockMerchant = { id: validVerificationData.merchantId };
            mockPrisma.user.findUnique.mockResolvedValue(mockUser);
            mockPrisma.merchant.findUnique.mockResolvedValue(mockMerchant);
            mockPrisma.identityVerification.create.mockRejectedValue(new Error('Database error'));
            const { ethers } = require('ethers');
            ethers.utils.isAddress.mockReturnValue(true);
            ethers.utils.verifyMessage.mockReturnValue(validVerificationData.address);
            // Act & Assert
            await (0, globals_1.expect)(service.verifyEthereumSignature(validVerificationData)).rejects.toThrow(IdentityVerificationError_1.IdentityVerificationError);
        });
    });
    (0, globals_1.describe)('getVerificationById', () => {
        (0, globals_1.it)('should return verification details for valid ID', async () => {
            // Arrange
            const verificationId = 'verification-123-456-789';
            const mockVerification = {
                id: verificationId,
                userId: 'user-123-456-789',
                merchantId: 'merchant-123-456-789',
                method: 'ethereum_signature',
                status: 'verified',
                confidence: 0.95,
                address: '******************************************',
                createdAt: new Date(),
                updatedAt: new Date(),
            };
            mockPrisma.identityVerification.findUnique.mockResolvedValue(mockVerification);
            // Act
            const result = await service.getVerificationById(verificationId);
            // Assert
            (0, globals_1.expect)(result).toEqual(mockVerification);
            (0, globals_1.expect)(mockPrisma.identityVerification.findUnique).toHaveBeenCalledWith({
                where: { id: verificationId },
            });
        });
        (0, globals_1.it)('should throw error for non-existent verification', async () => {
            // Arrange
            const verificationId = 'non-existent-verification-id';
            mockPrisma.identityVerification.findUnique.mockResolvedValue(null);
            // Act & Assert
            await (0, globals_1.expect)(service.getVerificationById(verificationId)).rejects.toThrow(IdentityVerificationError_1.IdentityVerificationError);
        });
    });
    (0, globals_1.describe)('getUserVerifications', () => {
        (0, globals_1.it)('should return user verifications with pagination', async () => {
            // Arrange
            const userId = 'user-123-456-789';
            const mockVerifications = [
                {
                    id: 'verification-1-123-456',
                    userId,
                    method: 'ethereum_signature',
                    status: 'verified',
                    createdAt: new Date(),
                },
                {
                    id: 'verification-2-123-456',
                    userId,
                    method: 'ethereum_signature',
                    status: 'verified',
                    createdAt: new Date(),
                },
            ];
            mockPrisma.identityVerification.findMany.mockResolvedValue(mockVerifications);
            mockPrisma.identityVerification.count.mockResolvedValue(2);
            // Act
            const result = await service.getUserVerifications(userId, { page: 1, limit: 10 });
            // Assert
            (0, globals_1.expect)(result.verifications).toEqual(mockVerifications);
            (0, globals_1.expect)(result.total).toBe(2);
            (0, globals_1.expect)(result.page).toBe(1);
            (0, globals_1.expect)(result.limit).toBe(10);
            (0, globals_1.expect)(mockPrisma.identityVerification.findMany).toHaveBeenCalledWith({
                where: { userId },
                orderBy: { createdAt: 'desc' },
                skip: 0,
                take: 10,
            });
        });
        (0, globals_1.it)('should handle empty results', async () => {
            // Arrange
            const userId = 'empty-user-123-456';
            mockPrisma.identityVerification.findMany.mockResolvedValue([]);
            mockPrisma.identityVerification.count.mockResolvedValue(0);
            // Act
            const result = await service.getUserVerifications(userId);
            // Assert
            (0, globals_1.expect)(result.verifications).toEqual([]);
            (0, globals_1.expect)(result.total).toBe(0);
        });
    });
    (0, globals_1.describe)('updateVerificationStatus', () => {
        (0, globals_1.it)('should successfully update verification status', async () => {
            // Arrange
            const verificationId = 'verification-update-123';
            const newStatus = 'rejected';
            const reason = 'Invalid signature detected';
            const mockUpdatedVerification = {
                id: verificationId,
                status: newStatus,
                reason,
                updatedAt: new Date(),
            };
            mockPrisma.identityVerification.update.mockResolvedValue(mockUpdatedVerification);
            // Act
            const result = await service.updateVerificationStatus(verificationId, newStatus, reason);
            // Assert
            (0, globals_1.expect)(result).toEqual(mockUpdatedVerification);
            (0, globals_1.expect)(mockPrisma.identityVerification.update).toHaveBeenCalledWith({
                where: { id: verificationId },
                data: {
                    status: newStatus,
                    reason,
                    updatedAt: globals_1.expect.any(Date),
                },
            });
        });
        (0, globals_1.it)('should throw error for invalid verification ID', async () => {
            // Arrange
            const verificationId = 'invalid-verification-123';
            mockPrisma.identityVerification.update.mockRejectedValue(new Error('Record not found'));
            // Act & Assert
            await (0, globals_1.expect)(service.updateVerificationStatus(verificationId, 'rejected')).rejects.toThrow(IdentityVerificationError_1.IdentityVerificationError);
        });
    });
    (0, globals_1.describe)('getVerificationStatistics', () => {
        (0, globals_1.it)('should return verification statistics', async () => {
            // Arrange
            const merchantId = 'merchant-stats-123';
            const dateFrom = new Date('2024-01-01');
            const dateTo = new Date('2024-01-31');
            mockPrisma.identityVerification.count
                .mockResolvedValueOnce(100) // total
                .mockResolvedValueOnce(85) // verified
                .mockResolvedValueOnce(10) // pending
                .mockResolvedValueOnce(5); // rejected
            // Act
            const result = await service.getVerificationStatistics(merchantId, dateFrom, dateTo);
            // Assert
            (0, globals_1.expect)(result).toEqual({
                total: 100,
                verified: 85,
                pending: 10,
                rejected: 5,
                verificationRate: 85,
            });
            // Verify database calls
            (0, globals_1.expect)(mockPrisma.identityVerification.count).toHaveBeenCalledTimes(4);
        });
        (0, globals_1.it)('should handle zero verifications', async () => {
            // Arrange
            mockPrisma.identityVerification.count.mockResolvedValue(0);
            // Act
            const result = await service.getVerificationStatistics();
            // Assert
            (0, globals_1.expect)(result.total).toBe(0);
            (0, globals_1.expect)(result.verificationRate).toBe(0);
        });
    });
    (0, globals_1.describe)('Error Handling', () => {
        (0, globals_1.it)('should handle network errors gracefully', async () => {
            // Arrange
            mockPrisma.user.findUnique.mockRejectedValue(new Error('Network error'));
            // Act & Assert
            await (0, globals_1.expect)(service.verifyEthereumSignature(validVerificationData)).rejects.toThrow(IdentityVerificationError_1.IdentityVerificationError);
        });
        (0, globals_1.it)('should handle timeout errors', async () => {
            // Arrange
            mockPrisma.user.findUnique.mockImplementation(() => new Promise((_, reject) => setTimeout(() => reject(new Error('Timeout')), 100)));
            // Act & Assert
            await (0, globals_1.expect)(service.verifyEthereumSignature(validVerificationData)).rejects.toThrow(IdentityVerificationError_1.IdentityVerificationError);
        });
    });
    (0, globals_1.describe)('Input Validation', () => {
        (0, globals_1.it)('should validate required fields', async () => {
            // Test missing address
            await (0, globals_1.expect)(service.verifyEthereumSignature({
                address: '',
                message: 'test',
                signature: '0xtest',
                userId: 'user-validation-123',
                merchantId: 'merchant-validation-123',
            })).rejects.toThrow();
            // Test missing message
            await (0, globals_1.expect)(service.verifyEthereumSignature({
                address: '******************************************',
                message: '',
                signature: '0xtest',
                userId: 'user-validation-123',
                merchantId: 'merchant-validation-123',
            })).rejects.toThrow();
            // Test missing signature
            await (0, globals_1.expect)(service.verifyEthereumSignature({
                address: '******************************************',
                message: 'test',
                signature: '',
                userId: 'user-validation-123',
                merchantId: 'merchant-validation-123',
            })).rejects.toThrow();
        });
        (0, globals_1.it)('should validate UUID format', async () => {
            await (0, globals_1.expect)(service.verifyEthereumSignature({
                address: '******************************************',
                message: 'test',
                signature: '0xtest',
                userId: 'invalid-uuid',
                merchantId: 'merchant-validation-123',
            })).rejects.toThrow();
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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