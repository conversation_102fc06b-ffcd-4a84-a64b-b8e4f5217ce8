{"version": 3, "file": "ModuleRegistry.js", "sourceRoot": "", "sources": ["../../../src/lib/ModuleRegistry.ts"], "names": [], "mappings": ";AAAA,oBAAoB;AACpB;;;;GAIG;;;AAEH,qCAAkC;AAClC,yCAAsC;AAiCtC;;GAEG;AACH,MAAa,cAAc;IAA3B;QAEY,YAAO,GAA8B,IAAI,GAAG,EAAE,CAAC;IAwN3D,CAAC;IAtNG;;KAEC;IACM,MAAM,CAAC,WAAW;QACrB,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC;YAC3B,cAAc,CAAC,QAAQ,GAAG,IAAI,cAAc,EAAE,CAAC;QACnD,CAAC;QAED,OAAO,cAAc,CAAC,QAAQ,CAAC;IACnC,CAAC;IAED;;;;;KAKC;IACM,cAAc,CAAC,IAAY,EAAE,MAAoB;QACxD,qBAAqB;QACjB,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;YACtB,KAAK,MAAM,UAAU,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;gBAC3C,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC;oBAChC,eAAM,CAAC,IAAI,CAAC,UAAU,IAAI,eAAe,UAAU,4BAA4B,CAAC,CAAC;gBACrF,CAAC;YACL,CAAC;QACL,CAAC;QAED,kBAAkB;QAClB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAE/B,aAAa;QACb,mBAAQ,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAC/B,IAAI;YACJ,MAAM;SACT,CAAC,CAAC;QAEH,eAAM,CAAC,IAAI,CAAC,sBAAsB,IAAI,EAAE,EAAE;YACtC,OAAO,EAAE,MAAM,CAAC,OAAO;YACvB,OAAO,EAAE,MAAM,CAAC,OAAO;YACvB,YAAY,EAAE,MAAM,CAAC,YAAY;SACpC,CAAC,CAAC;IACP,CAAC;IAED;;;;;KAKC;IACM,SAAS,CAAC,IAAY;QACzB,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IAED;;;;;KAKC;IACM,SAAS,CAAC,IAAY;QACzB,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IAED;;;;;KAKC;IACM,eAAe,CAAC,IAAY;QAC/B,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACtC,OAAO,MAAM,CAAC,CAAC,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC;IAC5C,CAAC;IAED;;;;;KAKC;IACM,YAAY,CAAC,IAAY;QAC5B,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAEtC,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,MAAM,EAAE,OAAO,GAAG,IAAI,CAAC;QACvB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAE/B,aAAa;QACb,mBAAQ,CAAC,IAAI,CAAC,gBAAgB,EAAE;YAC5B,IAAI;YACJ,MAAM,EAAE,MAAM;SACjB,CAAC,CAAC;QAEH,eAAM,CAAC,IAAI,CAAC,mBAAmB,IAAI,EAAE,CAAC,CAAC;QAEvC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;KAKC;IACM,aAAa,CAAC,IAAY;QAC7B,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAEtC,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,MAAM,EAAE,OAAO,GAAG,KAAK,CAAC;QACxB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAE/B,aAAa;QACb,mBAAQ,CAAC,IAAI,CAAC,iBAAiB,EAAE;YAC7B,IAAI;YACJ,MAAM,EAAE,MAAM;SACjB,CAAC,CAAC;QAEH,eAAM,CAAC,IAAI,CAAC,oBAAoB,IAAI,EAAE,CAAC,CAAC;QAExC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;KAMC;IACM,kBAAkB,CAAC,IAAY,EAAE,MAA6B;QACjE,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAEtC,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,uBAAuB;QACvB,MAAM,aAAa,GAAQ;YACvB,GAAG,MAAM;YACT,GAAG,MAAM;YACT,MAAM,EAAE;gBACJ,GAAG,MAAM,CAAC,MAAM;gBAChB,GAAG,CAAC,MAAM,CAAC,MAAM,IAAI,EAAE,CAAC;aAC3B;SACJ,CAAC;QAEF,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;QAEtC,aAAa;QACb,mBAAQ,CAAC,IAAI,CAAC,gBAAgB,EAAE;YAC5B,IAAI;YACJ,MAAM,EAAE,aAAa;SACxB,CAAC,CAAC;QAEH,eAAM,CAAC,IAAI,CAAC,iCAAiC,IAAI,EAAE,CAAC,CAAC;QAErD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;KAIC;IACM,aAAa;QAChB,MAAM,OAAO,GAAiC,EAAE,CAAC;QAEjD,wDAAwD;QACxD,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,EAAE;YAC1D,OAAO,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;QAC3B,CAAC,CAAC,CAAC;QAEH,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;;KAIC;IACM,iBAAiB;QACpB,MAAM,OAAO,GAAiC,EAAE,CAAC;QAEjD,wDAAwD;QACxD,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,EAAE;YAC1D,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;gBACjB,OAAO,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;YAC3B,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;;;KAKC;IACM,wBAAwB,CAAC,IAAY;QACxC,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAEtC,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE,YAAY,EAAE,CAAC;YACnC,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,OAAO,MAAM,EAAE,YAAY,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC;IACtF,CAAC;CACJ;AA1ND,wCA0NC;AAED,4BAA4B;AACf,QAAA,cAAc,GAAO,cAAc,CAAC,WAAW,EAAE,CAAC"}