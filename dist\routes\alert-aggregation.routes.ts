// jscpd:ignore-file
import { Router } from 'express';
import { AlertAggregationController } from '../controllers/alert-aggregation';
import { authenticate } from '../middlewares/auth';

const router: any = Router();

// Aggregation rule routes
router.get('/aggregation/rules', authenticate, AlertAggregationController.getAggregationRules);
router.get('/aggregation/rules/:id', authenticate, AlertAggregationController.getAggregationRule);
router.post('/aggregation/rules', authenticate, AlertAggregationController.createAggregationRule);
router.put(
  '/aggregation/rules/:id',
  authenticate,
  AlertAggregationController.updateAggregationRule
);
router.delete(
  '/aggregation/rules/:id',
  authenticate,
  AlertAggregationController.deleteAggregationRule
);

// Correlation rule routes
router.get('/correlation/rules', authenticate, AlertAggregationController.getCorrelationRules);
router.get('/correlation/rules/:id', authenticate, AlertAggregationController.getCorrelationRule);
router.post('/correlation/rules', authenticate, AlertAggregationController.createCorrelationRule);
router.put(
  '/correlation/rules/:id',
  authenticate,
  AlertAggregationController.updateCorrelationRule
);
router.delete(
  '/correlation/rules/:id',
  authenticate,
  AlertAggregationController.deleteCorrelationRule
);

export default router;
