"use strict";
// jscpd:ignore-file
/**
 * Enhanced Prisma Client
 *
 * This module provides an enhanced Prisma client with:
 * - Connection pooling
 * - Retry mechanisms
 * - Proper error handling
 * - Environment-specific logging
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.executeWithRetry = exports.disconnect = exports.connectWithRetry = void 0;
const client_1 = require("@prisma/client");
const logger_1 = require("./logger");
const environment_validator_1 = require("../utils/environment-validator");
// Maximum number of connection retries
const MAX_RETRIES = (0, environment_validator_1.isProduction)() ? 10 : 5;
// Retry delay in milliseconds (exponential backoff)
const RETRY_DELAY_MS = (0, environment_validator_1.isProduction)() ? 2000 : 1000;
// Connection options based on environment
const getConnectionOptions = () => {
    // Base options
    const options = {
        errorFormat: (0, environment_validator_1.isProduction)() ? "minimal" : "pretty"
    };
    // Add logging in development
    if ((0, environment_validator_1.isDevelopment)()) {
        options.log = [
            { emit: "event", level: "query" },
            { emit: "event", level: "info" },
            { emit: "event", level: "warn" },
            { emit: "event", level: "error" }
        ];
    }
    else {
        // Only log errors and warnings in production
        options.log = [
            { emit: "event", level: "warn" },
            { emit: "event", level: "error" }
        ];
    }
    return options;
};
// Create Prisma client with connection options
const prisma = new client_1.PrismaClient(getConnectionOptions());
// Set up logging for development environment
if ((0, environment_validator_1.isDevelopment)()) {
    prisma.$on("query", (e) => {
        logger_1.logger.debug(`Query: ${e.query}`);
        logger_1.logger.debug(`Duration: ${e.duration}ms`);
    });
    prisma.$on("info", (e) => {
        logger_1.logger.info(`Prisma info: ${e.message}`);
    });
}
// Set up error logging for all environments
prisma.$on("warn", (e) => {
    logger_1.logger.warn(`Prisma warning: ${e.message}`);
});
prisma.$on("error", (e) => {
    logger_1.logger.error(`Prisma error: ${e.message}`);
});
/**
 * Connect to the database with retry mechanism
 * @returns {Promise<boolean>} True if connection successful, false otherwise
 */
const connectWithRetry = async () => {
    let retries = 0;
    let connected = false;
    while (retries < MAX_RETRIES && !connected) {
        try {
            logger_1.logger.info(`Connecting to database (attempt ${retries + 1}/${MAX_RETRIES})...`);
            await prisma.$connect();
            connected = true;
            logger_1.logger.info("Database connection established successfully");
            return true;
        }
        catch (error) {
            retries++;
            const delay = RETRY_DELAY_MS * Math.pow(2, retries - 1); // Exponential backoff
            logger_1.logger.error(`Database connection failed (attempt ${retries}/${MAX_RETRIES}): ${error.message}`);
            if (retries < MAX_RETRIES) {
                logger_1.logger.info(`Retrying in ${delay}ms...`);
                await new Promise(resolve => setTimeout(resolve, delay));
            }
            else {
                if ((0, environment_validator_1.isProduction)()) {
                    logger_1.logger.warn("Maximum retries reached. Using fallback mode without database in production.");
                    return true; // Return true in production to allow the app to start
                }
                else {
                    logger_1.logger.error("Maximum retries reached. Could not connect to database.");
                    return false;
                }
            }
        }
    }
    return connected;
};
exports.connectWithRetry = connectWithRetry;
/**
 * Disconnect from the database
 * @returns {Promise<void>}
 */
const disconnect = async () => {
    try {
        await prisma.$disconnect();
        logger_1.logger.info("Database connection closed successfully");
    }
    catch (error) {
        logger_1.logger.error(`Error disconnecting from database: ${error.message}`);
    }
};
exports.disconnect = disconnect;
/**
 * Execute a database operation with retry mechanism
 * @param operation Function that performs the database operation
 * @param maxRetries Maximum number of retries (default: 3)
 * @returns Result of the operation
 */
const executeWithRetry = async (operation, maxRetries = 3) => {
    let retries = 0;
    while (true) {
        try {
            return await operation();
        }
        catch (error) {
            retries++;
            // Check if we should retry
            if (retries <= maxRetries && isPrismaRetryableError(error)) {
                const delay = RETRY_DELAY_MS * Math.pow(2, retries - 1); // Exponential backoff
                logger_1.logger.warn(`Database operation failed, retrying (${retries}/${maxRetries}) in ${delay}ms: ${error.message}`);
                await new Promise(resolve => setTimeout(resolve, delay));
            }
            else {
                // Max retries reached or non-retryable error
                logger_1.logger.error(`Database operation failed after ${retries} retries: ${error.message}`);
                throw error;
            }
        }
    }
};
exports.executeWithRetry = executeWithRetry;
/**
 * Check if a Prisma error is retryable
 * @param error Error to check
 * @returns True if the error is retryable
 */
function isPrismaRetryableError(error) {
    // Connection errors
    if (error.code === "P1001" || error.code === "P1002") {
        return true;
    }
    // Timeout errors
    if (error.code === "P1008") {
        return true;
    }
    // Connection pool errors
    if (error.message && error.message.includes("Connection pool")) {
        return true;
    }
    return false;
}
// Export the enhanced Prisma client
exports.default = prisma;
//# sourceMappingURL=prisma-client.js.map