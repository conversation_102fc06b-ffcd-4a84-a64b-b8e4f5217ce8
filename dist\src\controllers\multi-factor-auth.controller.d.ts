declare global {
    namespace Express {
        interface Request {
            user?: {
                id: string;
                role: string;
                merchantId?: string;
            };
        }
    }
}
/**
 * Get user's verification methods
 */
export declare const getUserVerificationMethods: any;
/**
 * Enable MFA for a user
 */
export declare const enableMFA: any;
/**
 * Disable MFA for a user
 */
export declare const disableMFA: any;
//# sourceMappingURL=multi-factor-auth.controller.d.ts.map