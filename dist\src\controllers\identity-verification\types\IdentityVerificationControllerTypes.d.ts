/**
 * Identity Verification Controller Types
 *
 * Type definitions for identity verification controller components.
 */
import { Request, Response } from 'express';
/**
 * Extended request interface with user information
 */
export interface AuthenticatedRequest extends Request {
    user?: {
        id: string;
        role: string;
        merchantId?: string;
    };
}
/**
 * Ethereum signature verification request
 */
export interface EthereumSignatureRequest {
    address: string;
    message: string;
    signature: string;
}
/**
 * ERC-1484 identity verification request
 */
export interface ERC1484IdentityRequest {
    address: string;
    ein: string;
    registryAddress: string;
}
/**
 * ERC-725 identity verification request
 */
export interface ERC725IdentityRequest {
    address: string;
    key: string;
    value: string;
}
/**
 * ENS verification request
 */
export interface ENSVerificationRequest {
    ensName: string;
    address: string;
}
/**
 * Polygon ID verification request
 */
export interface PolygonIDRequest {
    address: string;
    proof: any;
}
/**
 * Worldcoin verification request
 */
export interface WorldcoinRequest {
    address: string;
    nullifier: string;
    proof: any;
}
/**
 * Unstoppable Domains verification request
 */
export interface UnstoppableDomainsRequest {
    domain: string;
    address: string;
}
/**
 * Blockchain verification request
 */
export interface BlockchainVerificationRequest {
    walletAddress: string;
    network: string;
}
/**
 * Complete blockchain verification request
 */
export interface CompleteBlockchainVerificationRequest {
    requestId: string;
    signature: string;
}
/**
 * Add claim request
 */
export interface AddClaimRequest {
    verificationId: string;
    type: string;
    value: string;
    issuer: string;
}
/**
 * Set verification expiration request
 */
export interface SetVerificationExpirationRequest {
    verificationId: string;
    expiresAt: string;
}
/**
 * ENS domain verification request
 */
export interface ENSDomainRequest {
    ensName: string;
}
/**
 * Verification response
 */
export interface VerificationResponse {
    id: string;
    type: string;
    status: 'PENDING' | 'VERIFIED' | 'FAILED' | 'EXPIRED';
    address?: string;
    userId?: string;
    merchantId?: string;
    metadata?: any;
    createdAt: Date;
    updatedAt: Date;
    expiresAt?: Date;
}
/**
 * Claim response
 */
export interface ClaimResponse {
    id: string;
    verificationId: string;
    type: string;
    value: string;
    issuer: string;
    isRevoked: boolean;
    createdAt: Date;
    updatedAt: Date;
}
/**
 * Verification statistics response
 */
export interface VerificationStatsResponse {
    totalVerifications: number;
    verifiedCount: number;
    pendingCount: number;
    failedCount: number;
    expiredCount: number;
    verificationsByType: Record<string, number>;
    verificationsByNetwork: Record<string, number>;
}
/**
 * Supported networks response
 */
export interface SupportedNetworksResponse {
    networks: {
        id: string;
        name: string;
        chainId: number;
        rpcUrl: string;
        explorerUrl: string;
        isTestnet: boolean;
        isSupported: boolean;
    }[];
}
/**
 * API response wrapper
 */
export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    message?: string;
    error?: string;
}
/**
 * Pagination parameters
 */
export interface PaginationParams {
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
}
/**
 * Verification filters
 */
export interface VerificationFilters {
    type?: string;
    status?: 'PENDING' | 'VERIFIED' | 'FAILED' | 'EXPIRED';
    network?: string;
    userId?: string;
    merchantId?: string;
    dateFrom?: Date;
    dateTo?: Date;
    search?: string;
}
/**
 * Validation error interface
 */
export interface ValidationError {
    field: string;
    message: string;
    value?: any;
}
/**
 * Controller method options
 */
export interface ControllerMethodOptions {
    requireAuth?: boolean;
    requiredRole?: string;
    validateInput?: boolean;
    logRequest?: boolean;
}
/**
 * Request context interface
 */
export interface RequestContext {
    user?: {
        id: string;
        role: string;
        merchantId?: string;
    };
    requestId: string;
    timestamp: Date;
    ip: string;
    userAgent: string;
}
/**
 * Service dependencies interface
 */
export interface IdentityVerificationServiceDependencies {
    identityVerificationService: any;
    authorizationService: any;
    validationService: any;
    auditService?: any;
}
/**
 * Controller configuration
 */
export interface IdentityVerificationControllerConfig {
    enableAuditLogging?: boolean;
    enableRateLimiting?: boolean;
    defaultPageSize?: number;
    maxPageSize?: number;
    cacheEnabled?: boolean;
    cacheTtl?: number;
    supportedNetworks?: string[];
    maxVerificationAge?: number;
}
/**
 * Audit log entry
 */
export interface AuditLogEntry {
    action: string;
    resource: string;
    resourceId?: string;
    userId: string;
    userRole: string;
    timestamp: Date;
    details?: any;
    ipAddress: string;
    userAgent: string;
}
/**
 * Error response format
 */
export interface ErrorResponse {
    success: false;
    error: {
        message: string;
        code: string;
        type: string;
        details?: any;
    };
    timestamp: Date;
    requestId: string;
}
/**
 * Success response format
 */
export interface SuccessResponse<T = any> {
    success: true;
    data: T;
    message?: string;
    pagination?: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
    };
    timestamp: Date;
    requestId: string;
}
/**
 * Controller method result
 */
export type ControllerResult<T = any> = Promise<SuccessResponse<T> | ErrorResponse>;
/**
 * Middleware function type
 */
export type MiddlewareFunction = (req: AuthenticatedRequest, res: Response, next: Function) => void | Promise<void>;
/**
 * Controller method type
 */
export type ControllerMethod<T = any> = (req: AuthenticatedRequest, res: Response) => ControllerResult<T>;
/**
 * Verification method types
 */
export declare enum VerificationMethod {
    ETHEREUM_SIGNATURE = "ETHEREUM_SIGNATURE",
    ERC1484 = "ERC1484",
    ERC725 = "ERC725",
    ENS = "ENS",
    POLYGON_ID = "POLYGON_ID",
    WORLDCOIN = "WORLDCOIN",
    UNSTOPPABLE_DOMAINS = "UNSTOPPABLE_DOMAINS",
    BLOCKCHAIN_VERIFICATION = "BLOCKCHAIN_VERIFICATION",
    ENS_DOMAIN = "ENS_DOMAIN"
}
/**
 * Verification status enum
 */
export declare enum VerificationStatus {
    PENDING = "PENDING",
    VERIFIED = "VERIFIED",
    FAILED = "FAILED",
    EXPIRED = "EXPIRED"
}
/**
 * Supported blockchain networks
 */
export declare enum SupportedNetwork {
    ETHEREUM = "ethereum",
    POLYGON = "polygon",
    BSC = "bsc",
    ARBITRUM = "arbitrum",
    OPTIMISM = "optimism",
    AVALANCHE = "avalanche"
}
/**
 * User role enum
 */
export declare enum UserRole {
    USER = "USER",
    MERCHANT = "MERCHANT",
    ADMIN = "ADMIN"
}
/**
 * Authorization context
 */
export interface AuthorizationContext {
    user: {
        id: string;
        role: string;
        merchantId?: string;
    };
    resource: string;
    action: string;
    resourceId?: string;
}
/**
 * Permission check result
 */
export interface PermissionResult {
    allowed: boolean;
    reason?: string;
    requiredRole?: string;
    requiredPermissions?: string[];
}
//# sourceMappingURL=IdentityVerificationControllerTypes.d.ts.map