/**
 * Base Service
 * 
 * This is a base service class that provides common functionality
 * for all services in the application.
 */

export class BaseService {
  protected model: any;
  
  constructor(model: any) {
    this.model = model;
  }
  
  /**
   * Find all items
   */
  async findAll(query: Record<string, string | string[]> = {}) {
    return this.model.findMany(query);
  }
  
  /**
   * Find item by ID
   */
  async findById(id: string | number) {
    return this.model.findUnique({
      where: { id }
    });
  }
  
  /**
   * Create item
   */
  async create(data: any) {
    return this.model.create({
      data
    });
  }
  
  /**
   * Update item
   */
  async update(id: string | number, data: any) {
    return this.model.update({
      where: { id },
      data
    });
  }
  
  /**
   * Delete item
   */
  async delete(id: string | number) {
    return this.model.delete({
      where: { id }
    });
  }
}