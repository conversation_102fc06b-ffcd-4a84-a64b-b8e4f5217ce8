{"version": 3, "file": "ResponseMapper.js", "sourceRoot": "", "sources": ["../../../../../src/controllers/alert-aggregation/mappers/ResponseMapper.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;AAUH,6DAA0D;AAE1D;;GAEG;AACH,MAAa,cAAc;IACzB;;OAEG;IACH,MAAM,CAAC,WAAW,CAChB,GAAa,EACb,IAAO,EACP,OAAgB,EAChB,aAAqB,GAAG,EACxB,UAKC;QAED,MAAM,QAAQ,GAAuB;YACnC,OAAO,EAAE,IAAI;YACb,IAAI;YACJ,OAAO;YACP,UAAU;YACV,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,SAAS,EAAE,GAAG,CAAC,MAAM,CAAC,SAAS,IAAI,SAAS;SAC7C,CAAC;QAEF,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,SAAS,CAAC,GAAa,EAAE,KAAuB,EAAE,UAAmB;QAC1E,IAAI,aAA4B,CAAC;QAEjC,IAAI,KAAK,YAAY,mBAAQ,EAAE,CAAC;YAC9B,aAAa,GAAG;gBACd,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE;oBACL,OAAO,EAAE,KAAK,CAAC,OAAO;oBACtB,IAAI,EAAE,KAAK,CAAC,IAAI;oBAChB,IAAI,EAAE,KAAK,CAAC,IAAI;oBAChB,OAAO,EAAE,KAAK,CAAC,OAAO;iBACvB;gBACD,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,SAAS,EAAE,GAAG,CAAC,MAAM,CAAC,SAAS,IAAI,SAAS;aAC7C,CAAC;YAEF,UAAU,GAAG,UAAU,IAAI,KAAK,CAAC,UAAU,IAAI,GAAG,CAAC;QACrD,CAAC;aAAM,CAAC;YACN,aAAa,GAAG;gBACd,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE;oBACL,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI,uBAAuB;oBACjD,IAAI,EAAE,uBAAuB;oBAC7B,IAAI,EAAE,UAAU;iBACjB;gBACD,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,SAAS,EAAE,GAAG,CAAC,MAAM,CAAC,SAAS,IAAI,SAAS;aAC7C,CAAC;YAEF,UAAU,GAAG,UAAU,IAAI,GAAG,CAAC;QACjC,CAAC;QAED,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,wBAAwB,CAC7B,GAAa,EACb,KAAgC,EAChC,KAAa,EACb,OAAe,CAAC,EAChB,QAAgB,EAAE;QAElB,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;QAE5C,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,KAAK,EAAE,aAAa,KAAK,CAAC,MAAM,oBAAoB,EAAE,GAAG,EAAE;YAC/E,IAAI;YACJ,KAAK;YACL,KAAK;YACL,UAAU;SACX,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,mBAAmB,CAAC,GAAa,EAAE,IAA6B,EAAE,OAAgB;QACvF,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,IAAI,EAAE,OAAO,IAAI,yCAAyC,CAAC,CAAC;IACpF,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,0BAA0B,CAAC,GAAa,EAAE,IAA6B;QAC5E,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,IAAI,EAAE,uCAAuC,EAAE,GAAG,CAAC,CAAC;IAC5E,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,0BAA0B,CAAC,GAAa,EAAE,IAA6B;QAC5E,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,IAAI,EAAE,uCAAuC,CAAC,CAAC;IACvE,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,0BAA0B,CAAC,GAAa;QAC7C,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,IAAI,EAAE,uCAAuC,CAAC,CAAC;IACvE,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,wBAAwB,CAC7B,GAAa,EACb,KAAgC,EAChC,KAAa,EACb,OAAe,CAAC,EAChB,QAAgB,EAAE;QAElB,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;QAE5C,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,KAAK,EAAE,aAAa,KAAK,CAAC,MAAM,oBAAoB,EAAE,GAAG,EAAE;YAC/E,IAAI;YACJ,KAAK;YACL,KAAK;YACL,UAAU;SACX,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,mBAAmB,CAAC,GAAa,EAAE,IAA6B,EAAE,OAAgB;QACvF,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,IAAI,EAAE,OAAO,IAAI,yCAAyC,CAAC,CAAC;IACpF,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,0BAA0B,CAAC,GAAa,EAAE,IAA6B;QAC5E,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,IAAI,EAAE,uCAAuC,EAAE,GAAG,CAAC,CAAC;IAC5E,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,0BAA0B,CAAC,GAAa,EAAE,IAA6B;QAC5E,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,IAAI,EAAE,uCAAuC,CAAC,CAAC;IACvE,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,0BAA0B,CAAC,GAAa;QAC7C,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,IAAI,EAAE,uCAAuC,CAAC,CAAC;IACvE,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,mBAAmB,CACxB,GAAa,EACb,MAAa,EACb,UAAkB,mBAAmB;QAErC,MAAM,KAAK,GAAG,IAAI,mBAAQ,CAAC;YACzB,OAAO;YACP,IAAI,EAAE,YAAmB;YACzB,IAAI,EAAE,eAAsB;YAC5B,OAAO,EAAE,EAAE,MAAM,EAAE;SACpB,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,sBAAsB,CAC3B,GAAa,EACb,UAAkB,eAAe,EACjC,YAAqB;QAErB,MAAM,KAAK,GAAG,IAAI,mBAAQ,CAAC;YACzB,OAAO;YACP,IAAI,EAAE,gBAAuB;YAC7B,IAAI,EAAE,qBAA4B;YAClC,OAAO,EAAE,EAAE,YAAY,EAAE;SAC1B,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,iBAAiB,CAAC,GAAa,EAAE,WAAmB,UAAU;QACnE,MAAM,KAAK,GAAG,IAAI,mBAAQ,CAAC;YACzB,OAAO,EAAE,GAAG,QAAQ,YAAY;YAChC,IAAI,EAAE,WAAkB;YACxB,IAAI,EAAE,oBAA2B;SAClC,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,uBAAuB,CAAC,GAAa,EAAE,UAAkB,uBAAuB;QACrF,MAAM,KAAK,GAAG,IAAI,mBAAQ,CAAC;YACzB,OAAO;YACP,IAAI,EAAE,UAAiB;YACvB,IAAI,EAAE,uBAA8B;SACrC,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,gBAAgB,CACrB,IAAY,EACZ,KAAa,EACb,KAAa;QASb,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;QAE5C,OAAO;YACL,IAAI;YACJ,KAAK;YACL,KAAK;YACL,UAAU;YACV,OAAO,EAAE,IAAI,GAAG,UAAU;YAC1B,OAAO,EAAE,IAAI,GAAG,CAAC;SAClB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,iBAAiB,CACtB,OAAgB,EAChB,IAAQ,EACR,OAAgB,EAChB,KAAW;QAEX,OAAO;YACL,OAAO;YACP,IAAI;YACJ,OAAO;YACP,KAAK;SACN,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,YAAY,CAAC,EAAY;QAC9B,OAAO,CAAC,GAAQ,EAAE,GAAa,EAAE,IAAc,EAAE,EAAE;YACjD,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;QACpE,CAAC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,aAAa,CAAC,GAAa;QAChC,GAAG,CAAC,SAAS,CAAC,cAAc,EAAE,kBAAkB,CAAC,CAAC;QAClD,GAAG,CAAC,SAAS,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;QACtC,GAAG,CAAC,SAAS,CAAC,iBAAiB,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,WAAW,CAAC,MAAc,EAAE,GAAW,EAAE,UAAkB,EAAE,YAAoB;QACtF,OAAO,CAAC,GAAG,CAAC,GAAG,MAAM,IAAI,GAAG,MAAM,UAAU,MAAM,YAAY,IAAI,CAAC,CAAC;IACtE,CAAC;CACF;AApSD,wCAoSC"}