/**
 * Alert Aggregation Controller
 *
 * Modular controller for alert aggregation operations.
 */
import { BaseController } from '../base.controller';
/**
 * Modular Alert Aggregation Controller
 */
export declare class AlertAggregationController extends BaseController {
    private readonly authService;
    private readonly validationService;
    private readonly businessService;
    constructor();
    /**
     * Get all aggregation rules
     */
    getAggregationRules: any;
    /**
     * Get a specific aggregation rule
     */
    getAggregationRule: any;
    /**
     * Create a new aggregation rule
     */
    createAggregationRule: any;
    /**
     * Update an existing aggregation rule
     */
    updateAggregationRule: any;
    /**
     * Delete an aggregation rule
     */
    deleteAggregationRule: any;
    /**
     * Get all correlation rules
     */
    getCorrelationRules: any;
    /**
     * Get a specific correlation rule
     */
    getCorrelationRule: any;
    /**
     * Health check endpoint
     */
    healthCheck: any;
}
//# sourceMappingURL=AlertAggregationController.d.ts.map