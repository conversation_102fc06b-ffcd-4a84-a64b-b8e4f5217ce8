/**
 * Fraud Detection Types and Interfaces
 * 
 * Centralized type definitions for the fraud detection system.
 */

import { Transaction, PaymentMethod, Merchant } from "@prisma/client";

/**
 * Risk level enum
 */
export enum RiskLevel {
  LOW = "LOW",
  MEDIUM = "MEDIUM",
  HIGH = "HIGH",
  CRITICAL = "CRITICAL",
}

/**
 * Risk factor enum
 */
export enum RiskFactor {
  AMOUNT = "AMOUNT",
  LOCATION = "LOCATION",
  FREQUENCY = "FREQUENCY",
  TIME = "TIME",
  IP = "IP",
  DEVICE = "DEVICE",
  PAYMENT_METHOD = "PAYMENT_METHOD",
  BEHAVIOR = "BEHAVIOR",
  HISTORY = "HISTORY",
}

/**
 * Risk factor result interface
 */
export interface RiskFactorResult {
  factor: RiskFactor;
  score: number;
  reason: string;
  confidence: number;
  metadata?: any;
}

/**
 * Risk score interface
 */
export interface RiskScore {
  /**
   * Overall risk score (0-100)
   */
  score: number;

  /**
   * Risk level
   */
  level: RiskLevel;

  /**
   * Risk factors with their individual scores
   */
  factors: RiskFactorResult[];

  /**
   * Timestamp of the risk assessment
   */
  timestamp: Date;

  /**
   * Confidence in the assessment (0-1)
   */
  confidence: number;
}

/**
 * Transaction risk assessment interface
 */
export interface TransactionRiskAssessment {
  /**
   * Transaction ID
   */
  transactionId: string;

  /**
   * Risk score
   */
  riskScore: RiskScore;

  /**
   * Whether the transaction is flagged as potentially fraudulent
   */
  isFlagged: boolean;

  /**
   * Whether the transaction is blocked
   */
  isBlocked: boolean;

  /**
   * Reason for flagging or blocking
   */
  reason?: string;

  /**
   * Recommended action
   */
  recommendedAction?: string;

  /**
   * Processing time in milliseconds
   */
  processingTime?: number;
}

/**
 * Fraud detection configuration
 */
export interface FraudDetectionConfig {
  /**
   * Threshold for flagging transactions (0-100)
   */
  flagThreshold: number;

  /**
   * Threshold for blocking transactions (0-100)
   */
  blockThreshold: number;

  /**
   * Whether to automatically block high-risk transactions
   */
  autoBlock: boolean;

  /**
   * Factor weights (0-1)
   */
  factorWeights: {
    [key in RiskFactor]?: number;
  };

  /**
   * High-risk countries
   */
  highRiskCountries: string[];

  /**
   * High-risk IP ranges
   */
  highRiskIpRanges: string[];

  /**
   * Maximum transaction amount before flagging
   */
  maxTransactionAmount: number;

  /**
   * Maximum transactions per hour before flagging
   */
  maxTransactionsPerHour: number;

  /**
   * Maximum transactions per day before flagging
   */
  maxTransactionsPerDay: number;

  /**
   * Velocity check settings
   */
  velocitySettings: {
    enabled: boolean;
    timeWindowMinutes: number;
    maxTransactions: number;
    maxAmount: number;
  };

  /**
   * Machine learning settings
   */
  mlSettings: {
    enabled: boolean;
    modelVersion: string;
    confidenceThreshold: number;
  };
}

/**
 * Transaction context for risk assessment
 */
export interface TransactionContext {
  transaction: Transaction;
  ipAddress: string;
  userAgent: string;
  deviceId: string;
  merchant: Merchant;
  customerEmail?: string;
  sessionId?: string;
  referrer?: string;
  timestamp: Date;
}

/**
 * Risk detector interface
 */
export interface IRiskDetector {
  getName(): string;
  getFactor(): RiskFactor;
  detect(context: TransactionContext, config: FraudDetectionConfig): Promise<RiskFactorResult>;
  isEnabled(config: FraudDetectionConfig): boolean;
}

/**
 * Risk rule interface
 */
export interface IRiskRule {
  getName(): string;
  evaluate(context: TransactionContext, config: FraudDetectionConfig): Promise<boolean>;
  getDescription(): string;
}

/**
 * Fraud detection statistics
 */
export interface FraudDetectionStats {
  totalAssessments: number;
  flaggedTransactions: number;
  blockedTransactions: number;
  falsePositives: number;
  truePositives: number;
  averageRiskScore: number;
  assessmentsByRiskLevel: Record<RiskLevel, number>;
  assessmentsByFactor: Record<RiskFactor, number>;
  processingTimeStats: {
    average: number;
    min: number;
    max: number;
  };
}

/**
 * Risk assessment filters
 */
export interface RiskAssessmentFilters {
  merchantId?: string;
  riskLevel?: RiskLevel;
  isFlagged?: boolean;
  isBlocked?: boolean;
  dateFrom?: Date;
  dateTo?: Date;
  minScore?: number;
  maxScore?: number;
  limit?: number;
  offset?: number;
}

/**
 * Fraud alert interface
 */
export interface FraudAlert {
  id: string;
  transactionId: string;
  merchantId: string;
  riskScore: number;
  riskLevel: RiskLevel;
  alertType: 'FLAGGED' | 'BLOCKED' | 'MANUAL_REVIEW';
  message: string;
  isResolved: boolean;
  resolvedBy?: string;
  resolvedAt?: Date;
  createdAt: Date;
}

/**
 * Whitelist/Blacklist entry
 */
export interface ListEntry {
  id: string;
  type: 'WHITELIST' | 'BLACKLIST';
  category: 'IP' | 'EMAIL' | 'DEVICE' | 'COUNTRY';
  value: string;
  reason?: string;
  merchantId?: string;
  isActive: boolean;
  expiresAt?: Date;
  createdAt: Date;
}

/**
 * Machine learning model result
 */
export interface MLModelResult {
  prediction: number; // 0-1 probability of fraud
  confidence: number; // 0-1 confidence in prediction
  features: Record<string, number>;
  modelVersion: string;
  processingTime: number;
}

/**
 * Velocity check result
 */
export interface VelocityCheckResult {
  isViolation: boolean;
  currentCount: number;
  currentAmount: number;
  maxAllowed: number;
  timeWindow: string;
  violationType: 'COUNT' | 'AMOUNT' | 'BOTH';
}

/**
 * Fraud detection error codes
 */
export enum FraudDetectionErrorCode {
  INVALID_TRANSACTION = 'INVALID_TRANSACTION',
  INVALID_CONTEXT = 'INVALID_CONTEXT',
  DETECTOR_FAILED = 'DETECTOR_FAILED',
  RULE_EVALUATION_FAILED = 'RULE_EVALUATION_FAILED',
  CONFIG_NOT_FOUND = 'CONFIG_NOT_FOUND',
  ASSESSMENT_FAILED = 'ASSESSMENT_FAILED',
  ML_MODEL_ERROR = 'ML_MODEL_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR'
}

/**
 * Fraud detection error class
 */
export class FraudDetectionError extends Error {
  code: FraudDetectionErrorCode;
  statusCode: number;

  constructor(message: string, code: FraudDetectionErrorCode, statusCode: number = 400) {
    super(message);
    this.name = 'FraudDetectionError';
    this.code = code;
    this.statusCode = statusCode;
  }

  static invalidTransaction(message: string = 'Invalid transaction'): FraudDetectionError {
    return new FraudDetectionError(message, FraudDetectionErrorCode.INVALID_TRANSACTION, 400);
  }

  static detectorFailed(message: string = 'Risk detector failed'): FraudDetectionError {
    return new FraudDetectionError(message, FraudDetectionErrorCode.DETECTOR_FAILED, 500);
  }

  static assessmentFailed(message: string = 'Risk assessment failed'): FraudDetectionError {
    return new FraudDetectionError(message, FraudDetectionErrorCode.ASSESSMENT_FAILED, 500);
  }
}
