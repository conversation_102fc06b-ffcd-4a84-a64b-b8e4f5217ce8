/**
 * Identity Verification Response Mapper
 *
 * Handles response formatting for identity verification operations.
 */
import { Response } from 'express';
import { VerificationResponse, ClaimResponse, VerificationStatsResponse, SupportedNetworksResponse } from '../types/IdentityVerificationControllerTypes';
import { AppError } from '../../../utils/errors/AppError';
/**
 * Response mapper for identity verification
 */
export declare class IdentityVerificationResponseMapper {
    /**
     * Send success response
     */
    static sendSuccess<T>(res: Response, data: T, message?: string, statusCode?: number, pagination?: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
    }): void;
    /**
     * Send error response
     */
    static sendError(res: Response, error: AppError | Error, statusCode?: number): void;
    /**
     * Send verification result response
     */
    static sendVerificationResult(res: Response, result: any, message?: string): void;
    /**
     * Send verification response
     */
    static sendVerification(res: Response, verification: VerificationResponse, message?: string): void;
    /**
     * Send verifications list response
     */
    static sendVerificationsList(res: Response, verifications: VerificationResponse[], total: number, page?: number, limit?: number): void;
    /**
     * Send claim response
     */
    static sendClaim(res: Response, claim: ClaimResponse, message?: string, statusCode?: number): void;
    /**
     * Send claim created response
     */
    static sendClaimCreated(res: Response, claim: ClaimResponse): void;
    /**
     * Send verification statistics response
     */
    static sendVerificationStats(res: Response, stats: VerificationStatsResponse): void;
    /**
     * Send supported networks response
     */
    static sendSupportedNetworks(res: Response, networks: SupportedNetworksResponse): void;
    /**
     * Send blockchain verification request created response
     */
    static sendBlockchainVerificationRequestCreated(res: Response, request: any): void;
    /**
     * Send blockchain verification success response
     */
    static sendBlockchainVerificationSuccess(res: Response): void;
    /**
     * Send blockchain verification failure response
     */
    static sendBlockchainVerificationFailure(res: Response): void;
    /**
     * Send verification expiration check response
     */
    static sendVerificationExpirationCheck(res: Response, count: number): void;
    /**
     * Send verification expiration set response
     */
    static sendVerificationExpirationSet(res: Response, verification: VerificationResponse): void;
    /**
     * Send ENS domain verification response
     */
    static sendENSDomainVerification(res: Response, result: any): void;
    /**
     * Send validation error response
     */
    static sendValidationError(res: Response, errors: any[], message?: string): void;
    /**
     * Send authorization error response
     */
    static sendAuthorizationError(res: Response, message?: string, requiredRole?: string): void;
    /**
     * Send not found error response
     */
    static sendNotFoundError(res: Response, resource?: string): void;
    /**
     * Send internal server error response
     */
    static sendInternalServerError(res: Response, message?: string): void;
    /**
     * Format pagination metadata
     */
    static formatPagination(page: number, limit: number, total: number): {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
        hasNext: boolean;
        hasPrev: boolean;
    };
    /**
     * Handle async controller method
     */
    static asyncHandler(fn: Function): (req: any, res: Response, next: Function) => void;
    /**
     * Set response headers for API
     */
    static setApiHeaders(res: Response): void;
    /**
     * Log response for debugging
     */
    static logResponse(method: string, url: string, statusCode: number, responseTime: number): void;
    /**
     * Transform verification data for response
     */
    static transformVerification(verification: any): VerificationResponse;
    /**
     * Transform claim data for response
     */
    static transformClaim(claim: any): ClaimResponse;
    /**
     * Transform verification statistics for response
     */
    static transformVerificationStats(stats: any): VerificationStatsResponse;
}
//# sourceMappingURL=IdentityVerificationResponseMapper.d.ts.map