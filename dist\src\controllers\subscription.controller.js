"use strict";
// jscpd:ignore-file
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const subscription_plans_controller_1 = __importDefault(require("./subscription-plans.controller"));
const merchant_subscription_controller_1 = __importDefault(require("./merchant-subscription.controller"));
const subscription_history_controller_1 = __importDefault(require("./subscription-history.controller"));
// Create a facade controller that combines all subscription-related controllers
const subscriptionController = {
    // Plan management
    getAllPlans: subscription_plans_controller_1.default.getAllPlans,
    getPlanById: subscription_plans_controller_1.default.getPlanById,
    createPlan: subscription_plans_controller_1.default.createPlan,
    updatePlan: subscription_plans_controller_1.default.updatePlan,
    deletePlan: subscription_plans_controller_1.default.deletePlan,
    // Merchant subscription management
    subscribeMerchant: merchant_subscription_controller_1.default.subscribeMerchant,
    cancelSubscription: merchant_subscription_controller_1.default.cancelSubscription,
    checkSubscriptionStatus: merchant_subscription_controller_1.default.checkSubscriptionStatus,
    // Subscription history
    getSubscriptionHistory: subscription_history_controller_1.default.getSubscriptionHistory,
    downloadSubscriptionHistory: subscription_history_controller_1.default.downloadSubscriptionHistory,
    // Subscription analytics
    getSubscriptionAnalytics: async (req, res) => {
        try {
            const { merchantId } = req.params;
            const { dateRange } = req.query;
            // In a real implementation, this would query the database for subscription analytics
            // For now, we'll return mock data
            const analytics = {
                totalRevenue: 1250.00,
                subscriptionCount: 5,
                activeSubscriptions: 3,
                averageSubscriptionLength: 3.2, // in months
                mostPopularPlan: "Premium",
                revenueByPlan: [
                    { plan: "Basic", revenue: 150.00, count: 1 },
                    { plan: "Premium", revenue: 600.00, count: 3 },
                    { plan: "Enterprise", revenue: 500.00, count: 1 }
                ],
                subscriptionHistory: [
                    { month: "Jan 2023", subscriptions: 2, revenue: 250.00 },
                    { month: "Feb 2023", subscriptions: 3, revenue: 350.00 },
                    { month: "Mar 2023", subscriptions: 3, revenue: 350.00 },
                    { month: "Apr 2023", subscriptions: 4, revenue: 450.00 },
                    { month: "May 2023", subscriptions: 5, revenue: 550.00 }
                ]
            };
            return res.status(200).json({
                status: "success",
                data: analytics
            });
        }
        catch (error) {
            return res.status(500).json({
                status: "error",
                message: error.message || "Failed to retrieve subscription analytics"
            });
        }
    }
};
exports.default = subscriptionController;
//# sourceMappingURL=subscription.controller.js.map