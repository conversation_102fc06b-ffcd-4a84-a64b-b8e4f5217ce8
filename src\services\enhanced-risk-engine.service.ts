// jscpd:ignore-file
/**
 * Enhanced Risk Engine Service
 *
 * This service provides advanced risk scoring capabilities with machine learning integration,
 * behavioral analysis, and real-time risk evaluation.
 */

import { BaseService, ServiceError } from './base.service';
import { Transaction, PaymentMethod, Merchant, User } from '@prisma/client';
import { FraudDetectionService, RiskFactor, RiskLevel, RiskScore } from './fraud-detection';
import geoip from 'geoip-lite';
import { ApiErrorCode } from '../middlewares/apiResponseMiddleware';
import { logger } from '../utils/logger';
import { Transaction, PaymentMethod, Merchant, User } from '@prisma/client';

/**
 * Risk model type
 */
export enum RiskModelType {
  RULES_BASED = 'RULES_BASED',
  STATISTICAL = 'STATISTICAL',
  MACHINE_LEARNING = 'MACHINE_LEARNING',
  HYBRID = 'HYBRID',
}

/**
 * Behavioral pattern type
 */
export enum BehavioralPatternType {
  NORMAL = 'NORMAL',
  SUSPICIOUS = 'SUSPICIOUS',
  FRAUDULENT = 'FRAUDULENT',
  UNKNOWN = 'UNKNOWN',
}

/**
 * Risk velocity check result
 */
export interface RiskVelocityCheckResult {
  /**
   * Check type
   */
  type: string;

  /**
   * Current value
   */
  currentValue: number;

  /**
   * Threshold value
   */
  thresholdValue: number;

  /**
   * Whether the check was triggered
   */
  triggered: boolean;

  /**
   * Risk score contribution (0-100)
   */
  riskScore: number;
}

/**
 * Enhanced risk assessment result
 */
export interface EnhancedRiskAssessment {
  /**
   * Transaction ID
   */
  transactionId: string;

  /**
   * Risk score
   */
  riskScore: RiskScore;

  /**
   * Whether the transaction is flagged as potentially fraudulent
   */
  isFlagged: boolean;

  /**
   * Whether the transaction is blocked
   */
  isBlocked: boolean;

  /**
   * Reason for flagging or blocking
   */
  reason?: string;

  /**
   * Recommended action
   */
  recommendedAction?: string;

  /**
   * Risk model used
   */
  riskModel: RiskModelType;

  /**
   * Confidence score (0-1)
   */
  confidence: number;

  /**
   * Velocity checks
   */
  velocityChecks: RiskVelocityCheckResult[];

  /**
   * Behavioral pattern
   */
  behavioralPattern: BehavioralPatternType;

  /**
   * Risk factors with scores
   */
  riskFactors: Array<{
    factor: RiskFactor;
    score: number;
    reason: string;
    weight: number;
    contribution: number;
  }>;
}

/**
 * Enhanced risk engine configuration
 */
export interface EnhancedRiskEngineConfig {
  /**
   * Risk model to use
   */
  riskModel: RiskModelType;

  /**
   * Velocity check thresholds
   */
  velocityThresholds: {
    /**
     * Transactions per minute
     */
    transactionsPerMinute: number;

    /**
     * Transactions per hour
     */
    transactionsPerHour: number;

    /**
     * Transactions per day
     */
    transactionsPerDay: number;

    /**
     * Amount per minute
     */
    amountPerMinute: number;

    /**
     * Amount per hour
     */
    amountPerHour: number;

    /**
     * Amount per day
     */
    amountPerDay: number;

    /**
     * Failed transactions per day
     */
    failedTransactionsPerDay: number;

    /**
     * Countries per day
     */
    countriesPerDay: number;

    /**
     * IP addresses per day
     */
    ipAddressesPerDay: number;

    /**
     * Devices per day
     */
    devicesPerDay: number;
  };

  /**
   * Behavioral analysis settings
   */
  behavioralAnalysis: {
    /**
     * Whether to enable behavioral analysis
     */
    enabled: boolean;

    /**
     * Minimum transactions required for behavioral analysis
     */
    minTransactions: number;

    /**
     * Anomaly detection threshold (standard deviations)
     */
    anomalyThreshold: number;
  };

  /**
   * Machine learning settings
   */
  machineLearning: {
    /**
     * Whether to enable machine learning
     */
    enabled: boolean;

    /**
     * Minimum confidence required for ML predictions
     */
    minConfidence: number;

    /**
     * Model endpoint URL
     */
    modelEndpoint?: string;
  };
}

/**
 * Enhanced risk engine service
 */
export class EnhancedRiskEngineService extends BaseService {
  private fraudDetectionService: FraudDetectionService;

  constructor() {
    super();
    this.fraudDetectionService = new FraudDetectionService();
  }

  /**
   * Default configuration
   */
  private defaultConfig: EnhancedRiskEngineConfig = {
    riskModel: RiskModelType.HYBRID,
    velocityThresholds: {
      transactionsPerMinute: 3,
      transactionsPerHour: 10,
      transactionsPerDay: 30,
      amountPerMinute: 5000,
      amountPerHour: 20000,
      amountPerDay: 50000,
      failedTransactionsPerDay: 5,
      countriesPerDay: 3,
      ipAddressesPerDay: 5,
      devicesPerDay: 3,
    },
    behavioralAnalysis: { enabled: true, minTransactions: 5, anomalyThreshold: 2.5 },
    machineLearning: {
      enabled: true,
      minConfidence: 0.7,
      modelEndpoint: process.env.ML_MODEL_ENDPOINT,
    },
  };

  /**
   * Assess transaction risk with enhanced risk engine
   * @param transaction Transaction to assess
   * @param ipAddress IP address of the user
   * @param userAgent User agent of the user
   * @param deviceId Device ID of the user
   * @param merchant Merchant associated with the transaction
   * @returns Enhanced risk assessment
   */
  async assessTransactionRisk(
    transaction: Transaction,
    ipAddress: string,
    userAgent: string,
    deviceId: string,
    merchant: Merchant
  ): Promise<EnhancedRiskAssessment> {
    try {
      // Get merchant-specific configuration or use default
      const config: Record<string, any> =
        (await this.getMerchantRiskConfig(merchant.id)) || this.defaultConfig;

      // Get base risk assessment from fraud detection service
      const baseAssessment: any = await this.fraudDetectionService.assessTransactionRisk(
        transaction,
        ipAddress,
        userAgent,
        deviceId,
        merchant
      );

      // Perform velocity checks
      const velocityChecks: any = await this.performVelocityChecks(
        transaction,
        merchant.id,
        config.velocityThresholds
      );

      // Perform behavioral analysis if enabled
      let behavioralPattern: any = BehavioralPatternType.UNKNOWN;
      if (config.behavioralAnalysis.enabled) {
        behavioralPattern = await this.analyzeBehavioralPattern(
          transaction,
          merchant.id,
          config.behavioralAnalysis
        );
      }

      // Apply machine learning model if enabled
      let mlScore: number = 0;
      let confidence: number = 1.0;
      if (config.machineLearning.enabled && config.machineLearning.modelEndpoint) {
        const mlResult: any = await this.applyMachineLearningModel(
          transaction,
          ipAddress,
          userAgent,
          deviceId,
          merchant,
          config.machineLearning
        );
        mlScore = mlResult.score;
        confidence = mlResult.confidence;
      }

      // Calculate final risk score
      const riskScore: any = this.calculateEnhancedRiskScore(
        baseAssessment.riskScore,
        velocityChecks,
        behavioralPattern,
        mlScore,
        confidence
      );

      // Determine if transaction should be flagged or blocked
      const fraudConfig: any = await this.fraudDetectionService.getMerchantFraudConfig(
        merchant.id
      );
      const flagThreshold: any = fraudConfig?.flagThreshold || 70;
      const blockThreshold: any = fraudConfig?.blockThreshold || 85;
      const autoBlock: any = fraudConfig?.autoBlock || true;

      const isFlagged: any = riskScore.score >= flagThreshold;
      const isBlocked: any = autoBlock && riskScore.score >= blockThreshold;

      // Generate reason and recommended action
      const reason: any = this.generateReason(riskScore.factors, riskScore.level);
      const recommendedAction: any = this.generateRecommendedAction(riskScore.level, isBlocked);

      // Save enhanced risk assessment to database
      await this.saveEnhancedRiskAssessment(
        transaction.id,
        riskScore,
        isFlagged,
        isBlocked,
        config.riskModel,
        confidence,
        velocityChecks,
        behavioralPattern
      );

      return {
        transactionId: transaction.id,
        riskScore,
        isFlagged,
        isBlocked,
        reason,
        recommendedAction,
        riskModel: config.riskModel,
        confidence,
        velocityChecks,
        behavioralPattern,
        riskFactors: riskScore.factors,
      };
    } catch (error) {
      logger.error('Error assessing transaction risk with enhanced risk engine:', error);
      throw this.genericError('Failed to assess transaction risk', 500, ApiErrorCode.SERVER_ERROR);
    }
  }

  /**
   * Get merchant-specific risk configuration
   * @param merchantId Merchant ID
   * @returns Enhanced risk engine configuration
   */
  async getMerchantRiskConfig(
    merchantId: number | string
  ): Promise<EnhancedRiskEngineConfig | null> {
    try {
      const config: Record<string, any> = await this.prisma.enhancedRiskConfig.findUnique({
        where: { merchantId: typeof merchantId === 'string' ? parseInt(merchantId) : merchantId },
      });

      if (!config) {
        return null;
      }

      return {
        riskModel: config.riskModel as RiskModelType,
        velocityThresholds: JSON.parse(config.velocityThresholds),
        behavioralAnalysis: JSON.parse(config.behavioralAnalysis),
        machineLearning: JSON.parse(config.machineLearning),
      };
    } catch (error) {
      logger.error('Error getting merchant risk config:', error);
      return null;
    }
  }

  /**
   * Save enhanced risk assessment to database
   */
  private async saveEnhancedRiskAssessment(
    transactionId: string,
    riskScore: RiskScore,
    isFlagged: boolean,
    isBlocked: boolean,
    riskModel: RiskModelType,
    confidence: number,
    velocityChecks: RiskVelocityCheckResult[],
    behavioralPattern: BehavioralPatternType
  ): Promise<void> {
    try {
      await this.prisma.enhancedRiskAssessment.create({
        data: {
          transactionId,
          score: riskScore.score,
          level: riskScore.level,
          factors: JSON.stringify(riskScore.factors),
          isFlagged,
          isBlocked,
          riskModel,
          confidence,
          velocityChecks: JSON.stringify(velocityChecks),
          behavioralPattern,
          createdAt: new Date(),
        },
      });
    } catch (error) {
      logger.error('Error saving enhanced risk assessment:', error);
      // Don't throw error, just log it
    }
  }

  // Additional methods will be implemented in the next file
}
