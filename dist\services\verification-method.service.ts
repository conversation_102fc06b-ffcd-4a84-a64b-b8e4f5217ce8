// jscpd:ignore-file
import prisma from '../config/database';
import { AppError } from '../middlewares/error.middleware';
import { EventEmitter } from 'events';
import { PaymentMethodType } from './payment-method.service';
import { BinanceService } from './binance.service';
import { Transaction, PaymentMethodType, VerificationMethodType } from '../types';
import { EventEmitter } from 'events';
import { PaymentMethodType } from './payment-method.service';
import { BinanceService } from './binance.service';
import { Transaction, PaymentMethodType, VerificationMethodType } from '../types';

// Create a global event emitter for real-time verification method updates
export const verificationMethodEvents: any = new EventEmitter();

// Verification method types (same as payment method types)
export type VerificationMethodType = PaymentMethodType;

// Verification method creation data
interface CreateVerificationMethodData {
  name: string;
  type: VerificationMethodType;
  paymentMethodId: string;
  isActive?: boolean;
  verificationRequirements?: Record<string, any>;
  internalSettings?: Record<string, any>;
  displaySettings?: Record<string, any>;
}

// Verification method update data
interface UpdateVerificationMethodData {
  name?: string;
  isActive?: boolean;
  verificationRequirements?: Record<string, any>;
  internalSettings?: Record<string, any>;
  displaySettings?: Record<string, any>;
}

// Verification method service
export const VerificationMethodService: any = {
  // Create a new verification method
  async createVerificationMethod(data: CreateVerificationMethodData) {
    try {
      // Validate payment method
      const paymentMethod: any = await prisma.paymentMethod.findUnique({
        where: { id: data.paymentMethodId },
        include: { verificationMethods: true },
      });

      if (!paymentMethod) {
        throw new AppError({
          message: 'Payment method not found',
          type: ErrorType.NOT_FOUND,
          code: ErrorCode.RESOURCE_NOT_FOUND,
        });
      }

      // Check if verification method type matches payment method type
      if (data.type !== paymentMethod.type) {
        throw new AppError(
          `Verification method type (${data.type}) must match payment method type (${paymentMethod.type})`,
          400
        );
      }

      // Check if payment method already has a verification method of this type
      const existingVerificationMethod: any = paymentMethod.verificationMethods.find(
        (vm) => vm.type === data.type
      );

      if (existingVerificationMethod) {
        throw new AppError(
          `Payment method already has a verification method of type ${data.type}`,
          400
        );
      }

      // Create verification method
      const verificationMethod: any = await prisma.verificationMethod.create({
        data: {
          name: data.name,
          type: data.type,
          paymentMethodId: data.paymentMethodId,
          isActive: data.isActive !== undefined ? data.isActive : true,
          config: {
            verificationRequirements: data.verificationRequirements || {},
            internalSettings: data.internalSettings || {},
            displaySettings: data.displaySettings || {},
          },
        },
      });

      // Emit verification method created event
      verificationMethodEvents.emit('verificationMethod.created', verificationMethod);

      return verificationMethod;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError({
        message: 'Failed to create verification method',
        type: ErrorType.INTERNAL,
        code: ErrorCode.INTERNAL_SERVER_ERROR,
      });
    }
  },

  // Get verification method by ID
  async getVerificationMethodById(id: string) {
    const verificationMethod = await prisma.verificationMethod.findUnique({
      where: { id },
      include: {
        paymentMethod: {
          select: { id: true, name: true, type: true, merchantId: true },
        },
      },
    });

    if (!verificationMethod) {
      throw new AppError({
        message: 'Verification method not found',
        type: ErrorType.NOT_FOUND,
        code: ErrorCode.RESOURCE_NOT_FOUND,
      });
    }

    return verificationMethod;
  },

  // Update verification method
  async updateVerificationMethod(id: string, data: UpdateVerificationMethodData) {
    // Get current verification method
    const verificationMethod: any = await prisma.verificationMethod.findUnique({
      where: { id },
    });

    if (!verificationMethod) {
      throw new AppError({
        message: 'Verification method not found',
        type: ErrorType.NOT_FOUND,
        code: ErrorCode.RESOURCE_NOT_FOUND,
      });
    }

    // Get current config
    const currentConfig: any = (verificationMethod.config as any) || {};
    const verificationRequirements: any = currentConfig.verificationRequirements || {};
    const internalSettings: any = currentConfig.internalSettings || {};
    const displaySettings: any = currentConfig.displaySettings || {};

    // Update verification method
    const updatedVerificationMethod: any = await prisma.verificationMethod.update({
      where: { id },
      data: {
        name: data.name,
        isActive: data.isActive,
        config: {
          verificationRequirements: data.verificationRequirements
            ? { ...verificationRequirements, ...data.verificationRequirements }
            : verificationRequirements,
          internalSettings: data.internalSettings
            ? { ...internalSettings, ...data.internalSettings }
            : internalSettings,
          displaySettings: data.displaySettings
            ? { ...displaySettings, ...data.displaySettings }
            : displaySettings,
        },
      },
      include: {
        paymentMethod: {
          select: { id: true, name: true, type: true, merchantId: true },
        },
      },
    });

    // Emit verification method updated event
    verificationMethodEvents.emit('verificationMethod.updated', updatedVerificationMethod);

    return updatedVerificationMethod;
  },

  // Delete verification method
  async deleteVerificationMethod(id: string) {
    // Check if verification method exists
    const verificationMethod: any = await prisma.verificationMethod.findUnique({
      where: { id },
    });

    if (!verificationMethod) {
      throw new AppError({
        message: 'Verification method not found',
        type: ErrorType.NOT_FOUND,
        code: ErrorCode.RESOURCE_NOT_FOUND,
      });
    }

    // Delete verification method
    await prisma.verificationMethod.delete({
      where: { id },
    });

    // Emit verification method deleted event
    verificationMethodEvents.emit('verificationMethod.deleted', { ...verificationMethod });

    return { id, message: 'Verification method deleted successfully' };
  },

  // Get verification methods for payment method
  async getVerificationMethodsForPaymentMethod(paymentMethodId: string) {
    // Validate payment method
    const paymentMethod: any = await prisma.paymentMethod.findUnique({
      where: { id: paymentMethodId },
    });

    if (!paymentMethod) {
      throw new AppError({
        message: 'Payment method not found',
        type: ErrorType.NOT_FOUND,
        code: ErrorCode.RESOURCE_NOT_FOUND,
      });
    }

    // Get verification methods
    const verificationMethods: any = await prisma.verificationMethod.findMany({
      where: { paymentMethodId },
    });

    return verificationMethods;
  },

  // Get verification method types
  getVerificationMethodTypes() {
    return ['binance_pay', 'binance_c2c', 'binance_trc20', 'crypto_transfer'];
  },

  // Verify payment using verification method
  async verifyPayment(data: {
    transactionId: string;
    verificationMethodId: string;
    verificationData: Record<string, any>;
  }) {
    // Get verification method
    const verificationMethod: any = await prisma.verificationMethod.findUnique({
      where: { id: data.verificationMethodId },
      include: {
        paymentMethod: {
          include: { merchant: true },
        },
      },
    });

    if (!verificationMethod) {
      throw new AppError({
        message: 'Verification method not found',
        type: ErrorType.NOT_FOUND,
        code: ErrorCode.RESOURCE_NOT_FOUND,
      });
    }

    // Get transaction
    const transaction: any = await prisma.transaction.findUnique({
      where: { id: data.transactionId },
    });

    if (!transaction) {
      throw new AppError({
        message: 'Transaction not found',
        type: ErrorType.NOT_FOUND,
        code: ErrorCode.RESOURCE_NOT_FOUND,
      });
    }

    // Check if transaction is for the correct payment method
    if (transaction.paymentMethodId !== verificationMethod.paymentMethodId) {
      throw new AppError({
        message: 'Transaction is not for this payment method',
        type: ErrorType.VALIDATION,
        code: ErrorCode.INVALID_INPUT,
      });
    }

    // Check if transaction is already verified
    if (transaction.status === 'COMPLETED') {
      return { verified: true, transaction };
    }

    // Check if transaction is expired
    if (transaction.expiresAt && transaction.expiresAt < new Date()) {
      // Update transaction status to expired
      await prisma.transaction.update({
        where: { id: transaction.id },
        data: { status: 'EXPIRED' },
      });
      throw new AppError({
        message: 'Transaction has expired',
        type: ErrorType.VALIDATION,
        code: ErrorCode.INVALID_INPUT,
      });
    }

    // Perform verification based on verification method type
    let verified: boolean = false;
    let verificationResult: any = null;

    // Get merchant API credentials if needed
    const merchant: any = verificationMethod.paymentMethod.merchant;
    const config: any = (verificationMethod.config as any) || {};
    const internalSettings: any = config.internalSettings || {};
    const usesMerchantCredentials: any = internalSettings?.usesMerchantCredentials === true;

    // Get API credentials
    let apiKey: string = '';
    let apiSecret: string = '';

    if (usesMerchantCredentials) {
      // Use merchant API credentials
      apiKey = merchant.apiKey || '';
      apiSecret = merchant.apiSecret || '';
    } else {
      // Use payment method config credentials
      const paymentMethodConfig: any = verificationMethod.paymentMethod.config as any;
      apiKey = paymentMethodConfig?.apiKey || '';
      apiSecret = paymentMethodConfig?.apiSecret || '';
    }

    switch (verificationMethod.type) {
      case 'binance_pay':
        // Verify Binance Pay transaction ID
        if (!data.verificationData.txId) {
          throw new AppError({
            message: 'Transaction ID is required for Binance Pay verification',
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD,
          });
        }

        try {
          // Call Binance API to verify the transaction
          const result: any = await BinanceService.verifyBinancePayTransaction(
            apiKey,
            apiSecret,
            data.verificationData.txId,
            transaction.amount,
            transaction.currency
          );

          verified = result.verified;
          verificationResult = result.transaction;
        } catch (error) {
          console.error('Binance Pay verification error:', error);
          throw new AppError({
            message: 'Failed to verify Binance Pay transaction',
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR,
          });
        }
        break;

      case 'binance_c2c':
        // Verify Binance C2C note
        if (!data.verificationData.note) {
          throw new AppError({
            message: 'Note is required for Binance C2C verification',
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD,
          });
        }

        try {
          // Call Binance API to verify the transaction
          const result: any = await BinanceService.verifyBinanceC2CTransaction(
            apiKey,
            apiSecret,
            data.verificationData.note,
            transaction.amount,
            transaction.currency
          );

          verified = result.verified;
          verificationResult = result.transaction;
        } catch (error) {
          console.error('Binance C2C verification error:', error);
          throw new AppError({
            message: 'Failed to verify Binance C2C transaction',
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR,
          });
        }
        break;

      case 'binance_trc20':
        // Verify Binance TRC20 transaction
        if (!data.verificationData.txHash) {
          throw new AppError({
            message: 'Transaction hash is required for Binance TRC20 verification',
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD,
          });
        }

        try {
          // Call Binance API to verify the transaction
          const result: any = await BinanceService.verifyTrc20Deposit(
            apiKey,
            apiSecret,
            data.verificationData.txHash,
            transaction.amount,
            transaction.currency === 'USDT' ? 'USDT' : 'USDC'
          );

          verified = result.verified;
          verificationResult = result.deposit || { txHash: data.verificationData.txHash };
        } catch (error) {
          console.error('Binance TRC20 verification error:', error);
          throw new AppError({
            message: 'Failed to verify Binance TRC20 transaction',
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR,
          });
        }
        break;

      case 'crypto_transfer':
        // Verify blockchain transaction
        if (!data.verificationData.txHash || !data.verificationData.network) {
          throw new AppError({
            message: 'Transaction hash and network are required for crypto transfer verification',
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD,
          });
        }

        // In a real implementation, this would verify the transaction on the blockchain
        // For now, we'll simulate a successful verification
        verified = true;
        verificationResult = {
          txHash: data.verificationData.txHash,
          network: data.verificationData.network,
          verified: true,
          timestamp: new Date().toISOString(),
        };
        break;

      default:
        throw new AppError(`Unsupported verification method type: ${verificationMethod.type}`, 400);
    }

    // Update transaction status based on verification result
    if (verified) {
      await prisma.transaction.update({
        where: { id: transaction.id },
        data: {
          status: 'COMPLETED',
          completedAt: new Date(),
          metadata: {
            ...((transaction.metadata as any) || {}),
            verificationResult,
            verifiedAt: new Date().toISOString(),
            verificationMethod: verificationMethod.type,
          },
        },
      });
    } else {
      await prisma.transaction.update({
        where: { id: transaction.id },
        data: {
          status: 'FAILED',
          metadata: {
            ...((transaction.metadata as any) || {}),
            verificationResult,
            failedAt: new Date().toISOString(),
            verificationMethod: verificationMethod.type,
            reason: 'Verification failed',
          },
        },
      });
    }

    // Get updated transaction
    const updatedTransaction: any = await prisma.transaction.findUnique({
      where: { id: transaction.id },
      include: {
        paymentMethod: true,
        merchant: {
          select: {
            id: true,
            name: true,
            email: true,
            businessName: true,
          },
        },
      },
    });

    return {
      verified,
      transaction: updatedTransaction,
    };
  },
};
