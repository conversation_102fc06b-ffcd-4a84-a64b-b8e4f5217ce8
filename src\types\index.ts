/**
 * Core Type Definitions
 *
 * This file contains core type definitions used throughout the application.
 */

// Import Express types for extensions
import { Request, Response, NextFunction } from 'express';
import { PrismaClient } from '@prisma/client';

// Common Types
export type UUID = string;
export type Timestamp = Date;
export type JSONValue = string | number | boolean | null | JSONObject | JSONArray;
export interface JSONObject {
  [key: string]: JSONValue;
}
export type JSONArray = JSONValue[];

// User Types
export enum UserRole {
  ADMIN = 'ADMIN',
  MERCHANT = 'MERCHANT',
  CUSTOMER = 'CUSTOMER',
  SUPPORT = 'SUPPORT',
}

export interface User {
  id: UUID;
  email: string;
  hashedPassword?: string;
  firstName?: string;
  lastName?: string;
  role: UserRole;
  isActive: boolean;
  isVerified: boolean;
  lastLoginAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  merchants?: Merchant[];
}

// Merchant Types
export enum MerchantStatus {
  PENDING = 'PENDING',
  ACTIVE = 'ACTIVE',
  SUSPENDED = 'SUSPENDED',
  INACTIVE = 'INACTIVE',
}

export interface Merchant {
  id: UUID;
  userId?: UUID;
  name: string;
  businessName?: string;
  email: string;
  hashedPassword?: string;
  contactPhone?: string;
  phoneNumber?: string;
  merchantLocation?: string;
  country?: string;
  governorate?: string;
  status: MerchantStatus;
  isActive: boolean;
  isVerified: boolean;
  apiKey?: string;
  apiSecret?: string;
  callbackUrl?: string;
  webhookUrl?: string;
  walletAddresses?: Record<string, string>;
  businessAddress?: string;
  domain?: string;
  currentPlanId?: UUID;
  planExpiryDate?: Date;
  totalRevenue: number;
  subscriptionStatus?: string;
  createdAt: Date;
  updatedAt: Date;
  user?: User;
  paymentMethods?: PaymentMethod[];
  transactions?: Transaction[];
}

// Payment Method Types
export enum PaymentMethodType {
  CRYPTO = 'CRYPTO',
  CARD = 'CARD',
  BANK_TRANSFER = 'BANK_TRANSFER',
  MOBILE_MONEY = 'MOBILE_MONEY',
  WALLET = 'WALLET',
  OTHER = 'OTHER',
}

export interface PaymentMethod {
  id: UUID;
  name: string;
  type: PaymentMethodType;
  isActive: boolean;
  displayName?: string;
  description?: string;
  config?: Record<string, any>;
  icon?: string;
  displayOrder: number;
  merchantId: UUID;
  network?: string;
  address?: string;
  qrCodeUrl?: string;
  note?: string;
  apiKey?: string;
  secretKey?: string;
  createdAt: Date;
  updatedAt: Date;
  merchant?: Merchant;
  transactions?: Transaction[];
  verificationMethods?: VerificationMethod[];
}

// Transaction Types
export enum TransactionStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  EXPIRED = 'EXPIRED',
  REFUNDED = 'REFUNDED',
  CANCELLED = 'CANCELLED',
}

export interface Transaction {
  id: UUID;
  reference?: string;
  amount: number;
  currency: string;
  status: TransactionStatus;
  method?: string;
  methodDetails?: Record<string, any>;
  merchantId: UUID;
  paymentMethodId?: UUID;
  paymentPageId?: UUID;
  successUrl?: string;
  cancelUrl?: string;
  callbackUrl?: string;
  verificationData?: Record<string, any>;
  verificationStatus?: string;
  verificationDetails?: Record<string, any>;
  verificationMethod?: string;
  customerEmail?: string;
  customerName?: string;
  customerPhone?: string;
  txHash?: string;
  network?: string;
  senderAddress?: string;
  confirmations?: number;
  metadata?: Record<string, any>;
  expiresAt?: Date;
  completedAt?: Date;
  failedAt?: Date;
  refundedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  merchant?: Merchant;
  paymentMethod?: PaymentMethod;
}

// Verification Types
export enum VerificationStatus {
  PENDING = 'PENDING',
  IN_PROGRESS = 'IN_PROGRESS',
  VERIFIED = 'VERIFIED',
  FAILED = 'FAILED',
  EXPIRED = 'EXPIRED',
}

export enum VerificationMethodType {
  BLOCKCHAIN = 'BLOCKCHAIN',
  API = 'API',
  MANUAL = 'MANUAL',
  BINANCE = 'BINANCE',
  OTHER = 'OTHER',
}

export interface VerificationMethod {
  id: UUID;
  name: string;
  type: VerificationMethodType;
  isActive: boolean;
  config?: Record<string, any>;
  paymentMethodId?: UUID;
  createdAt: Date;
  updatedAt: Date;
  paymentMethod?: PaymentMethod;
}

export interface Verification {
  id: UUID;
  transactionId: UUID;
  methodId: UUID;
  status: VerificationStatus;
  details?: Record<string, any>;
  verifiedAt?: Date;
  failedAt?: Date;
  expiresAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  transaction?: Transaction;
  method?: VerificationMethod;
}

// Alert Types
export enum AlertType {
  SECURITY = 'SECURITY',
  PAYMENT = 'PAYMENT',
  SYSTEM = 'SYSTEM',
  FRAUD = 'FRAUD',
  VERIFICATION = 'VERIFICATION',
  OTHER = 'OTHER',
}

export enum AlertSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL',
}

export enum AlertStatus {
  OPEN = 'OPEN',
  IN_PROGRESS = 'IN_PROGRESS',
  RESOLVED = 'RESOLVED',
  CLOSED = 'CLOSED',
  IGNORED = 'IGNORED',
}

export interface Alert {
  id: UUID;
  type: AlertType;
  severity: AlertSeverity;
  status: AlertStatus;
  title: string;
  message: string;
  details?: Record<string, any>;
  source?: string;
  merchantId?: UUID;
  userId?: UUID;
  transactionId?: UUID;
  resolvedById?: UUID;
  resolvedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  merchant?: Merchant;
  user?: User;
  transaction?: Transaction;
  resolvedBy?: User;
}

// Database Types
export interface TableSchema {
  name: string;
  columns: ColumnSchema[];
}

export interface ColumnSchema {
  name: string;
  type: string;
  nullable?: boolean;
  defaultValue?: string;
}

export interface MigrationRecord {
  id: number;
  name: string;
  applied_at: Date;
  status?: string;
  error?: string;
}

export enum MigrationStatus {
  PENDING = 'PENDING',
  APPLIED = 'APPLIED',
  FAILED = 'FAILED',
}

export interface VerificationResult {
  success: boolean;
  missingTables: string[];
  missingColumns: { table: string; columns: string[] }[];
  missingForeignKeys: {
    table: string;
    column: string;
    referencedTable: string;
    referencedColumn: string;
  }[];
  message?: string;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  statusCode?: number;
}

// Pagination Types
export interface PaginationParams {
  page?: number;
  limit?: number;
  offset?: number;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

// Express Request Extensions
export interface AuthenticatedUser {
  id: UUID;
  email: string;
  role: UserRole;
  merchantId?: UUID;
}

/**
 * Extended Express Request with user information
 */
export interface AuthenticatedRequest extends Request {
  user?: AuthenticatedUser;
  apiVersion?: string;
  requestId?: string;
  startTime?: number;
}

/**
 * Extended Express Response with additional methods
 */
export interface ExtendedResponse extends Response {
  success: (data?: unknown, message?: string) => Response;
  error: (message: string, statusCode?: number, details?: any) => Response;
  paginated: (data: any[], pagination: PaginationInfo) => Response;
}

/**
 * Pagination information
 */
export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

/**
 * Prisma transaction client type
 */
export type PrismaTransactionClient = Parameters<Parameters<PrismaClient['$transaction']>[0]>[0];

/**
 * Express middleware function
 */
export type MiddlewareFunction = (
  req: Request,
  res: Response,
  next: NextFunction
) => void | Promise<void>;

/**
 * Express error middleware function
 */
export type ErrorMiddlewareFunction = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
) => void | Promise<void>;

// Export all types
export default {
  UserRole,
  MerchantStatus,
  PaymentMethodType,
  TransactionStatus,
  VerificationStatus,
  VerificationMethodType,
  AlertType,
  AlertSeverity,
  AlertStatus,
  MigrationStatus,
};
