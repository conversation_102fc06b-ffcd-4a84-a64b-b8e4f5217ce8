"use strict";
// jscpd:ignore-file
/**
 * Enhanced Verification Controller
 *
 * Handles verification operations using the new verification service.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getVerificationMethodByType = exports.getAllVerificationMethods = exports.getVerificationMethodsForPaymentMethod = exports.verifyPayment = void 0;
const client_1 = require("@prisma/client");
const VerificationService_1 = require("../services/verification/VerificationService");
const VerificationStrategyFactory_1 = require("../factories/verification/VerificationStrategyFactory");
const VerificationPluginManager_1 = require("../plugins/verification/VerificationPluginManager");
const BinanceVerificationPlugin_1 = __importDefault(require("../plugins/verification/BinanceVerificationPlugin"));
const LoggingPreProcessor_1 = require("../services/verification/processors/LoggingPreProcessor");
const NotificationPostProcessor_1 = require("../services/verification/processors/NotificationPostProcessor");
const logger_1 = require("../lib/logger");
const error_middleware_1 = require("../middlewares/error.middleware");
const prisma = new client_1.PrismaClient();
const verificationService = new VerificationService_1.VerificationService(prisma);
const verificationPluginManager = VerificationPluginManager_1.VerificationPluginManager.getInstance(verificationService);
// Register pre-processors
verificationService.registerPreProcessor(new LoggingPreProcessor_1.LoggingPreProcessor());
// Register post-processors
verificationService.registerPostProcessor(new NotificationPostProcessor_1.NotificationPostProcessor(prisma));
// Register plugins
verificationPluginManager.registerPlugin(BinanceVerificationPlugin_1.default);
/**
 * Verify a payment
 */
const verifyPayment = async (req, res, next) => {
    try {
        const { transactionId, merchantId, paymentMethodId, paymentMethodType, amount, currency, verificationData } = req.body;
        // Validate required fields
        if (!transactionId || !merchantId || !paymentMethodId || !paymentMethodType || !amount || !currency || !verificationData) {
            return next(new error_middleware_1.AppError({
                message: "Missing required fields",
                type: ErrorType.VALIDATION,
                code: ErrorCode.MISSING_REQUIRED_FIELD
            }));
        }
        // Validate verification data
        if (!verificationData.verificationMethod) {
            return next(new error_middleware_1.AppError({
                message: "Verification method is required",
                type: ErrorType.VALIDATION,
                code: ErrorCode.MISSING_REQUIRED_FIELD
            }));
        }
        // Check if the verification method exists
        const strategyFactory = VerificationStrategyFactory_1.VerificationStrategyFactory.getInstance();
        if (!strategyFactory.hasStrategy(verificationData.verificationMethod)) {
            return next(new error_middleware_1.AppError(`Unsupported verification method: ${verificationData.verificationMethod}`, 400));
        }
        // Verify the payment
        const result = await verificationService.verify({
            verificationMethod: verificationData.verificationMethod,
            transactionId,
            merchantId,
            paymentMethodId,
            paymentMethodType,
            amount,
            currency,
            verificationData
        });
        // Return the result
        res.json({
            verified: result.success,
            status: result.success ? "success" : "failed",
            message: result.message,
            details: result.details,
            timestamp: result.timestamp
        });
    }
    catch (error) {
        logger_1.logger.error("Verification error:", error);
        next(new error_middleware_1.AppError({
            message: "Verification failed",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};
exports.verifyPayment = verifyPayment;
/**
 * Get verification methods for a payment method
 */
const getVerificationMethodsForPaymentMethod = async (req, res, next) => {
    try {
        const { paymentMethodType } = req.params;
        if (!paymentMethodType) {
            return next(new error_middleware_1.AppError({
                message: "Payment method type is required",
                type: ErrorType.VALIDATION,
                code: ErrorCode.MISSING_REQUIRED_FIELD
            }));
        }
        // Get verification methods for the payment method
        const verificationMethods = verificationService.getVerificationMethodsForPaymentMethod(paymentMethodType);
        // Map to a simpler format
        const methods = verificationMethods.map(method => ({
            type: method.getType(),
            displayName: method.getDisplayName(),
            description: method.getDescription(),
            requiredFields: method.getRequiredFields(),
            enabled: method.isEnabled()
        }));
        res.json({
            paymentMethodType,
            verificationMethods: methods
        });
    }
    catch (error) {
        logger_1.logger.error("Error getting verification methods:", error);
        next(new error_middleware_1.AppError({
            message: "Failed to get verification methods",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};
exports.getVerificationMethodsForPaymentMethod = getVerificationMethodsForPaymentMethod;
/**
 * Get all verification methods
 */
const getAllVerificationMethods = async (req, res, next) => {
    try {
        // Get all verification methods
        const verificationMethods = verificationService.getAllVerificationMethods();
        // Map to a simpler format
        const methods = verificationMethods.map(method => ({
            type: method.getType(),
            displayName: method.getDisplayName(),
            description: method.getDescription(),
            supportedPaymentMethods: method.getSupportedPaymentMethods(),
            requiredFields: method.getRequiredFields(),
            enabled: method.isEnabled()
        }));
        res.json({
            verificationMethods: methods
        });
    }
    catch (error) {
        logger_1.logger.error("Error getting all verification methods:", error);
        next(new error_middleware_1.AppError({
            message: "Failed to get verification methods",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};
exports.getAllVerificationMethods = getAllVerificationMethods;
/**
 * Get verification method by type
 */
const getVerificationMethodByType = async (req, res, next) => {
    try {
        const { type } = req.params;
        if (!type) {
            return next(new error_middleware_1.AppError({
                message: "Verification method type is required",
                type: ErrorType.VALIDATION,
                code: ErrorCode.MISSING_REQUIRED_FIELD
            }));
        }
        // Get the verification method
        const strategyFactory = VerificationStrategyFactory_1.VerificationStrategyFactory.getInstance();
        if (!strategyFactory.hasStrategy(type)) {
            return next(new error_middleware_1.AppError(`Verification method not found: ${type}`, 404));
        }
        const method = strategyFactory.getStrategy(type);
        // Map to a simpler format
        const methodData = {
            type: method.getType(),
            displayName: method.getDisplayName(),
            description: method.getDescription(),
            supportedPaymentMethods: method.getSupportedPaymentMethods(),
            requiredFields: method.getRequiredFields(),
            enabled: method.isEnabled(),
            configuration: method.getConfiguration()
        };
        res.json({
            verificationMethod: methodData
        });
    }
    catch (error) {
        logger_1.logger.error("Error getting verification method:", error);
        next(new error_middleware_1.AppError({
            message: "Failed to get verification method",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};
exports.getVerificationMethodByType = getVerificationMethodByType;
exports.default = {
    verifyPayment: exports.verifyPayment,
    getVerificationMethodsForPaymentMethod: exports.getVerificationMethodsForPaymentMethod,
    getAllVerificationMethods: exports.getAllVerificationMethods,
    getVerificationMethodByType: exports.getVerificationMethodByType
};
//# sourceMappingURL=enhanced-verification.controller.js.map