// jscpd:ignore-file
/**
 * Load Testing Script
 *
 * This script performs load testing on the API to verify system performance under high load.
 * It tests:
 * - Response time under load
 * - Error rate under load
 * - Database connection stability
 * - Memory usage under load
 *
 * Note: This is a simple load testing script. For more comprehensive load testing,
 * consider using dedicated tools like Artillery, JMeter, or k6.
 */

import axios from 'axios';
import { performance } from 'perf_hooks';
import { getSystemHealth } from '../utils/health-monitor';
import { getSystemHealth } from '../utils/health-monitor';

// Configuration
const BASE_URL: string = 'http://localhost:3002';
const NUM_REQUESTS = 1000;
const CONCURRENCY: number = 50;
const ENDPOINTS = ['/health', '/api/health', '/api/health/liveness', '/api/health/readiness'];

// Results storage
interface RequestResult {
  endpoint: string;
  statusCode: number;
  responseTime: number;
  error?: string;
}

// Main load test function
async function runLoadTest(): any {
  console.log(`Starting load test with ${NUM_REQUESTS} requests (${CONCURRENCY} concurrent)...`);

  const results: RequestResult[] = [];
  const startTime: any = performance.now();

  // Get initial system health
  const initialHealth: any = await getSystemHealth();
  console.log('Initial system health:', initialHealth.status);
  console.log('Initial CPU usage:', initialHealth.resources.cpu.toFixed(2) + '%');
  console.log('Initial memory usage:', initialHealth.resources.memory.usedPercent.toFixed(2) + '%');

  // Create batches of requests
  const batches: any[] = [];
  const batchSize = Math.ceil(NUM_REQUESTS / CONCURRENCY);

  for (let i: number = 0; i < CONCURRENCY; i++) {
    batches.push(runBatch(i, batchSize, results));
  }

  // Run all batches concurrently
  await Promise.all(batches);

  const endTime: any = performance.now();
  const totalTime: any = endTime - startTime;

  // Get final system health
  const finalHealth: any = await getSystemHealth();
  console.log('Final system health:', finalHealth.status);
  console.log('Final CPU usage:', finalHealth.resources.cpu.toFixed(2) + '%');
  console.log('Final memory usage:', finalHealth.resources.memory.usedPercent.toFixed(2) + '%');

  // Calculate statistics
  const successCount: any = results.filter(
    (r) => r.statusCode >= 200 && r.statusCode < 300
  ).length;
  const errorCount: any = results.filter((r) => r.statusCode >= 400 || r.error).length;
  const avgResponseTime: any =
    results.reduce((sum, r) => sum + r.responseTime, 0) / results.length;
  const maxResponseTime: any = Math.max(...results.map((r) => r.responseTime));
  const minResponseTime: any = Math.min(...results.map((r) => r.responseTime));

  // Calculate percentiles
  const sortedResponseTimes: any = results.map((r) => r.responseTime).sort((a, b) => a - b);
  const p50: any = sortedResponseTimes[Math.floor(sortedResponseTimes.length * 0.5)];
  const p90: any = sortedResponseTimes[Math.floor(sortedResponseTimes.length * 0.9)];
  const p95: any = sortedResponseTimes[Math.floor(sortedResponseTimes.length * 0.95)];
  const p99: any = sortedResponseTimes[Math.floor(sortedResponseTimes.length * 0.99)];

  // Print results
  console.log('\nLoad Test Results:');
  console.log('------------------');
  console.log(`Total Requests: ${results.length}`);
  console.log(
    `Successful Requests: ${successCount} (${((successCount / results.length) * 100).toFixed(2)}%)`
  );
  console.log(
    `Failed Requests: ${errorCount} (${((errorCount / results.length) * 100).toFixed(2)}%)`
  );
  console.log(`Total Time: ${totalTime.toFixed(2)}ms`);
  console.log(`Requests Per Second: ${(results.length / (totalTime / 1000)).toFixed(2)}`);
  console.log(`Average Response Time: ${avgResponseTime.toFixed(2)}ms`);
  console.log(`Min Response Time: ${minResponseTime.toFixed(2)}ms`);
  console.log(`Max Response Time: ${maxResponseTime.toFixed(2)}ms`);
  console.log(`P50 Response Time: ${p50.toFixed(2)}ms`);
  console.log(`P90 Response Time: ${p90.toFixed(2)}ms`);
  console.log(`P95 Response Time: ${p95.toFixed(2)}ms`);
  console.log(`P99 Response Time: ${p99.toFixed(2)}ms`);

  // Print results by endpoint
  console.log('\nResults by Endpoint:');
  console.log('-------------------');

  for (const endpoint of ENDPOINTS) {
    const endpointResults: any = results.filter((r) => r.endpoint === endpoint);
    const endpointSuccessCount: any = endpointResults.filter(
      (r) => r.statusCode >= 200 && r.statusCode < 300
    ).length;
    const endpointAvgResponseTime: any =
      endpointResults.reduce((sum, r) => sum + r.responseTime, 0) / endpointResults.length;

    console.log(`Endpoint: ${endpoint}`);
    console.log(`  Requests: ${endpointResults.length}`);
    console.log(
      `  Success Rate: ${((endpointSuccessCount / endpointResults.length) * 100).toFixed(2)}%`
    );
    console.log(`  Avg Response Time: ${endpointAvgResponseTime.toFixed(2)}ms`);
  }

  // Check if the test passed
  const passed: any = errorCount / results.length < 0.05; // Less than 5% error rate

  if (passed) {
    console.log('\nLoad Test PASSED ✅');
  } else {
    console.log('\nLoad Test FAILED ❌');
    console.log('Error rate too high');
  }

  return passed;
}

// Run a batch of requests
async function runBatch(batchId: number, batchSize: number, results: RequestResult[]): any {
  for (let i: number = 0; i < batchSize; i++) {
    // Select a random endpoint
    const endpoint: any = ENDPOINTS[Math.floor(Math.random() * ENDPOINTS.length)];

    try {
      const startTime: any = performance.now();
      const response: any = await axios.get(`${BASE_URL}${endpoint}`);
      const endTime: any = performance.now();

      results.push({
        endpoint,
        statusCode: response.status,
        responseTime: endTime - startTime,
      });
    } catch (error) {
      results.push({
        endpoint,
        statusCode: error.response?.status || 0,
        responseTime: 0,
        error: (error as Error).message,
      });
    }

    // Add a small delay to prevent overwhelming the server
    await new Promise((resolve) => setTimeout(resolve, 10));
  }
}

// Run the load test if this script is executed directly
if (require.main === module) {
  runLoadTest()
    .then((passed: boolean) => {
      process.exit(passed ? 0 : 1);
    })
    .catch((error: any) => {
      console.error('Load test failed with error:', error);
      process.exit(1);
    });
}

export default runLoadTest;
