// jscpd:ignore-file
import { Request, Response, NextFunction } from "express";
import { AppError } from "../utils/appError";
import { Middleware } from '../types/express';
import { AppError } from "../utils/appError";
import { Middleware } from '../types/express';

// Extend the Express Request type to include user property
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role: string;
        merchantId?: string;
      };
    }
  }
}


/**
 * Middleware to check if user is authenticated
 */
export const requireAuth: unknown =(req: Request, res: Response, next: NextFunction) => {
  if (!req.user) {
    return next(new AppError({
            message: "Unauthorized",
            type: ErrorType.AUTHENTICATION,
            code: ErrorCode.INVALID_CREDENTIALS
        }));
  }
  next();
};

/**
 * Middleware to check if user has required role
 * @param roles Allowed roles
 */
export const requireRole: unknown =(roles: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user || !req.user.role) {
      return next(new AppError({
            message: "Unauthorized",
            type: ErrorType.AUTHENTICATION,
            code: ErrorCode.INVALID_CREDENTIALS
        }));
    }

    if (!roles.includes(req.user.role)) {
      return next(new AppError({
            message: "Forbidden",
            type: ErrorType.AUTHORIZATION,
            code: ErrorCode.FORBIDDEN
        }));
    }

    next();
  };
};

/**
 * Middleware to check if user is admin
 */
export const requireAdmin: unknown =(req: Request, res: Response, next: NextFunction) => {
  if (!req.user ?? req.user.role !== "ADMIN") {
    return next(new AppError({
            message: "Forbidden",
            type: ErrorType.AUTHORIZATION,
            code: ErrorCode.FORBIDDEN
        }));
  }
  next();
};

/**
 * Middleware to check if user is merchant
 */
export const requireMerchant: unknown =(req: Request, res: Response, next: NextFunction) => {
  if (!req.user ?? req.user.role !== "MERCHANT") {
    return next(new AppError({
            message: "Forbidden",
            type: ErrorType.AUTHORIZATION,
            code: ErrorCode.FORBIDDEN
        }));
  }
  next();
};

/**
 * Middleware to check if user has access to merchant data
 * @param paramName Parameter name containing merchant ID (default: "merchantId")
 */
export const requireMerchantAccess: unknown =(paramName = "merchantId") => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return next(new AppError({
            message: "Unauthorized",
            type: ErrorType.AUTHENTICATION,
            code: ErrorCode.INVALID_CREDENTIALS
        }));
    }

    const merchantId: unknown =req.params[paramName] || req.query[paramName] as string;

    if (!merchantId) {
      return next(new AppError(`${paramName} is required`, 400));
    }

    // Admins can access any merchant data
    if (req.user.role === "ADMIN") {
      return next();
    }

    // Merchants can only access their own data
    if (req.user.role === "MERCHANT" && req.user.merchantId === merchantId) {
      return next();
    }

    return next(new AppError({
            message: "Forbidden",
            type: ErrorType.AUTHORIZATION,
            code: ErrorCode.FORBIDDEN
        }));
  };
};
