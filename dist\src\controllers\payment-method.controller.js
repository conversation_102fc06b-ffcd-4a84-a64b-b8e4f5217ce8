"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentMethodController = void 0;
const BaseController_1 = require("../../core/BaseController");
const payment_method_service_1 = require("../../services/refactored/payment-method.service");
const ErrorFactory_1 = require("../../utils/errors/ErrorFactory");
/**
 * Payment method controller
 * This controller handles payment method-related operations
 */
class PaymentMethodController extends BaseController_1.BaseController {
    /**
     * Create a new payment method controller
     */
    constructor() {
        super();
        /**
         * Get all payment methods
         */
        this.getPaymentMethods = this.createHandler(async (req, res) => {
            // Check authorization
            const { userRole, merchantId } = this.checkAuthorization(req);
            // Parse query parameters
            const requestedMerchantId = req.query.merchantId;
            // Determine target merchant ID
            const targetMerchantId = this.determineTargetMerchantId(userRole, merchantId, requestedMerchantId);
            // Parse pagination parameters
            const { limit, offset } = this.parsePagination(req);
            // Get payment methods
            const result = await this.paymentMethodService.getPaymentMethods({
                merchantId: targetMerchantId,
                limit,
                offset
            });
            // Send paginated response
            return this.sendPaginatedSuccess(res, result.data, result.total, limit, offset);
        });
        /**
         * Get a payment method by ID
         */
        this.getPaymentMethod = this.createHandler(async (req, res) => {
            // Check authorization
            const { userRole, merchantId } = this.checkAuthorization(req);
            // Get payment method ID from params
            const { id } = req.params;
            // Get payment method
            const paymentMethod = await this.paymentMethodService.getPaymentMethodById(id);
            // Check if payment method exists
            if (!paymentMethod) {
                throw ErrorFactory_1.ErrorFactory.notFound('Payment method', id);
            }
            // Check if user has permission to view this payment method
            if (userRole !== 'ADMIN' && paymentMethod.merchantId !== merchantId) {
                throw ErrorFactory_1.ErrorFactory.authorization('You do not have permission to view this payment method');
            }
            // Send success response
            return this.sendSuccess(res, paymentMethod);
        });
        /**
         * Create a new payment method
         */
        this.createPaymentMethod = this.createHandler(async (req, res) => {
            // Check authorization
            const { userRole, userId, merchantId } = this.checkAuthorization(req);
            // Get request body
            const { name, type, config, isDefault, merchantId: requestedMerchantId } = req.body;
            // Validate required fields
            if (!name || !type) {
                throw ErrorFactory_1.ErrorFactory.validation('Name and type are required');
            }
            // Determine target merchant ID
            let targetMerchantId = merchantId;
            // Admins can create payment methods for any merchant
            if (userRole === 'ADMIN' && requestedMerchantId) {
                targetMerchantId = requestedMerchantId;
            }
            // Check if merchant ID exists
            if (!targetMerchantId) {
                throw ErrorFactory_1.ErrorFactory.validation('Merchant ID is required');
            }
            // Create payment method
            const paymentMethod = await this.paymentMethodService.createPaymentMethod({
                name,
                type,
                config: config || {},
                isDefault: isDefault || false,
                merchantId: targetMerchantId,
                createdBy: userId
            });
            // Send success response
            return this.sendSuccess(res, paymentMethod, 201);
        });
        /**
         * Update a payment method
         */
        this.updatePaymentMethod = this.createHandler(async (req, res) => {
            // Check authorization
            const { userRole, userId, merchantId } = this.checkAuthorization(req);
            // Get payment method ID from params
            const { id } = req.params;
            // Get payment method
            const paymentMethod = await this.paymentMethodService.getPaymentMethodById(id);
            // Check if payment method exists
            if (!paymentMethod) {
                throw ErrorFactory_1.ErrorFactory.notFound('Payment method', id);
            }
            // Check if user has permission to update this payment method
            if (userRole !== 'ADMIN' && paymentMethod.merchantId !== merchantId) {
                throw ErrorFactory_1.ErrorFactory.authorization('You do not have permission to update this payment method');
            }
            // Get request body
            const { name, type, config, isDefault } = req.body;
            // Prepare update data
            const updateData = {
                updatedBy: userId
            };
            if (name)
                updateData.name = name;
            if (type)
                updateData.type = type;
            if (config)
                updateData.config = config;
            if (isDefault !== undefined)
                updateData.isDefault = isDefault;
            // Update payment method
            const updatedPaymentMethod = await this.paymentMethodService.updatePaymentMethod(id, updateData);
            // Send success response
            return this.sendSuccess(res, updatedPaymentMethod);
        });
        /**
         * Delete a payment method
         */
        this.deletePaymentMethod = this.createHandler(async (req, res) => {
            // Check authorization
            const { userRole, merchantId } = this.checkAuthorization(req);
            // Get payment method ID from params
            const { id } = req.params;
            // Get payment method
            const paymentMethod = await this.paymentMethodService.getPaymentMethodById(id);
            // Check if payment method exists
            if (!paymentMethod) {
                throw ErrorFactory_1.ErrorFactory.notFound('Payment method', id);
            }
            // Check if user has permission to delete this payment method
            if (userRole !== 'ADMIN' && paymentMethod.merchantId !== merchantId) {
                throw ErrorFactory_1.ErrorFactory.authorization('You do not have permission to delete this payment method');
            }
            // Delete payment method
            await this.paymentMethodService.deletePaymentMethod(id);
            // Send success response
            return this.sendSuccess(res, { message: 'Payment method deleted successfully' });
        });
        /**
         * Set a payment method as default
         */
        this.setDefaultPaymentMethod = this.createHandler(async (req, res) => {
            // Check authorization
            const { userRole, userId, merchantId } = this.checkAuthorization(req);
            // Get payment method ID from params
            const { id } = req.params;
            // Get payment method
            const paymentMethod = await this.paymentMethodService.getPaymentMethodById(id);
            // Check if payment method exists
            if (!paymentMethod) {
                throw ErrorFactory_1.ErrorFactory.notFound('Payment method', id);
            }
            // Check if user has permission to update this payment method
            if (userRole !== 'ADMIN' && paymentMethod.merchantId !== merchantId) {
                throw ErrorFactory_1.ErrorFactory.authorization('You do not have permission to update this payment method');
            }
            // Set payment method as default
            await this.paymentMethodService.setDefaultPaymentMethod(id, paymentMethod.merchantId);
            // Send success response
            return this.sendSuccess(res, { message: 'Payment method set as default successfully' });
        });
        /**
         * Verify a payment method
         */
        this.verifyPaymentMethod = this.createHandler(async (req, res) => {
            // Check authorization
            const { userRole, merchantId } = this.checkAuthorization(req);
            // Get payment method ID from params
            const { id } = req.params;
            // Get payment method
            const paymentMethod = await this.paymentMethodService.getPaymentMethodById(id);
            // Check if payment method exists
            if (!paymentMethod) {
                throw ErrorFactory_1.ErrorFactory.notFound('Payment method', id);
            }
            // Check if user has permission to verify this payment method
            if (userRole !== 'ADMIN' && paymentMethod.merchantId !== merchantId) {
                throw ErrorFactory_1.ErrorFactory.authorization('You do not have permission to verify this payment method');
            }
            // Get request body
            const { verificationData } = req.body;
            // Verify payment method
            const verificationResult = await this.paymentMethodService.verifyPaymentMethod(id, verificationData);
            // Send success response
            return this.sendSuccess(res, verificationResult);
        });
        this.paymentMethodService = new payment_method_service_1.PaymentMethodService();
    }
}
exports.PaymentMethodController = PaymentMethodController;
//# sourceMappingURL=payment-method.controller.js.map