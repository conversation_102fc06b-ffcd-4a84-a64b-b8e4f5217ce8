import { PrismaClient } from '@prisma/client';
import fs from 'fs';
import path from 'path';
import { Parse<PERSON> } from 'json2csv';
import PDFDocument from 'pdfkit';
import ExcelJS from 'exceljs';
import dayjs from 'dayjs';
import { v4 as uuidv4 } from 'uuid';
import nodemailer from 'nodemailer';
import cron from 'node-cron';
import { ReportOptimizationService } from './report-optimization.service';

const prisma = new PrismaClient();

export class AdvancedReportService {
  private reportsDir: string;
  private scheduledTasks: Map<string, cron.ScheduledTask>;
  private optimizationService: ReportOptimizationService;

  constructor() {
    this.reportsDir = path.join(__dirname, '../../reports');
    this.scheduledTasks = new Map();
    this.optimizationService = new ReportOptimizationService();

    // Ensure reports directory exists
    if (!fs.existsSync(this.reportsDir)) {
      fs.mkdirSync(this.reportsDir, { recursive: true });
    }
  }

  /**
   * Initialize scheduled reports from the database
   */
  private async initializeScheduledReports(): Promise<void> {
    try {
      const scheduledReports = await prisma.scheduledReport.findMany({
        where: { isActive: true },
      });

      for (const report of scheduledReports) {
        this.scheduleReport(report);
      }

      console.log(`Initialized ${scheduledReports.length} scheduled reports`);
    } catch (error) {
      console.error('Error initializing scheduled reports:', error);
    }
  }

  /**
   * Schedule a report to run based on its cron expression
   */
  private scheduleReport(report: unknown): void {
    // Cancel existing task if it exists
    if (this.scheduledTasks.has(report.id)) {
      this.scheduledTasks.get(report.id)?.stop();
    }

    // Schedule new task
    const task = cron.schedule(report.cronExpression, async () => {
      try {
        await this.runScheduledReport(report.id);
      } catch (error) {
        console.error(`Error running scheduled report ${report.id}:`, error);
      }
    });

    this.scheduledTasks.set(report.id, task);
  }

  /**
   * Run a scheduled report
   */
  public async runScheduledReport(scheduledReportId: string): Promise<unknown> {
    // Create a report run record
    const reportRun = await prisma.reportRun.create({
      data: {
        scheduledReportId,
        status: 'PROCESSING',
        startedAt: new Date(),
      },
    });

    try {
      // Get the scheduled report
      const scheduledReport = await prisma.scheduledReport.findUnique({
        where: { id: scheduledReportId },
        include: { template: true, createdBy: true },
      });

      if (!scheduledReport) {
        throw new Error('Scheduled report not found');
      }

      // Generate the report
      const result = await this.generateReport(
        scheduledReport.template.type,
        scheduledReport.parameters as unknown,
        scheduledReport.exportFormat,
        scheduledReport.name
      );

      // Update the report run
      await prisma.reportRun.update({
        where: { id: reportRun.id },
        data: {
          status: 'SUCCESS',
          completedAt: new Date(),
          filePath: result.filePath,
          fileType: scheduledReport.exportFormat,
          metadata: { rowCount: result.rowCount },
        },
      });

      // Update the scheduled report
      await prisma.scheduledReport.update({
        where: { id: scheduledReportId },
        data: { lastRunAt: new Date() },
      });

      // Send the report to recipients
      if (scheduledReport.recipients && scheduledReport.recipients.length > 0) {
        await this.sendReportByEmail(
          scheduledReport.recipients,
          scheduledReport.name,
          result.filePath,
          scheduledReport.exportFormat
        );
      }

      return result;
    } catch (error: Error) {
      // Update the report run with error
      await prisma.reportRun.update({
        where: { id: reportRun.id },
        data: {
          status: 'FAILED',
          completedAt: new Date(),
          error: error.message,
        },
      });

      throw error;
    }
  }

  /**
   * Generate a report based on type and parameters
   */
  public async generateReport(
    type: string,
    parameters: unknown,
    format: string,
    name?: string
  ): Promise<unknown> {
    // Generate filename
    const reportName = name || `${type.toLowerCase()}_report`;
    const timestamp = dayjs().format('YYYYMMDD_HHmmss');
    const filename = `${reportName}_${timestamp}`;
    const filePath = path.join(this.reportsDir, `${filename}.${format.toLowerCase()}`);

    // Estimate report size to determine if we should use streaming
    const sizeEstimate = await this.optimizationService.estimateReportSize(type, parameters);

    let rowCount: number;

    if (sizeEstimate.recommendStreaming) {
      // Use streaming for large reports
      console.log(
        `Using streaming for large report: ${sizeEstimate.recordCount} records, ${Math.round(
          sizeEstimate.estimatedSizeBytes / 1024 / 1024
        )}MB`
      );

      await this.optimizationService.generateLargeReport(type, parameters, format, filePath);
      rowCount = sizeEstimate.recordCount;
    } else {
      // Use traditional method for smaller reports
      const data = await this.getReportData(type, parameters);
      await this.exportReport(data, format, filename);
      rowCount = data.length;
    }

    // Create saved report record
    const savedReport = await prisma.savedReport.create({
      data: {
        name: reportName,
        type,
        format,
        filePath: filePath.replace(/\\/g, '/'), // Normalize path for storage
        fileSize: fs.existsSync(filePath) ? fs.statSync(filePath).size : 0,
        parameters: JSON.stringify(parameters),
        createdById: parameters.userId,
      },
    });

    return {
      id: savedReport.id,
      filePath,
      rowCount,
      format,
      fileSize: savedReport.fileSize,
    };
  }

  /**
   * Get report data based on type and parameters
   */
  private async getReportData(type: string, parameters: unknown): Promise<unknown[]> {
    switch (type.toUpperCase()) {
      case 'TRANSACTION':
        return this.getTransactionReportData(parameters);
      case 'CUSTOMER':
        return this.getCustomerReportData(parameters);
      case 'PAYMENT_METHOD':
        return this.getPaymentMethodReportData(parameters);
      case 'SUBSCRIPTION':
        return this.getSubscriptionReportData(parameters);
      default:
        throw new Error(`Unsupported report type: ${type}`);
    }
  }

  /**
   * Get transaction report data
   */
  private async getTransactionReportData(parameters: unknown): Promise<unknown[]> {
    const {
      startDate,
      endDate,
      merchantId,
      status,
      minAmount,
      maxAmount,
      currency,
      userId,
      userRole,
    } = parameters;

    // Build where clause
    const where: unknown = {};

    if (startDate || endDate) {
      where.createdAt = {};
      if (startDate) where.createdAt.gte = new Date(startDate);
      if (endDate) where.createdAt.lte = new Date(endDate);
    }

    if (merchantId) {
      where.merchantId = merchantId;
    } else if (userRole !== 'ADMIN') {
      // If not admin and no merchant ID specified, get user's merchant
      const merchant = await prisma.merchant.findFirst({
        where: { userId },
      });

      if (!merchant) {
        throw new Error('Merchant profile not found');
      }

      where.merchantId = merchant.id;
    }

    if (status) where.status = status;
    if (currency) where.currency = currency;

    if (minAmount || maxAmount) {
      where.amount = {};
      if (minAmount) where.amount.gte = parseFloat(minAmount);
      if (maxAmount) where.amount.lte = parseFloat(maxAmount);
    }

    // Get transactions
    const transactions = await prisma.transaction.findMany({
      where,
      include: {
        merchant: {
          select: {
            businessName: true,
            contactEmail: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Format transactions for report
    return transactions.map((transaction) => ({
      id: transaction.id,
      reference: transaction.reference,
      amount: transaction.amount,
      currency: transaction.currency,
      status: transaction.status,
      paymentMethod: transaction.paymentMethod,
      merchantName: transaction.merchant?.businessName ?? 'Unknown',
      merchantEmail: transaction.merchant?.contactEmail ?? 'Unknown',
      description: transaction.description ?? '',
      createdAt: dayjs(transaction.createdAt).format('YYYY-MM-DD HH:mm:ss'),
      updatedAt: dayjs(transaction.updatedAt).format('YYYY-MM-DD HH:mm:ss'),
    }));
  }

  /**
   * Get customer report data
   */
  private async getCustomerReportData(parameters: unknown): Promise<unknown[]> {
    const { startDate, endDate, merchantId, status, userId, userRole } = parameters;

    // Build where clause for merchant
    const merchantWhere: unknown = {};

    if (merchantId) {
      merchantWhere.id = merchantId;
    } else if (userRole !== 'ADMIN') {
      // If not admin and no merchant ID specified, get user's merchant
      const merchant = await prisma.merchant.findFirst({
        where: { userId },
      });

      if (!merchant) {
        throw new Error('Merchant profile not found');
      }

      merchantWhere.id = merchant.id;
    }

    // Get customers
    const customers = await prisma.customer.findMany({
      where: {
        merchantId: merchantWhere.id,
        ...(startDate && { createdAt: { gte: new Date(startDate) } }),
        ...(endDate && { createdAt: { lte: new Date(endDate) } }),
        ...(status && { status }),
      },
      include: {
        merchant: {
          select: {
            businessName: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Format customers for report
    return customers.map((customer) => ({
      id: customer.id,
      email: customer.email,
      firstName: customer.firstName ?? '',
      lastName: customer.lastName ?? '',
      phone: customer.phone ?? '',
      status: customer.status,
      merchantName: customer.merchant?.businessName ?? 'Unknown',
      createdAt: dayjs(customer.createdAt).format('YYYY-MM-DD HH:mm:ss'),
      updatedAt: dayjs(customer.updatedAt).format('YYYY-MM-DD HH:mm:ss'),
    }));
  }

  /**
   * Get payment method report data
   */
  private async getPaymentMethodReportData(parameters: unknown): Promise<unknown[]> {
    const { startDate, endDate, merchantId, type, userId, userRole } = parameters;

    // Build where clause for merchant
    const merchantWhere: unknown = {};

    if (merchantId) {
      merchantWhere.id = merchantId;
    } else if (userRole !== 'ADMIN') {
      // If not admin and no merchant ID specified, get user's merchant
      const merchant = await prisma.merchant.findFirst({
        where: { userId },
      });

      if (!merchant) {
        throw new Error('Merchant profile not found');
      }

      merchantWhere.id = merchant.id;
    }

    // Get payment methods
    const paymentMethods = await prisma.paymentMethod.findMany({
      where: {
        merchantId: merchantWhere.id,
        ...(startDate && { createdAt: { gte: new Date(startDate) } }),
        ...(endDate && { createdAt: { lte: new Date(endDate) } }),
        ...(type && { type }),
      },
      include: {
        merchant: {
          select: {
            businessName: true,
          },
        },
        customer: {
          select: {
            email: true,
            firstName: true,
            lastName: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Format payment methods for report
    return paymentMethods.map((paymentMethod) => ({
      id: paymentMethod.id,
      type: paymentMethod.type,
      last4: paymentMethod.last4 ?? '',
      expiryMonth: paymentMethod.expiryMonth ?? '',
      expiryYear: paymentMethod.expiryYear ?? '',
      isDefault: paymentMethod.isDefault,
      merchantName: paymentMethod.merchant?.businessName ?? 'Unknown',
      customerEmail: paymentMethod.customer?.email ?? 'Unknown',
      customerName:
        `${paymentMethod.customer?.firstName ?? ''} ${
          paymentMethod.customer?.lastName ?? ''
        }`.trim() || 'Unknown',
      createdAt: dayjs(paymentMethod.createdAt).format('YYYY-MM-DD HH:mm:ss'),
      updatedAt: dayjs(paymentMethod.updatedAt).format('YYYY-MM-DD HH:mm:ss'),
    }));
  }

  /**
   * Get subscription report data
   */
  private async getSubscriptionReportData(parameters: unknown): Promise<unknown[]> {
    const { startDate, endDate, merchantId, status, userId, userRole } = parameters;

    // Build where clause for merchant
    const merchantWhere: unknown = {};

    if (merchantId) {
      merchantWhere.id = merchantId;
    } else if (userRole !== 'ADMIN') {
      // If not admin and no merchant ID specified, get user's merchant
      const merchant = await prisma.merchant.findFirst({
        where: { userId },
      });

      if (!merchant) {
        throw new Error('Merchant profile not found');
      }

      merchantWhere.id = merchant.id;
    }

    // Get subscriptions
    const subscriptions = await prisma.subscription.findMany({
      where: {
        merchantId: merchantWhere.id,
        ...(startDate && { createdAt: { gte: new Date(startDate) } }),
        ...(endDate && { createdAt: { lte: new Date(endDate) } }),
        ...(status && { status }),
      },
      include: {
        merchant: {
          select: {
            businessName: true,
          },
        },
        customer: {
          select: {
            email: true,
            firstName: true,
            lastName: true,
          },
        },
        plan: {
          select: {
            name: true,
            amount: true,
            currency: true,
            interval: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Format subscriptions for report
    return subscriptions.map((subscription) => ({
      id: subscription.id,
      status: subscription.status,
      startDate: dayjs(subscription.startDate).format('YYYY-MM-DD'),
      endDate: subscription.endDate ? dayjs(subscription.endDate).format('YYYY-MM-DD') : 'N/A',
      nextBillingDate: subscription.nextBillingDate
        ? dayjs(subscription.nextBillingDate).format('YYYY-MM-DD')
        : 'N/A',
      planName: subscription.plan?.name ?? 'Unknown',
      amount: subscription.plan?.amount ?? 0,
      currency: subscription.plan?.currency ?? 'USD',
      interval: subscription.plan?.interval ?? 'month',
      merchantName: subscription.merchant?.businessName ?? 'Unknown',
      customerEmail: subscription.customer?.email ?? 'Unknown',
      customerName:
        `${subscription.customer?.firstName ?? ''} ${
          subscription.customer?.lastName ?? ''
        }`.trim() || 'Unknown',
      createdAt: dayjs(subscription.createdAt).format('YYYY-MM-DD HH:mm:ss'),
      updatedAt: dayjs(subscription.updatedAt).format('YYYY-MM-DD HH:mm:ss'),
    }));
  }

  /**
   * Export report data to the specified format
   */
  private async exportReport(data: unknown[], format: string, filename: string): Promise<string> {
    switch (format.toUpperCase()) {
      case 'CSV':
        return this.exportToCsv(data, filename);
      case 'PDF':
        return this.exportToPdf(data, filename);
      case 'EXCEL':
        return this.exportToExcel(data, filename);
      case 'JSON':
        return this.exportToJson(data, filename);
      default:
        throw new Error(`Unsupported export format: ${format}`);
    }
  }

  /**
   * Export data to CSV
   */
  private async exportToCsv(data: unknown[], filename: string): Promise<string> {
    const filePath = path.join(this.reportsDir, `${filename}.csv`);

    if (data.length === 0) {
      // Create empty CSV file
      fs.writeFileSync(filePath, '');
      return filePath;
    }

    // Get fields from first data item
    const fields = Object.keys(data[0]);

    // Create CSV parser
    const json2csvParser = new Parser({ fields });
    const csv = json2csvParser.parse(data);

    // Write to file
    fs.writeFileSync(filePath, csv);

    return filePath;
  }

  /**
   * Export data to PDF
   */
  private async exportToPdf(data: unknown[], filename: string): Promise<string> {
    const filePath = path.join(this.reportsDir, `${filename}.pdf`);

    // Create PDF document
    const doc = new PDFDocument({ margin: 30 });
    const stream = fs.createWriteStream(filePath);

    // Pipe PDF to file
    doc.pipe(stream);

    // Add title
    doc.fontSize(20).text(filename, { align: 'center' });
    doc.moveDown();

    // Add timestamp
    doc
      .fontSize(10)
      .text(`Generated: ${dayjs().format('YYYY-MM-DD HH:mm:ss')}`, { align: 'right' });
    doc.moveDown();

    if (data.length === 0) {
      doc.fontSize(12).text('No data available for this report.', { align: 'center' });
    } else {
      // Get fields from first data item
      const fields = Object.keys(data[0]);

      // Calculate column widths
      const columnWidth = (doc.page.width - 60) / fields.length;

      // Add header row
      doc.fontSize(10).font('Helvetica-Bold');
      fields.forEach((field, i) => {
        doc.text(field, 30 + i * columnWidth, doc.y, { width: columnWidth, align: 'left' });
      });
      doc.moveDown();

      // Add data rows
      doc.font('Helvetica');
      data.forEach((row) => {
        const y = doc.y;
        fields.forEach((field, i) => {
          doc.text(String(row[field] ?? ''), 30 + i * columnWidth, y, {
            width: columnWidth,
            align: 'left',
          });
        });
        doc.moveDown();

        // Add page break if needed
        if (doc.y > doc.page.height - 50) {
          doc.addPage();
        }
      });
    }

    // Finalize PDF
    doc.end();

    // Wait for file to be written
    return new Promise((resolve, reject) => {
      stream.on('finish', () => resolve(filePath));
      stream.on('error', reject);
    });
  }

  /**
   * Export data to Excel
   */
  private async exportToExcel(data: unknown[], filename: string): Promise<string> {
    const filePath = path.join(this.reportsDir, `${filename}.xlsx`);

    // Create workbook
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Report');

    if (data.length === 0) {
      // Add empty worksheet
      worksheet.addRow(['No data available for this report.']);
    } else {
      // Get fields from first data item
      const fields = Object.keys(data[0]);

      // Add header row
      worksheet.addRow(fields);

      // Style header row
      worksheet.getRow(1).font = { bold: true };

      // Add data rows
      data.forEach((row) => {
        worksheet.addRow(fields.map((field) => row[field]));
      });

      // Auto-fit columns
      worksheet.columns.forEach((column) => {
        column.width = 15;
      });
    }

    // Write to file
    await workbook.xlsx.writeFile(filePath);

    return filePath;
  }

  /**
   * Export data to JSON
   */
  private async exportToJson(data: unknown[], filename: string): Promise<string> {
    const filePath = path.join(this.reportsDir, `${filename}.json`);

    // Write to file
    fs.writeFileSync(filePath, JSON.stringify(data, null, 2));

    return filePath;
  }

  /**
   * Send report by email
   */
  private async sendReportByEmail(
    recipients: string[],
    reportName: string,
    filePath: string,
    format: string
  ): Promise<void> {
    // Create nodemailer transporter
    const transporter = nodemailer.createTransport({
      // Configure your email provider here
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT ?? '587'),
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASSWORD,
      },
    });

    // Send email
    await transporter.sendMail({
      from: process.env.EMAIL_FROM ?? '<EMAIL>',
      to: recipients.join(','),
      subject: `${reportName} Report`,
      text: `Please find attached the ${reportName} report.`,
      html: `
        <h1>${reportName} Report</h1>
        <p>Please find attached the ${reportName} report.</p>
        <p>Generated on: ${dayjs().format('YYYY-MM-DD HH:mm:ss')}</p>
      `,
      attachments: [
        {
          filename: path.basename(filePath),
          path: filePath,
        },
      ],
    });
  }

  /**
   * Create a report template
   */
  public async createReportTemplate(data: unknown): Promise<unknown> {
    return prisma.reportTemplate.create({
      data,
    });
  }

  /**
   * Update a report template
   */
  public async updateReportTemplate(id: string, data: unknown): Promise<unknown> {
    return prisma.reportTemplate.update({
      where: { id },
      data,
    });
  }

  /**
   * Delete a report template
   */
  public async deleteReportTemplate(id: string): Promise<unknown> {
    return prisma.reportTemplate.delete({
      where: { id },
    });
  }

  /**
   * Get report templates
   */
  public async getReportTemplates(userId: string, includeSystem: boolean = true): Promise<unknown[]> {
    const where: unknown = {
      OR: [{ createdById: userId }],
    };

    if (includeSystem) {
      where.OR.push({ isSystem: true });
    }

    return prisma.reportTemplate.findMany({
      where,
      orderBy: {
        name: 'asc',
      },
    });
  }

  /**
   * Get a report template by ID
   */
  public async getReportTemplateById(id: string): Promise<unknown> {
    return prisma.reportTemplate.findUnique({
      where: { id },
    });
  }

  /**
   * Create a scheduled report
   */
  public async createScheduledReport(data: unknown): Promise<unknown> {
    const scheduledReport = await prisma.scheduledReport.create({
      data,
    });

    // Schedule the report
    if (scheduledReport.isActive) {
      this.scheduleReport(scheduledReport);
    }

    return scheduledReport;
  }

  /**
   * Update a scheduled report
   */
  public async updateScheduledReport(id: string, data: unknown): Promise<unknown> {
    const scheduledReport = await prisma.scheduledReport.update({
      where: { id },
      data,
    });

    // Update the scheduled task
    if (scheduledReport.isActive) {
      this.scheduleReport(scheduledReport);
    } else {
      // Cancel the task if it exists
      if (this.scheduledTasks.has(id)) {
        this.scheduledTasks.get(id)?.stop();
        this.scheduledTasks.delete(id);
      }
    }

    return scheduledReport;
  }

  /**
   * Delete a scheduled report
   */
  public async deleteScheduledReport(id: string): Promise<unknown> {
    // Cancel the task if it exists
    if (this.scheduledTasks.has(id)) {
      this.scheduledTasks.get(id)?.stop();
      this.scheduledTasks.delete(id);
    }

    return prisma.scheduledReport.delete({
      where: { id },
    });
  }

  /**
   * Get scheduled reports
   */
  public async getScheduledReports(userId: string): Promise<unknown[]> {
    return prisma.scheduledReport.findMany({
      where: {
        createdById: userId,
      },
      include: {
        template: true,
      },
      orderBy: {
        name: 'asc',
      },
    });
  }

  /**
   * Get a scheduled report by ID
   */
  public async getScheduledReportById(id: string): Promise<unknown> {
    return prisma.scheduledReport.findUnique({
      where: { id },
      include: {
        template: true,
      },
    });
  }

  /**
   * Get saved reports
   */
  public async getSavedReports(userId: string): Promise<unknown[]> {
    return prisma.savedReport.findMany({
      where: {
        createdById: userId,
      },
      include: {
        template: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  /**
   * Get a saved report by ID
   */
  public async getSavedReportById(id: string): Promise<unknown> {
    return prisma.savedReport.findUnique({
      where: { id },
      include: {
        template: true,
      },
    });
  }

  /**
   * Delete a saved report
   */
  public async deleteSavedReport(id: string): Promise<unknown> {
    const savedReport = await prisma.savedReport.findUnique({
      where: { id },
    });

    if (savedReport && savedReport.filePath) {
      // Delete the file
      try {
        fs.unlinkSync(savedReport.filePath);
      } catch (error) {
        console.error(`Error deleting report file: ${savedReport.filePath}`, error);
      }
    }

    return prisma.savedReport.delete({
      where: { id },
    });
  }

  /**
   * Initialize scheduled reports on server start
   */
  public async initializeScheduledReports(): Promise<void> {
    try {
      console.log('Initializing scheduled reports...');

      // Get all active scheduled reports
      const activeReports = await prisma.scheduledReport.findMany({
        where: {
          isActive: true,
        },
        include: {
          template: true,
        },
      });

      console.log(`Found ${activeReports.length} active scheduled reports`);

      // Schedule each active report
      for (const report of activeReports) {
        try {
          this.scheduleReport(report);
          console.log(`Scheduled report: ${report.name} (${report.schedule})`);
        } catch (error) {
          console.error(`Error scheduling report ${report.name}:`, error);
        }
      }

      console.log('Scheduled reports initialization completed');
    } catch (error) {
      console.error('Error initializing scheduled reports:', error);
    }
  }
}
