// jscpd:ignore-file

import { Request, Response, NextFunction } from 'express';
import subscriptionService from "../services/subscription.service";

class SubscriptionPlansController {
    async getAllPlans(req: Request, res: Response) {
        try {
            const plans: unknown =await subscriptionService.getAllPlans();
      
            return res.status(200).json({
                status: "success",
                data: plans
            });
        } catch (error) {
            return res.status(500).json({
                status: "error",
                message: (error as Error).message ?? "Failed to retrieve subscription plans"
            });
        }
    }

    async getPlanById(req: Request, res: Response) {
        try {
            const { id } = req.params;
            const plan: unknown =await subscriptionService.getPlanById(id);

            if (!plan) {
                return res.status(404).json({
                    status: "error",
                    message: "Subscription plan not found"
                });
            }

            return res.status(200).json({
                status: "success",
                data: plan
            });
        } catch (error) {
            return res.status(500).json({
                status: "error",
                message: (error as Error).message ?? "Failed to retrieve subscription plan"
            });
        }
    }

    async createPlan(req: Request, res: Response) {
        try {
            const { name, duration, maxPayments, maxMethods, price, features } = req.body;
      
            // Basic validation
            if (!name || !duration || !price) {
                return res.status(400).json({
                    status: "error",
                    message: "Name, duration, and price are required"
                });
            }

            const newPlan: unknown =await subscriptionService.createPlan({
                name,
                duration,
                maxPayments: maxPayments ?? 100,
                maxMethods: maxMethods ?? 2,
                price,
                features: features ?? []
            });

            return res.status(201).json({
                status: "success",
                data: newPlan
            });
        } catch (error) {
            return res.status(500).json({
                status: "error",
                message: (error as Error).message ?? "Failed to create subscription plan"
            });
        }
    }

    async updatePlan(req: Request, res: Response) {
        try {
            const { id } = req.params;
            const updateData: unknown =req.body;

            // Prevent updating the ID
            delete updateData.id;

            const updatedPlan: unknown =await subscriptionService.updatePlan(id, updateData);

            if (!updatedPlan) {
                return res.status(404).json({
                    status: "error",
                    message: "Subscription plan not found"
                });
            }

            return res.status(200).json({
                status: "success",
                data: updatedPlan
            });
        } catch (error) {
            return res.status(500).json({
                status: "error",
                message: (error as Error).message ?? "Failed to update subscription plan"
            });
        }
    }

    async deletePlan(req: Request, res: Response) {
        try {
            const { id } = req.params;
            const success: unknown =await subscriptionService.deletePlan(id);

            if (!success) {
                return res.status(404).json({
                    status: "error",
                    message: "Subscription plan not found"
                });
            }

            return res.status(200).json({
                status: "success",
                message: "Subscription plan deleted successfully"
            });
        } catch (error) {
            return res.status(500).json({
                status: "error",
                message: (error as Error).message ?? "Failed to delete subscription plan"
            });
        }
    }
}

export default new SubscriptionPlansController();
