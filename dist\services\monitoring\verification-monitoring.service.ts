// jscpd:ignore-file
/**
 * Verification Monitoring Service
 * 
 * This service monitors the performance and health of the verification system.
 * It tracks metrics such as success rate, latency, and error distribution.
 */

import { PrismaClient } from "@prisma/client";
import { logger } from "../../utils/logger";
import { VerificationErrorType } from "../../utils/verification-error-handler";
import { logger } from "../../utils/logger";
import { VerificationErrorType } from "../../utils/verification-error-handler";

/**
 * Verification metric type
 */
export enum VerificationMetricType {
  /**
   * Verification attempt
   */
  ATTEMPT = "ATTEMPT",
  
  /**
   * Verification success
   */
  SUCCESS = "SUCCESS",
  
  /**
   * Verification failure
   */
  FAILURE = "FAILURE",
  
  /**
   * Verification latency
   */
  LATENCY = "LATENCY",
}

/**
 * Verification monitoring service
 */
export class VerificationMonitoringService {
    private prisma: PrismaClient;
    private metrics: Map<string, number>;
    private errorCounts: Map<string, number>;
    private methodCounts: Map<string, Map<string, number>>;
    private latencies: Map<string, number[]>;
  
    /**
   * Constructor
   */
    constructor() {
        this.prisma = new PrismaClient();
        this.metrics = new Map<string, number>();
        this.errorCounts = new Map<string, number>();
        this.methodCounts = new Map<string, Map<string, number>>();
        this.latencies = new Map<string, number[]>();
    
        // Initialize metrics
        this.resetMetrics();
    
        // Start periodic reporting
        this.startPeriodicReporting();
    }
  
    /**
   * Reset metrics
   */
    private resetMetrics(): void {
        this.metrics.set(VerificationMetricType.ATTEMPT, 0);
        this.metrics.set(VerificationMetricType.SUCCESS, 0);
        this.metrics.set(VerificationMetricType.FAILURE, 0);
        this.metrics.set(VerificationMetricType.LATENCY, 0);
        this.errorCounts.clear();
        this.methodCounts.clear();
        this.latencies.clear();
    }
  
    /**
   * Start periodic reporting
   */
    private startPeriodicReporting(): void {
    // Report metrics every 5 minutes
        setInterval(() => {
            this.reportMetrics();
        }, 5 * 60 * 1000);
    }
  
    /**
   * Report metrics
   */
    private reportMetrics(): void {
        try {
            // Calculate success rate
            const attempts: any =this.metrics.get(VerificationMetricType.ATTEMPT) || 0;
            const successes: any =this.metrics.get(VerificationMetricType.SUCCESS) || 0;
            const failures: any =this.metrics.get(VerificationMetricType.FAILURE) || 0;
      
            if (attempts === 0) {
                return; // No metrics to report
            }
      
            const successRate: any =(successes / attempts) * 100;
      
            // Calculate average latency
            let avgLatency: number =0;
            let totalLatencies: number =0;
      
            this.latencies.forEach((latencies, method) => {
                if (latencies.length > 0) {
                    const sum: any =latencies.reduce((a, b) => a + b, 0);
                    const avg: any =sum / latencies.length;
                    avgLatency += sum;
                    totalLatencies += latencies.length;
          
                    logger.info(`Average latency for ${method}: ${avg.toFixed(2)}ms`);
                }
            });
      
            if (totalLatencies > 0) {
                avgLatency = avgLatency / totalLatencies;
            }
      
            // Log overall metrics
            logger.info("Verification metrics report", {
                attempts,
                successes,
                failures,
                successRate: `${successRate.toFixed(2)}%`,
                avgLatency: `${avgLatency.toFixed(2)}ms`
            });
      
            // Log error distribution
            if (this.errorCounts.size > 0) {
                logger.info("Verification error distribution", {
                    errorCounts: Object.fromEntries(this.errorCounts)
                });
            }
      
            // Log method-specific metrics
            this.methodCounts.forEach((counts, method) => {
                const methodAttempts: any =counts.get(VerificationMetricType.ATTEMPT) || 0;
                const methodSuccesses: any =counts.get(VerificationMetricType.SUCCESS) || 0;
                const methodFailures: any =counts.get(VerificationMetricType.FAILURE) || 0;
        
                if (methodAttempts > 0) {
                    const methodSuccessRate: any =(methodSuccesses / methodAttempts) * 100;
          
                    logger.info(`Metrics for ${method}`, {
                        attempts: methodAttempts,
                        successes: methodSuccesses,
                        failures: methodFailures,
                        successRate: `${methodSuccessRate.toFixed(2)}%`
                    });
                }
            });
      
            // Store metrics in database
            this.storeMetricsInDatabase(attempts, successes, failures, avgLatency);
      
            // Reset metrics for next period
            this.resetMetrics();
        } catch (error) {
            logger.error("Error reporting verification metrics", {
                error: (error as Error).message || error
            });
        }
    }
  
    /**
   * Store metrics in database
   * @param attempts Number of verification attempts
   * @param successes Number of successful verifications
   * @param failures Number of failed verifications
   * @param avgLatency Average verification latency
   */
    private async storeMetricsInDatabase(
        attempts: number,
        successes: number,
        failures: number,
        avgLatency: number
    ): Promise<void> {
        try {
            await this.prisma.verificationMetric.create({
                data: {, timestamp: new Date(),
                    attempts,
                    successes,
                    failures,
                    successRate: (successes / attempts) * 100,
                    avgLatency,
                    errorDistribution: JSON.stringify(Object.fromEntries(this.errorCounts)),
                    methodDistribution: JSON.stringify(
                        Array.from(this.methodCounts.entries()).reduce((acc, [method, counts]) => {
                            acc[method] = Object.fromEntries(counts);
                            return acc;
                        }, {})
                    )
                }
            });
        } catch (error) {
            logger.error("Error storing verification metrics in database", {
                error: (error as Error).message || error
            });
        }
    }
  
    /**
   * Track verification attempt
   * @param method Verification method
   */
    trackAttempt(method: string): void {
        this.incrementMetric(VerificationMetricType.ATTEMPT);
        this.incrementMethodMetric(method, VerificationMetricType.ATTEMPT);
    }
  
    /**
   * Track verification success
   * @param method Verification method
   * @param latencyMs Verification latency in milliseconds
   */
    trackSuccess(method: string, latencyMs: number): void {
        this.incrementMetric(VerificationMetricType.SUCCESS);
        this.incrementMethodMetric(method, VerificationMetricType.SUCCESS);
        this.trackLatency(method, latencyMs);
    }
  
    /**
   * Track verification failure
   * @param method Verification method
   * @param errorType Error type
   * @param latencyMs Verification latency in milliseconds
   */
    trackFailure(method: string, errorType: VerificationErrorType, latencyMs: number): void {
        this.incrementMetric(VerificationMetricType.FAILURE);
        this.incrementMethodMetric(method, VerificationMetricType.FAILURE);
        this.incrementErrorCount(errorType);
        this.trackLatency(method, latencyMs);
    }
  
    /**
   * Track verification latency
   * @param method Verification method
   * @param latencyMs Verification latency in milliseconds
   */
    trackLatency(method: string, latencyMs: number): void {
        if (!this.latencies.has(method)) {
            this.latencies.set(method, []);
        }
    
        this.latencies.get(method).push(latencyMs);
        this.metrics.set(VerificationMetricType.LATENCY, this.metrics.get(VerificationMetricType.LATENCY) + latencyMs);
    }
  
    /**
   * Increment metric
   * @param metricType Metric type
   */
    private incrementMetric(metricType: VerificationMetricType): void {
        this.metrics.set(metricType, (this.metrics.get(metricType) || 0) + 1);
    }
  
    /**
   * Increment method-specific metric
   * @param method Verification method
   * @param metricType Metric type
   */
    private incrementMethodMetric(method: string, metricType: VerificationMetricType): void {
        if (!this.methodCounts.has(method)) {
            this.methodCounts.set(method, new Map<string, number>());
        }
    
        const methodMetrics: any =this.methodCounts.get(method);
        methodMetrics.set(metricType, (methodMetrics.get(metricType) || 0) + 1);
    }
  
    /**
   * Increment error count
   * @param errorType Error type
   */
    private incrementErrorCount(errorType: VerificationErrorType): void {
        this.errorCounts.set(errorType, (this.errorCounts.get(errorType) || 0) + 1);
    }
}
