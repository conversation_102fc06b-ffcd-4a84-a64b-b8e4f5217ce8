/**
 * Base Validator
 *
 * Common validation utilities shared across fraud detection validators.
 */
/**
 * Base validator with common validation methods
 */
export declare class BaseValidator {
    /**
     * Validate merchant ID parameter
     */
    validateMerchantId(merchantId: any): number;
    /**
     * Validate date range parameters
     */
    validateDateRange(startDate?: any, endDate?: any): {
        start: Date;
        end: Date;
    };
    /**
     * Validate pagination parameters
     */
    validatePaginationParams(query: any): {
        page: number;
        limit: number;
        sortBy?: string;
        sortOrder?: 'asc' | 'desc';
    };
    /**
     * Check if string is a valid UUID
     */
    protected isValidUUID(uuid: string): boolean;
    /**
     * Check if string is a valid IP address
     */
    protected isValidIPAddress(ip: string): boolean;
    /**
     * Check if string is a valid country code
     */
    protected isValidCountryCode(code: string): boolean;
    /**
     * Check if string is a valid IP range (CIDR notation)
     */
    protected isValidIPRange(range: string): boolean;
}
//# sourceMappingURL=BaseValidator.d.ts.map