"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createTransactionFromPaymentPage = exports.getMerchantPaymentPages = exports.deletePaymentPage = exports.updatePaymentPage = exports.createPaymentPage = exports.getPaymentPageBySlug = exports.getPaymentPageById = exports.getAllPaymentPages = void 0;
const payment_page_service_1 = require("../services/payment-page.service");
const error_middleware_1 = require("../middlewares/error.middleware");
const index_1 = require("../index");
// Get all payment pages
exports.getAllPaymentPages = (0, error_middleware_1.asyncHandler)(async (req, res) => {
    // In a real implementation, this would get all payment pages with pagination
    // For now, we'll get all payment pages for all merchants (admin only)
    const paymentPages = await index_1.prisma.paymentPage.findMany({
        orderBy: { createdAt: "desc" },
        include: { merchant: {
                select: { id: true,
                    name: true,
                    email: true,
                    businessName: true
                }
            }
        }
    });
    res.status(200).json(paymentPages);
});
// Get payment page by ID
exports.getPaymentPageById = (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const paymentPage = await payment_page_service_1.PaymentPageService.getPaymentPageById(id);
    res.status(200).json(paymentPage);
});
// Get payment page by slug
exports.getPaymentPageBySlug = (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const { slug, merchantId } = req.params;
    const paymentPage = await payment_page_service_1.PaymentPageService.getPaymentPageBySlug(slug, merchantId);
    res.status(200).json(paymentPage);
});
// Create a new payment page
exports.createPaymentPage = (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const { title, description, slug, amount, currency, merchantId, isActive, isTemplate, logoUrl, successUrl, cancelUrl, allowCustomAmount, collectCustomerInfo, redirectAutomatically, paymentMethodIds, expiryMinutes, metadata } = req.body;
    // Validate required fields
    if (!title || !slug || !amount || !currency || !merchantId || !paymentMethodIds || !Array.isArray(paymentMethodIds)) {
        throw new error_middleware_1.AppError({
            message: "Missing required fields",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }
    // Create payment page
    const paymentPage = await payment_page_service_1.PaymentPageService.createPaymentPage({
        title,
        description,
        slug,
        amount,
        currency,
        merchantId,
        isActive,
        isTemplate,
        logoUrl,
        successUrl,
        cancelUrl,
        allowCustomAmount,
        collectCustomerInfo,
        redirectAutomatically,
        paymentMethodIds,
        expiryMinutes,
        metadata
    });
    res.status(201).json(paymentPage);
});
// Update payment page
exports.updatePaymentPage = (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const { title, description, slug, amount, currency, isActive, isTemplate, logoUrl, successUrl, cancelUrl, allowCustomAmount, collectCustomerInfo, redirectAutomatically, paymentMethodIds, expiryMinutes, metadata } = req.body;
    // Update payment page
    const paymentPage = await payment_page_service_1.PaymentPageService.updatePaymentPage(id, {
        title,
        description,
        slug,
        amount,
        currency,
        isActive,
        isTemplate,
        logoUrl,
        successUrl,
        cancelUrl,
        allowCustomAmount,
        collectCustomerInfo,
        redirectAutomatically,
        paymentMethodIds,
        expiryMinutes,
        metadata
    });
    res.status(200).json(paymentPage);
});
// Delete payment page
exports.deletePaymentPage = (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const result = await payment_page_service_1.PaymentPageService.deletePaymentPage(id);
    res.status(200).json(result);
});
// Get merchant payment pages
exports.getMerchantPaymentPages = (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const { merchantId } = req.params;
    const paymentPages = await payment_page_service_1.PaymentPageService.getMerchantPaymentPages(merchantId);
    res.status(200).json(paymentPages);
});
// Create transaction from payment page
exports.createTransactionFromPaymentPage = (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const { amount, paymentMethodId, customerEmail, customerName, customerPhone, metadata } = req.body;
    // Validate required fields
    if (!paymentMethodId) {
        throw new error_middleware_1.AppError({
            message: "Payment method is required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }
    // Create transaction
    const transaction = await payment_page_service_1.PaymentPageService.createTransactionFromPaymentPage(id, {
        amount,
        paymentMethodId,
        customerEmail,
        customerName,
        customerPhone,
        metadata
    });
    res.status(201).json(transaction);
});
//# sourceMappingURL=payment-page.controller.js.map