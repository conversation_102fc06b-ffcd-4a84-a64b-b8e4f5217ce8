// jscpd:ignore-file
/**
 * WebSocket Monitoring Routes
 * 
 * These routes provide endpoints for monitoring WebSocket connections
 * and retrieving statistics and health information.
 */

import { Router } from "express";
import websocketMonitor from "../utils/websocket-monitor";
import { isAdmin } from "../middlewares/auth.middleware";
import { logger } from "../lib/logger";
import { isAdmin } from "../middlewares/auth.middleware";
import { logger } from "../lib/logger";

const router: any =Router();

/**
 * Get WebSocket statistics
 * @route GET /api/monitoring/websocket/stats
 * @access Admin
 */
router.get("/stats", isAdmin, (req, res) => {
    try {
        const stats: any =websocketMonitor.getStats();
    
        res.status(200).json({
            status: "success",
            data: {
                stats
            }
        });
    } catch (error) {
        logger.error("Error retrieving WebSocket stats:", error);
    
        res.status(500).json({
            status: "error",
            message: "Failed to retrieve WebSocket statistics",
            error: error instanceof Error ? (error as Error).message : String(error)
        });
    }
});

/**
 * Get active WebSocket connections
 * @route GET /api/monitoring/websocket/connections
 * @access Admin
 */
router.get("/connections", isAdmin, (req, res) => {
    try {
        const stats: any =websocketMonitor.getStats();
    
        res.status(200).json({
            status: "success",
            data: {, activeConnections: stats.activeConnections,
                totalConnections: stats.totalConnections,
                lastActivity: stats.lastActivity
            }
        });
    } catch (error) {
        logger.error("Error retrieving WebSocket connections:", error);
    
        res.status(500).json({
            status: "error",
            message: "Failed to retrieve WebSocket connections",
            error: error instanceof Error ? (error as Error).message : String(error)
        });
    }
});

/**
 * Get WebSocket connection history
 * @route GET /api/monitoring/websocket/history
 * @access Admin
 */
router.get("/history", isAdmin, (req, res) => {
    try {
        const stats: any =websocketMonitor.getStats();
    
        res.status(200).json({
            status: "success",
            data: {, connectionHistory: stats.connectionHistory,
                disconnectionHistory: stats.disconnectionHistory,
                errorHistory: stats.errorHistory,
                messageHistory: (stats as Error).messageHistory
            }
        });
    } catch (error) {
        logger.error("Error retrieving WebSocket history:", error);
    
        res.status(500).json({
            status: "error",
            message: "Failed to retrieve WebSocket history",
            error: error instanceof Error ? (error as Error).message : String(error)
        });
    }
});

export default router;
