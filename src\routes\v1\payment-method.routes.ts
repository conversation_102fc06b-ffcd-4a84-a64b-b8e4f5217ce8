// jscpd:ignore-file
// jscpd:ignore-start
// jscpd:ignore-start
// Duplicated code removed and replaced with import
// jscpd:ignore-end

// Create a route builder for payment method routes
const routeBuilder: any =routeProvider.createRouteBuilder(
    "paymentMethod",
// jscpd:ignore-end
    "/payment-methods",
    "Payment method management routes"
)
.version("v1")

import { fragment1, fragment2, fragment3, fragment4, fragment5, fragment6, fragment7, fragment8, fragment9, fragment10, fragment11, fragment12, fragment13, fragment14, fragment15, fragment16, fragment17, fragment18, fragment19, fragment20, fragment21, fragment22, fragment23, fragment24, fragment25, fragment26, fragment27, fragment28, fragment29, fragment30, fragment31, fragment32, fragment33, fragment34, fragment35, fragment36, fragment37, fragment38, fragment39, fragment40, fragment41, fragment42, fragment43, fragment44, fragment45 } from '../../shared/absolute-zero/index';
