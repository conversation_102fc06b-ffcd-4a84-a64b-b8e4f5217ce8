"use strict";
// jscpd:ignore-file
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const log_service_1 = __importDefault(require("../services/log.service"));
class LogController {
    async getAllLogs(req, res) {
        try {
            const { level, source } = req.query;
            let logs;
            if (level && ["info", "warning", "error"].includes(level)) {
                logs = await log_service_1.default.getLogsByLevel(level);
            }
            else if (source) {
                logs = await log_service_1.default.getLogsBySource(source);
            }
            else {
                logs = await log_service_1.default.getAllLogs();
            }
            return res.status(200).json({
                status: "success",
                data: logs
            });
        }
        catch (error) {
            return res.status(500).json({
                status: "error",
                message: error.message || "Failed to retrieve logs"
            });
        }
    }
    async createLog(req, res) {
        try {
            const { level, message, source, details } = req.body;
            // Basic validation
            if (!level || !message || !source) {
                return res.status(400).json({
                    status: "error",
                    message: "Level, message, and source are required"
                });
            }
            if (!["info", "warning", "error"].includes(level)) {
                return res.status(400).json({
                    status: "error",
                    message: "Level must be one of: info, warning, error"
                });
            }
            const newLog = await log_service_1.default.createLog({
                level: level,
                message,
                source,
                details
            });
            return res.status(201).json({
                status: "success",
                data: newLog
            });
        }
        catch (error) {
            return res.status(500).json({
                status: "error",
                message: error.message || "Failed to create log"
            });
        }
    }
}
exports.default = new LogController();
//# sourceMappingURL=log.controller.js.map