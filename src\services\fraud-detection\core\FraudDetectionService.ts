/**
 * Fraud Detection Service
 *
 * Main orchestrator for fraud detection and risk assessment.
 */

import { PrismaClient } from '@prisma/client';
import {
  TransactionContext,
  TransactionRiskAssessment,
  FraudDetectionConfig,
  RiskScore,
  RiskLevel,
  RiskFactorResult,
  IRiskDetector,
  IRiskRule,
  FraudDetectionError,
  FraudDetectionErrorCode,
} from './FraudDetectionTypes';
import { logger } from '../../../lib/logger';

/**
 * Main fraud detection service
 */
export class FraudDetectionService {
  private prisma: PrismaClient;
  private detectors: Map<string, IRiskDetector>;
  private rules: Map<string, IRiskRule>;
  private defaultConfig: FraudDetectionConfig;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
    this.detectors = new Map();
    this.rules = new Map();
    this.defaultConfig = this.getDefaultConfig();
  }

  /**
   * Register a risk detector
   */
  registerDetector(detector: IRiskDetector): any {
    this.detectors.set(detector.getName(), detector);
    logger.info(`Registered fraud detector: ${detector.getName()}`);
  }

  /**
   * Register a risk rule
   */
  registerRule(rule: IRiskRule): any {
    this.rules.set(rule.getName(), rule);
    logger.info(`Registered fraud rule: ${rule.getName()}`);
  }

  /**
   * Assess transaction risk
   */
  async assessTransactionRisk(context: TransactionContext): Promise<TransactionRiskAssessment> {
    const startTime = Date.now();

    try {
      // Validate context
      this.validateContext(context);

      // Get configuration
      const config = await this.getConfig(context.merchant.id);

      // Run risk detectors
      const factors = await this.runDetectors(context, config);

      // Calculate overall risk score
      const riskScore = this.calculateRiskScore(factors, config);

      // Evaluate rules
      const ruleResults = await this.evaluateRules(context, config);

      // Determine actions
      const { isFlagged, isBlocked } = this.determineActions(riskScore, ruleResults, config);

      // Generate reason and recommendation
      const reason = this.generateReason(factors, riskScore.level);
      const recommendedAction = this.generateRecommendedAction(riskScore.level, isBlocked);

      // Save assessment
      await this.saveAssessment(context.transaction.id, riskScore, isFlagged, isBlocked);

      const processingTime = Date.now() - startTime;

      logger.info(`Risk assessment completed for transaction ${context.transaction.id}`, {
        score: riskScore.score,
        level: riskScore.level,
        isFlagged,
        isBlocked,
        processingTime,
      });

      return {
        transactionId: context.transaction.id,
        riskScore,
        isFlagged,
        isBlocked,
        reason,
        recommendedAction,
        processingTime,
      };
    } catch (error: any) {
      const processingTime = Date.now() - startTime;

      logger.error('Error assessing transaction risk:', error);

      if (error instanceof FraudDetectionError) {
        throw error;
      }

      throw FraudDetectionError.assessmentFailed(
        `Failed to assess transaction risk: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`
      );
    }
  }

  /**
   * Get risk assessment by transaction ID
   */
  async getRiskAssessment(transactionId: string) {
    try {
      const assessment = await this.prisma.riskAssessment.findFirst({
        where: {
          transactionId: transactionId,
        },
      });

      if (!assessment) {
        throw FraudDetectionError.assessmentFailed('Risk assessment not found');
      }

      return {
        ...assessment,
        factors: JSON.parse(assessment.factors as string),
      };
    } catch (error: any) {
      logger.error('Error getting risk assessment:', error);
      throw FraudDetectionError.assessmentFailed('Failed to retrieve risk assessment');
    }
  }

  /**
   * Update risk assessment
   */
  async updateRiskAssessment(transactionId: string, updates: unknown) {
    try {
      // Find the assessment first to get its ID
      const existingAssessment = await this.prisma.riskAssessment.findFirst({
        where: { transactionId },
      });

      if (!existingAssessment) {
        throw FraudDetectionError.assessmentFailed('Risk assessment not found for update');
      }

      return await this.prisma.riskAssessment.update({
        where: { id: existingAssessment.id },
        data: updates,
      });
    } catch (error: any) {
      logger.error('Error updating risk assessment:', error);
      throw FraudDetectionError.assessmentFailed('Failed to update risk assessment');
    }
  }

  /**
   * Get fraud detection statistics
   */
  async getStatistics(merchantId?: string, dateFrom?: Date, dateTo?: Date) {
    try {
      const where: unknown = {};

      if (merchantId) {
        where.transaction = { merchantId };
      }

      if (dateFrom ?? dateTo) {
        where.createdAt = {};
        if (dateFrom) where.createdAt.gte = dateFrom;
        if (dateTo) where.createdAt.lte = dateTo;
      }

      const [totalAssessments, flaggedCount, blockedCount, averageScore] = await Promise.all([
        this.prisma.riskAssessment.count({ where }),
        this.prisma.riskAssessment.count({ where: { ...where, isFlagged: true } }),
        this.prisma.riskAssessment.count({ where: { ...where, isBlocked: true } }),
        this.prisma.riskAssessment.aggregate({
          where,
          _avg: { score: true },
        }),
      ]);

      return {
        totalAssessments,
        flaggedTransactions: flaggedCount,
        blockedTransactions: blockedCount,
        averageRiskScore: averageScore._avg.score ?? 0,
        flaggedPercentage: totalAssessments > 0 ? (flaggedCount / totalAssessments) * 100 : 0,
        blockedPercentage: totalAssessments > 0 ? (blockedCount / totalAssessments) * 100 : 0,
      };
    } catch (error: any) {
      logger.error('Error getting fraud detection statistics:', error);
      throw FraudDetectionError.assessmentFailed('Failed to get statistics');
    }
  }

  /**
   * Validate transaction context
   */
  private validateContext(context: TransactionContext): any {
    if (!context.transaction) {
      throw FraudDetectionError.invalidTransaction('Transaction is required');
    }

    if (!context.merchant) {
      throw FraudDetectionError.invalidTransaction('Merchant is required');
    }

    if (!context.ipAddress) {
      throw FraudDetectionError.invalidTransaction('IP address is required');
    }
  }

  /**
   * Get fraud detection configuration
   */
  private async getConfig(merchantId: string): Promise<FraudDetectionConfig> {
    try {
      const merchantConfig = await this.prisma.fraudDetectionConfig.findUnique({
        where: { merchantId: parseInt(merchantId) },
      });

      if (merchantConfig) {
        return {
          ...this.defaultConfig,
          ...merchantConfig,
          factorWeights: JSON.parse(merchantConfig.factorWeights as string),
          highRiskCountries: merchantConfig.highRiskCountries.split(','),
          highRiskIpRanges: merchantConfig.highRiskIpRanges.split(','),
        };
      }

      return this.defaultConfig;
    } catch (error: any) {
      logger.warn('Error getting merchant config, using default:', error);
      return this.defaultConfig;
    }
  }

  /**
   * Run all enabled risk detectors
   */
  private async runDetectors(
    context: TransactionContext,
    config: FraudDetectionConfig
  ): Promise<RiskFactorResult[]> {
    const results: RiskFactorResult[] = [];

    for (const [name, detector] of Array.from(this.detectors.entries())) {
      try {
        if (detector.isEnabled(config)) {
          const result = await detector.detect(context, config);
          results.push(result);
        }
      } catch (error: any) {
        logger.error(`Error running detector ${name}:`, error);
        // Continue with other detectors
      }
    }

    return results;
  }

  /**
   * Evaluate all risk rules
   */
  private async evaluateRules(
    context: TransactionContext,
    config: FraudDetectionConfig
  ): Promise<boolean[]> {
    const results: boolean[] = [];

    for (const [name, rule] of Array.from(this.rules.entries())) {
      try {
        const result = await rule.evaluate(context, config);
        results.push(result);
      } catch (error: any) {
        logger.error(`Error evaluating rule ${name}:`, error);
        // Continue with other rules
      }
    }

    return results;
  }

  /**
   * Calculate overall risk score
   */
  private calculateRiskScore(factors: RiskFactorResult[], config: FraudDetectionConfig): RiskScore {
    let totalScore = 0;
    let totalWeight = 0;
    let totalConfidence = 0;

    factors.forEach((factor) => {
      const weight = config.factorWeights[factor.factor] ?? 0;
      totalScore += factor.score * weight;
      totalWeight += weight;
      totalConfidence += factor.confidence;
    });

    const score = totalWeight > 0 ? Math.round(totalScore / totalWeight) : 0;
    const confidence = factors.length > 0 ? totalConfidence / factors.length : 0;
    const level = this.determineRiskLevel(score);

    return {
      score,
      level,
      factors,
      timestamp: new Date(),
      confidence,
    };
  }

  /**
   * Determine risk level based on score
   */
  private determineRiskLevel(score: number): RiskLevel {
    if (score >= 85) return 'CRITICAL' as RiskLevel;
    if (score >= 70) return 'HIGH' as RiskLevel;
    if (score >= 40) return 'MEDIUM' as RiskLevel;
    return 'LOW' as RiskLevel;
  }

  /**
   * Determine if transaction should be flagged or blocked
   */
  private determineActions(
    riskScore: RiskScore,
    ruleResults: boolean[],
    config: FraudDetectionConfig
  ) {
    const isFlagged = riskScore.score >= config.flagThreshold ?? ruleResults.some((r) => r);
    const isBlocked =
      config.autoBlock && (riskScore.score >= config.blockThreshold ?? ruleResults.some((r) => r));

    return { isFlagged, isBlocked };
  }

  /**
   * Generate reason for risk assessment
   */
  private generateReason(factors: RiskFactorResult[], level: RiskLevel): string {
    if (level === 'LOW') {
      return 'Low risk transaction';
    }

    const highRiskFactors = factors
      .filter((f) => f.score >= 70)
      .sort((a, b) => b.score - a.score)
      .slice(0, 3);

    if (highRiskFactors.length === 0) {
      return `${level.toLowerCase()} risk due to combination of factors`;
    }

    return `${level.toLowerCase()} risk due to: ${highRiskFactors.map((f) => f.reason).join(', ')}`;
  }

  /**
   * Generate recommended action
   */
  private generateRecommendedAction(level: RiskLevel, isBlocked: boolean): string {
    if (isBlocked) return 'Block transaction and notify merchant';

    switch (level) {
      case 'CRITICAL':
        return 'Manual review required before processing';
      case 'HIGH':
        return 'Additional verification recommended';
      case 'MEDIUM':
        return 'Monitor transaction';
      case 'LOW':
        return 'Process normally';
      default:
        return 'Process normally';
    }
  }

  /**
   * Save risk assessment to database
   */
  private async saveAssessment(
    transactionId: string,
    riskScore: RiskScore,
    isFlagged: boolean,
    isBlocked: boolean
  ) {
    try {
      await this.prisma.riskAssessment.create({
        data: {
          transactionId,
          score: riskScore.score,
          level: riskScore.level,
          factors: JSON.stringify(riskScore.factors),
          isFlagged,
          isBlocked,
          // confidence: riskScore.confidence, // Not in schema
          createdAt: new Date(),
        },
      });
    } catch (error: any) {
      logger.error('Error saving risk assessment:', error);
      // Don't throw - assessment was successful
    }
  }

  /**
   * Get default configuration
   */
  private getDefaultConfig(): FraudDetectionConfig {
    return {
      flagThreshold: 70,
      blockThreshold: 85,
      autoBlock: true,
      factorWeights: {
        AMOUNT: 0.2,
        LOCATION: 0.15,
        FREQUENCY: 0.15,
        TIME: 0.1,
        IP: 0.15,
        DEVICE: 0.05,
        PAYMENT_METHOD: 0.05,
        BEHAVIOR: 0.1,
        HISTORY: 0.05,
      },
      highRiskCountries: ['RU', 'CN', 'NG', 'UA', 'BY', 'KP', 'IR', 'IQ', 'SY', 'VE'],
      highRiskIpRanges: ['************/24', '***********/24'],
      maxTransactionAmount: 10000,
      maxTransactionsPerHour: 10,
      maxTransactionsPerDay: 30,
      velocitySettings: {
        enabled: true,
        timeWindowMinutes: 60,
        maxTransactions: 10,
        maxAmount: 5000,
      },
      mlSettings: {
        enabled: false,
        modelVersion: 'v1.0',
        confidenceThreshold: 0.8,
      },
    };
  }
}
