/**
 * Production Security Configuration
 *
 * This file contains the security configuration for the production environment.
 * It includes settings for JWT, CORS, rate limiting, and other security-related options.
 */
import { CorsOptions } from 'cors';
import rateLimit from 'express-rate-limit';
type RateLimitOptions = Parameters<typeof rateLimit>[0];
/**
 * JWT configuration for production
 */
export declare const jwtConfig: any;
/**
 * CORS configuration for production
 */
export declare const corsConfig: CorsOptions;
/**
 * Rate limiting configuration for production
 */
export declare const rateLimitConfig: RateLimitOptions;
/**
 * Payment rate limiting configuration for production
 */
export declare const paymentRateLimitConfig: RateLimitOptions;
/**
 * Verification rate limiting configuration for production
 */
export declare const verificationRateLimitConfig: RateLimitOptions;
/**
 * Content Security Policy configuration for production
 */
export declare const contentSecurityPolicyConfig: any;
/**
 * HSTS configuration for production
 */
export declare const hstsConfig: any;
/**
 * Session configuration for production
 */
export declare const sessionConfig: any;
/**
 * Initialize security configurations
 * This function validates the configurations and logs warnings for missing values
 */
export declare const initializeSecurityConfigurations: any;
/**
 * Production security configuration
 */
export declare const productionSecurityConfig: any;
export default productionSecurityConfig;
//# sourceMappingURL=production.security.config.d.ts.map