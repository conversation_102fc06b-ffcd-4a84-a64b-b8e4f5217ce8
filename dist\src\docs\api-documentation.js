"use strict";
/**
 * API Documentation Generator
 *
 * Generates comprehensive API documentation for all endpoints
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.APIDocumentationGenerator = void 0;
class APIDocumentationGenerator {
    constructor() {
        this.endpoints = [];
        this.initializeEndpoints();
    }
    /**
     * Initialize all API endpoints
     */
    initializeEndpoints() {
        this.addIdentityVerificationEndpoints();
        this.addFraudDetectionEndpoints();
        this.addAdminEndpoints();
        this.addAlertAggregationEndpoints();
    }
    /**
     * Add Identity Verification endpoints
     */
    addIdentityVerificationEndpoints() {
        this.endpoints.push({
            method: 'POST',
            path: '/api/identity/verify/ethereum',
            description: 'Verify Ethereum signature for identity verification',
            requestBody: {
                contentType: 'application/json',
                description: 'Ethereum signature verification data',
                schema: {
                    type: 'object',
                    properties: {
                        address: { type: 'string', description: 'Ethereum wallet address' },
                        message: { type: 'string', description: 'Message that was signed' },
                        signature: { type: 'string', description: 'Ethereum signature' },
                    },
                    required: ['address', 'message', 'signature'],
                },
                example: {
                    address: '******************************************',
                    message: 'Verify identity for AmazingPay',
                    signature: '0xabcdef1234567890...',
                },
            },
            responses: [
                {
                    statusCode: 200,
                    description: 'Verification successful',
                    schema: {
                        type: 'object',
                        properties: {
                            success: { type: 'boolean' },
                            data: {
                                type: 'object',
                                properties: {
                                    verificationId: { type: 'string' },
                                    method: { type: 'string' },
                                    confidence: { type: 'number' },
                                },
                            },
                        },
                    },
                    example: {
                        success: true,
                        data: {
                            verificationId: 'verification-123',
                            method: 'ethereum_signature',
                            confidence: 0.95,
                        },
                    },
                },
                {
                    statusCode: 400,
                    description: 'Invalid signature or verification failed',
                    example: {
                        success: false,
                        error: {
                            type: 'VALIDATION',
                            code: 'INVALID_SIGNATURE',
                            message: 'Signature verification failed',
                        },
                    },
                },
            ],
            authentication: ['Bearer Token'],
            examples: [
                {
                    title: 'Successful Verification',
                    description: 'Example of successful Ethereum signature verification',
                    request: {
                        address: '******************************************',
                        message: 'Verify identity for AmazingPay',
                        signature: '0xabcdef1234567890...',
                    },
                    response: {
                        success: true,
                        data: {
                            verificationId: 'verification-123',
                            method: 'ethereum_signature',
                            confidence: 0.95,
                        },
                    },
                },
            ],
        });
        this.endpoints.push({
            method: 'GET',
            path: '/api/identity/verification/:verificationId',
            description: 'Get identity verification details',
            parameters: [
                {
                    name: 'verificationId',
                    type: 'string',
                    location: 'path',
                    required: true,
                    description: 'Unique verification identifier',
                    example: 'verification-123',
                },
            ],
            responses: [
                {
                    statusCode: 200,
                    description: 'Verification details retrieved successfully',
                    example: {
                        success: true,
                        data: {
                            id: 'verification-123',
                            method: 'ethereum_signature',
                            status: 'verified',
                            confidence: 0.95,
                            createdAt: '2024-01-15T10:30:00Z',
                        },
                    },
                },
                {
                    statusCode: 404,
                    description: 'Verification not found',
                    example: {
                        success: false,
                        error: {
                            type: 'NOT_FOUND',
                            code: 'VERIFICATION_NOT_FOUND',
                            message: 'Verification not found',
                        },
                    },
                },
            ],
            authentication: ['Bearer Token'],
        });
    }
    /**
     * Add Fraud Detection endpoints
     */
    addFraudDetectionEndpoints() {
        this.endpoints.push({
            method: 'POST',
            path: '/api/fraud/assess',
            description: 'Assess transaction risk for fraud detection',
            requestBody: {
                contentType: 'application/json',
                description: 'Transaction data for risk assessment',
                schema: {
                    type: 'object',
                    properties: {
                        transactionId: { type: 'string', description: 'Transaction identifier' },
                        amount: { type: 'number', description: 'Transaction amount' },
                        currency: { type: 'string', description: 'Transaction currency' },
                        ipAddress: { type: 'string', description: 'User IP address' },
                        userAgent: { type: 'string', description: 'User agent string' },
                        deviceId: { type: 'string', description: 'Device identifier' },
                    },
                    required: ['transactionId', 'amount', 'currency', 'ipAddress'],
                },
                example: {
                    transactionId: 'tx-123',
                    amount: 1000,
                    currency: 'USD',
                    ipAddress: '***********',
                    userAgent: 'Mozilla/5.0...',
                    deviceId: 'device-123',
                },
            },
            responses: [
                {
                    statusCode: 200,
                    description: 'Risk assessment completed',
                    example: {
                        success: true,
                        data: {
                            transactionId: 'tx-123',
                            riskScore: {
                                score: 25,
                                level: 'LOW',
                                factors: [],
                                timestamp: '2024-01-15T10:30:00Z',
                                confidence: 0.9,
                            },
                            isFlagged: false,
                            isBlocked: false,
                            reason: 'Low risk transaction',
                            recommendedAction: 'Process normally',
                        },
                    },
                },
            ],
            authentication: ['Bearer Token'],
            authorization: ['MERCHANT', 'ADMIN'],
        });
        this.endpoints.push({
            method: 'GET',
            path: '/api/fraud/flagged',
            description: 'Get flagged transactions',
            parameters: [
                {
                    name: 'page',
                    type: 'number',
                    location: 'query',
                    required: false,
                    description: 'Page number for pagination',
                    example: 1,
                },
                {
                    name: 'limit',
                    type: 'number',
                    location: 'query',
                    required: false,
                    description: 'Number of items per page',
                    example: 20,
                },
                {
                    name: 'riskLevel',
                    type: 'string',
                    location: 'query',
                    required: false,
                    description: 'Filter by risk level',
                    example: 'HIGH',
                },
            ],
            responses: [
                {
                    statusCode: 200,
                    description: 'Flagged transactions retrieved successfully',
                    example: {
                        success: true,
                        data: {
                            transactions: [
                                {
                                    id: 'tx-456',
                                    amount: 5000,
                                    riskScore: 85,
                                    riskLevel: 'HIGH',
                                    flaggedAt: '2024-01-15T10:30:00Z',
                                },
                            ],
                            total: 1,
                            page: 1,
                            limit: 20,
                        },
                    },
                },
            ],
            authentication: ['Bearer Token'],
            authorization: ['ADMIN', 'FRAUD_ANALYST'],
        });
    }
    /**
     * Add Admin endpoints
     */
    addAdminEndpoints() {
        this.endpoints.push({
            method: 'POST',
            path: '/api/admin/users',
            description: 'Create new admin user',
            requestBody: {
                contentType: 'application/json',
                description: 'Admin user creation data',
                schema: {
                    type: 'object',
                    properties: {
                        name: { type: 'string', description: 'Admin name' },
                        email: { type: 'string', description: 'Admin email' },
                        password: { type: 'string', description: 'Admin password' },
                        roleId: { type: 'string', description: 'Role identifier' },
                    },
                    required: ['name', 'email', 'password', 'roleId'],
                },
                example: {
                    name: 'John Admin',
                    email: '<EMAIL>',
                    password: 'securePassword123',
                    roleId: 'admin-role-123',
                },
            },
            responses: [
                {
                    statusCode: 201,
                    description: 'Admin user created successfully',
                    example: {
                        success: true,
                        data: {
                            id: 'admin-456',
                            name: 'John Admin',
                            email: '<EMAIL>',
                            role: 'ADMIN',
                            createdAt: '2024-01-15T10:30:00Z',
                        },
                    },
                },
            ],
            authentication: ['Bearer Token'],
            authorization: ['SUPER_ADMIN'],
        });
        this.endpoints.push({
            method: 'GET',
            path: '/api/admin/users',
            description: 'Get all admin users',
            parameters: [
                {
                    name: 'page',
                    type: 'number',
                    location: 'query',
                    required: false,
                    description: 'Page number',
                    example: 1,
                },
                {
                    name: 'limit',
                    type: 'number',
                    location: 'query',
                    required: false,
                    description: 'Items per page',
                    example: 20,
                },
            ],
            responses: [
                {
                    statusCode: 200,
                    description: 'Admin users retrieved successfully',
                    example: {
                        success: true,
                        data: {
                            users: [
                                {
                                    id: 'admin-456',
                                    name: 'John Admin',
                                    email: '<EMAIL>',
                                    role: 'ADMIN',
                                    lastLogin: '2024-01-15T09:00:00Z',
                                },
                            ],
                            total: 1,
                            page: 1,
                            limit: 20,
                        },
                    },
                },
            ],
            authentication: ['Bearer Token'],
            authorization: ['ADMIN', 'SUPER_ADMIN'],
        });
    }
    /**
     * Add Alert Aggregation endpoints
     */
    addAlertAggregationEndpoints() {
        this.endpoints.push({
            method: 'GET',
            path: '/api/alerts',
            description: 'Get aggregated alerts',
            parameters: [
                {
                    name: 'severity',
                    type: 'string',
                    location: 'query',
                    required: false,
                    description: 'Filter by alert severity',
                    example: 'HIGH',
                },
                {
                    name: 'status',
                    type: 'string',
                    location: 'query',
                    required: false,
                    description: 'Filter by alert status',
                    example: 'ACTIVE',
                },
            ],
            responses: [
                {
                    statusCode: 200,
                    description: 'Alerts retrieved successfully',
                    example: {
                        success: true,
                        data: {
                            alerts: [
                                {
                                    id: 'alert-123',
                                    type: 'FRAUD_DETECTION',
                                    severity: 'HIGH',
                                    message: 'High risk transaction detected',
                                    status: 'ACTIVE',
                                    createdAt: '2024-01-15T10:30:00Z',
                                },
                            ],
                            summary: {
                                total: 1,
                                critical: 0,
                                high: 1,
                                medium: 0,
                                low: 0,
                            },
                        },
                    },
                },
            ],
            authentication: ['Bearer Token'],
            authorization: ['ADMIN', 'OPERATOR'],
        });
    }
    /**
     * Generate OpenAPI specification
     */
    generateOpenAPISpec() {
        const spec = {
            openapi: '3.0.3',
            info: {
                title: 'AmazingPay Flow API',
                description: 'Comprehensive payment processing system with identity verification, fraud detection, and admin management',
                version: '1.0.0',
                contact: {
                    name: 'AmazingPay Support',
                    email: '<EMAIL>',
                },
            },
            servers: [
                {
                    url: 'https://api.amazingpay.com',
                    description: 'Production server',
                },
                {
                    url: 'https://staging-api.amazingpay.com',
                    description: 'Staging server',
                },
            ],
            paths: {},
            components: {
                securitySchemes: {
                    bearerAuth: {
                        type: 'http',
                        scheme: 'bearer',
                        bearerFormat: 'JWT',
                    },
                },
            },
        };
        // Add paths
        this.endpoints.forEach((endpoint) => {
            if (!spec.paths[endpoint.path]) {
                spec.paths[endpoint.path] = {};
            }
            spec.paths[endpoint.path][endpoint.method.toLowerCase()] = {
                summary: endpoint.description,
                description: endpoint.description,
                parameters: endpoint.parameters?.map((param) => ({
                    name: param.name,
                    in: param.location,
                    required: param.required,
                    description: param.description,
                    schema: { type: param.type },
                    example: param.example,
                })),
                requestBody: endpoint.requestBody
                    ? {
                        required: true,
                        content: {
                            [endpoint.requestBody.contentType]: {
                                schema: endpoint.requestBody.schema,
                                example: endpoint.requestBody.example,
                            },
                        },
                    }
                    : undefined,
                responses: endpoint.responses.reduce((acc, response) => {
                    acc[response.statusCode] = {
                        description: response.description,
                        content: response.example
                            ? {
                                'application/json': {
                                    example: response.example,
                                },
                            }
                            : undefined,
                    };
                    return acc;
                }, {}),
                security: endpoint.authentication ? [{ bearerAuth: [] }] : undefined,
            };
        });
        return spec;
    }
    /**
     * Generate markdown documentation
     */
    generateMarkdownDocs() {
        let docs = '# AmazingPay Flow API Documentation\n\n';
        docs += 'This document provides comprehensive documentation for the AmazingPay Flow API.\n\n';
        docs += '## Authentication\n\n';
        docs +=
            'All API endpoints require authentication using Bearer tokens in the Authorization header:\n';
        docs += '```\nAuthorization: Bearer <your-token>\n```\n\n';
        docs += '## Endpoints\n\n';
        // Group endpoints by category
        const categories = {
            'Identity Verification': this.endpoints.filter((e) => e.path.includes('/identity')),
            'Fraud Detection': this.endpoints.filter((e) => e.path.includes('/fraud')),
            'Admin Management': this.endpoints.filter((e) => e.path.includes('/admin')),
            'Alert Management': this.endpoints.filter((e) => e.path.includes('/alerts')),
        };
        Object.entries(categories).forEach(([category, endpoints]) => {
            if (endpoints.length > 0) {
                docs += `### ${category}\n\n`;
                endpoints.forEach((endpoint) => {
                    docs += `#### ${endpoint.method} ${endpoint.path}\n\n`;
                    docs += `${endpoint.description}\n\n`;
                    if (endpoint.parameters && endpoint.parameters.length > 0) {
                        docs += '**Parameters:**\n\n';
                        endpoint.parameters.forEach((param) => {
                            docs += `- \`${param.name}\` (${param.type}, ${param.location}${param.required ? ', required' : ', optional'}): ${param.description}\n`;
                        });
                        docs += '\n';
                    }
                    if (endpoint.requestBody) {
                        docs += '**Request Body:**\n\n';
                        docs += '```json\n';
                        docs += JSON.stringify(endpoint.requestBody.example, null, 2);
                        docs += '\n```\n\n';
                    }
                    docs += '**Responses:**\n\n';
                    endpoint.responses.forEach((response) => {
                        docs += `- **${response.statusCode}**: ${response.description}\n`;
                        if (response.example) {
                            docs += '```json\n';
                            docs += JSON.stringify(response.example, null, 2);
                            docs += '\n```\n';
                        }
                    });
                    docs += '\n';
                });
            }
        });
        return docs;
    }
    /**
     * Get all endpoints
     */
    getEndpoints() {
        return this.endpoints;
    }
    /**
     * Generate Postman collection
     */
    generatePostmanCollection() {
        return {
            info: {
                name: 'AmazingPay Flow API',
                description: 'Complete API collection for AmazingPay Flow',
                schema: 'https://schema.getpostman.com/json/collection/v2.1.0/collection.json',
            },
            auth: {
                type: 'bearer',
                bearer: [
                    {
                        key: 'token',
                        value: '{{bearerToken}}',
                        type: 'string',
                    },
                ],
            },
            item: this.endpoints.map((endpoint) => ({
                name: `${endpoint.method} ${endpoint.path}`,
                request: {
                    method: endpoint.method,
                    header: [
                        {
                            key: 'Content-Type',
                            value: 'application/json',
                        },
                    ],
                    url: {
                        raw: `{{baseUrl}}${endpoint.path}`,
                        host: ['{{baseUrl}}'],
                        path: endpoint.path.split('/').filter((p) => p),
                    },
                    body: endpoint.requestBody
                        ? {
                            mode: 'raw',
                            raw: JSON.stringify(endpoint.requestBody.example, null, 2),
                        }
                        : undefined,
                },
                response: endpoint.responses.map((response) => ({
                    name: `${response.statusCode} - ${response.description}`,
                    originalRequest: {
                        method: endpoint.method,
                        header: [],
                        url: {
                            raw: `{{baseUrl}}${endpoint.path}`,
                            host: ['{{baseUrl}}'],
                            path: endpoint.path.split('/').filter((p) => p),
                        },
                    },
                    status: response.description,
                    code: response.statusCode,
                    _postman_previewlanguage: 'json',
                    header: [
                        {
                            key: 'Content-Type',
                            value: 'application/json',
                        },
                    ],
                    cookie: [],
                    body: JSON.stringify(response.example, null, 2),
                })),
            })),
            variable: [
                {
                    key: 'baseUrl',
                    value: 'https://api.amazingpay.com',
                },
                {
                    key: 'bearerToken',
                    value: 'your-jwt-token-here',
                },
            ],
        };
    }
}
exports.APIDocumentationGenerator = APIDocumentationGenerator;
//# sourceMappingURL=api-documentation.js.map