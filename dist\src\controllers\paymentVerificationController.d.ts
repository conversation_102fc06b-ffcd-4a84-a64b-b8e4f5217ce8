import { Request, Response } from "express";
import { PrismaClient } from "@prisma/client";
/**
 * Controller for payment verification
 */
export declare class PaymentVerificationController {
    private paymentVerificationService;
    /**
   * Create a new PaymentVerificationController instance
   * @param prisma Prisma client
   */
    constructor(prisma: PrismaClient);
    /**
   * Verify a payment
   * @param req Request
   * @param res Response
   */
    verifyPayment(req: Request, res: Response): Promise<void>;
    /**
   * Get a transaction by ID
   * @param req Request
   * @param res Response
   */
    getTransaction(req: Request, res: Response): Promise<void>;
    /**
   * Update a transaction status
   * @param req Request
   * @param res Response
   */
    updateTransactionStatus(req: Request, res: Response): Promise<void>;
}
export default PaymentVerificationController;
//# sourceMappingURL=paymentVerificationController.d.ts.map