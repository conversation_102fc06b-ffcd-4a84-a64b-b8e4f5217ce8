/**
 * Module Registry
 *
 * A central registry for module configurations.
 */
/**
 * Module configuration
 */
export interface ModuleConfig {
    /**
     * Whether the module is enabled
     */
    enabled: boolean;
    /**
     * Module-specific configuration
     */
    config: Record<string, any>;
    /**
     * Module dependencies
     */
    dependencies?: string[];
    /**
     * Module version
     */
    version?: string;
    /**
     * Module description
     */
    description?: string;
}
/**
 * Module registry
 */
export declare class ModuleRegistry {
    private static instance;
    private modules;
    /**
   * Get the singleton instance
   */
    static getInstance(): ModuleRegistry;
    /**
   * Register a module
   *
   * @param name Module name
   * @param config Module configuration
   */
    registerModule(name: string, config: ModuleConfig): void;
    /**
   * Get a module configuration
   *
   * @param name Module name
   * @returns Module configuration or undefined if not found
   */
    getModule(name: string): ModuleConfig | undefined;
    /**
   * Check if a module is registered
   *
   * @param name Module name
   * @returns True if module is registered
   */
    hasModule(name: string): boolean;
    /**
   * Check if a module is enabled
   *
   * @param name Module name
   * @returns True if module is enabled, false if disabled or not found
   */
    isModuleEnabled(name: string): boolean;
    /**
   * Enable a module
   *
   * @param name Module name
   * @returns True if module was enabled, false if not found
   */
    enableModule(name: string): boolean;
    /**
   * Disable a module
   *
   * @param name Module name
   * @returns True if module was disabled, false if not found
   */
    disableModule(name: string): boolean;
    /**
   * Update module configuration
   *
   * @param name Module name
   * @param config New configuration (partial)
   * @returns True if module was updated, false if not found
   */
    updateModuleConfig(name: string, config: Partial<ModuleConfig>): boolean;
    /**
   * Get all registered modules
   *
   * @returns Object with module names as keys and configurations as values
   */
    getAllModules(): Record<string, ModuleConfig>;
    /**
   * Get enabled modules
   *
   * @returns Object with module names as keys and configurations as values
   */
    getEnabledModules(): Record<string, ModuleConfig>;
    /**
   * Check if all dependencies of a module are enabled
   *
   * @param name Module name
   * @returns True if all dependencies are enabled, false otherwise
   */
    areDependenciesSatisfied(name: string): boolean;
}
export declare const moduleRegistry: any;
//# sourceMappingURL=ModuleRegistry.d.ts.map