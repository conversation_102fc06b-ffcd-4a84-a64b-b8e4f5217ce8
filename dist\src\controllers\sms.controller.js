"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SmsController = void 0;
const base_controller_1 = require("./base.controller");
const asyncHandler_1 = require("../utils/asyncHandler");
const AppError_1 = require("../utils/errors/AppError");
const sms_service_1 = require("../services/sms.service");
/**
 * SmsController
 * Controller for handling SMS operations
 */
class SmsController extends base_controller_1.BaseController {
    constructor() {
        super();
        /**
         * Test the SMS service by sending a test SMS
         */
        this.testSmsService = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Get user role
                const userRole = req.user?.role;
                // Check if user is authorized
                if (!userRole || userRole !== "ADMIN") {
                    throw new AppError_1.AppError({
                        message: "Unauthorized",
                        type: ErrorType.AUTHENTICATION,
                        code: ErrorCode.INVALID_CREDENTIALS
                    });
                }
                // Get phone number from body
                const { phoneNumber } = req.body;
                // Validate phone number
                if (!phoneNumber) {
                    throw new AppError_1.AppError({
                        message: "Phone number is required",
                        type: ErrorType.VALIDATION,
                        code: ErrorCode.MISSING_REQUIRED_FIELD
                    });
                }
                // Create SMS service
                const smsService = new sms_service_1.SmsService();
                // Test SMS service
                const success = await smsService.testSmsService(phoneNumber);
                if (success) {
                    return res.status(200).json({
                        success: true,
                        message: "Test SMS sent successfully"
                    });
                }
                else {
                    throw new AppError_1.AppError({
                        message: "Failed to send test SMS",
                        type: ErrorType.INTERNAL,
                        code: ErrorCode.INTERNAL_SERVER_ERROR
                    });
                }
            }
            catch (error) {
                if (error instanceof AppError_1.AppError) {
                    throw error;
                }
                throw new AppError_1.AppError({
                    message: "Failed to test SMS service",
                    type: ErrorType.INTERNAL,
                    code: ErrorCode.INTERNAL_SERVER_ERROR
                });
            }
        });
        /**
         * Send a custom SMS
         */
        this.sendCustomSms = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Get user role
                const userRole = req.user?.role;
                // Check if user is authorized
                if (!userRole || userRole !== "ADMIN") {
                    throw new AppError_1.AppError({
                        message: "Unauthorized",
                        type: ErrorType.AUTHENTICATION,
                        code: ErrorCode.INVALID_CREDENTIALS
                    });
                }
                // Get SMS data from body
                const { phoneNumber, message } = req.body;
                // Validate required fields
                if (!phoneNumber || !message) {
                    throw new AppError_1.AppError({
                        message: "Phone number and message are required",
                        type: ErrorType.VALIDATION,
                        code: ErrorCode.MISSING_REQUIRED_FIELD
                    });
                }
                // Create SMS service
                const smsService = new sms_service_1.SmsService();
                // Send SMS
                const success = await smsService.sendSms(phoneNumber, message);
                if (success) {
                    return res.status(200).json({
                        success: true,
                        message: "SMS sent successfully"
                    });
                }
                else {
                    throw new AppError_1.AppError({
                        message: "Failed to send SMS",
                        type: ErrorType.INTERNAL,
                        code: ErrorCode.INTERNAL_SERVER_ERROR
                    });
                }
            }
            catch (error) {
                if (error instanceof AppError_1.AppError) {
                    throw error;
                }
                throw new AppError_1.AppError({
                    message: "Failed to send custom SMS",
                    type: ErrorType.INTERNAL,
                    code: ErrorCode.INTERNAL_SERVER_ERROR
                });
            }
        });
        /**
         * Get admin phone numbers
         */
        this.getAdminPhoneNumbers = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Get user role
                const userRole = req.user?.role;
                // Check if user is authorized
                if (!userRole || userRole !== "ADMIN") {
                    throw new AppError_1.AppError({
                        message: "Unauthorized",
                        type: ErrorType.AUTHENTICATION,
                        code: ErrorCode.INVALID_CREDENTIALS
                    });
                }
                // Create SMS service
                const smsService = new sms_service_1.SmsService();
                // Get admin phone numbers
                const phoneNumbers = await smsService.getAdminPhoneNumbers();
                return res.status(200).json({
                    success: true,
                    data: phoneNumbers
                });
            }
            catch (error) {
                if (error instanceof AppError_1.AppError) {
                    throw error;
                }
                throw new AppError_1.AppError({
                    message: "Failed to get admin phone numbers",
                    type: ErrorType.INTERNAL,
                    code: ErrorCode.INTERNAL_SERVER_ERROR
                });
            }
        });
    }
}
exports.SmsController = SmsController;
exports.default = new SmsController();
//# sourceMappingURL=sms.controller.js.map