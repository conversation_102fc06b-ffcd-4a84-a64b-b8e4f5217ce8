// jscpd:ignore-file
/**
 * Enhanced Auth Middleware
 *
 * Middleware for authentication and authorization with RBAC support.
 */

import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger';
import { AppError } from '../utils/appError';
import { RBACService } from '../services/rbac.service';
import { AuditService } from '../services/audit.service';
import { verifyToken } from '../utils/jwt.utils';
import { Middleware } from '../types/express';
import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger';
import { AppError } from '../utils/appError';
import { RBACService } from '../services/rbac.service';
import { AuditService } from '../services/audit.service';
import { verifyToken } from '../utils/jwt.utils';
import { Middleware } from '../types/express';

// Extend the Express Request type to include user property
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role: string;
        merchantId?: string;
      };
    }
  }
}

const prisma: unknown = new PrismaClient();
const rbacService: unknown = new RBACService(prisma);
const auditService: unknown = new AuditService(prisma);

/**
 * Enhanced authentication middleware
 */
export const enhancedAuthenticate: Middleware = async (req, res, next) => {
  try {
    // Get token from Authorization header
    const authHeader: unknown = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new AppError('No token provided. Please log in.', 401, true);
    }

    // Extract the token
    const token: unknown = authHeader.split(' ')[1];

    if (!token) {
      throw new AppError('Invalid token format. Please log in again.', 401, true);
    }

    // Verify the token
    const decoded: unknown = verifyToken(token);

    // Set the user in the request object
    req.user = {
      userId: decoded.id, // Fixed: using id instead of userId
      role: decoded.role,
    };

    // Get user permissions from RBAC service
    const permissions = await rbacService.getUserPermissions(decoded.id); // Fixed: using id instead of userId
    req.user.permissions = permissions;

    // Log authentication success
    logger.debug(`User ${decoded.id} authenticated successfully`, {
      // Fixed: using id instead of userId
      userId: decoded.id, // Fixed: using id instead of userId
      role: decoded.role,
      requestId: req.requestId,
    });

    next();
  } catch (error) {
    if (error instanceof AppError) {
      return next(error);
    }

    logger.error('Authentication error:', error);
    return next(new AppError('Invalid or expired token. Please log in again.', 401, true));
  }
};

/**
 * Enhanced permission-based authorization middleware
 */
export const requirePermission = (resource: string, action: string) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        return next(new AppError('Authentication required. Please log in.', 401, true));
      }

      // Check if user has the required permission
      const hasPermission =
        req.user.permissions?.includes(`${resource}:${action}`) ||
        (await rbacService.hasPermission(req.user.id, resource, action)); // Fixed: using id instead of userId

      if (!hasPermission) {
        // Log unauthorized access attempt
        logger.warn(
          `User ${req.user.id} attempted to access ${resource}:${action} without permission`,
          {
            // Fixed: using id instead of userId
            userId: req.user.id, // Fixed: using id instead of userId
            role: req.user.role,
            resource,
            action,
            path: req.path,
            method: req.method,
            ip: req.ip,
          }
        );

        // Audit the unauthorized access attempt
        await auditService.logAction({
          userId: req.user.id, // Fixed: using id instead of userId
          action: 'access_denied',
          resource,
          resourceId: req.params.id,
          ipAddress: req.ip,
          userAgent: req.headers['user-agent'],
          statusCode: 403,
          errorMessage: `Permission denied: ${resource}:${action}`, // Fixed comma
        });

        return next(new AppError('You do not have permission to perform this action.', 403, true));
      }

      next();
    } catch (error) {
      logger.error('Authorization error:', error);
      next(new AppError('Authorization error', 500, false));
    }
  };
};

/**
 * Enhanced role-based authorization middleware
 */
export const requireRole = (roleTypes: string[]) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        return next(new AppError('Authentication required. Please log in.', 401, true));
      }

      const user = await prisma.user.findUnique({
        where: { id: req.user.id }, // Fixed: using id instead of userId
        include: { roles: true },
      });

      if (!user) {
        return next(new AppError('User not found.', 401, true));
      }

      // Check if user has any of the required roles
      const hasRole = user.roles.some((role) => roleTypes.includes(role.type));

      if (!hasRole) {
        // Log unauthorized access attempt
        logger.warn(`User ${req.user.id} attempted to access a route without required role`, {
          // Fixed: using id instead of userId
          userId: req.user.id, // Fixed: using id instead of userId
          userRoles: user.roles.map((r) => r.type),
          requiredRoles: roleTypes,
          path: req.path,
          method: req.method,
          ip: req.ip,
        });

        // Audit the unauthorized access attempt
        await auditService.logAction({
          userId: req.user.id, // Fixed: using id instead of userId
          action: 'access_denied',
          resource: 'role',
          ipAddress: req.ip,
          userAgent: req.headers['user-agent'],
          statusCode: 403,
          errorMessage: `Role denied: Required one of [${roleTypes.join(', ')}]`, // Fixed comma
        });

        return next(
          new AppError('You do not have the required role to perform this action.', 403, true)
        );
      }

      next();
    } catch (error) {
      logger.error('Role authorization error:', error);
      next(new AppError('Authorization error', 500, false));
    }
  };
};

/**
 * Enhanced resource ownership middleware
 */
export const requireOwnership = (
  resourceType: string,
  getResourceOwnerId: (req: Request) => Promise<string>
) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        return next(new AppError('Authentication required. Please log in.', 401, true));
      }

      // Check if user is an admin (admins can access any resource)
      const isAdmin = await rbacService.hasPermission(req.user.id, resourceType, 'admin_access'); // Fixed: using id instead of userId

      if (isAdmin) {
        return next();
      }

      // Get the owner ID of the requested resource
      const ownerId = await getResourceOwnerId(req);

      // Check if user is the owner
      if (req.user.id !== ownerId) {
        // Fixed: using id instead of userId
        // Log unauthorized access attempt
        logger.warn(`User ${req.user.id} attempted to access a resource owned by ${ownerId}`, {
          // Fixed: using id instead of userId
          userId: req.user.id, // Fixed: using id instead of userId
          resourceType,
          resourceId: req.params.id,
          ownerId,
          path: req.path,
          method: req.method,
          ip: req.ip,
        });

        // Audit the unauthorized access attempt
        await auditService.logAction({
          userId: req.user.id, // Fixed: using id instead of userId
          action: 'access_denied',
          resource: resourceType,
          resourceId: req.params.id,
          ipAddress: req.ip,
          userAgent: req.headers['user-agent'],
          statusCode: 403,
          errorMessage: `Ownership denied: Resource owned by ${ownerId}`, // Fixed comma
        });

        return next(new AppError('You do not have permission to access this resource.', 403, true));
      }

      next();
    } catch (error) {
      logger.error('Ownership authorization error:', error);
      next(new AppError('Authorization error', 500, false));
    }
  };
};

// Export all middleware functions as a group
export const enhancedAuthMiddleware = {
  enhancedAuthenticate,
  requirePermission,
  requireRole,
  requireOwnership,
};
