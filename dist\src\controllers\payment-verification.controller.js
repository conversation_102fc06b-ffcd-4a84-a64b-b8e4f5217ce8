"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentVerificationController = void 0;
const BaseController_1 = require("./base/BaseController");
const payment_verification_service_1 = require("../services/verification/payment-verification.service");
const payment_1 = require("../types/payment");
class PaymentVerificationController extends BaseController_1.BaseController {
    constructor() {
        super();
        /**
         * Verify a payment
         */
        this.verifyPayment = this.asyncHandler(async (req, res) => {
            try {
                const { method, transactionId, amount, currency, recipientAddress, merchantApiKey, merchantSecretKey } = req.body;
                // Validate required fields
                if (!method || !transactionId || !amount || !currency) {
                    return this.sendValidationError(res, {
                        method: !method ? ["Payment method is required"] : [],
                        transactionId: !transactionId ? ["Transaction ID is required"] : [],
                        amount: !amount ? ["Amount is required"] : [],
                        currency: !currency ? ["Currency is required"] : []
                    });
                }
                // Validate payment method
                if (!Object.values(payment_1.PaymentMethod).includes(method)) {
                    return this.sendValidationError(res, {
                        method: [`Invalid payment method: ${method}. Valid methods are: ${Object.values(payment_1.PaymentMethod).join(", ")}`]
                    });
                }
                // Check if recipient address is required
                if ((method === payment_1.PaymentMethod.BINANCE_TRC20 || method === payment_1.PaymentMethod.CRYPTO_TRANSFER) && !recipientAddress) {
                    return this.sendValidationError(res, {
                        recipientAddress: ["Recipient address is required for this payment method"]
                    });
                }
                // Check if merchant API keys are required
                if ((method === payment_1.PaymentMethod.BINANCE_PAY || method === payment_1.PaymentMethod.BINANCE_C2C || method === payment_1.PaymentMethod.BINANCE_TRC20) && (!merchantApiKey || !merchantSecretKey)) {
                    return this.sendValidationError(res, {
                        merchantApiKey: !merchantApiKey ? ["Merchant API key is required for this payment method"] : [],
                        merchantSecretKey: !merchantSecretKey ? ["Merchant secret key is required for this payment method"] : []
                    });
                }
                // Verify payment
                const result = await this.paymentVerificationService.verifyPayment(method, transactionId, amount, currency, recipientAddress, merchantApiKey, merchantSecretKey);
                // Send success response
                return this.sendSuccess(res, result, 200);
            }
            catch (error) {
                return this.sendError(res, error);
            }
        });
        /**
         * Verify a Binance Pay payment
         */
        this.verifyBinancePayPayment = this.asyncHandler(async (req, res) => {
            try {
                const { transactionId, amount, currency, merchantApiKey, merchantSecretKey } = req.body;
                // Validate required fields
                if (!transactionId || !amount || !currency || !merchantApiKey || !merchantSecretKey) {
                    return this.sendValidationError(res, {
                        transactionId: !transactionId ? ["Transaction ID is required"] : [],
                        amount: !amount ? ["Amount is required"] : [],
                        currency: !currency ? ["Currency is required"] : [],
                        merchantApiKey: !merchantApiKey ? ["Merchant API key is required"] : [],
                        merchantSecretKey: !merchantSecretKey ? ["Merchant secret key is required"] : []
                    });
                }
                // Verify payment
                const result = await this.paymentVerificationService.verifyPayment(payment_1.PaymentMethod.BINANCE_PAY, transactionId, amount, currency, "", merchantApiKey, merchantSecretKey);
                // Send success response
                return this.sendSuccess(res, result, 200);
            }
            catch (error) {
                return this.sendError(res, error);
            }
        });
        /**
         * Verify a Binance C2C payment
         */
        this.verifyBinanceC2CPayment = this.asyncHandler(async (req, res) => {
            try {
                const { note, amount, currency, merchantApiKey, merchantSecretKey } = req.body;
                // Validate required fields
                if (!note || !amount || !currency || !merchantApiKey || !merchantSecretKey) {
                    return this.sendValidationError(res, {
                        note: !note ? ["Note is required"] : [],
                        amount: !amount ? ["Amount is required"] : [],
                        currency: !currency ? ["Currency is required"] : [],
                        merchantApiKey: !merchantApiKey ? ["Merchant API key is required"] : [],
                        merchantSecretKey: !merchantSecretKey ? ["Merchant secret key is required"] : []
                    });
                }
                // Verify payment
                const result = await this.paymentVerificationService.verifyPayment(payment_1.PaymentMethod.BINANCE_C2C, note, amount, currency, "", merchantApiKey, merchantSecretKey);
                // Send success response
                return this.sendSuccess(res, result, 200);
            }
            catch (error) {
                return this.sendError(res, error);
            }
        });
        /**
         * Verify a Binance TRC20 payment
         */
        this.verifyBinanceTRC20Payment = this.asyncHandler(async (req, res) => {
            try {
                const { txHash, amount, currency, recipientAddress, merchantApiKey, merchantSecretKey } = req.body;
                // Validate required fields
                if (!txHash || !amount || !currency || !recipientAddress || !merchantApiKey || !merchantSecretKey) {
                    return this.sendValidationError(res, {
                        txHash: !txHash ? ["Transaction hash is required"] : [],
                        amount: !amount ? ["Amount is required"] : [],
                        currency: !currency ? ["Currency is required"] : [],
                        recipientAddress: !recipientAddress ? ["Recipient address is required"] : [],
                        merchantApiKey: !merchantApiKey ? ["Merchant API key is required"] : [],
                        merchantSecretKey: !merchantSecretKey ? ["Merchant secret key is required"] : []
                    });
                }
                // Verify payment
                const result = await this.paymentVerificationService.verifyPayment(payment_1.PaymentMethod.BINANCE_TRC20, txHash, amount, currency, recipientAddress, merchantApiKey, merchantSecretKey);
                // Send success response
                return this.sendSuccess(res, result, 200);
            }
            catch (error) {
                return this.sendError(res, error);
            }
        });
        /**
         * Verify a crypto transfer payment
         */
        this.verifyCryptoTransferPayment = this.asyncHandler(async (req, res) => {
            try {
                const { txHash, amount, currency, recipientAddress } = req.body;
                // Validate required fields
                if (!txHash || !amount || !currency || !recipientAddress) {
                    return this.sendValidationError(res, {
                        txHash: !txHash ? ["Transaction hash is required"] : [],
                        amount: !amount ? ["Amount is required"] : [],
                        currency: !currency ? ["Currency is required"] : [],
                        recipientAddress: !recipientAddress ? ["Recipient address is required"] : []
                    });
                }
                // Verify payment
                const result = await this.paymentVerificationService.verifyPayment(payment_1.PaymentMethod.CRYPTO_TRANSFER, txHash, amount, currency, recipientAddress);
                // Send success response
                return this.sendSuccess(res, result, 200);
            }
            catch (error) {
                return this.sendError(res, error);
            }
        });
        this.paymentVerificationService = new payment_verification_service_1.PaymentVerificationService();
    }
    /**
     * Send success response
     * @param res Response
     * @param data Response data
     * @param statusCode Status code
     * @returns Response
     */
    sendSuccess(res, data, statusCode = 200) {
        return res.status(statusCode).json({
            success: true,
            data
        });
    }
}
exports.PaymentVerificationController = PaymentVerificationController;
//# sourceMappingURL=payment-verification.controller.js.map