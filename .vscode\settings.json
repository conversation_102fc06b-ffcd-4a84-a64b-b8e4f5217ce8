{"editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "eslint.validate": ["javascript", "typescript"], "typescript.tsdk": "node_modules/typescript/lib", "typescript.enablePromptUseWorkspaceTsdk": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "prettier.singleQuote": true, "prettier.trailingComma": "es5", "prettier.printWidth": 100, "prettier.tabWidth": 2, "prettier.semi": true, "codemetrics.basics.ComplexityLevelExtreme": 25, "codemetrics.basics.ComplexityLevelHigh": 15, "codemetrics.basics.ComplexityLevelNormal": 10, "codemetrics.basics.ComplexityLevelLow": 5, "codemetrics.basics.CodeLensEnabled": true, "sonarlint.rules": {"typescript:S1871": {"level": "on"}, "typescript:S4144": {"level": "on"}, "typescript:S1192": {"level": "on"}, "typescript:S1066": {"level": "on"}}, "sonarlint.output.showAnalyzerLogs": true, "sonarlint.output.showVerboseLogs": false, "workbench.colorCustomizations": {"editorError.foreground": "#ff0000", "editorWarning.foreground": "#ffcc00", "editorInfo.foreground": "#00aaff"}, "search.exclude": {"**/node_modules": true, "**/dist": true, "**/.jscpd": true, "**/coverage": true}}