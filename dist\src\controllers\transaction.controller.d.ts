import { BaseController } from "../../core/BaseController";
/**
 * Transaction controller
 * This controller handles transaction-related operations
 */
export declare class TransactionController extends BaseController {
    private transactionService;
    /**
     * Create a new transaction controller
     */
    constructor();
    /**
     * Get all transactions
     */
    getTransactions: any;
    /**
     * Get a transaction by ID
     */
    getTransaction: any;
    /**
     * Create a new transaction
     */
    createTransaction: any;
    /**
     * Update a transaction
     */
    updateTransaction: any;
    /**
     * Delete a transaction
     */
    deleteTransaction: any;
    /**
     * Get transaction statistics
     */
    getTransactionStats: any;
}
//# sourceMappingURL=transaction.controller.d.ts.map