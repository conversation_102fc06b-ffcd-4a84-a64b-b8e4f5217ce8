#!/bin/sh

# Docker entrypoint script for AmazingPay Flow
# Handles database migrations, health checks, and application startup

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') [ENTRYPOINT] $1"
}

log_info() {
    echo "${GREEN}$(date '+%Y-%m-%d %H:%M:%S') [INFO] $1${NC}"
}

log_warn() {
    echo "${YELLOW}$(date '+%Y-%m-%d %H:%M:%S') [WARN] $1${NC}"
}

log_error() {
    echo "${RED}$(date '+%Y-%m-%d %H:%M:%S') [ERROR] $1${NC}"
}

# Wait for database to be ready
wait_for_db() {
    log_info "Waiting for database to be ready..."
    
    # Extract database connection details from DATABASE_URL
    if [ -z "$DATABASE_URL" ]; then
        log_error "DATABASE_URL environment variable is not set"
        exit 1
    fi
    
    # Simple connection test using node
    node -e "
        const { PrismaClient } = require('@prisma/client');
        const prisma = new PrismaClient();
        
        async function testConnection() {
            try {
                await prisma.\$connect();
                console.log('Database connection successful');
                await prisma.\$disconnect();
                process.exit(0);
            } catch (error) {
                console.error('Database connection failed:', error.message);
                process.exit(1);
            }
        }
        
        testConnection();
    " || {
        log_error "Database connection failed"
        exit 1
    }
    
    log_info "Database is ready"
}

# Run database migrations
run_migrations() {
    log_info "Running database migrations..."
    
    if npx prisma migrate deploy; then
        log_info "Database migrations completed successfully"
    else
        log_error "Database migrations failed"
        exit 1
    fi
}

# Seed database if needed
seed_database() {
    if [ "$SEED_DATABASE" = "true" ]; then
        log_info "Seeding database..."
        
        if npx prisma db seed; then
            log_info "Database seeding completed successfully"
        else
            log_warn "Database seeding failed (this may be expected in production)"
        fi
    fi
}

# Validate environment variables
validate_environment() {
    log_info "Validating environment variables..."
    
    required_vars="DATABASE_URL JWT_SECRET"
    missing_vars=""
    
    for var in $required_vars; do
        if [ -z "$(eval echo \$$var)" ]; then
            missing_vars="$missing_vars $var"
        fi
    done
    
    if [ -n "$missing_vars" ]; then
        log_error "Missing required environment variables:$missing_vars"
        exit 1
    fi
    
    log_info "Environment validation passed"
}

# Health check function
health_check() {
    log_info "Performing initial health check..."
    
    # Start the application in background for health check
    node dist/index.js &
    APP_PID=$!
    
    # Wait for application to start
    sleep 10
    
    # Check if application is responding
    if curl -f http://localhost:3000/health > /dev/null 2>&1; then
        log_info "Health check passed"
        kill $APP_PID 2>/dev/null || true
        wait $APP_PID 2>/dev/null || true
    else
        log_error "Health check failed"
        kill $APP_PID 2>/dev/null || true
        wait $APP_PID 2>/dev/null || true
        exit 1
    fi
}

# Cleanup function
cleanup() {
    log_info "Shutting down gracefully..."
    if [ -n "$APP_PID" ]; then
        kill $APP_PID 2>/dev/null || true
        wait $APP_PID 2>/dev/null || true
    fi
    exit 0
}

# Set up signal handlers
trap cleanup SIGTERM SIGINT

# Main execution
main() {
    log_info "Starting AmazingPay Flow application..."
    log_info "Environment: ${NODE_ENV:-development}"
    log_info "Port: ${PORT:-3000}"
    
    # Validate environment
    validate_environment
    
    # Wait for database
    wait_for_db
    
    # Run migrations
    run_migrations
    
    # Seed database if needed
    seed_database
    
    # Perform health check in development/staging
    if [ "$NODE_ENV" != "production" ] || [ "$SKIP_HEALTH_CHECK" != "true" ]; then
        health_check
    fi
    
    log_info "Starting application server..."
    
    # Start the application
    exec node dist/index.js
}

# Handle different commands
case "$1" in
    "migrate")
        log_info "Running migrations only..."
        validate_environment
        wait_for_db
        run_migrations
        ;;
    "seed")
        log_info "Running database seed only..."
        validate_environment
        wait_for_db
        seed_database
        ;;
    "health")
        log_info "Running health check only..."
        health_check
        ;;
    "start"|"")
        main
        ;;
    *)
        log_info "Running custom command: $*"
        exec "$@"
        ;;
esac
