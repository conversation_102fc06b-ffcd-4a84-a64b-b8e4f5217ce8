// jscpd:ignore-file
import { Request, Response } from 'express';

// Local types and mocks
enum ErrorType {
  VALIDATION = 'VALIDATION',
  NOT_FOUND = 'NOT_FOUND',
  INTERNAL = 'INTERNAL',
  AUTHENTICATION = 'AUTHENTICATION',
}

enum ErrorCode {
  INVALID_INPUT = 'INVALID_INPUT',
  RESOURCE_NOT_FOUND = 'RESOURCE_NOT_FOUND',
  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR',
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
}

class AppError extends Error {
  constructor(options: { message: string; type: ErrorType; code: ErrorCode }) {
    super(options.message);
    this.name = 'AppError';
  }
}

const asyncHandler = (fn: Function) => fn;

const TransactionService = {
  updateTransactionStatus: async (id: string, status: string, data: any) => {},
};

const BinancePayService = {
  verifyWebhookSignature: (body: any, signature: string, secret: string) => true,
};

const prisma = {
  transaction: {
    findFirst: async (options: any) => ({
      id: '1',
      reference: 'test',
      paymentMethod: {
        config: { apiSecret: 'test-secret' },
      },
    }),
  },
};

// Handle Binance Pay webhook
export const handleBinancePayWebhook = asyncHandler(async (req: Request, res: Response) => {
  // Validate webhook data
  if (!req.body || !req.body.data || !req.body.timestamp || !req.body.nonce) {
    throw new AppError({
      message: 'Invalid webhook data',
      type: ErrorType.VALIDATION,
      code: ErrorCode.INVALID_INPUT,
    });
  }

  const { data, timestamp, nonce } = req.body;

  // Validate webhook signature
  const signature = req.headers['binancepay-signature'] as string;

  if (!signature) {
    throw new AppError({
      message: 'Missing webhook signature',
      type: ErrorType.VALIDATION,
      code: ErrorCode.INVALID_INPUT,
    });
  }

  // Get transaction by merchant trade number
  const merchantTradeNo = data.merchantTradeNo;

  if (!merchantTradeNo) {
    throw new AppError({
      message: 'Missing merchant trade number',
      type: ErrorType.VALIDATION,
      code: ErrorCode.INVALID_INPUT,
    });
  }

  const transaction = await prisma.transaction.findFirst({
    where: { reference: merchantTradeNo },
    include: { paymentMethod: true },
  });

  if (!transaction) {
    throw new AppError({
      message: 'Transaction not found',
      type: ErrorType.NOT_FOUND,
      code: ErrorCode.RESOURCE_NOT_FOUND,
    });
  }

  // Get API secret from payment method config
  if (!transaction.paymentMethod || !transaction.paymentMethod.config) {
    throw new AppError({
      message: 'Payment method configuration not found',
      type: ErrorType.INTERNAL,
      code: ErrorCode.INTERNAL_SERVER_ERROR,
    });
  }

  const config: Record<string, unknown> = transaction.paymentMethod.config as any;
  const apiSecret = config.apiSecret as string;

  if (!apiSecret) {
    throw new AppError({
      message: 'Missing API secret',
      type: ErrorType.INTERNAL,
      code: ErrorCode.INTERNAL_SERVER_ERROR,
    });
  }

  // Verify webhook signature
  const isValid = BinancePayService.verifyWebhookSignature(req.body, signature, apiSecret);

  if (!isValid) {
    throw new AppError({
      message: 'Invalid webhook signature',
      type: ErrorType.AUTHENTICATION,
      code: ErrorCode.INVALID_CREDENTIALS,
    });
  }

  // Process webhook based on status
  const status = data.status;

  if (status === 'PAY_SUCCESS' || status === 'PAID') {
    // Update transaction status
    await TransactionService.updateTransactionStatus(transaction.id, 'COMPLETED', {
      verificationResult: data,
      metadata: {
        verifiedAt: new Date().toISOString(),
        verificationMethod: 'binance_pay',
        automatic: true,
        webhookTimestamp: timestamp,
      },
    });
  } else if (status === 'PAY_CLOSED' || status === 'PAY_REFUND' || status === 'CANCELED') {
    // Update transaction status
    await TransactionService.updateTransactionStatus(transaction.id, 'FAILED', {
      verificationResult: data,
      metadata: {
        failedAt: new Date().toISOString(),
        verificationMethod: 'binance_pay',
        reason: `Payment ${status.toLowerCase()}`,
        automatic: true,
        webhookTimestamp: timestamp,
      },
    });
  }

  // Acknowledge webhook
  res.status(200).json({ success: true });
});
