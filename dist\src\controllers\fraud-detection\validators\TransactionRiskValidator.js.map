{"version": 3, "file": "TransactionRiskValidator.js", "sourceRoot": "", "sources": ["../../../../../src/controllers/fraud-detection/validators/TransactionRiskValidator.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;AAEH,6DAAgF;AAEhF,mDAAgD;AAEhD;;GAEG;AACH,MAAa,wBAAyB,SAAQ,6BAAa;IAEzD;;OAEG;IACH,6BAA6B,CAAC,IAAS;QACrC,MAAM,MAAM,GAAsB,EAAE,CAAC;QAErC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACxB,MAAM,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,eAAe,EAAE,OAAO,EAAE,4BAA4B,EAAE,CAAC,CAAC;QACjF,CAAC;aAAM,IAAI,OAAO,IAAI,CAAC,aAAa,KAAK,QAAQ,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;YAC3F,MAAM,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,eAAe,EAAE,OAAO,EAAE,+BAA+B,EAAE,KAAK,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;QAC/G,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,MAAM,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE,wBAAwB,EAAE,CAAC,CAAC;QACzE,CAAC;aAAM,IAAI,OAAO,IAAI,CAAC,SAAS,KAAK,QAAQ,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YACxF,MAAM,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE,2BAA2B,EAAE,KAAK,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;QACnG,CAAC;QAED,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,IAAI,OAAO,IAAI,CAAC,SAAS,KAAK,QAAQ,EAAE,CAAC;YACvE,MAAM,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE,6BAA6B,EAAE,KAAK,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;QACrG,CAAC;QAED,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,IAAI,OAAO,IAAI,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;YACrE,MAAM,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE,4BAA4B,EAAE,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;QAClG,CAAC;QAED,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtB,MAAM,IAAI,mBAAQ,CAAC;gBACjB,OAAO,EAAE,mBAAmB;gBAC5B,IAAI,EAAE,oBAAS,CAAC,UAAU;gBAC1B,IAAI,EAAE,oBAAS,CAAC,aAAa;gBAC7B,OAAO,EAAE,EAAE,MAAM,EAAE;aACpB,CAAC,CAAC;QACL,CAAC;QAED,OAAO;YACL,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,SAAS,EAAE,IAAI,CAAC,SAAS,IAAI,SAAS;YACtC,QAAQ,EAAE,IAAI,CAAC,QAAQ,IAAI,SAAS;SACrC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,qBAAqB,CAAC,aAAkB;QACtC,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,MAAM,IAAI,mBAAQ,CAAC;gBACjB,OAAO,EAAE,4BAA4B;gBACrC,IAAI,EAAE,oBAAS,CAAC,UAAU;gBAC1B,IAAI,EAAE,oBAAS,CAAC,sBAAsB;aACvC,CAAC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,EAAE,CAAC;YACrC,MAAM,IAAI,mBAAQ,CAAC;gBACjB,OAAO,EAAE,qCAAqC;gBAC9C,IAAI,EAAE,oBAAS,CAAC,UAAU;gBAC1B,IAAI,EAAE,oBAAS,CAAC,aAAa;aAC9B,CAAC,CAAC;QACL,CAAC;QAED,OAAO,aAAa,CAAC;IACvB,CAAC;CACF;AAnED,4DAmEC"}