/**
 * Enhanced Risk Engine Controller
 *
 * This controller handles API requests related to the enhanced risk engine.
 */
import { Request, Response } from "express";
import { BaseController } from "./base.controller";
/**
 * Enhanced risk engine controller
 */
export declare class EnhancedRiskEngineController extends BaseController {
    private enhancedRiskEngineService;
    private prisma;
    constructor();
    /**
     * Assess transaction risk
     */
    assessTransactionRisk: (req: Request, res: Response, next: import("express").NextFunction) => void;
    /**
     * Get risk assessment for a transaction
     */
    getTransactionRiskAssessment: (req: Request, res: Response, next: import("express").NextFunction) => void;
    /**
   * Get merchant risk configuration
   */
    getMerchantRiskConfig: (req: Request, res: Response, next: import("express").NextFunction) => void;
    /**
   * Update merchant risk configuration
   */
    updateMerchantRiskConfig: (req: Request, res: Response, next: import("express").NextFunction) => void;
    /**
   * Get risk statistics
   */
    getRiskStatistics: (req: Request, res: Response, next: import("express").NextFunction) => void;
}
//# sourceMappingURL=enhanced-risk-engine.controller.d.ts.map