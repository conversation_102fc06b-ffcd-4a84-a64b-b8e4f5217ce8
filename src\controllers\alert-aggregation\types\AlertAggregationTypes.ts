/**
 * Alert Aggregation Controller Types
 * 
 * Type definitions for alert aggregation controller components.
 */

import { Request, Response, Next(...args: any[]) => any } from 'express';
import { AlertType, AlertSeverity } from '../../../types';

/**
 * Extended request interface with user information
 */
export interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    role: string;
    merchantId?: string;
  };
}

/**
 * Alert aggregation rule creation request
 */
export interface CreateAggregationRuleRequest {
  name: string;
  description: string;
  type: AlertType | 'ANY';
  severity: AlertSeverity | 'ANY';
  timeWindow: number;
  threshold: number;
  groupBy: string[];
  enabled?: boolean;
}

/**
 * Alert aggregation rule update request
 */
export interface UpdateAggregationRuleRequest {
  name?: string;
  description?: string;
  type?: AlertType | 'ANY';
  severity?: AlertSeverity | 'ANY';
  timeWindow?: number;
  threshold?: number;
  groupBy?: string[];
  enabled?: boolean;
}

/**
 * Alert correlation rule creation request
 */
export interface CreateCorrelationRuleRequest {
  name: string;
  description: string;
  conditions: CorrelationCondition[];
  timeWindow: number;
  enabled?: boolean;
}

/**
 * Alert correlation rule update request
 */
export interface UpdateCorrelationRuleRequest {
  name?: string;
  description?: string;
  conditions?: CorrelationCondition[];
  timeWindow?: number;
  enabled?: boolean;
}

/**
 * Correlation condition interface
 */
export interface CorrelationCondition {
  alertType: AlertType;
  severity: AlertSeverity;
  count: number;
  operator: 'EQUALS' | 'GREATER_THAN' | 'LESS_THAN' | 'GREATER_THAN_OR_EQUAL' | 'LESS_THAN_OR_EQUAL';
}

/**
 * Alert aggregation rule response
 */
export interface AggregationRuleResponse {
  id: string;
  name: string;
  description: string;
  type: AlertType | 'ANY';
  severity: AlertSeverity | 'ANY';
  timeWindow: number;
  threshold: number;
  groupBy: string[];
  enabled: boolean;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Alert correlation rule response
 */
export interface CorrelationRuleResponse {
  id: string;
  name: string;
  description: string;
  conditions: CorrelationCondition[];
  timeWindow: number;
  enabled: boolean;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * API response wrapper
 */
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

/**
 * Pagination parameters
 */
export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

/**
 * Filter parameters for aggregation rules
 */
export interface AggregationRuleFilters {
  type?: AlertType | 'ANY';
  severity?: AlertSeverity | 'ANY';
  enabled?: boolean;
  search?: string;
}

/**
 * Filter parameters for correlation rules
 */
export interface CorrelationRuleFilters {
  enabled?: boolean;
  search?: string;
}

/**
 * Validation error interface
 */
export interface ValidationError {
  field: string;
  message: string;
  value?: unknown;
}

/**
 * Controller method options
 */
export interface ControllerMethodOptions {
  requireAuth?: boolean;
  requiredRole?: string;
  validateInput?: boolean;
  logRequest?: boolean;
}

/**
 * Request context interface
 */
export interface RequestContext {
  user?: {
    id: string;
    role: string;
    merchantId?: string;
  };
  requestId: string;
  timestamp: Date;
  ip: string;
  userAgent: string;
}

/**
 * Service dependencies interface
 */
export interface AlertAggregationServiceDependencies {
  aggregationRuleService: unknown;
  correlationRuleService: unknown;
  authorizationService: unknown;
  validationService: unknown;
  auditService?: unknown;
}

/**
 * Controller configuration
 */
export interface AlertAggregationControllerConfig {
  enableAuditLogging?: boolean;
  enableRateLimiting?: boolean;
  defaultPageSize?: number;
  maxPageSize?: number;
  cacheEnabled?: boolean;
  cacheTtl?: number;
}

/**
 * Audit log entry
 */
export interface AuditLogEntry {
  action: string;
  resource: string;
  resourceId?: string;
  userId: string;
  userRole: string;
  timestamp: Date;
  details?: unknown;
  ipAddress: string;
  userAgent: string;
}

/**
 * Rate limiting configuration
 */
export interface RateLimitConfig {
  windowMs: number;
  maxRequests: number;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
}

/**
 * Cache configuration
 */
export interface CacheConfig {
  enabled: boolean;
  ttl: number;
  keyPrefix: string;
  invalidateOnUpdate: boolean;
}

/**
 * Error response format
 */
export interface ErrorResponse {
  success: false;
  error: {
    message: string;
    code: string;
    type: string;
    details?: unknown;
  };
  timestamp: Date;
  requestId: string;
}

/**
 * Success response format
 */
export interface SuccessResponse<T = unknown> {
  success: true;
  data: T;
  message?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  timestamp: Date;
  requestId: string;
}

/**
 * Controller method result
 */
export type ControllerResult<T = unknown> = Promise<SuccessResponse<T> | ErrorResponse>;

/**
 * Middleware (...args: any[]) => any type
 */
export type Middleware(...args: any[]) => any = (req: AuthenticatedRequest, res: Response, next: (...args: any[]) => any) => void | Promise<any>;

/**
 * Controller method type
 */
export type ControllerMethod<T = unknown> = (req: AuthenticatedRequest, res: Response) => ControllerResult<T>;

/**
 * Validation schema interface
 */
export interface ValidationSchema {
  [key: string]: {
    type: 'string' | 'number' | 'boolean' | 'array' | 'object';
    required?: boolean;
    minLength?: number;
    maxLength?: number;
    min?: number;
    max?: number;
    enum?: unknown[];
    pattern?: RegExp;
    custom?: (value: any) => boolean | string;
  };
}

/**
 * Authorization context
 */
export interface AuthorizationContext {
  user: {
    id: string;
    role: string;
    merchantId?: string;
  };
  resource: string;
  action: string;
  resourceId?: string;
}

/**
 * Permission check result
 */
export interface PermissionResult {
  allowed: boolean;
  reason?: string;
  requiredRole?: string;
  requiredPermissions?: string[];
}
