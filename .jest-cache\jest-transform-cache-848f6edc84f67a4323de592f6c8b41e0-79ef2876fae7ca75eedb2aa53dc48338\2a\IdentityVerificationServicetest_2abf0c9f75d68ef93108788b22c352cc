b75ca5d69a1af0eece658521a9d55bb5
"use strict";
/**
 * Unit Tests for Identity Verification Service
 *
 * Comprehensive test suite covering all functionality of the IdentityVerificationService
 */
Object.defineProperty(exports, "__esModule", { value: true });
const globals_1 = require("@jest/globals");
// Mock dependencies
globals_1.jest.mock('@prisma/client');
globals_1.jest.mock('ethers');
const IdentityVerificationService_1 = require("../core/IdentityVerificationService");
const IdentityVerificationError_1 = require("../core/IdentityVerificationError");
(0, globals_1.describe)('IdentityVerificationService', () => {
    let service;
    let mockPrisma;
    (0, globals_1.beforeEach)(() => {
        // Create mock Prisma client
        mockPrisma = {
            identityVerification: {
                create: globals_1.jest.fn(),
                findUnique: globals_1.jest.fn(),
                findMany: globals_1.jest.fn(),
                update: globals_1.jest.fn(),
                delete: globals_1.jest.fn(),
                count: globals_1.jest.fn(),
            },
            user: {
                findUnique: globals_1.jest.fn(),
                create: globals_1.jest.fn(),
                update: globals_1.jest.fn(),
            },
            merchant: {
                findUnique: globals_1.jest.fn(),
            },
        };
        // Initialize service with mock
        service = new IdentityVerificationService_1.IdentityVerificationService(mockPrisma);
    });
    (0, globals_1.afterEach)(() => {
        globals_1.jest.clearAllMocks();
    });
    (0, globals_1.describe)('verifyEthereumSignature', () => {
        const validVerificationData = {
            address: '******************************************',
            message: 'Verify identity for AmazingPay',
            signature: '0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890ab1c',
            userId: 'user-123-456-789',
            merchantId: 'merchant-123-456-789',
        };
        (0, globals_1.it)('should successfully verify a valid Ethereum signature', async () => {
            // Arrange
            const mockVerificationResult = {
                id: 'verification-123-456-789',
                userId: validVerificationData.userId,
                merchantId: validVerificationData.merchantId,
                method: 'ETHEREUM_SIGNATURE',
                status: 'VERIFIED',
                address: validVerificationData.address,
                createdAt: new Date(),
            };
            // Mock Prisma calls
            mockPrisma.identityVerification.create.mockResolvedValue(mockVerificationResult);
            // Act
            const result = await service.verifyEthereumSignature(validVerificationData);
            // Assert
            (0, globals_1.expect)(result).toBeDefined();
            (0, globals_1.expect)(result.success).toBe(true);
            (0, globals_1.expect)(result.verificationId).toBe(mockVerificationResult.id);
            (0, globals_1.expect)(result.method).toBe('ETHEREUM_SIGNATURE');
            (0, globals_1.expect)(result.status).toBe('VERIFIED');
            // Verify Prisma calls
            (0, globals_1.expect)(mockPrisma.identityVerification.create).toHaveBeenCalledWith({
                data: globals_1.expect.objectContaining({
                    userId: validVerificationData.userId,
                    merchantId: validVerificationData.merchantId,
                    method: 'ETHEREUM_SIGNATURE',
                    status: 'VERIFIED',
                    address: globals_1.expect.any(String),
                }),
            });
        });
        (0, globals_1.it)('should return error for invalid Ethereum address', async () => {
            // Arrange
            const invalidData = {
                ...validVerificationData,
                address: 'invalid-address',
            };
            // Act
            const result = await service.verifyEthereumSignature(invalidData);
            // Assert
            (0, globals_1.expect)(result.success).toBe(false);
            (0, globals_1.expect)(result.error).toContain('Invalid Ethereum address format');
            // Verify no database calls were made
            (0, globals_1.expect)(mockPrisma.identityVerification.create).not.toHaveBeenCalled();
        });
        (0, globals_1.it)('should throw error for invalid signature', async () => {
            // Arrange
            const mockUser = { id: validVerificationData.userId };
            const mockMerchant = { id: validVerificationData.merchantId };
            mockPrisma.user.findUnique.mockResolvedValue(mockUser);
            mockPrisma.merchant.findUnique.mockResolvedValue(mockMerchant);
            const { ethers } = require('ethers');
            ethers.utils.isAddress.mockReturnValue(true);
            ethers.utils.verifyMessage.mockReturnValue('0xdifferentaddress');
            // Act & Assert
            await (0, globals_1.expect)(service.verifyEthereumSignature(validVerificationData)).rejects.toThrow(IdentityVerificationError_1.IdentityVerificationError);
            (0, globals_1.expect)(mockPrisma.identityVerification.create).not.toHaveBeenCalled();
        });
        (0, globals_1.it)('should throw error when user not found', async () => {
            // Arrange
            mockPrisma.user.findUnique.mockResolvedValue(null);
            const { ethers } = require('ethers');
            ethers.utils.isAddress.mockReturnValue(true);
            // Act & Assert
            await (0, globals_1.expect)(service.verifyEthereumSignature(validVerificationData)).rejects.toThrow(IdentityVerificationError_1.IdentityVerificationError);
            (0, globals_1.expect)(mockPrisma.identityVerification.create).not.toHaveBeenCalled();
        });
        (0, globals_1.it)('should throw error when merchant not found', async () => {
            // Arrange
            const mockUser = { id: validVerificationData.userId };
            mockPrisma.user.findUnique.mockResolvedValue(mockUser);
            mockPrisma.merchant.findUnique.mockResolvedValue(null);
            const { ethers } = require('ethers');
            ethers.utils.isAddress.mockReturnValue(true);
            // Act & Assert
            await (0, globals_1.expect)(service.verifyEthereumSignature(validVerificationData)).rejects.toThrow(IdentityVerificationError_1.IdentityVerificationError);
            (0, globals_1.expect)(mockPrisma.identityVerification.create).not.toHaveBeenCalled();
        });
        (0, globals_1.it)('should handle database errors gracefully', async () => {
            // Arrange
            const mockUser = { id: validVerificationData.userId };
            const mockMerchant = { id: validVerificationData.merchantId };
            mockPrisma.user.findUnique.mockResolvedValue(mockUser);
            mockPrisma.merchant.findUnique.mockResolvedValue(mockMerchant);
            mockPrisma.identityVerification.create.mockRejectedValue(new Error('Database error'));
            const { ethers } = require('ethers');
            ethers.utils.isAddress.mockReturnValue(true);
            ethers.utils.verifyMessage.mockReturnValue(validVerificationData.address);
            // Act & Assert
            await (0, globals_1.expect)(service.verifyEthereumSignature(validVerificationData)).rejects.toThrow(IdentityVerificationError_1.IdentityVerificationError);
        });
    });
    (0, globals_1.describe)('getVerificationById', () => {
        (0, globals_1.it)('should return verification details for valid ID', async () => {
            // Arrange
            const verificationId = 'verification-123-456-789';
            const mockVerification = {
                id: verificationId,
                userId: 'user-123-456-789',
                merchantId: 'merchant-123-456-789',
                method: 'ethereum_signature',
                status: 'verified',
                confidence: 0.95,
                address: '******************************************',
                createdAt: new Date(),
                updatedAt: new Date(),
            };
            mockPrisma.identityVerification.findUnique.mockResolvedValue(mockVerification);
            // Act
            const result = await service.getVerificationById(verificationId);
            // Assert
            (0, globals_1.expect)(result).toEqual(mockVerification);
            (0, globals_1.expect)(mockPrisma.identityVerification.findUnique).toHaveBeenCalledWith({
                where: { id: verificationId },
            });
        });
        (0, globals_1.it)('should throw error for non-existent verification', async () => {
            // Arrange
            const verificationId = 'non-existent-verification-id';
            mockPrisma.identityVerification.findUnique.mockResolvedValue(null);
            // Act & Assert
            await (0, globals_1.expect)(service.getVerificationById(verificationId)).rejects.toThrow(IdentityVerificationError_1.IdentityVerificationError);
        });
    });
    (0, globals_1.describe)('getVerificationsForUser', () => {
        (0, globals_1.it)('should return user verifications', async () => {
            // Arrange
            const userId = 'user-123-456-789';
            const mockVerifications = [
                {
                    id: 'verification-1-123-456',
                    userId,
                    method: 'ethereum_signature',
                    status: 'verified',
                    createdAt: new Date(),
                },
                {
                    id: 'verification-2-123-456',
                    userId,
                    method: 'ethereum_signature',
                    status: 'verified',
                    createdAt: new Date(),
                },
            ];
            mockPrisma.identityVerification.findMany.mockResolvedValue(mockVerifications);
            // Act
            const result = await service.getVerificationsForUser(userId);
            // Assert
            (0, globals_1.expect)(result).toEqual(mockVerifications);
            (0, globals_1.expect)(mockPrisma.identityVerification.findMany).toHaveBeenCalledWith({
                where: { userId },
                include: { claims: true },
                orderBy: { createdAt: 'desc' },
                take: 50,
                skip: 0,
            });
        });
        (0, globals_1.it)('should handle empty results', async () => {
            // Arrange
            const userId = 'empty-user-123-456';
            mockPrisma.identityVerification.findMany.mockResolvedValue([]);
            // Act
            const result = await service.getVerificationsForUser(userId);
            // Assert
            (0, globals_1.expect)(result).toEqual([]);
        });
    });
    (0, globals_1.describe)('getVerificationStats', () => {
        (0, globals_1.it)('should return verification statistics', async () => {
            // Arrange
            const filters = {
                merchantId: 'merchant-stats-123',
                dateFrom: new Date('2024-01-01'),
                dateTo: new Date('2024-01-31'),
            };
            mockPrisma.identityVerification.count
                .mockResolvedValueOnce(100) // total
                .mockResolvedValueOnce(85) // successful
                .mockResolvedValueOnce(5) // failed
                .mockResolvedValueOnce(10); // pending
            mockPrisma.identityVerification.groupBy.mockResolvedValue([
                { method: 'ETHEREUM_SIGNATURE', _count: { method: 50 } },
                { method: 'ERC1484', _count: { method: 30 } },
            ]);
            // Act
            const result = await service.getVerificationStats(filters);
            // Assert
            (0, globals_1.expect)(result).toEqual({
                totalVerifications: 100,
                successfulVerifications: 85,
                failedVerifications: 5,
                pendingVerifications: 10,
                verificationsByMethod: {
                    ETHEREUM_SIGNATURE: 50,
                    ERC1484: 30,
                },
                averageVerificationTime: 5000,
            });
            // Verify database calls
            (0, globals_1.expect)(mockPrisma.identityVerification.count).toHaveBeenCalledTimes(4);
            (0, globals_1.expect)(mockPrisma.identityVerification.groupBy).toHaveBeenCalledTimes(1);
        });
        (0, globals_1.it)('should handle zero verifications', async () => {
            // Arrange
            mockPrisma.identityVerification.count.mockResolvedValue(0);
            mockPrisma.identityVerification.groupBy.mockResolvedValue([]);
            // Act
            const result = await service.getVerificationStats();
            // Assert
            (0, globals_1.expect)(result.totalVerifications).toBe(0);
            (0, globals_1.expect)(result.successfulVerifications).toBe(0);
        });
    });
    (0, globals_1.describe)('Error Handling', () => {
        (0, globals_1.it)('should handle network errors gracefully', async () => {
            // Arrange
            const testData = {
                address: '******************************************',
                message: 'Test message',
                signature: '0xabcdef1234567890',
                userId: 'user-123',
                merchantId: 'merchant-123',
            };
            mockPrisma.user.findUnique.mockRejectedValue(new Error('Network error'));
            // Act & Assert
            const result = await service.verifyEthereumSignature(testData);
            (0, globals_1.expect)(result.success).toBe(false);
            (0, globals_1.expect)(result.error).toContain('Network error');
        });
        (0, globals_1.it)('should handle timeout errors', async () => {
            // Arrange
            const testData = {
                address: '******************************************',
                message: 'Test message',
                signature: '0xabcdef1234567890',
                userId: 'user-123',
                merchantId: 'merchant-123',
            };
            mockPrisma.user.findUnique.mockImplementation(() => new Promise((_, reject) => setTimeout(() => reject(new Error('Timeout')), 100)));
            // Act & Assert
            const result = await service.verifyEthereumSignature(testData);
            (0, globals_1.expect)(result.success).toBe(false);
        });
    });
    (0, globals_1.describe)('Input Validation', () => {
        (0, globals_1.it)('should validate required fields', async () => {
            // Test missing address
            const result1 = await service.verifyEthereumSignature({
                address: '',
                message: 'test',
                signature: '0xtest',
                userId: 'user-validation-123',
                merchantId: 'merchant-validation-123',
            });
            (0, globals_1.expect)(result1.success).toBe(false);
            (0, globals_1.expect)(result1.error).toContain('Address is required');
            // Test missing message
            const result2 = await service.verifyEthereumSignature({
                address: '******************************************',
                message: '',
                signature: '0xtest',
                userId: 'user-validation-123',
                merchantId: 'merchant-validation-123',
            });
            (0, globals_1.expect)(result2.success).toBe(false);
            (0, globals_1.expect)(result2.error).toContain('Message is required');
            // Test missing signature
            const result3 = await service.verifyEthereumSignature({
                address: '******************************************',
                message: 'test',
                signature: '',
                userId: 'user-validation-123',
                merchantId: 'merchant-validation-123',
            });
            (0, globals_1.expect)(result3.success).toBe(false);
            (0, globals_1.expect)(result3.error).toContain('Signature is required');
        });
        (0, globals_1.it)('should validate address format', async () => {
            const result = await service.verifyEthereumSignature({
                address: 'invalid-address',
                message: 'test',
                signature: '0xtest',
                userId: 'user-validation-123',
                merchantId: 'merchant-validation-123',
            });
            (0, globals_1.expect)(result.success).toBe(false);
            (0, globals_1.expect)(result.error).toContain('Invalid Ethereum address format');
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************