/**
 * Verification Monitoring Controller
 *
 * This controller provides endpoints for monitoring the verification system.
 */
import { Request, Response } from 'express';
import { BaseController } from '../base/BaseController';
/**
 * Verification monitoring controller
 */
export declare class VerificationMonitoringController extends BaseController {
    /**
     * Constructor
     */
    constructor();
    /**
     * Get verification metrics
     * @param req Request
     * @param res Response
     */
    getVerificationMetrics(req: Request, res: Response): Promise<Response<any, Record<string, any>>>;
    /**
     * Get verification method metrics
     * @param req Request
     * @param res Response
     */
    getVerificationMethodMetrics(req: Request, res: Response): Promise<Response<any, Record<string, any>>>;
    /**
     * Aggregate metrics
     * @param metrics Metrics
     * @returns Aggregated metrics
     */
    private aggregateMetrics;
    /**
     * Aggregate error distribution
     * @param metrics Metrics
     * @returns Aggregated error distribution
     */
    private aggregateErrorDistribution;
}
//# sourceMappingURL=verification-monitoring.controller.d.ts.map