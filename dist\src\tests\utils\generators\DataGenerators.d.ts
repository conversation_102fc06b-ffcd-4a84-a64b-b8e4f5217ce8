/**
 * Data Generators
 *
 * Functions to generate mock data for testing purposes.
 */
/**
 * Generate a random UUID
 */
export declare function generateUUID(): string;
/**
 * Generate a random email
 */
export declare function generateEmail(domain?: string): string;
/**
 * Generate a random string
 */
export declare function generateRandomString(length?: number, type?: 'alphanumeric' | 'alphabetic' | 'numeric' | 'lowercase' | 'uppercase'): string;
/**
 * Generate a random number
 */
export declare function generateRandomNumber(min?: number, max?: number): number;
/**
 * Generate a random decimal
 */
export declare function generateRandomDecimal(min?: number, max?: number, decimals?: number): number;
/**
 * Generate a random date
 */
export declare function generateRandomDate(start?: Date, end?: Date): Date;
/**
 * Generate a random boolean
 */
export declare function generateRandomBoolean(): boolean;
/**
 * Generate a random array element
 */
export declare function generateRandomArrayElement<T>(array: T[]): T;
/**
 * Generate mock user data
 */
export declare function generateMockUser(overrides?: unknown): unknown;
/**
 * Generate mock merchant data
 */
export declare function generateMockMerchant(overrides?: unknown): unknown;
/**
 * Generate mock transaction data
 */
export declare function generateMockTransaction(overrides?: unknown): unknown;
/**
 * Generate mock payment method data
 */
export declare function generateMockPaymentMethod(overrides?: unknown): unknown;
/**
 * Generate mock subscription data
 */
export declare function generateMockSubscription(overrides?: unknown): unknown;
/**
 * Generate mock notification data
 */
export declare function generateMockNotification(overrides?: unknown): unknown;
/**
 * Generate mock webhook data
 */
export declare function generateMockWebhook(overrides?: unknown): unknown;
/**
 * Generate an array of mock data
 */
export declare function generateMockArray<T>(generator: (overrides?: unknown) => T, count?: number, overrides?: unknown): T[];
/**
 * Generate mock data with relationships
 */
export declare function generateMockDataWithRelationships(): {
    users: unknown[];
    merchants: unknown[];
    transactions: unknown[];
    paymentMethods: unknown[];
};
/**
 * Generate mock data based on schema
 */
export declare function generateMockDataFromSchema(schema: unknown, overrides?: unknown): unknown;
//# sourceMappingURL=DataGenerators.d.ts.map