import { Request, Response } from 'express';
export declare class AdvancedReportController {
    private reportService;
    constructor();
    /**
     * Generate a report
     */
    generateReport: (req: Request, res: Response) => Promise<void>;
    /**
     * Get report templates
     */
    getReportTemplates: (req: Request, res: Response) => Promise<void>;
    /**
     * Get a report template by ID
     */
    getReportTemplateById: (req: Request, res: Response) => Promise<void>;
    /**
     * Create a report template
     */
    createReportTemplate: (req: Request, res: Response) => Promise<void>;
    /**
     * Update a report template
     */
    updateReportTemplate: (req: Request, res: Response) => Promise<void>;
    /**
     * Delete a report template
     */
    deleteReportTemplate: (req: Request, res: Response) => Promise<void>;
    /**
     * Get scheduled reports
     */
    getScheduledReports: (req: Request, res: Response) => Promise<void>;
    /**
     * Get a scheduled report by ID
     */
    getScheduledReportById: (req: Request, res: Response) => Promise<void>;
    /**
     * Create a scheduled report
     */
    createScheduledReport: (req: Request, res: Response) => Promise<void>;
    /**
     * Update a scheduled report
     */
    updateScheduledReport: (req: Request, res: Response) => Promise<void>;
    /**
     * Delete a scheduled report
     */
    deleteScheduledReport: (req: Request, res: Response) => Promise<void>;
    /**
     * Run a scheduled report now
     */
    runScheduledReport: (req: Request, res: Response) => Promise<void>;
    /**
     * Get saved reports
     */
    getSavedReports: (req: Request, res: Response) => Promise<void>;
    /**
     * Get a saved report by ID
     */
    getSavedReportById: (req: Request, res: Response) => Promise<void>;
    /**
     * Delete a saved report
     */
    deleteSavedReport: (req: Request, res: Response) => Promise<void>;
    /**
     * Download a saved report
     */
    downloadSavedReport: (req: Request, res: Response) => Promise<void>;
}
//# sourceMappingURL=advanced-report.controller.d.ts.map