import { Request, Response } from "express";
import { Base<PERSON>ontroller } from "./base.controller";
/**
 * Payment recommendation controller
 */
export declare class PaymentRecommendationController extends BaseController {
    private paymentRecommendationService;
    constructor();
    /**
   * Get payment method recommendations
   */
    getRecommendations: (req: Request, res: Response, next: import("express").NextFunction) => void;
    /**
   * Get payment method recommendation for a specific transaction
   */
    getTransactionRecommendation: (req: Request, res: Response, next: import("express").NextFunction) => void;
    /**
   * Update merchant recommendation weights
   */
    updateRecommendationWeights: (req: Request, res: Response, next: import("express").NextFunction) => void;
    /**
   * Get merchant recommendation weights
   */
    getRecommendationWeights: (req: Request, res: Response, next: import("express").NextFunction) => void;
    /**
   * Validate recommendation weights
   * @param weights Recommendation weights
   * @throws ServiceError if validation fails
   */
    private validateWeights;
}
//# sourceMappingURL=payment-recommendation.controller.d.ts.map