/**
 * Base Response Mapper
 *
 * Centralized response handling to eliminate code duplication across controllers.
 * Provides consistent API responses with proper error handling and logging.
 */
import { Response } from 'express';
import { AppError } from '../../core/errors/AppError';
export interface APIResponse<T = any> {
    success: boolean;
    data?: T;
    message: string;
    pagination?: PaginationInfo;
    timestamp: Date;
    requestId: string;
}
export interface APIErrorResponse {
    success: false;
    error: {
        message: string;
        code?: string;
        type: string;
        details?: any;
    };
    timestamp: Date;
    requestId: string;
}
export interface PaginationInfo {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
}
/**
 * Base Response Mapper - Eliminates duplication across all controllers
 */
export declare class BaseResponseMapper {
    /**
     * Send successful response
     */
    static sendSuccess<T>(res: Response, data: T, message?: string, statusCode?: number, pagination?: PaginationInfo): void;
    /**
     * Send error response
     */
    static sendError(res: Response, error: Error | AppError, statusCode?: number): void;
    /**
     * Send paginated response
     */
    static sendPaginated<T>(res: Response, data: T[], total: number, page: number, limit: number, message?: string): void;
    /**
     * Send created response
     */
    static sendCreated<T>(res: Response, data: T, message?: string): void;
    /**
     * Send updated response
     */
    static sendUpdated<T>(res: Response, data: T, message?: string): void;
    /**
     * Send deleted response
     */
    static sendDeleted(res: Response, message?: string): void;
    /**
     * Send not found response
     */
    static sendNotFound(res: Response, message?: string): void;
    /**
     * Send validation error response
     */
    static sendValidationError(res: Response, details: any, message?: string): void;
    /**
     * Send unauthorized response
     */
    static sendUnauthorized(res: Response, message?: string): void;
    /**
     * Send forbidden response
     */
    static sendForbidden(res: Response, message?: string): void;
    /**
     * Send conflict response
     */
    static sendConflict(res: Response, message?: string): void;
    /**
     * Send rate limit response
     */
    static sendRateLimit(res: Response, message?: string): void;
    /**
     * Send internal server error response
     */
    static sendInternalError(res: Response, message?: string): void;
    /**
     * Send service unavailable response
     */
    static sendServiceUnavailable(res: Response, message?: string): void;
    /**
     * Create pagination info
     */
    static createPagination(page: number, limit: number, total: number): PaginationInfo;
    /**
     * Handle async controller method with automatic error handling
     */
    static handleAsync(res: Response, asyncFn: () => Promise<any>, successMessage?: string, successStatusCode?: number): Promise<void>;
}
//# sourceMappingURL=BaseResponseMapper.d.ts.map