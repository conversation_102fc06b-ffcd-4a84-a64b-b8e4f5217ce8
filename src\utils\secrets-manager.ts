import { logger } from './logger';

export type SecretType = 'database' | 'jwt' | 'api' | 'payment' | 'email' | 'redis' | 'other';

export interface Secret {
  name: string;
  value: string;
  type: SecretType;
}

export class SecretsManager {
  private static instance: SecretsManager;
  private secrets: Map<string, Secret> = new Map();
  private initialized: boolean = false;

  private constructor() {}

  public static getInstance(): SecretsManager {
    if (!SecretsManager.instance) {
      SecretsManager.instance = new SecretsManager();
    }
    return SecretsManager.instance;
  }

  public async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    logger.info('Initializing secrets manager');

    // Try to load from AWS Secrets Manager first
    await this.loadFromAwsSecretsManager();

    // Load from environment variables as fallback or additional source
    await this.loadFromEnvironment();

    this.initialized = true;
    logger.info('Secrets manager initialized');
  }

  public async loadFromEnvironment(): Promise<void> {
    logger.info('Loading secrets from environment variables');

    // Database secrets
    this.addSecret('DB_HOST', process.env.DB_HOST || '', 'database');
    this.addSecret('DB_PORT', process.env.DB_PORT || '', 'database');
    this.addSecret('DB_USERNAME', process.env.DB_USERNAME || '', 'database');
    this.addSecret('DB_PASSWORD', process.env.DB_PASSWORD || '', 'database');
    this.addSecret('DB_NAME', process.env.DB_NAME || '', 'database');
    this.addSecret('DB_SSL', process.env.DB_SSL || '', 'database');
    this.addSecret('DB_CONNECTION_POOL_MIN', process.env.DB_CONNECTION_POOL_MIN || '', 'database');
    this.addSecret('DB_CONNECTION_POOL_MAX', process.env.DB_CONNECTION_POOL_MAX || '', 'database');
    this.addSecret('DB_STATEMENT_TIMEOUT', process.env.DB_STATEMENT_TIMEOUT || '', 'database');

    // JWT secrets
    this.addSecret('JWT_SECRET', process.env.JWT_SECRET || process.env.JWT_SECRET_KEY || '', 'jwt');
    this.addSecret('JWT_SECRET_KEY', process.env.JWT_SECRET_KEY || process.env.JWT_SECRET || '', 'jwt');
    this.addSecret('JWT_EXPIRES_IN', process.env.JWT_EXPIRES_IN || '', 'jwt');
    this.addSecret('JWT_REFRESH_EXPIRES_IN', process.env.JWT_REFRESH_EXPIRES_IN || '', 'jwt');
    this.addSecret('JWT_ALGORITHM', process.env.JWT_ALGORITHM || '', 'jwt');
    this.addSecret('JWT_ISSUER', process.env.JWT_ISSUER || '', 'jwt');
    this.addSecret('JWT_AUDIENCE', process.env.JWT_AUDIENCE || '', 'jwt');

    // API secrets
    this.addSecret('API_KEY', process.env.API_KEY || '', 'api');
    this.addSecret('API_URL', process.env.API_URL || '', 'api');
    this.addSecret('FRONTEND_URL', process.env.FRONTEND_URL || '', 'api');

    // Payment secrets
    this.addSecret('BINANCE_API_KEY', process.env.BINANCE_API_KEY || '', 'payment');
    this.addSecret('BINANCE_API_SECRET', process.env.BINANCE_API_SECRET || '', 'payment');
    this.addSecret('BINANCE_API_URL', process.env.BINANCE_API_URL || '', 'payment');
    this.addSecret('BINANCE_WEBHOOK_SECRET', process.env.BINANCE_WEBHOOK_SECRET || '', 'payment');

    // Email secrets
    this.addSecret('SMTP_HOST', process.env.SMTP_HOST || '', 'email');
    this.addSecret('SMTP_PORT', process.env.SMTP_PORT || '', 'email');
    this.addSecret('SMTP_USER', process.env.SMTP_USER || '', 'email');
    this.addSecret('SMTP_PASSWORD', process.env.SMTP_PASSWORD || '', 'email');
    this.addSecret('SMTP_SECURE', process.env.SMTP_SECURE || '', 'email');
    this.addSecret('EMAIL_FROM', process.env.EMAIL_FROM || '', 'email');
    this.addSecret('EMAIL_FROM_NAME', process.env.EMAIL_FROM_NAME || '', 'email');
    this.addSecret('EMAIL_VERIFICATION_TEMPLATE', process.env.EMAIL_VERIFICATION_TEMPLATE || '', 'email');
    this.addSecret('EMAIL_PAYMENT_TEMPLATE', process.env.EMAIL_PAYMENT_TEMPLATE || '', 'email');
    this.addSecret('EMAIL_ADMIN_ALERT_TEMPLATE', process.env.EMAIL_ADMIN_ALERT_TEMPLATE || '', 'email');

    // Redis secrets
    this.addSecret('REDIS_URL', process.env.REDIS_URL || '', 'redis');
    this.addSecret('REDIS_HOST', process.env.REDIS_HOST || '', 'redis');
    this.addSecret('REDIS_PORT', process.env.REDIS_PORT || '', 'redis');
    this.addSecret('REDIS_PASSWORD', process.env.REDIS_PASSWORD || '', 'redis');
    this.addSecret('REDIS_TLS', process.env.REDIS_TLS || '', 'redis');
  }

  private async loadFromAwsSecretsManager(): Promise<void> {
    logger.info('Loading secrets from AWS Secrets Manager');

    try {
      // AWS Secrets Manager integration would go here
      // For now, we'll just use environment variables
      await this.loadFromEnvironment();
    } catch (error) {
      logger.error('Failed to load secrets from AWS Secrets Manager', error);
      // Fall back to environment variables
      await this.loadFromEnvironment();
    }
  }

  private addSecret(name: string, value: string, type: SecretType): void {
    this.secrets.set(name, { name, value, type });
  }

  public getDatabaseUrl(): string {
    // Check if we have a direct DATABASE_URL environment variable
    if (process.env.DATABASE_URL) {
      return process.env.DATABASE_URL;
    }

    // Otherwise, construct from individual components
    const host = this.getSecret('DB_HOST')?.value || 'localhost';
    const port = this.getSecret('DB_PORT')?.value || '5432';
    const username = this.getSecret('DB_USERNAME')?.value || 'postgres';
    const password = this.getSecret('DB_PASSWORD')?.value || '';
    const database = this.getSecret('DB_NAME')?.value || 'amazingpay';
    const ssl = this.getSecret('DB_SSL')?.value === 'true' ? '?sslmode=require' : '';

    return `postgresql://${username}:${password}@${host}:${port}/${database}${ssl}`;
  }

  public getSecret(name: string): Secret | undefined {
    return this.secrets.get(name);
  }
}

// Export singleton instance
export const secretsManager = SecretsManager.getInstance();