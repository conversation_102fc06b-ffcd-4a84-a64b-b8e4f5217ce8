"use strict";
/**
 * Alert Aggregation Controller
 *
 * Modular controller for alert aggregation operations.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.AlertAggregationController = void 0;
const base_controller_1 = require("../base.controller");
const asyncHandler_1 = require("../../utils/asyncHandler");
const prisma = __importStar(require("../../lib/prisma"));
const AuthorizationService_1 = require("./services/AuthorizationService");
const ValidationService_1 = require("./services/ValidationService");
const AlertAggregationBusinessService_1 = require("./services/AlertAggregationBusinessService");
const ResponseMapper_1 = require("./mappers/ResponseMapper");
/**
 * Modular Alert Aggregation Controller
 */
class AlertAggregationController extends base_controller_1.BaseController {
    constructor() {
        super();
        /**
         * Get all aggregation rules
         */
        this.getAggregationRules = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Authorization
                const authContext = this.authService.createAuthorizationContext(req.user, 'aggregation-rules', 'read');
                const permission = await this.authService.checkPermission(authContext);
                if (!permission.allowed) {
                    this.authService.handleAuthorizationError(permission);
                }
                // Validation
                const pagination = this.validationService.validatePaginationParams(req.query);
                // Build filters
                const filters = {};
                if (req.query.type)
                    filters.type = req.query.type;
                if (req.query.severity)
                    filters.severity = req.query.severity;
                if (req.query.enabled !== undefined)
                    filters.enabled = req.query.enabled === 'true';
                if (req.query.search)
                    filters.search = req.query.search;
                // Business logic
                const result = await this.businessService.getAggregationRules(filters, pagination);
                // Response
                ResponseMapper_1.ResponseMapper.sendAggregationRulesList(res, result.rules, result.total, pagination.page, pagination.limit);
            }
            catch (error) {
                ResponseMapper_1.ResponseMapper.sendError(res, error);
            }
        });
        /**
         * Get a specific aggregation rule
         */
        this.getAggregationRule = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Authorization
                const authContext = this.authService.createAuthorizationContext(req.user, 'aggregation-rules', 'read', req.params.id);
                const permission = await this.authService.checkPermission(authContext);
                if (!permission.allowed) {
                    this.authService.handleAuthorizationError(permission);
                }
                // Validation
                const ruleId = this.validationService.validateId(req.params.id, 'Rule ID');
                // Business logic
                const rule = await this.businessService.getAggregationRule(ruleId);
                // Response
                ResponseMapper_1.ResponseMapper.sendAggregationRule(res, rule);
            }
            catch (error) {
                ResponseMapper_1.ResponseMapper.sendError(res, error);
            }
        });
        /**
         * Create a new aggregation rule
         */
        this.createAggregationRule = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Authorization
                const authContext = this.authService.createAuthorizationContext(req.user, 'aggregation-rules', 'create');
                const permission = await this.authService.checkPermission(authContext);
                if (!permission.allowed) {
                    this.authService.handleAuthorizationError(permission);
                }
                // Validation
                const validatedData = this.validationService.validateCreateAggregationRule(req.body);
                // Business logic
                const rule = await this.businessService.createAggregationRule(validatedData);
                // Response
                ResponseMapper_1.ResponseMapper.sendAggregationRuleCreated(res, rule);
            }
            catch (error) {
                ResponseMapper_1.ResponseMapper.sendError(res, error);
            }
        });
        /**
         * Update an existing aggregation rule
         */
        this.updateAggregationRule = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Authorization
                const authContext = this.authService.createAuthorizationContext(req.user, 'aggregation-rules', 'update', req.params.id);
                const permission = await this.authService.checkPermission(authContext);
                if (!permission.allowed) {
                    this.authService.handleAuthorizationError(permission);
                }
                // Validation
                const ruleId = this.validationService.validateId(req.params.id, 'Rule ID');
                const validatedData = this.validationService.validateUpdateAggregationRule(req.body);
                // Business logic
                const rule = await this.businessService.updateAggregationRule(ruleId, validatedData);
                // Response
                ResponseMapper_1.ResponseMapper.sendAggregationRuleUpdated(res, rule);
            }
            catch (error) {
                ResponseMapper_1.ResponseMapper.sendError(res, error);
            }
        });
        /**
         * Delete an aggregation rule
         */
        this.deleteAggregationRule = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Authorization
                const authContext = this.authService.createAuthorizationContext(req.user, 'aggregation-rules', 'delete', req.params.id);
                const permission = await this.authService.checkPermission(authContext);
                if (!permission.allowed) {
                    this.authService.handleAuthorizationError(permission);
                }
                // Validation
                const ruleId = this.validationService.validateId(req.params.id, 'Rule ID');
                // Business logic
                await this.businessService.deleteAggregationRule(ruleId);
                // Response
                ResponseMapper_1.ResponseMapper.sendAggregationRuleDeleted(res);
            }
            catch (error) {
                ResponseMapper_1.ResponseMapper.sendError(res, error);
            }
        });
        /**
         * Get all correlation rules
         */
        this.getCorrelationRules = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Authorization
                const authContext = this.authService.createAuthorizationContext(req.user, 'correlation-rules', 'read');
                const permission = await this.authService.checkPermission(authContext);
                if (!permission.allowed) {
                    this.authService.handleAuthorizationError(permission);
                }
                // Validation
                const pagination = this.validationService.validatePaginationParams(req.query);
                // Build filters
                const filters = {};
                if (req.query.enabled !== undefined)
                    filters.enabled = req.query.enabled === 'true';
                if (req.query.search)
                    filters.search = req.query.search;
                // Business logic
                const result = await this.businessService.getCorrelationRules(filters, pagination);
                // Response
                ResponseMapper_1.ResponseMapper.sendCorrelationRulesList(res, result.rules, result.total, pagination.page, pagination.limit);
            }
            catch (error) {
                ResponseMapper_1.ResponseMapper.sendError(res, error);
            }
        });
        /**
         * Get a specific correlation rule
         */
        this.getCorrelationRule = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Authorization
                const authContext = this.authService.createAuthorizationContext(req.user, 'correlation-rules', 'read', req.params.id);
                const permission = await this.authService.checkPermission(authContext);
                if (!permission.allowed) {
                    this.authService.handleAuthorizationError(permission);
                }
                // Validation
                const ruleId = this.validationService.validateId(req.params.id, 'Rule ID');
                // Business logic
                const rule = await this.businessService.getCorrelationRule(ruleId);
                // Response
                ResponseMapper_1.ResponseMapper.sendCorrelationRule(res, rule);
            }
            catch (error) {
                ResponseMapper_1.ResponseMapper.sendError(res, error);
            }
        });
        /**
         * Health check endpoint
         */
        this.healthCheck = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                ResponseMapper_1.ResponseMapper.sendSuccess(res, {
                    status: 'healthy',
                    timestamp: new Date(),
                    version: '1.0.0',
                    services: {
                        authorization: 'active',
                        validation: 'active',
                        business: 'active',
                        database: 'connected',
                    },
                }, 'Alert Aggregation Controller is healthy');
            }
            catch (error) {
                ResponseMapper_1.ResponseMapper.sendError(res, error);
            }
        });
        this.authService = new AuthorizationService_1.AuthorizationService();
        this.validationService = new ValidationService_1.ValidationService();
        this.businessService = new AlertAggregationBusinessService_1.AlertAggregationBusinessService(prisma);
    }
}
exports.AlertAggregationController = AlertAggregationController;
//# sourceMappingURL=AlertAggregationController.js.map