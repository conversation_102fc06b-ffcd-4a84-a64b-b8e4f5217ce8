"use strict";
/**
 * Fraud Configuration Validator
 *
 * Focused validator for fraud detection configuration operations.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.FraudConfigValidator = void 0;
const AppError_1 = require("../../../utils/errors/AppError");
const BaseValidator_1 = require("./BaseValidator");
/**
 * Validator for fraud configuration operations
 */
class FraudConfigValidator extends BaseValidator_1.BaseValidator {
    /**
     * Validate fraud configuration update request
     */
    validateUpdateFraudConfig(data) {
        const errors = [];
        if (data.flagThreshold !== undefined) {
            if (typeof data.flagThreshold !== 'number' || data.flagThreshold < 0 || data.flagThreshold > 100) {
                errors.push({ field: 'flagThreshold', message: 'Flag threshold must be a number between 0 and 100', value: data.flagThreshold });
            }
        }
        if (data.blockThreshold !== undefined) {
            if (typeof data.blockThreshold !== 'number' || data.blockThreshold < 0 || data.blockThreshold > 100) {
                errors.push({ field: 'blockThreshold', message: 'Block threshold must be a number between 0 and 100', value: data.blockThreshold });
            }
        }
        if (data.flagThreshold !== undefined && data.blockThreshold !== undefined) {
            if (data.blockThreshold <= data.flagThreshold) {
                errors.push({ field: 'blockThreshold', message: 'Block threshold must be greater than flag threshold' });
            }
        }
        if (data.autoBlock !== undefined && typeof data.autoBlock !== 'boolean') {
            errors.push({ field: 'autoBlock', message: 'Auto block must be a boolean', value: data.autoBlock });
        }
        this.validateFactorWeights(data.factorWeights, errors);
        this.validateHighRiskCountries(data.highRiskCountries, errors);
        this.validateHighRiskIpRanges(data.highRiskIpRanges, errors);
        this.validateTransactionLimits(data, errors);
        if (errors.length > 0) {
            throw new AppError_1.AppError({
                message: 'Validation failed',
                type: AppError_1.ErrorType.VALIDATION,
                code: AppError_1.ErrorCode.INVALID_INPUT,
                details: { errors }
            });
        }
        return this.buildUpdateRequest(data);
    }
    /**
     * Validate factor weights
     */
    validateFactorWeights(factorWeights, errors) {
        if (factorWeights !== undefined) {
            if (typeof factorWeights !== 'object' || factorWeights === null) {
                errors.push({ field: 'factorWeights', message: 'Factor weights must be an object', value: factorWeights });
            }
            else {
                Object.entries(factorWeights).forEach(([key, value]) => {
                    if (typeof value !== 'number' || value < 0 || value > 1) {
                        errors.push({
                            field: `factorWeights.${key}`,
                            message: 'Factor weight must be a number between 0 and 1',
                            value
                        });
                    }
                });
            }
        }
    }
    /**
     * Validate high risk countries
     */
    validateHighRiskCountries(highRiskCountries, errors) {
        if (highRiskCountries !== undefined) {
            if (!Array.isArray(highRiskCountries)) {
                errors.push({ field: 'highRiskCountries', message: 'High risk countries must be an array', value: highRiskCountries });
            }
            else {
                highRiskCountries.forEach((country, index) => {
                    if (typeof country !== 'string' || !this.isValidCountryCode(country)) {
                        errors.push({
                            field: `highRiskCountries[${index}]`,
                            message: 'Invalid country code format',
                            value: country
                        });
                    }
                });
            }
        }
    }
    /**
     * Validate high risk IP ranges
     */
    validateHighRiskIpRanges(highRiskIpRanges, errors) {
        if (highRiskIpRanges !== undefined) {
            if (!Array.isArray(highRiskIpRanges)) {
                errors.push({ field: 'highRiskIpRanges', message: 'High risk IP ranges must be an array', value: highRiskIpRanges });
            }
            else {
                highRiskIpRanges.forEach((ipRange, index) => {
                    if (typeof ipRange !== 'string' || !this.isValidIPRange(ipRange)) {
                        errors.push({
                            field: `highRiskIpRanges[${index}]`,
                            message: 'Invalid IP range format',
                            value: ipRange
                        });
                    }
                });
            }
        }
    }
    /**
     * Validate transaction limits
     */
    validateTransactionLimits(data, errors) {
        if (data.maxTransactionAmount !== undefined) {
            if (typeof data.maxTransactionAmount !== 'number' || data.maxTransactionAmount <= 0) {
                errors.push({ field: 'maxTransactionAmount', message: 'Max transaction amount must be a positive number', value: data.maxTransactionAmount });
            }
        }
        if (data.maxTransactionsPerHour !== undefined) {
            if (typeof data.maxTransactionsPerHour !== 'number' || data.maxTransactionsPerHour <= 0 || !Number.isInteger(data.maxTransactionsPerHour)) {
                errors.push({ field: 'maxTransactionsPerHour', message: 'Max transactions per hour must be a positive integer', value: data.maxTransactionsPerHour });
            }
        }
        if (data.maxTransactionsPerDay !== undefined) {
            if (typeof data.maxTransactionsPerDay !== 'number' || data.maxTransactionsPerDay <= 0 || !Number.isInteger(data.maxTransactionsPerDay)) {
                errors.push({ field: 'maxTransactionsPerDay', message: 'Max transactions per day must be a positive integer', value: data.maxTransactionsPerDay });
            }
        }
    }
    /**
     * Build update request object
     */
    buildUpdateRequest(data) {
        const result = {};
        if (data.flagThreshold !== undefined)
            result.flagThreshold = data.flagThreshold;
        if (data.blockThreshold !== undefined)
            result.blockThreshold = data.blockThreshold;
        if (data.autoBlock !== undefined)
            result.autoBlock = data.autoBlock;
        if (data.factorWeights !== undefined)
            result.factorWeights = data.factorWeights;
        if (data.highRiskCountries !== undefined)
            result.highRiskCountries = data.highRiskCountries;
        if (data.highRiskIpRanges !== undefined)
            result.highRiskIpRanges = data.highRiskIpRanges;
        if (data.maxTransactionAmount !== undefined)
            result.maxTransactionAmount = data.maxTransactionAmount;
        if (data.maxTransactionsPerHour !== undefined)
            result.maxTransactionsPerHour = data.maxTransactionsPerHour;
        if (data.maxTransactionsPerDay !== undefined)
            result.maxTransactionsPerDay = data.maxTransactionsPerDay;
        return result;
    }
}
exports.FraudConfigValidator = FraudConfigValidator;
//# sourceMappingURL=FraudConfigValidator.js.map