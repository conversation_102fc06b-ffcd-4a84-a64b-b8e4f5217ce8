import { BaseController } from "./base.controller";
declare global {
    namespace Express {
        interface Request {
            user?: {
                id: string;
                role: string;
                merchantId?: string;
            };
        }
    }
}
/**
 * WebhookController
 * Controller for handling webhook operations
 */
export declare class WebhookController extends BaseController {
    constructor();
    /**
     * Get webhooks for the authenticated merchant
     */
    getWebhooks: any;
    /**
     * Get a single webhook by ID
     */
    getWebhook: any;
    /**
     * Retry a failed webhook
     */
    retryWebhook: any;
    /**
     * Update the webhook URL for the authenticated merchant
     */
    updateWebhookUrl: any;
    /**
     * Test the webhook configuration for the authenticated merchant
     */
    testWebhook: any;
}
declare const _default: WebhookController;
export default _default;
//# sourceMappingURL=webhook.controller.d.ts.map