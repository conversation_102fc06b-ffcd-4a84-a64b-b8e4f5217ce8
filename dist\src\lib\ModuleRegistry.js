"use strict";
// jscpd:ignore-file
/**
 * Module Registry
 *
 * A central registry for module configurations.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.moduleRegistry = exports.ModuleRegistry = void 0;
const logger_1 = require("./logger");
const EventBus_1 = require("./EventBus");
/**
 * Module registry
 */
class ModuleRegistry {
    constructor() {
        this.modules = new Map();
    }
    /**
   * Get the singleton instance
   */
    static getInstance() {
        if (!ModuleRegistry.instance) {
            ModuleRegistry.instance = new ModuleRegistry();
        }
        return ModuleRegistry.instance;
    }
    /**
   * Register a module
   *
   * @param name Module name
   * @param config Module configuration
   */
    registerModule(name, config) {
        // Check dependencies
        if (config.dependencies) {
            for (const dependency of config.dependencies) {
                if (!this.modules.has(dependency)) {
                    logger_1.logger.warn(`Module ${name} depends on ${dependency}, but it is not registered`);
                }
            }
        }
        // Register module
        this.modules.set(name, config);
        // Emit event
        EventBus_1.eventBus.emit("module.registered", {
            name,
            config
        });
        logger_1.logger.info(`Registered module: ${name}`, {
            enabled: config.enabled,
            version: config.version,
            dependencies: config.dependencies
        });
    }
    /**
   * Get a module configuration
   *
   * @param name Module name
   * @returns Module configuration or undefined if not found
   */
    getModule(name) {
        return this.modules.get(name);
    }
    /**
   * Check if a module is registered
   *
   * @param name Module name
   * @returns True if module is registered
   */
    hasModule(name) {
        return this.modules.has(name);
    }
    /**
   * Check if a module is enabled
   *
   * @param name Module name
   * @returns True if module is enabled, false if disabled or not found
   */
    isModuleEnabled(name) {
        const module = this.modules.get(name);
        return module ? module?.enabled : false;
    }
    /**
   * Enable a module
   *
   * @param name Module name
   * @returns True if module was enabled, false if not found
   */
    enableModule(name) {
        const module = this.modules.get(name);
        if (!module) {
            return false;
        }
        module?.enabled = true;
        this.modules.set(name, module);
        // Emit event
        EventBus_1.eventBus.emit("module.enabled", {
            name,
            config: module
        });
        logger_1.logger.info(`Enabled module: ${name}`);
        return true;
    }
    /**
   * Disable a module
   *
   * @param name Module name
   * @returns True if module was disabled, false if not found
   */
    disableModule(name) {
        const module = this.modules.get(name);
        if (!module) {
            return false;
        }
        module?.enabled = false;
        this.modules.set(name, module);
        // Emit event
        EventBus_1.eventBus.emit("module.disabled", {
            name,
            config: module
        });
        logger_1.logger.info(`Disabled module: ${name}`);
        return true;
    }
    /**
   * Update module configuration
   *
   * @param name Module name
   * @param config New configuration (partial)
   * @returns True if module was updated, false if not found
   */
    updateModuleConfig(name, config) {
        const module = this.modules.get(name);
        if (!module) {
            return false;
        }
        // Update configuration
        const updatedConfig = {
            ...module,
            ...config,
            config: {
                ...module.config,
                ...(config.config || {})
            }
        };
        this.modules.set(name, updatedConfig);
        // Emit event
        EventBus_1.eventBus.emit("module.updated", {
            name,
            config: updatedConfig
        });
        logger_1.logger.info(`Updated module configuration: ${name}`);
        return true;
    }
    /**
   * Get all registered modules
   *
   * @returns Object with module names as keys and configurations as values
   */
    getAllModules() {
        const modules = {};
        // Use Array.from to convert the Map entries to an array
        Array.from(this.modules.entries()).forEach(([name, config]) => {
            modules[name] = config;
        });
        return modules;
    }
    /**
   * Get enabled modules
   *
   * @returns Object with module names as keys and configurations as values
   */
    getEnabledModules() {
        const modules = {};
        // Use Array.from to convert the Map entries to an array
        Array.from(this.modules.entries()).forEach(([name, config]) => {
            if (config.enabled) {
                modules[name] = config;
            }
        });
        return modules;
    }
    /**
   * Check if all dependencies of a module are enabled
   *
   * @param name Module name
   * @returns True if all dependencies are enabled, false otherwise
   */
    areDependenciesSatisfied(name) {
        const module = this.modules.get(name);
        if (!module || !module?.dependencies) {
            return true;
        }
        return module?.dependencies.every(dependency => this.isModuleEnabled(dependency));
    }
}
exports.ModuleRegistry = ModuleRegistry;
// Export singleton instance
exports.moduleRegistry = ModuleRegistry.getInstance();
//# sourceMappingURL=ModuleRegistry.js.map