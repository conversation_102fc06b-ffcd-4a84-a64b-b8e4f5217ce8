// jscpd:ignore-file
import express from "express";
import cacheController from "../controllers/cache.controller";
import { authenticateJWT, isAdmin } from '../middlewares/auth';

const router: any =express.Router();

/**
 * @route   GET /api/cache/status
 * @desc    Get cache status
 * @access  Admin
 */
router.get(
    "/status",
    authenticateJWT,
    isAdmin,
    cacheController.getStatus
);

/**
 * @route   POST /api/cache/enable
 * @desc    Enable cache
 * @access  Admin
 */
router.post(
    "/enable",
    authenticateJWT,
    isAdmin,
    cacheController.enable
);

/**
 * @route   POST /api/cache/disable
 * @desc    Disable cache
 * @access  Admin
 */
router.post(
    "/disable",
    authenticateJWT,
    isAdmin,
    cacheController.disable
);

/**
 * @route   POST /api/cache/clear
 * @desc    Clear cache
 * @access  Admin
 */
router.post(
    "/clear",
    authenticateJWT,
    isAdmin,
    cacheController.clear
);

export default router;
