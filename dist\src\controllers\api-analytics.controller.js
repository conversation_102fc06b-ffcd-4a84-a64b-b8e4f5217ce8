"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApiAnalyticsController = void 0;
const BaseController_1 = require("../../core/BaseController");
const ApiAnalyticsService_1 = require("../../services/analytics/ApiAnalyticsService");
/**
 * API analytics controller
 * This controller handles API analytics requests
 */
class ApiAnalyticsController extends BaseController_1.BaseController {
    /**
     * Create a new API analytics controller
     */
    constructor() {
        super();
        /**
         * Get API analytics
         */
        this.getAnalytics = this.createHandler(async (req, res) => {
            // Check authorization
            const { userRole } = this.checkAuthorization(req);
            // Only admins can access analytics
            if (userRole !== "ADMIN") {
                return this.sendError(res, 403, "Only admins can access API analytics");
            }
            // Parse query parameters
            const startDate = req.query.startDate ? new Date(req.query.startDate) : undefined;
            const endDate = req.query.endDate ? new Date(req.query.endDate) : undefined;
            const path = req.query.path;
            const method = req.query.method;
            const statusCode = req.query.statusCode ? parseInt(req.query.statusCode) : undefined;
            const userId = req.query.id; // Fixed: using id instead of userId as string;
            const userRole = req.query.userRole;
            const apiVersion = req.query.apiVersion;
            const limit = req.query.limit ? parseInt(req.query.limit) : 100;
            const offset = req.query.offset ? parseInt(req.query.offset) : 0;
            // Get analytics
            const analytics = await this.apiAnalyticsService.getAnalytics({
                startDate,
                endDate,
                path,
                method,
                statusCode,
                userId,
                userRole,
                apiVersion,
                limit,
                offset
            });
            // Send paginated response
            return this.sendPaginatedSuccess(res, analytics.data, analytics.total, limit, offset);
        });
        /**
         * Get API analytics summary
         */
        this.getAnalyticsSummary = this.createHandler(async (req, res) => {
            // Check authorization
            const { userRole } = this.checkAuthorization(req);
            // Only admins can access analytics
            if (userRole !== "ADMIN") {
                return this.sendError(res, 403, "Only admins can access API analytics");
            }
            // Parse query parameters
            const startDate = req.query.startDate ? new Date(req.query.startDate) : undefined;
            const endDate = req.query.endDate ? new Date(req.query.endDate) : undefined;
            const path = req.query.path;
            const method = req.query.method;
            const statusCode = req.query.statusCode ? parseInt(req.query.statusCode) : undefined;
            const userId = req.query.id; // Fixed: using id instead of userId as string;
            const userRole = req.query.userRole;
            const apiVersion = req.query.apiVersion;
            // Get analytics summary
            const summary = await this.apiAnalyticsService.getAnalyticsSummary({
                startDate,
                endDate,
                path,
                method,
                statusCode,
                userId,
                userRole,
                apiVersion
            });
            // Send success response
            return this.sendSuccess(res, summary);
        });
        /**
         * Get API analytics by version
         */
        this.getAnalyticsByVersion = this.createHandler(async (req, res) => {
            // Check authorization
            const { userRole } = this.checkAuthorization(req);
            // Only admins can access analytics
            if (userRole !== "ADMIN") {
                return this.sendError(res, 403, "Only admins can access API analytics");
            }
            // Get version from params
            const { version } = req.params;
            // Parse query parameters
            const startDate = req.query.startDate ? new Date(req.query.startDate) : undefined;
            const endDate = req.query.endDate ? new Date(req.query.endDate) : undefined;
            const limit = req.query.limit ? parseInt(req.query.limit) : 100;
            const offset = req.query.offset ? parseInt(req.query.offset) : 0;
            // Get analytics
            const analytics = await this.apiAnalyticsService.getAnalytics({
                startDate,
                endDate,
                apiVersion: version,
                limit,
                offset
            });
            // Send paginated response
            return this.sendPaginatedSuccess(res, analytics.data, analytics.total, limit, offset);
        });
        /**
         * Get API analytics summary by version
         */
        this.getAnalyticsSummaryByVersion = this.createHandler(async (req, res) => {
            // Check authorization
            const { userRole } = this.checkAuthorization(req);
            // Only admins can access analytics
            if (userRole !== "ADMIN") {
                return this.sendError(res, 403, "Only admins can access API analytics");
            }
            // Get version from params
            const { version } = req.params;
            // Parse query parameters
            const startDate = req.query.startDate ? new Date(req.query.startDate) : undefined;
            const endDate = req.query.endDate ? new Date(req.query.endDate) : undefined;
            // Get analytics summary
            const summary = await this.apiAnalyticsService.getAnalyticsSummary({
                startDate,
                endDate,
                apiVersion: version
            });
            // Send success response
            return this.sendSuccess(res, summary);
        });
        this.apiAnalyticsService = ApiAnalyticsService_1.ApiAnalyticsService.getInstance();
    }
}
exports.ApiAnalyticsController = ApiAnalyticsController;
exports.default = ApiAnalyticsController;
//# sourceMappingURL=api-analytics.controller.js.map