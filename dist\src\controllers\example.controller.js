"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExampleController = void 0;
const base_controller_1 = require("./base.controller");
const example_service_1 = require("../services/example.service");
/**
 * Example controller
 */
class ExampleController extends base_controller_1.BaseController {
    constructor() {
        super();
        /**
       * Get all examples
       */
        this.getAll = this.asyncHandler(async (req, res) => {
            try {
                // Get pagination parameters
                const { skip, take, page, limit } = this.getPaginationParams(req);
                // Get sort parameters
                const { field, order } = this.getSortParams(req);
                // Get filter parameters
                const filters = this.getFilterParams(req, ["status", "type"]);
                // Get search parameters
                const search = this.getSearchParams(req, ["name", "description"]);
                // Get examples
                const { examples, total } = await this.exampleService.getAll({
                    skip,
                    take,
                    orderBy: { [field]: order },
                    where: {
                        ...filters,
                        ...search
                    }
                });
                // Create pagination info
                const pagination = this.createPaginationInfo(req, total);
                // Send paginated response
                this.sendPaginated(res, examples, pagination, "Examples retrieved successfully");
            }
            catch (error) {
                this.sendError(res, error);
            }
        });
        /**
       * Get example by ID
       */
        this.getById = this.asyncHandler(async (req, res) => {
            try {
                const id = req.params.id;
                // Get example
                const example = await this.exampleService.getById(id);
                // Send success response
                this.sendSuccess(res, example, "Example retrieved successfully");
            }
            catch (error) {
                this.sendError(res, error);
            }
        });
        /**
       * Create example
       */
        this.create = this.asyncHandler(async (req, res) => {
            try {
                const data = req.body;
                // Create example
                const example = await this.exampleService.create(data);
                // Send success response
                this.sendSuccess(res, example, "Example created successfully");
            }
            catch (error) {
                this.sendError(res, error);
            }
        });
        /**
       * Update example
       */
        this.update = this.asyncHandler(async (req, res) => {
            try {
                const id = req.params.id;
                const data = req.body;
                // Update example
                const example = await this.exampleService.update(id, data);
                // Send success response
                this.sendSuccess(res, example, "Example updated successfully");
            }
            catch (error) {
                this.sendError(res, error);
            }
        });
        /**
       * Delete example
       */
        this.delete = this.asyncHandler(async (req, res) => {
            try {
                const id = req.params.id;
                // Delete example
                await this.exampleService.delete(id);
                // Send success response
                this.sendSuccess(res, null, "Example deleted successfully");
            }
            catch (error) {
                this.sendError(res, error);
            }
        });
        this.exampleService = new example_service_1.ExampleService();
    }
}
exports.ExampleController = ExampleController;
//# sourceMappingURL=example.controller.js.map