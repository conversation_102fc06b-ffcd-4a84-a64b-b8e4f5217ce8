/**
 * Verification Optimization Controller
 *
 * This controller provides endpoints for optimizing verification performance.
 */
import { Request, Response } from "express";
/**
 * Verification optimization controller
 */
export declare class VerificationOptimizationController {
    private optimizationService;
    /**
   * Constructor
   */
    constructor();
    /**
   * Analyze verification performance
   * @param req Request
   * @param res Response
   */
    analyzePerformance(req: Request, res: Response): Promise<void>;
    /**
   * Generate optimization recommendations
   * @param req Request
   * @param res Response
   */
    generateRecommendations(req: Request, res: Response): Promise<void>;
    /**
   * Apply optimization recommendations
   * @param req Request
   * @param res Response
   */
    applyRecommendations(req: Request, res: Response): Promise<void>;
}
//# sourceMappingURL=verification-optimization.controller.d.ts.map