/**
 * Admin Validation Service
 *
 * Handles input validation for admin operations.
 */
import { CreateAdminUserRequest, UpdateAdminUserRequest, CreateRoleRequest, UpdateRoleRequest, CreatePermissionRequest } from '../types/AdminControllerTypes';
/**
 * Validation service for admin operations
 */
export declare class AdminValidationService {
    /**
     * Validate admin user creation request
     */
    validateCreateAdminUser(data: any): CreateAdminUserRequest;
    /**
     * Validate admin user update request
     */
    validateUpdateAdminUser(data: any): UpdateAdminUserRequest;
    /**
     * Validate role creation request
     */
    validateCreateRole(data: any): CreateRoleRequest;
    /**
     * Validate role update request
     */
    validateUpdateRole(data: any): UpdateRoleRequest;
    /**
     * Validate permission creation request
     */
    validateCreatePermission(data: any): CreatePermissionRequest;
    /**
     * Validate ID parameter
     */
    validateId(id: any, fieldName?: string): string;
    /**
     * Validate pagination parameters
     */
    validatePaginationParams(query: any): {
        page: number;
        limit: number;
        sortBy?: string;
        sortOrder?: 'asc' | 'desc';
    };
    /**
     * Check if string is a valid email
     */
    private isValidEmail;
    /**
     * Check if string is a valid password
     */
    private isValidPassword;
    /**
     * Check if string is a valid UUID
     */
    private isValidUUID;
}
//# sourceMappingURL=AdminValidationService.d.ts.map