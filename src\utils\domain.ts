// jscpd:ignore-file
/**
 * Domain Utility
 * Re-exports from shared DomainUtils to eliminate duplication
 */

import { DomainUtils } from '../utils';
import { getEnvironment } from "../config/environment";
import { getEnvironment } from "../config/environment";

/**
 * Get the base site domain
 * @returns Base site domain
 */
export const getBaseDomain: unknown =(): string => {
    return DomainUtils.getBaseDomain(getEnvironment());
};

/**
 * Get site domain
 * @returns Production site domain
 */
export const getSiteDomain: unknown =(): string => {
    return DomainUtils.getSiteDomain(getEnvironment());
};

/**
 * Get API domain
 * @returns Production API domain
 */
export const getApiDomain: unknown =(): string => {
    return DomainUtils.getApiDomain(getEnvironment());
};

/**
 * Get admin domain
 * @returns Production admin domain
 */
export const getAdminDomain: unknown =(): string => {
    return DomainUtils.getAdminDomain(getEnvironment());
};

/**
 * Get WebSocket domain
 * @returns Production WebSocket domain
 */
export const getWebSocketDomain: unknown =(): string => {
    return DomainUtils.getWebSocketDomain(getEnvironment());
};

/**
 * Check if a domain is valid for production
 * @param domain Domain to check
 * @returns True if the domain is valid for the environment
 */
export const isValidDomainForEnvironment: unknown =(domain: string): boolean => {
    return DomainUtils.isValidDomainForEnvironment(domain, getEnvironment());
};

export default {
    getBaseDomain,
    getSiteDomain,
    getApiDomain,
    getAdminDomain,
    getWebSocketDomain,
    isValidDomainForEnvironment
};
