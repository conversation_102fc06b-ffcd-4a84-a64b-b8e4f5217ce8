"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const logger_1 = require("../utils/logger");
const payment_monitoring_service_1 = __importStar(require("../services/monitoring/payment-monitoring.service"));
/**
 * Monitoring controller
 */
const monitoringController = {
    /**
   * Get monitoring metrics
   */
    getMetrics: async (req, res) => {
        try {
            const metrics = payment_monitoring_service_1.default.getMetrics();
            return res.status(200).json(metrics);
        }
        catch (error) {
            logger_1.logger.error("Error getting monitoring metrics:", error);
            return res.status(500).json({ error: "Failed to get monitoring metrics" });
        }
    },
    /**
   * Get alerts
   */
    getAlerts: async (req, res) => {
        try {
            const includeResolved = req.query.includeResolved === "true";
            const alerts = payment_monitoring_service_1.default.getAlerts(includeResolved);
            return res.status(200).json(alerts);
        }
        catch (error) {
            logger_1.logger.error("Error getting alerts:", error);
            return res.status(500).json({ error: "Failed to get alerts" });
        }
    },
    /**
   * Resolve alert
   */
    resolveAlert: async (req, res) => {
        try {
            const { index } = req.params;
            const { resolution } = req.body;
            payment_monitoring_service_1.default.resolveAlert(parseInt(index), resolution);
            return res.status(200).json({ success: true });
        }
        catch (error) {
            logger_1.logger.error("Error resolving alert:", error);
            return res.status(500).json({ error: "Failed to resolve alert" });
        }
    },
    /**
   * Create alert
   */
    createAlert: async (req, res) => {
        try {
            const { level, message, data } = req.body;
            if (!level || !message) {
                return res.status(400).json({ error: "Level and message are required" });
            }
            // Validate alert level
            if (!Object.values(payment_monitoring_service_1.AlertLevel).includes(level)) {
                return res.status(400).json({ error: "Invalid alert level" });
            }
            // Emit alert event
            payment_monitoring_service_1.monitoringEvents.emit(payment_monitoring_service_1.MonitoringEventType.ALERT, {
                type: payment_monitoring_service_1.MonitoringEventType.ALERT,
                timestamp: new Date().toISOString(),
                data: { level, message, ...data }
            });
            return res.status(201).json({ success: true });
        }
        catch (error) {
            logger_1.logger.error("Error creating alert:", error);
            return res.status(500).json({ error: "Failed to create alert" });
        }
    },
    /**
   * Reset metrics
   */
    resetMetrics: async (req, res) => {
        try {
            payment_monitoring_service_1.default.resetMetrics();
            return res.status(200).json({ success: true });
        }
        catch (error) {
            logger_1.logger.error("Error resetting metrics:", error);
            return res.status(500).json({ error: "Failed to reset metrics" });
        }
    },
    /**
   * Emit monitoring event
   */
    emitEvent: async (req, res) => {
        try {
            const { type, data, merchantId, paymentId } = req.body;
            if (!type || !Object.values(payment_monitoring_service_1.MonitoringEventType).includes(type)) {
                return res.status(400).json({ error: "Invalid event type" });
            }
            // Emit event
            payment_monitoring_service_1.monitoringEvents.emit(type, {
                type,
                timestamp: new Date().toISOString(),
                data: data || {},
                merchantId,
                paymentId
            });
            return res.status(200).json({ success: true });
        }
        catch (error) {
            logger_1.logger.error("Error emitting monitoring event:", error);
            return res.status(500).json({ error: "Failed to emit monitoring event" });
        }
    }
};
exports.default = monitoringController;
//# sourceMappingURL=monitoring.controller.js.map