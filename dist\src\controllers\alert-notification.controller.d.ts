import { BaseController } from "./base.controller";
declare global {
    namespace Express {
        interface Request {
            user?: {
                id: string;
                role: string;
                merchantId?: string;
            };
        }
    }
}
/**
 * AlertNotificationController
 * Controller for handling alert notifications
 */
export declare class AlertNotificationController extends BaseController {
    constructor();
    /**
     * Get notifications for a user
     */
    getNotifications: any;
}
declare const _default: AlertNotificationController;
export default _default;
//# sourceMappingURL=alert-notification.controller.d.ts.map