{"file": "F:\\Amazing pay flow\\src\\tests\\advanced-report.controller.test.ts", "mappings": ";;;;;AAAA,mCAAyE;AACzE,0DAAgC;AAChC,sDAA8B;AAC9B,0FAAqF;AACrF,iFAA4E;AAE5E,mBAAmB;AACnB,WAAE,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;AAE/C,MAAM,GAAG,GAAG,IAAA,iBAAO,GAAE,CAAC;AACtB,GAAG,CAAC,GAAG,CAAC,iBAAO,CAAC,IAAI,EAAE,CAAC,CAAC;AAExB,uBAAuB;AACvB,MAAM,kBAAkB,GAAG,CAAC,GAAQ,EAAE,GAAQ,EAAE,IAAS,EAAE,EAAE;IAC3D,GAAG,CAAC,IAAI,GAAG;QACT,EAAE,EAAE,QAAQ;QACZ,IAAI,EAAE,UAAU;QAChB,KAAK,EAAE,kBAAkB;KAC1B,CAAC;IACF,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AAEF,eAAe;AACf,MAAM,gBAAgB,GAAG,IAAI,qDAAwB,EAAE,CAAC;AACxD,GAAG,CAAC,IAAI,CAAC,uBAAuB,EAAE,kBAAkB,EAAE,gBAAgB,CAAC,cAAc,CAAC,CAAC;AACvF,GAAG,CAAC,GAAG,CAAC,wBAAwB,EAAE,kBAAkB,EAAE,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;AAC3F,GAAG,CAAC,GAAG,CAAC,4BAA4B,EAAE,kBAAkB,EAAE,gBAAgB,CAAC,qBAAqB,CAAC,CAAC;AAClG,GAAG,CAAC,IAAI,CAAC,wBAAwB,EAAE,kBAAkB,EAAE,gBAAgB,CAAC,oBAAoB,CAAC,CAAC;AAC9F,GAAG,CAAC,GAAG,CAAC,4BAA4B,EAAE,kBAAkB,EAAE,gBAAgB,CAAC,oBAAoB,CAAC,CAAC;AACjG,GAAG,CAAC,MAAM,CAAC,4BAA4B,EAAE,kBAAkB,EAAE,gBAAgB,CAAC,oBAAoB,CAAC,CAAC;AACpG,GAAG,CAAC,GAAG,CAAC,wBAAwB,EAAE,kBAAkB,EAAE,gBAAgB,CAAC,mBAAmB,CAAC,CAAC;AAC5F,GAAG,CAAC,GAAG,CAAC,4BAA4B,EAAE,kBAAkB,EAAE,gBAAgB,CAAC,sBAAsB,CAAC,CAAC;AACnG,GAAG,CAAC,IAAI,CAAC,wBAAwB,EAAE,kBAAkB,EAAE,gBAAgB,CAAC,qBAAqB,CAAC,CAAC;AAC/F,GAAG,CAAC,GAAG,CAAC,4BAA4B,EAAE,kBAAkB,EAAE,gBAAgB,CAAC,qBAAqB,CAAC,CAAC;AAClG,GAAG,CAAC,MAAM,CACR,4BAA4B,EAC5B,kBAAkB,EAClB,gBAAgB,CAAC,qBAAqB,CACvC,CAAC;AACF,GAAG,CAAC,IAAI,CAAC,gCAAgC,EAAE,kBAAkB,EAAE,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;AACpG,GAAG,CAAC,GAAG,CAAC,oBAAoB,EAAE,kBAAkB,EAAE,gBAAgB,CAAC,eAAe,CAAC,CAAC;AACpF,GAAG,CAAC,GAAG,CAAC,wBAAwB,EAAE,kBAAkB,EAAE,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;AAC3F,GAAG,CAAC,MAAM,CAAC,wBAAwB,EAAE,kBAAkB,EAAE,gBAAgB,CAAC,iBAAiB,CAAC,CAAC;AAE7F,IAAA,iBAAQ,EAAC,0BAA0B,EAAE,GAAG,EAAE;IACxC,IAAI,iBAAsB,CAAC;IAE3B,IAAA,mBAAU,EAAC,GAAG,EAAE;QACd,iBAAiB,GAAG;YAClB,cAAc,EAAE,WAAE,CAAC,EAAE,EAAE;YACvB,kBAAkB,EAAE,WAAE,CAAC,EAAE,EAAE;YAC3B,qBAAqB,EAAE,WAAE,CAAC,EAAE,EAAE;YAC9B,oBAAoB,EAAE,WAAE,CAAC,EAAE,EAAE;YAC7B,oBAAoB,EAAE,WAAE,CAAC,EAAE,EAAE;YAC7B,oBAAoB,EAAE,WAAE,CAAC,EAAE,EAAE;YAC7B,mBAAmB,EAAE,WAAE,CAAC,EAAE,EAAE;YAC5B,sBAAsB,EAAE,WAAE,CAAC,EAAE,EAAE;YAC/B,qBAAqB,EAAE,WAAE,CAAC,EAAE,EAAE;YAC9B,qBAAqB,EAAE,WAAE,CAAC,EAAE,EAAE;YAC9B,qBAAqB,EAAE,WAAE,CAAC,EAAE,EAAE;YAC9B,kBAAkB,EAAE,WAAE,CAAC,EAAE,EAAE;YAC3B,eAAe,EAAE,WAAE,CAAC,EAAE,EAAE;YACxB,kBAAkB,EAAE,WAAE,CAAC,EAAE,EAAE;YAC3B,iBAAiB,EAAE,WAAE,CAAC,EAAE,EAAE;SAC3B,CAAC;QAEF,4BAA4B;QAC5B,WAAE,CAAC,MAAM,CAAC,+CAAqB,CAAC,CAAC,kBAAkB,CAAC,GAAG,EAAE,CAAC,iBAAiB,CAAC,CAAC;IAC/E,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAS,EAAC,GAAG,EAAE;QACb,WAAE,CAAC,aAAa,EAAE,CAAC;IACrB,CAAC,CAAC,CAAC;IAEH,IAAA,iBAAQ,EAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,IAAA,WAAE,EAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,MAAM,UAAU,GAAG;gBACjB,EAAE,EAAE,UAAU;gBACd,QAAQ,EAAE,qBAAqB;gBAC/B,MAAM,EAAE,KAAK;aACd,CAAC;YAEF,iBAAiB,CAAC,cAAc,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;YAE/D,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,IAAI,CAAC;gBACrE,IAAI,EAAE,aAAa;gBACnB,MAAM,EAAE,KAAK;gBACb,SAAS,EAAE,YAAY;gBACvB,OAAO,EAAE,YAAY;aACtB,CAAC,CAAC;YAEH,IAAA,eAAM,EAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAClC,IAAA,eAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,IAAA,eAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YAC/C,IAAA,eAAM,EAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC,oBAAoB,CAC3D,aAAa,EACb;gBACE,SAAS,EAAE,YAAY;gBACvB,OAAO,EAAE,YAAY;gBACrB,MAAM,EAAE,QAAQ;gBAChB,QAAQ,EAAE,UAAU;aACrB,EACD,KAAK,CACN,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,IAAA,WAAE,EAAC,8BAA8B,EAAE,KAAK,IAAI,EAAE;YAC5C,iBAAiB,CAAC,cAAc,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC;YAE/E,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,IAAI,CAAC;gBACrE,IAAI,EAAE,aAAa;gBACnB,MAAM,EAAE,KAAK;aACd,CAAC,CAAC;YAEH,IAAA,eAAM,EAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAClC,IAAA,eAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1C,IAAA,eAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,iBAAQ,EAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,IAAA,WAAE,EAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,MAAM,aAAa,GAAG;gBACpB;oBACE,EAAE,EAAE,YAAY;oBAChB,IAAI,EAAE,eAAe;oBACrB,IAAI,EAAE,aAAa;iBACpB;aACF,CAAC;YAEF,iBAAiB,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;YAEtE,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;YAElE,IAAA,eAAM,EAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAClC,IAAA,eAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,IAAA,eAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;YAClD,IAAA,eAAM,EAAC,iBAAiB,CAAC,kBAAkB,CAAC,CAAC,oBAAoB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QACpF,CAAC,CAAC,CAAC;QAEH,IAAA,WAAE,EAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,MAAM,aAAa,GAAG;gBACpB;oBACE,EAAE,EAAE,YAAY;oBAChB,IAAI,EAAE,eAAe;oBACrB,IAAI,EAAE,aAAa;iBACpB;aACF,CAAC;YAEF,iBAAiB,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;YAEtE,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC;iBAChC,GAAG,CAAC,wBAAwB,CAAC;iBAC7B,KAAK,CAAC,EAAE,aAAa,EAAE,OAAO,EAAE,CAAC,CAAC;YAErC,IAAA,eAAM,EAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAClC,IAAA,eAAM,EAAC,iBAAiB,CAAC,kBAAkB,CAAC,CAAC,oBAAoB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;QACrF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,iBAAQ,EAAC,6BAA6B,EAAE,GAAG,EAAE;QAC3C,IAAA,WAAE,EAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;YAC5D,MAAM,YAAY,GAAG;gBACnB,IAAI,EAAE,cAAc;gBACpB,WAAW,EAAE,kBAAkB;gBAC/B,IAAI,EAAE,aAAa;gBACnB,MAAM,EAAE,EAAE,OAAO,EAAE,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE;aACtC,CAAC;YAEF,MAAM,YAAY,GAAG;gBACnB,EAAE,EAAE,YAAY;gBAChB,GAAG,YAAY;gBACf,WAAW,EAAE,QAAQ;aACtB,CAAC;YAEF,iBAAiB,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;YAEvE,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAEtF,IAAA,eAAM,EAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAClC,IAAA,eAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,IAAA,eAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YACjD,IAAA,eAAM,EAAC,iBAAiB,CAAC,oBAAoB,CAAC,CAAC,oBAAoB,CAAC;gBAClE,GAAG,YAAY;gBACf,WAAW,EAAE,QAAQ;aACtB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,iBAAQ,EAAC,gCAAgC,EAAE,GAAG,EAAE;QAC9C,IAAA,WAAE,EAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;YAC5D,MAAM,UAAU,GAAG;gBACjB,IAAI,EAAE,kBAAkB;gBACxB,WAAW,EAAE,qBAAqB;aACnC,CAAC;YAEF,MAAM,YAAY,GAAG;gBACnB,EAAE,EAAE,YAAY;gBAChB,GAAG,UAAU;aACd,CAAC;YAEF,iBAAiB,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;YAEvE,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAE9F,IAAA,eAAM,EAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAClC,IAAA,eAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,IAAA,eAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YACjD,IAAA,eAAM,EAAC,iBAAiB,CAAC,oBAAoB,CAAC,CAAC,oBAAoB,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;QAChG,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,iBAAQ,EAAC,mCAAmC,EAAE,GAAG,EAAE;QACjD,IAAA,WAAE,EAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;YAC5D,iBAAiB,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC;YAE7D,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,CAAC,MAAM,CAAC,mCAAmC,CAAC,CAAC;YAEhF,IAAA,eAAM,EAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAClC,IAAA,eAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,IAAA,eAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;YAC3E,IAAA,eAAM,EAAC,iBAAiB,CAAC,oBAAoB,CAAC,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;QACpF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,iBAAQ,EAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,IAAA,WAAE,EAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,MAAM,WAAW,GAAG;gBAClB;oBACE,EAAE,EAAE,aAAa;oBACjB,IAAI,EAAE,eAAe;oBACrB,QAAQ,EAAE,WAAW;iBACtB;aACF,CAAC;YAEF,iBAAiB,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;YAErE,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;YAElE,IAAA,eAAM,EAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAClC,IAAA,eAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,IAAA,eAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;YAChD,IAAA,eAAM,EAAC,iBAAiB,CAAC,mBAAmB,CAAC,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;QAC/E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,iBAAQ,EAAC,6BAA6B,EAAE,GAAG,EAAE;QAC3C,IAAA,WAAE,EAAC,+CAA+C,EAAE,KAAK,IAAI,EAAE;YAC7D,MAAM,mBAAmB,GAAG;gBAC1B,IAAI,EAAE,eAAe;gBACrB,UAAU,EAAE,YAAY;gBACxB,QAAQ,EAAE,WAAW;gBACrB,QAAQ,EAAE,IAAI;aACf,CAAC;YAEF,MAAM,mBAAmB,GAAG;gBAC1B,EAAE,EAAE,aAAa;gBACjB,GAAG,mBAAmB;gBACtB,WAAW,EAAE,QAAQ;aACtB,CAAC;YAEF,iBAAiB,CAAC,qBAAqB,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,CAAC;YAE/E,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAE7F,IAAA,eAAM,EAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAClC,IAAA,eAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,IAAA,eAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;YACxD,IAAA,eAAM,EAAC,iBAAiB,CAAC,qBAAqB,CAAC,CAAC,oBAAoB,CAAC;gBACnE,GAAG,mBAAmB;gBACtB,WAAW,EAAE,QAAQ;aACtB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,iBAAQ,EAAC,qCAAqC,EAAE,GAAG,EAAE;QACnD,IAAA,WAAE,EAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,MAAM,UAAU,GAAG;gBACjB,EAAE,EAAE,UAAU;gBACd,MAAM,EAAE,WAAW;aACpB,CAAC;YAEF,iBAAiB,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;YAEnE,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;YAEnF,IAAA,eAAM,EAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAClC,IAAA,eAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,IAAA,eAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YAC/C,IAAA,eAAM,EAAC,iBAAiB,CAAC,kBAAkB,CAAC,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;QACnF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,iBAAQ,EAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,IAAA,WAAE,EAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,MAAM,WAAW,GAAG;gBAClB;oBACE,EAAE,EAAE,UAAU;oBACd,IAAI,EAAE,oBAAoB;oBAC1B,IAAI,EAAE,aAAa;oBACnB,MAAM,EAAE,KAAK;iBACd;aACF,CAAC;YAEF,iBAAiB,CAAC,eAAe,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;YAEjE,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;YAE9D,IAAA,eAAM,EAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAClC,IAAA,eAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,IAAA,eAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;YAChD,IAAA,eAAM,EAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,iBAAQ,EAAC,+BAA+B,EAAE,GAAG,EAAE;QAC7C,IAAA,WAAE,EAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,iBAAiB,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC;YAE1D,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,CAAC,MAAM,CAAC,6BAA6B,CAAC,CAAC;YAE1E,IAAA,eAAM,EAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAClC,IAAA,eAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,IAAA,eAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;YACxE,IAAA,eAAM,EAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;QAC/E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["F:\\Amazing pay flow\\src\\tests\\advanced-report.controller.test.ts"], "sourcesContent": ["import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';\nimport request from 'supertest';\nimport express from 'express';\nimport { AdvancedReportController } from '../controllers/advanced-report.controller';\nimport { AdvancedReportService } from '../services/advanced-report.service';\n\n// Mock the service\nvi.mock('../services/advanced-report.service');\n\nconst app = express();\napp.use(express.json());\n\n// Mock auth middleware\nconst mockAuthMiddleware = (req: any, res: any, next: any) => {\n  req.user = {\n    id: 'user-1',\n    role: 'MERCHANT',\n    email: '<EMAIL>',\n  };\n  next();\n};\n\n// Setup routes\nconst reportController = new AdvancedReportController();\napp.post('/api/reports/generate', mockAuthMiddleware, reportController.generateReport);\napp.get('/api/reports/templates', mockAuthMiddleware, reportController.getReportTemplates);\napp.get('/api/reports/templates/:id', mockAuthMiddleware, reportController.getReportTemplateById);\napp.post('/api/reports/templates', mockAuthMiddleware, reportController.createReportTemplate);\napp.put('/api/reports/templates/:id', mockAuthMiddleware, reportController.updateReportTemplate);\napp.delete('/api/reports/templates/:id', mockAuthMiddleware, reportController.deleteReportTemplate);\napp.get('/api/reports/scheduled', mockAuthMiddleware, reportController.getScheduledReports);\napp.get('/api/reports/scheduled/:id', mockAuthMiddleware, reportController.getScheduledReportById);\napp.post('/api/reports/scheduled', mockAuthMiddleware, reportController.createScheduledReport);\napp.put('/api/reports/scheduled/:id', mockAuthMiddleware, reportController.updateScheduledReport);\napp.delete(\n  '/api/reports/scheduled/:id',\n  mockAuthMiddleware,\n  reportController.deleteScheduledReport\n);\napp.post('/api/reports/scheduled/:id/run', mockAuthMiddleware, reportController.runScheduledReport);\napp.get('/api/reports/saved', mockAuthMiddleware, reportController.getSavedReports);\napp.get('/api/reports/saved/:id', mockAuthMiddleware, reportController.getSavedReportById);\napp.delete('/api/reports/saved/:id', mockAuthMiddleware, reportController.deleteSavedReport);\n\ndescribe('AdvancedReportController', () => {\n  let mockReportService: any;\n\n  beforeEach(() => {\n    mockReportService = {\n      generateReport: vi.fn(),\n      getReportTemplates: vi.fn(),\n      getReportTemplateById: vi.fn(),\n      createReportTemplate: vi.fn(),\n      updateReportTemplate: vi.fn(),\n      deleteReportTemplate: vi.fn(),\n      getScheduledReports: vi.fn(),\n      getScheduledReportById: vi.fn(),\n      createScheduledReport: vi.fn(),\n      updateScheduledReport: vi.fn(),\n      deleteScheduledReport: vi.fn(),\n      runScheduledReport: vi.fn(),\n      getSavedReports: vi.fn(),\n      getSavedReportById: vi.fn(),\n      deleteSavedReport: vi.fn(),\n    };\n\n    // Mock the service instance\n    vi.mocked(AdvancedReportService).mockImplementation(() => mockReportService);\n  });\n\n  afterEach(() => {\n    vi.clearAllMocks();\n  });\n\n  describe('POST /api/reports/generate', () => {\n    it('should generate a report successfully', async () => {\n      const mockReport = {\n        id: 'report-1',\n        filePath: '/path/to/report.csv',\n        format: 'CSV',\n      };\n\n      mockReportService.generateReport.mockResolvedValue(mockReport);\n\n      const response = await request(app).post('/api/reports/generate').send({\n        type: 'TRANSACTION',\n        format: 'CSV',\n        startDate: '2023-01-01',\n        endDate: '2023-12-31',\n      });\n\n      expect(response.status).toBe(200);\n      expect(response.body.success).toBe(true);\n      expect(response.body.data).toEqual(mockReport);\n      expect(mockReportService.generateReport).toHaveBeenCalledWith(\n        'TRANSACTION',\n        {\n          startDate: '2023-01-01',\n          endDate: '2023-12-31',\n          userId: 'user-1',\n          userRole: 'MERCHANT',\n        },\n        'CSV'\n      );\n    });\n\n    it('should handle service errors', async () => {\n      mockReportService.generateReport.mockRejectedValue(new Error('Service error'));\n\n      const response = await request(app).post('/api/reports/generate').send({\n        type: 'TRANSACTION',\n        format: 'CSV',\n      });\n\n      expect(response.status).toBe(500);\n      expect(response.body.success).toBe(false);\n      expect(response.body.message).toBe('Service error');\n    });\n  });\n\n  describe('GET /api/reports/templates', () => {\n    it('should get report templates successfully', async () => {\n      const mockTemplates = [\n        {\n          id: 'template-1',\n          name: 'Test Template',\n          type: 'TRANSACTION',\n        },\n      ];\n\n      mockReportService.getReportTemplates.mockResolvedValue(mockTemplates);\n\n      const response = await request(app).get('/api/reports/templates');\n\n      expect(response.status).toBe(200);\n      expect(response.body.success).toBe(true);\n      expect(response.body.data).toEqual(mockTemplates);\n      expect(mockReportService.getReportTemplates).toHaveBeenCalledWith('user-1', true);\n    });\n\n    it('should handle includeSystem parameter', async () => {\n      const mockTemplates = [\n        {\n          id: 'template-1',\n          name: 'User Template',\n          type: 'TRANSACTION',\n        },\n      ];\n\n      mockReportService.getReportTemplates.mockResolvedValue(mockTemplates);\n\n      const response = await request(app)\n        .get('/api/reports/templates')\n        .query({ includeSystem: 'false' });\n\n      expect(response.status).toBe(200);\n      expect(mockReportService.getReportTemplates).toHaveBeenCalledWith('user-1', false);\n    });\n  });\n\n  describe('POST /api/reports/templates', () => {\n    it('should create a report template successfully', async () => {\n      const templateData = {\n        name: 'New Template',\n        description: 'Test Description',\n        type: 'TRANSACTION',\n        config: { columns: ['id', 'amount'] },\n      };\n\n      const mockTemplate = {\n        id: 'template-1',\n        ...templateData,\n        createdById: 'user-1',\n      };\n\n      mockReportService.createReportTemplate.mockResolvedValue(mockTemplate);\n\n      const response = await request(app).post('/api/reports/templates').send(templateData);\n\n      expect(response.status).toBe(201);\n      expect(response.body.success).toBe(true);\n      expect(response.body.data).toEqual(mockTemplate);\n      expect(mockReportService.createReportTemplate).toHaveBeenCalledWith({\n        ...templateData,\n        createdById: 'user-1',\n      });\n    });\n  });\n\n  describe('PUT /api/reports/templates/:id', () => {\n    it('should update a report template successfully', async () => {\n      const updateData = {\n        name: 'Updated Template',\n        description: 'Updated Description',\n      };\n\n      const mockTemplate = {\n        id: 'template-1',\n        ...updateData,\n      };\n\n      mockReportService.updateReportTemplate.mockResolvedValue(mockTemplate);\n\n      const response = await request(app).put('/api/reports/templates/template-1').send(updateData);\n\n      expect(response.status).toBe(200);\n      expect(response.body.success).toBe(true);\n      expect(response.body.data).toEqual(mockTemplate);\n      expect(mockReportService.updateReportTemplate).toHaveBeenCalledWith('template-1', updateData);\n    });\n  });\n\n  describe('DELETE /api/reports/templates/:id', () => {\n    it('should delete a report template successfully', async () => {\n      mockReportService.deleteReportTemplate.mockResolvedValue({});\n\n      const response = await request(app).delete('/api/reports/templates/template-1');\n\n      expect(response.status).toBe(200);\n      expect(response.body.success).toBe(true);\n      expect(response.body.message).toBe('Report template deleted successfully');\n      expect(mockReportService.deleteReportTemplate).toHaveBeenCalledWith('template-1');\n    });\n  });\n\n  describe('GET /api/reports/scheduled', () => {\n    it('should get scheduled reports successfully', async () => {\n      const mockReports = [\n        {\n          id: 'scheduled-1',\n          name: 'Weekly Report',\n          schedule: '0 0 * * 1',\n        },\n      ];\n\n      mockReportService.getScheduledReports.mockResolvedValue(mockReports);\n\n      const response = await request(app).get('/api/reports/scheduled');\n\n      expect(response.status).toBe(200);\n      expect(response.body.success).toBe(true);\n      expect(response.body.data).toEqual(mockReports);\n      expect(mockReportService.getScheduledReports).toHaveBeenCalledWith('user-1');\n    });\n  });\n\n  describe('POST /api/reports/scheduled', () => {\n    it('should create a scheduled report successfully', async () => {\n      const scheduledReportData = {\n        name: 'Weekly Report',\n        templateId: 'template-1',\n        schedule: '0 0 * * 1',\n        isActive: true,\n      };\n\n      const mockScheduledReport = {\n        id: 'scheduled-1',\n        ...scheduledReportData,\n        createdById: 'user-1',\n      };\n\n      mockReportService.createScheduledReport.mockResolvedValue(mockScheduledReport);\n\n      const response = await request(app).post('/api/reports/scheduled').send(scheduledReportData);\n\n      expect(response.status).toBe(201);\n      expect(response.body.success).toBe(true);\n      expect(response.body.data).toEqual(mockScheduledReport);\n      expect(mockReportService.createScheduledReport).toHaveBeenCalledWith({\n        ...scheduledReportData,\n        createdById: 'user-1',\n      });\n    });\n  });\n\n  describe('POST /api/reports/scheduled/:id/run', () => {\n    it('should run a scheduled report successfully', async () => {\n      const mockResult = {\n        id: 'report-1',\n        status: 'COMPLETED',\n      };\n\n      mockReportService.runScheduledReport.mockResolvedValue(mockResult);\n\n      const response = await request(app).post('/api/reports/scheduled/scheduled-1/run');\n\n      expect(response.status).toBe(200);\n      expect(response.body.success).toBe(true);\n      expect(response.body.data).toEqual(mockResult);\n      expect(mockReportService.runScheduledReport).toHaveBeenCalledWith('scheduled-1');\n    });\n  });\n\n  describe('GET /api/reports/saved', () => {\n    it('should get saved reports successfully', async () => {\n      const mockReports = [\n        {\n          id: 'report-1',\n          name: 'Transaction Report',\n          type: 'TRANSACTION',\n          format: 'CSV',\n        },\n      ];\n\n      mockReportService.getSavedReports.mockResolvedValue(mockReports);\n\n      const response = await request(app).get('/api/reports/saved');\n\n      expect(response.status).toBe(200);\n      expect(response.body.success).toBe(true);\n      expect(response.body.data).toEqual(mockReports);\n      expect(mockReportService.getSavedReports).toHaveBeenCalledWith('user-1');\n    });\n  });\n\n  describe('DELETE /api/reports/saved/:id', () => {\n    it('should delete a saved report successfully', async () => {\n      mockReportService.deleteSavedReport.mockResolvedValue({});\n\n      const response = await request(app).delete('/api/reports/saved/report-1');\n\n      expect(response.status).toBe(200);\n      expect(response.body.success).toBe(true);\n      expect(response.body.message).toBe('Saved report deleted successfully');\n      expect(mockReportService.deleteSavedReport).toHaveBeenCalledWith('report-1');\n    });\n  });\n});\n"], "version": 3}