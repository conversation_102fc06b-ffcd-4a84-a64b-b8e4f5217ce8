// jscpd:ignore-file
/**
 * Controller Utilities
 * 
 * This file contains utility functions for controllers.
 */

import { Request, Response, NextFunction } from 'express';
import { AppError, ErrorType, ErrorCode } from '../utils/errors';
import { createSuccessResponse, createPaginatedResponse } from './errorHandling';
import { AppError, ErrorType, ErrorCode } from '../utils/errors';
import { createSuccessResponse, createPaginatedResponse } from './errorHandling';
import { User, Merchant } from '../types';

// Extend the Express Request type to include user property
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role: string;
        merchantId?: string;
      };
    }
  }
}


/**
 * Check authorization
 * @param req Request object
 * @returns User role, ID, and merchant ID
 * @throws AppError if user is not authorized
 */
export function checkAuthorization(req: Request): {
  userRole: string;
  userId: string;
  merchantId?: string;
} {
  const userRole = req.user?.role;
  const userId: unknown =req.user?.id;
  const merchantId: unknown =req.user?.merchantId;

  if (!userRole || !userId) {
    throw new AppError('Unauthorized', 401);
  }

  return { userRole, userId, merchantId };
}

/**
 * Check admin role
 * @param userRole User role
 * @throws AppError if user is not an admin
 */
export function checkAdminRole(userRole: string): void {
  if (userRole !== 'ADMIN') {
    throw new AppError('Unauthorized', 401);
  }
}

/**
 * Parse date range
 * @param startDateStr Start date string
 * @param endDateStr End date string
 * @returns Start and end dates
 * @throws AppError if dates are invalid
 */
export function parseDateRange(
  startDateStr?: string,
  endDateStr?: string
): { startDate: Date; endDate: Date } {
  if (!startDateStr || !endDateStr) {
    throw new AppError('Start date and end date are required', 400);
  }

  const startDate: Date =new Date(startDateStr);
  const endDate: Date =new Date(endDateStr);

  if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
    throw new AppError('Invalid date format', 400);
  }

  if (startDate > endDate) {
    throw new AppError('Start date must be before end date', 400);
  }

  return { startDate, endDate };
}

/**
 * Determine target merchant ID
 * @param userRole User role
 * @param merchantId User's merchant ID
 * @param requestedMerchantId Requested merchant ID
 * @returns Target merchant ID
 * @throws AppError if user is not authorized
 */
export function determineTargetMerchantId(
  userRole: string,
  merchantId?: string,
  requestedMerchantId?: string
): string {
import { User, Merchant } from '../types';


  let targetMerchantId: string;

  if (userRole === 'ADMIN') {
    targetMerchantId = requestedMerchantId ?? '';
  } else if (userRole === 'MERCHANT') {
    targetMerchantId = merchantId ?? '';
  } else {
    throw new AppError('Unauthorized', 401);
  }

  if (!targetMerchantId) {
    throw new AppError('Merchant ID is required', 400);
  }

  return targetMerchantId;
}
