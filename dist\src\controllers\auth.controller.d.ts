import { Request, Response } from 'express';
declare global {
    namespace Express {
        interface Request {
            user?: {
                id: string;
                role: string;
                merchantId?: string;
            };
        }
    }
}
declare class AuthController {
    private rbacService;
    constructor();
    login(req: Request, res: Response): Promise<Response<any, Record<string, any>>>;
    logout(req: Request, res: Response): Promise<Response<any, Record<string, any>>>;
    getCurrentUser(req: Request, res: Response): Promise<Response<any, Record<string, any>>>;
    getTwoFactorStatus(req: Request, res: Response): Promise<Response<any, Record<string, any>>>;
    setupTwoFactor(req: Request, res: Response): Promise<Response<any, Record<string, any>>>;
    verifyAndEnableTwoFactor(req: Request, res: Response): Promise<Response<any, Record<string, any>>>;
    recoverWithBackupCode(req: Request, res: Response): Promise<Response<any, Record<string, any>>>;
    registerMerchant(req: Request, res: Response): Promise<Response<any, Record<string, any>>>;
    /**
     * Get user permissions
     * @param req Request
     * @param res Response
     */
    getUserPermissions(req: Request, res: Response): Promise<Response<any, Record<string, any>>>;
}
declare const _default: AuthController;
export default _default;
//# sourceMappingURL=auth.controller.d.ts.map