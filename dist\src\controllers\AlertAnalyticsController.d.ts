import { BaseController } from "../base/BaseController";
/**
 * Alert analytics controller
 */
export declare class AlertAnalyticsController extends BaseController {
    private analyticsService;
    constructor();
    /**
     * Get alert count by status
     * @route GET /api/alerts/analytics/count-by-status
     */
    getAlertCountByStatus: any;
    /**
     * Get alert count by severity
     * @route GET /api/alerts/analytics/count-by-severity
     */
    getAlertCountBySeverity: any;
    /**
     * Get alert count by type
     * @route GET /api/alerts/analytics/count-by-type
     */
    getAlertCountByType: any;
    /**
     * Get alert count by day
     * @route GET /api/alerts/analytics/count-by-day
     */
    getAlertCountByDay: any;
    /**
     * Get alert count by hour
     * @route GET /api/alerts/analytics/count-by-hour
     */
    getAlertCountByHour: any;
    /**
     * Get top merchants by alert count
     * @route GET /api/alerts/analytics/top-merchants
     */
    getTopMerchantsByAlertCount: any;
    /**
     * Get alert resolution time statistics
     * @route GET /api/alerts/analytics/resolution-time
     */
    getAlertResolutionTimeStats: any;
    /**
     * Get alert trends
     * @route GET /api/alerts/analytics/trends
     */
    getAlertTrends: any;
}
//# sourceMappingURL=AlertAnalyticsController.d.ts.map