// jscpd:ignore-file
import { Router } from "express";
import { PaymentVerificationController } from "../controllers/paymentVerificationController";
import { PrismaClient } from "@prisma/client";
import { authenticateJWT } from '../middlewares/authMiddleware';
import { PaymentVerificationController } from "../controllers/paymentVerificationController";
import { PrismaClient } from "@prisma/client";
import { authenticateJWT } from '../middlewares/authMiddleware';

const router: any =Router();
const prisma = new PrismaClient();
const paymentVerificationController: any =new PaymentVerificationController(prisma);

/**
 * @route POST /api/verify
 * @desc Verify a payment
 * @access Private
 */
router.post(
    "/",
    authenticateJWT,
    paymentVerificationController.verifyPayment.bind(paymentVerificationController)
);

/**
 * @route GET /api/verify/:id
 * @desc Get a transaction by ID
 * @access Private
 */
router.get(
    "/:id",
    authenticateJWT,
    paymentVerificationController.getTransaction.bind(paymentVerificationController)
);

/**
 * @route PUT /api/verify/:id/status
 * @desc Update a transaction status
 * @access Private
 */
router.put(
    "/:id/status",
    authenticateJWT,
    paymentVerificationController.updateTransactionStatus.bind(paymentVerificationController)
);

export default router;
