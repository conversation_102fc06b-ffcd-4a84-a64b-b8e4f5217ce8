/**
 * Verification Utilities
 *
 * This module provides common utilities for verification scripts to eliminate duplication.
 */
import { VerificationResult } from '../types';
export interface VerificationResult {
    category: string;
    name: string;
    status: 'pass' | 'fail' | 'warn';
    message: string;
    details?: unknown;
}
/**
 * Verification context
 */
export interface VerificationContext {
    apiUrl: string;
    authToken?: string | null;
    results: VerificationResult[];
}
/**
 * Create a new verification context
 * @param apiUrl API URL
 * @returns Verification context
 */
export declare function createVerificationContext(apiUrl: string): VerificationContext;
/**
 * Verify API endpoints
 * @param context Verification context
 */
export declare function verifyApiEndpoints(context: VerificationContext): Promise<void>;
/**
 * Verify database connectivity
 * @param context Verification context
 */
export declare function verifyDatabaseConnectivity(context: VerificationContext): Promise<void>;
/**
 * Verify authentication
 * @param context Verification context
 */
export declare function verifyAuthentication(context: VerificationContext): Promise<void>;
/**
 * Verify payment processing
 * @param context Verification context
 */
export declare function verifyPaymentProcessing(context: VerificationContext): Promise<void>;
/**
 * Verify environment configuration
 * @param context Verification context
 */
export declare function verifyEnvironmentConfiguration(context: VerificationContext): Promise<void>;
/**
 * Print verification results
 * @param context Verification context
 */
export declare function printResults(context: VerificationContext): void;
/**
 * Get overall status
 * @param context Verification context
 * @returns True if verification is successful, false otherwise
 */
export declare function getOverallStatus(context: VerificationContext): boolean;
//# sourceMappingURL=verification-utils.d.ts.map