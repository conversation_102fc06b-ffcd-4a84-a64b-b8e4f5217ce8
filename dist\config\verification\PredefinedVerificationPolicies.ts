// jscpd:ignore-file
/**
 * Predefined Verification Policies
 * 
 * Defines predefined verification policies.
 */

import { VerificationPolicy } from "../../services/verification/policy/VerificationPolicy";

/**
 * High-value transaction policy
 * 
 * Requires document and phone verification for transactions over $10,000.
 */
export const HIGH_VALUE_POLICY: any =new VerificationPolicy("high_value_policy")
    .setDescription("Requires document and phone verification for transactions over $10,000")
    .requireMethods(["document_verification", "phone_verification"])
    .whenAmountExceeds(10000);

/**
 * Medium-value transaction policy
 * 
 * Requires phone verification for transactions over $1,000.
 */
export const MEDIUM_VALUE_POLICY: any =new VerificationPolicy("medium_value_policy")
    .setDescription("Requires phone verification for transactions over $1,000")
    .requireMethod("phone_verification")
    .whenAmountExceeds(1000)
    .whenAmountBelow(10000);

/**
 * Crypto transaction policy
 * 
 * Requires blockchain verification for crypto transactions.
 */
export const CRYPTO_TRANSACTION_POLICY: any =new VerificationPolicy("crypto_transaction_policy")
    .setDescription("Requires blockchain verification for crypto transactions")
    .requireMethod("blockchain_verification")
    .addCondition(request => request.paymentMethodType.includes("crypto") || 
                           request.paymentMethodType.includes("binance"));

/**
 * New merchant policy
 * 
 * Requires additional verification for new merchants.
 */
export const NEW_MERCHANT_POLICY: any =new VerificationPolicy("new_merchant_policy")
    .setDescription("Requires additional verification for new merchants")
    .requireMethods(["email_verification", "phone_verification"])
    .addCondition(request) => {
    // In a real implementation, this would check if the merchant is new
    // For now, we'll use a metadata flag
        return request.metadata?.isNewMerchant === true;
    });

/**
 * High-risk country policy
 * 
 * Requires additional verification for transactions from high-risk countries.
 */
export const HIGH_RISK_COUNTRY_POLICY: any =new VerificationPolicy("high_risk_country_policy")
    .setDescription("Requires additional verification for transactions from high-risk countries")
    .requireMethods(["document_verification", "phone_verification"])
    .addCondition(request) => {
        const highRiskCountries = ["XX", "YY", "ZZ"]; // Example country codes
        return highRiskCountries.includes(request.metadata?.country);
    });

/**
 * Binance Pay policy
 * 
 * Specific verification requirements for Binance Pay.
 */
export const BINANCE_PAY_POLICY: any =new VerificationPolicy("binance_pay_policy")
    .setDescription("Specific verification requirements for Binance Pay")
    .requireMethod("binance_pay_verification")
    .forPaymentMethod("binance_pay");

/**
 * Binance TRC20 policy
 * 
 * Specific verification requirements for Binance TRC20.
 */
export const BINANCE_TRC20_POLICY: any =new VerificationPolicy("binance_trc20_policy")
    .setDescription("Specific verification requirements for Binance TRC20")
    .requireMethod("binance_trc20_verification")
    .forPaymentMethod("binance_trc20");

/**
 * All predefined policies
 */
export const PREDEFINED_VERIFICATION_POLICIES: any =[
    HIGH_VALUE_POLICY,
    MEDIUM_VALUE_POLICY,
    CRYPTO_TRANSACTION_POLICY,
    NEW_MERCHANT_POLICY,
    HIGH_RISK_COUNTRY_POLICY,
    BINANCE_PAY_POLICY,
    BINANCE_TRC20_POLICY
];
