// jscpd:ignore-file
/**
 * Merchant Self-Service Routes
 * 
 * This file defines the routes for merchant self-service tools.
 */

import express from "express";
import { MerchantSelfServiceController } from "../controllers/merchant-self-service.controller";
import { authMiddleware } from '../middlewares/authMiddleware';
import { roleMiddleware } from '../middlewares/roleMiddleware';
import { merchantAccessMiddleware } from '../middlewares/merchantAccessMiddleware';
import { Merchant } from '../types';
import { authMiddleware } from '../middlewares/authMiddleware';
import { roleMiddleware } from '../middlewares/roleMiddleware';
import { merchantAccessMiddleware } from '../middlewares/merchantAccessMiddleware';
import { Merchant } from '../types';


const router: any =express.Router();
const merchantSelfServiceController: any =new MerchantSelfServiceController();

/**
 * @route POST /api/merchant-self-service/merchants/:merchantId/api-keys
 * @desc Create API key
 * @access Private (Admin, Merchant)
 */
router.post(
    "/merchants/:merchantId/api-keys",
    authMiddleware,
    merchantAccessMiddleware,
    merchantSelfServiceController.createApiKey
);

/**
 * @route GET /api/merchant-self-service/merchants/:merchantId/api-keys
 * @desc Get merchant API keys
 * @access Private (Admin, Merchant)
 */
router.get(
    "/merchants/:merchantId/api-keys",
    authMiddleware,
    merchantAccessMiddleware,
    merchantSelfServiceController.getMerchantApiKeys
);

/**
 * @route DELETE /api/merchant-self-service/api-keys/:apiKeyId
 * @desc Delete API key
 * @access Private (Admin, Merchant)
 */
router.delete(
    "/api-keys/:apiKeyId",
    authMiddleware,
    merchantSelfServiceController.deleteApiKey
);

/**
 * @route POST /api/merchant-self-service/merchants/:merchantId/webhooks
 * @desc Create webhook
 * @access Private (Admin, Merchant)
 */
router.post(
    "/merchants/:merchantId/webhooks",
    authMiddleware,
    merchantAccessMiddleware,
    merchantSelfServiceController.createWebhook
);

/**
 * @route GET /api/merchant-self-service/merchants/:merchantId/webhooks
 * @desc Get merchant webhooks
 * @access Private (Admin, Merchant)
 */
router.get(
    "/merchants/:merchantId/webhooks",
    authMiddleware,
    merchantAccessMiddleware,
    merchantSelfServiceController.getMerchantWebhooks
);

/**
 * @route PUT /api/merchant-self-service/webhooks/:webhookId
 * @desc Update webhook
 * @access Private (Admin, Merchant)
 */
router.put(
    "/webhooks/:webhookId",
    authMiddleware,
    merchantSelfServiceController.updateWebhook
);

/**
 * @route DELETE /api/merchant-self-service/webhooks/:webhookId
 * @desc Delete webhook
 * @access Private (Admin, Merchant)
 */
router.delete(
    "/webhooks/:webhookId",
    authMiddleware,
    merchantSelfServiceController.deleteWebhook
);

/**
 * @route POST /api/merchant-self-service/merchants/:merchantId/notification-preferences
 * @desc Set notification preference
 * @access Private (Admin, Merchant)
 */
router.post(
    "/merchants/:merchantId/notification-preferences",
    authMiddleware,
    merchantAccessMiddleware,
    merchantSelfServiceController.setNotificationPreference
);

/**
 * @route GET /api/merchant-self-service/merchants/:merchantId/notification-preferences
 * @desc Get merchant notification preferences
 * @access Private (Admin, Merchant)
 */
router.get(
    "/merchants/:merchantId/notification-preferences",
    authMiddleware,
    merchantAccessMiddleware,
    merchantSelfServiceController.getMerchantNotificationPreferences
);

export default router;
