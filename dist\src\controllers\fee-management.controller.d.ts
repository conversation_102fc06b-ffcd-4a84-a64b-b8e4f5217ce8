/**
 * Fee Management Controller
 *
 * This controller handles fee management-related API endpoints.
 */
import { Request, Response } from 'express';
import { BaseController } from './base/BaseController';
/**
 * Fee management controller
 */
export declare class FeeManagementController extends BaseController {
    private feeManagementService;
    constructor();
    /**
     * Calculate fee for a transaction
     * @param req Request
     * @param res Response
     */
    calculateFee: (req: Request, res: Response) => Promise<void>;
    /**
     * Create fee tier
     * @param req Request
     * @param res Response
     */
    createFeeTier: (req: Request, res: Response) => Promise<void>;
    /**
     * Get fee tiers
     * @param req Request
     * @param res Response
     */
    getFeeTiers: (req: Request, res: Response) => Promise<void>;
    /**
     * Get fee tier by ID
     * @param req Request
     * @param res Response
     */
    getFeeTierById: (req: Request, res: Response) => Promise<void>;
    /**
     * Update fee tier
     * @param req Request
     * @param res Response
     */
    updateFeeTier: (req: Request, res: Response) => Promise<void>;
    /**
     * Delete fee tier
     * @param req Request
     * @param res Response
     */
    deleteFeeTier: (req: Request, res: Response) => Promise<void>;
    /**
     * Get fee calculations for merchant
     * @param req Request
     * @param res Response
     */
    getMerchantFeeCalculations: (req: Request, res: Response) => Promise<void>;
    /**
     * Create merchant fee discount
     * @param req Request
     * @param res Response
     */
    createMerchantFeeDiscount: (req: Request, res: Response) => Promise<void>;
    /**
     * Get merchant fee discounts
     * @param req Request
     * @param res Response
     */
    getMerchantFeeDiscounts: (req: Request, res: Response) => Promise<void>;
    /**
     * Update merchant fee discount
     * @param req Request
     * @param res Response
     */
    updateMerchantFeeDiscount: (req: Request, res: Response) => Promise<void>;
    /**
     * Delete merchant fee discount
     * @param req Request
     * @param res Response
     */
    deleteMerchantFeeDiscount: (req: Request, res: Response) => Promise<void>;
}
declare const _default: FeeManagementController;
export default _default;
//# sourceMappingURL=fee-management.controller.d.ts.map