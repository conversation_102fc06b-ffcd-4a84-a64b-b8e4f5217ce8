"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AlertAnalyticsController = void 0;
const base_controller_1 = require("./base.controller");
const asyncHandler_1 = require("../utils/asyncHandler");
const AppError_1 = require("../utils/errors/AppError");
const alert_analytics_service_1 = require("../services/alert-analytics.service");
/**
 * AlertAnalyticsController
 */
class AlertAnalyticsController extends base_controller_1.BaseController {
    constructor() {
        super();
        /**
         * Get alert count by status
         */
        this.getAlertCountByStatus = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Check authorization
                const { userRole, userId, merchantId } = this.checkAuthorization(req);
                // Parse date range
                const { startDate, endDate } = this.parseDateRange(req.query.startDate, req.query.endDate);
                // Determine target merchant ID
                const targetMerchantId = this.determineTargetMerchantId(userRole, merchantId, req.query.merchantId);
                // Create analytics service
                const analyticsService = new alert_analytics_service_1.AlertAnalyticsService();
                // Get alert count by status
                const data = await analyticsService.getAlertCountByStatus(startDate, endDate, targetMerchantId);
                // Return data
                return res.status(200).json({
                    success: true,
                    data
                });
            }
            catch (error) {
                if (error instanceof AppError_1.AppError) {
                    throw error;
                }
                throw new AppError_1.AppError({
                    message: "Failed to get alert count by status",
                    type: ErrorType.INTERNAL,
                    code: ErrorCode.INTERNAL_SERVER_ERROR
                });
            }
        });
        /**
         * Get alert count by severity
         */
        this.getAlertCountBySeverity = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Check authorization
                const { userRole, userId, merchantId } = this.checkAuthorization(req);
                // Parse date range
                const { startDate, endDate } = this.parseDateRange(req.query.startDate, req.query.endDate);
                // Determine target merchant ID
                const targetMerchantId = this.determineTargetMerchantId(userRole, merchantId, req.query.merchantId);
                // Create analytics service
                const analyticsService = new alert_analytics_service_1.AlertAnalyticsService();
                // Get alert count by severity
                const data = await analyticsService.getAlertCountBySeverity(startDate, endDate, targetMerchantId);
                // Return data
                return res.status(200).json({
                    success: true,
                    data
                });
            }
            catch (error) {
                if (error instanceof AppError_1.AppError) {
                    throw error;
                }
                throw new AppError_1.AppError({
                    message: "Failed to get alert count by severity",
                    type: ErrorType.INTERNAL,
                    code: ErrorCode.INTERNAL_SERVER_ERROR
                });
            }
        });
        /**
         * Get alert count by type
         */
        this.getAlertCountByType = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Check authorization
                const { userRole, userId, merchantId } = this.checkAuthorization(req);
                // Parse date range
                const { startDate, endDate } = this.parseDateRange(req.query.startDate, req.query.endDate);
                // Determine target merchant ID
                const targetMerchantId = this.determineTargetMerchantId(userRole, merchantId, req.query.merchantId);
                // Create analytics service
                const analyticsService = new alert_analytics_service_1.AlertAnalyticsService();
                // Get alert count by type
                const data = await analyticsService.getAlertCountByType(startDate, endDate, targetMerchantId);
                // Return data
                return res.status(200).json({
                    success: true,
                    data
                });
            }
            catch (error) {
                if (error instanceof AppError_1.AppError) {
                    throw error;
                }
                throw new AppError_1.AppError({
                    message: "Failed to get alert count by type",
                    type: ErrorType.INTERNAL,
                    code: ErrorCode.INTERNAL_SERVER_ERROR
                });
            }
        });
        /**
         * Get alert count by day
         */
        this.getAlertCountByDay = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Check authorization
                const { userRole, userId, merchantId } = this.checkAuthorization(req);
                // Parse date range
                const { startDate, endDate } = this.parseDateRange(req.query.startDate, req.query.endDate);
                // Determine target merchant ID
                const targetMerchantId = this.determineTargetMerchantId(userRole, merchantId, req.query.merchantId);
                // Create analytics service
                const analyticsService = new alert_analytics_service_1.AlertAnalyticsService();
                // Get alert count by day
                const data = await analyticsService.getAlertCountByDay(startDate, endDate, targetMerchantId);
                // Return data
                return res.status(200).json({
                    success: true,
                    data
                });
            }
            catch (error) {
                if (error instanceof AppError_1.AppError) {
                    throw error;
                }
                throw new AppError_1.AppError({
                    message: "Failed to get alert count by day",
                    type: ErrorType.INTERNAL,
                    code: ErrorCode.INTERNAL_SERVER_ERROR
                });
            }
        });
        /**
         * Get alert count by hour
         */
        this.getAlertCountByHour = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Check authorization
                const { userRole, userId, merchantId } = this.checkAuthorization(req);
                // Get query parameters
                const dateStr = req.query.date;
                // Validate date parameter
                if (!dateStr) {
                    throw new AppError_1.AppError({
                        message: "Date is required",
                        type: ErrorType.VALIDATION,
                        code: ErrorCode.MISSING_REQUIRED_FIELD
                    });
                }
                // Parse date
                const date = new Date(dateStr);
                // Validate date
                if (isNaN(date.getTime())) {
                    throw new AppError_1.AppError({
                        message: "Invalid date format",
                        type: ErrorType.VALIDATION,
                        code: ErrorCode.INVALID_INPUT
                    });
                }
                // Determine target merchant ID
                const targetMerchantId = this.determineTargetMerchantId(userRole, merchantId, req.query.merchantId);
                // Create analytics service
                const analyticsService = new alert_analytics_service_1.AlertAnalyticsService();
                // Get alert count by hour
                const data = await analyticsService.getAlertCountByHour(date, targetMerchantId);
                // Return data
                return res.status(200).json({
                    success: true,
                    data
                });
            }
            catch (error) {
                if (error instanceof AppError_1.AppError) {
                    throw error;
                }
                throw new AppError_1.AppError({
                    message: "Failed to get alert count by hour",
                    type: ErrorType.INTERNAL,
                    code: ErrorCode.INTERNAL_SERVER_ERROR
                });
            }
        });
        /**
         * Get top merchants by alert count
         */
        this.getTopMerchantsByAlertCount = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Get user role and ID
                const userRole = req.user?.role;
                // Only admins can access this endpoint
                this.checkAdminRole(userRole);
                // Parse date range
                const { startDate, endDate } = this.parseDateRange(req.query.startDate, req.query.endDate);
                // Get limit parameter
                const limitStr = req.query.limit;
                const limit = limitStr ? parseInt(limitStr) : 10;
                // Validate limit
                if (isNaN(limit) || limit <= 0) {
                    throw new AppError_1.AppError({
                        message: "Invalid limit",
                        type: ErrorType.VALIDATION,
                        code: ErrorCode.INVALID_INPUT
                    });
                }
                // Create analytics service
                const analyticsService = new alert_analytics_service_1.AlertAnalyticsService();
                // Get top merchants by alert count
                const data = await analyticsService.getTopMerchantsByAlertCount(startDate, endDate, limit);
                // Return data
                return res.status(200).json({
                    success: true,
                    data
                });
            }
            catch (error) {
                if (error instanceof AppError_1.AppError) {
                    throw error;
                }
                throw new AppError_1.AppError({
                    message: "Failed to get top merchants by alert count",
                    type: ErrorType.INTERNAL,
                    code: ErrorCode.INTERNAL_SERVER_ERROR
                });
            }
        });
        /**
         * Get alert resolution time statistics
         */
        this.getAlertResolutionTimeStats = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Check authorization
                const { userRole, userId, merchantId } = this.checkAuthorization(req);
                // Parse date range
                const { startDate, endDate } = this.parseDateRange(req.query.startDate, req.query.endDate);
                // Determine target merchant ID
                const targetMerchantId = this.determineTargetMerchantId(userRole, merchantId, req.query.merchantId);
                // Create analytics service
                const analyticsService = new alert_analytics_service_1.AlertAnalyticsService();
                // Get alert resolution time statistics
                const data = await analyticsService.getAlertResolutionTimeStats(startDate, endDate, targetMerchantId);
                // Return data
                return res.status(200).json({
                    success: true,
                    data
                });
            }
            catch (error) {
                if (error instanceof AppError_1.AppError) {
                    throw error;
                }
                throw new AppError_1.AppError({
                    message: "Failed to get alert resolution time statistics",
                    type: ErrorType.INTERNAL,
                    code: ErrorCode.INTERNAL_SERVER_ERROR
                });
            }
        });
        /**
         * Get alert trends
         */
        this.getAlertTrends = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Check authorization
                const { userRole, userId, merchantId } = this.checkAuthorization(req);
                // Get query parameters
                const daysStr = req.query.days;
                // Parse days
                const days = daysStr ? parseInt(daysStr) : 30;
                // Validate days
                if (isNaN(days) || days <= 0) {
                    throw new AppError_1.AppError({
                        message: "Invalid days parameter",
                        type: ErrorType.VALIDATION,
                        code: ErrorCode.INVALID_INPUT
                    });
                }
                // Determine target merchant ID
                const targetMerchantId = this.determineTargetMerchantId(userRole, merchantId, req.query.merchantId);
                // Create analytics service
                const analyticsService = new alert_analytics_service_1.AlertAnalyticsService();
                // Get alert trends
                const data = await analyticsService.getAlertTrends(days, targetMerchantId);
                // Return data
                return res.status(200).json({
                    success: true,
                    data
                });
            }
            catch (error) {
                if (error instanceof AppError_1.AppError) {
                    throw error;
                }
                throw new AppError_1.AppError({
                    message: "Failed to get alert trends",
                    type: ErrorType.INTERNAL,
                    code: ErrorCode.INTERNAL_SERVER_ERROR
                });
            }
        });
    }
    /**
     * Helper method to check admin role
     */
    checkAdminRole(userRole) {
        if (!userRole || userRole !== "ADMIN") {
            throw new AppError_1.AppError({
                message: "Unauthorized. Admin role required.",
                type: ErrorType.AUTHENTICATION,
                code: ErrorCode.INVALID_CREDENTIALS
            });
        }
    }
    /**
     * Helper method to parse date range
     */
    parseDateRange(startDateStr, endDateStr) {
        if (!startDateStr || !endDateStr) {
            throw new AppError_1.AppError({
                message: "Start date and end date are required",
                type: ErrorType.VALIDATION,
                code: ErrorCode.MISSING_REQUIRED_FIELD
            });
        }
        const startDate = new Date(startDateStr);
        const endDate = new Date(endDateStr);
        if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
            throw new AppError_1.AppError({
                message: "Invalid date format",
                type: ErrorType.VALIDATION,
                code: ErrorCode.INVALID_INPUT
            });
        }
        return { startDate, endDate };
    }
    /**
     * Helper method to determine target merchant ID
     */
    determineTargetMerchantId(userRole, merchantId, requestedMerchantId) {
        // Admin can request data for any merchant
        if (userRole === "ADMIN" && requestedMerchantId) {
            return requestedMerchantId;
        }
        // Non-admin users can only access their own merchant data
        if (userRole !== "ADMIN" && (!merchantId || (requestedMerchantId && requestedMerchantId !== merchantId))) {
            throw new AppError_1.AppError({
                message: "Unauthorized. You can only access your own merchant data.",
                type: ErrorType.AUTHENTICATION,
                code: ErrorCode.INVALID_CREDENTIALS
            });
        }
        return merchantId;
    }
    /**
     * Helper method to check authorization
     */
    checkAuthorization(req) {
        const userRole = req.user?.role;
        const userId = req.user?.id;
        const merchantId = req.user?.merchantId;
        if (!userRole || !userId) {
            throw new AppError_1.AppError({
                message: "Unauthorized",
                type: ErrorType.AUTHENTICATION,
                code: ErrorCode.INVALID_CREDENTIALS
            });
        }
        return { userRole, userId, merchantId };
    }
}
exports.AlertAnalyticsController = AlertAnalyticsController;
exports.default = new AlertAnalyticsController();
//# sourceMappingURL=alert-analytics.controller.js.map