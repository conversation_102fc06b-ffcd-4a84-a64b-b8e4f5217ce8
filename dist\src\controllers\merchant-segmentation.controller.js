"use strict";
// jscpd:ignore-file
/**
 * Merchant Segmentation Controller
 *
 * This controller handles API requests related to merchant segmentation.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.MerchantSegmentationController = void 0;
const base_controller_1 = require("./base.controller");
const merchant_segmentation_service_1 = require("../services/merchant-segmentation.service");
const logger_1 = require("../utils/logger");
/**
 * Merchant segmentation controller
 */
class MerchantSegmentationController extends base_controller_1.BaseController {
    constructor() {
        super();
        /**
       * Create a new merchant category
       */
        this.createCategory = this.asyncHandler(async (req, res) => {
            try {
                const { name, description } = req.body;
                // Validate required fields
                if (!name) {
                    return res.badRequest("Category name is required");
                }
                // Create category
                const category = await this.merchantSegmentationService.createCategory(name, description);
                return res.success("Merchant category created", category);
            }
            catch (error) {
                logger_1.logger.error("Error creating merchant category:", error);
                return res.serverError("Failed to create merchant category");
            }
        });
        /**
       * Get all merchant categories
       */
        this.getAllCategories = this.asyncHandler(async (req, res) => {
            try {
                const categories = await this.merchantSegmentationService.getAllCategories();
                return res.success("Merchant categories retrieved", categories);
            }
            catch (error) {
                logger_1.logger.error("Error getting merchant categories:", error);
                return res.serverError("Failed to get merchant categories");
            }
        });
        /**
       * Add merchant to category
       */
        this.addMerchantToCategory = this.asyncHandler(async (req, res) => {
            try {
                const { merchantId, categoryId } = req.params;
                // Add merchant to category
                const merchant = await this.merchantSegmentationService.addMerchantToCategory(merchantId, categoryId);
                return res.success("Merchant added to category", merchant);
            }
            catch (error) {
                logger_1.logger.error("Error adding merchant to category:", error);
                return res.serverError("Failed to add merchant to category");
            }
        });
        /**
       * Create a new merchant segment
       */
        this.createSegment = this.asyncHandler(async (req, res) => {
            try {
                const { name, description, criteria } = req.body;
                // Validate required fields
                if (!name) {
                    return res.badRequest("Segment name is required");
                }
                if (!criteria) {
                    return res.badRequest("Segmentation criteria are required");
                }
                // Create segment
                const segment = await this.merchantSegmentationService.createSegment(name, description || "", criteria);
                return res.success("Merchant segment created", segment);
            }
            catch (error) {
                logger_1.logger.error("Error creating merchant segment:", error);
                return res.serverError("Failed to create merchant segment");
            }
        });
        /**
       * Get all merchant segments
       */
        this.getAllSegments = this.asyncHandler(async (req, res) => {
            try {
                const segments = await this.merchantSegmentationService.getAllSegments();
                return res.success("Merchant segments retrieved", segments);
            }
            catch (error) {
                logger_1.logger.error("Error getting merchant segments:", error);
                return res.serverError("Failed to get merchant segments");
            }
        });
        /**
       * Apply segment to matching merchants
       */
        this.applySegment = this.asyncHandler(async (req, res) => {
            try {
                const { segmentId } = req.params;
                // Apply segment
                const merchantCount = await this.merchantSegmentationService.applySegmentToMerchants(segmentId);
                return res.success("Segment applied to merchants", { merchantCount });
            }
            catch (error) {
                logger_1.logger.error("Error applying segment to merchants:", error);
                return res.serverError("Failed to apply segment to merchants");
            }
        });
        /**
       * Create a new merchant performance tier
       */
        this.createPerformanceTier = this.asyncHandler(async (req, res) => {
            try {
                const { name, description, minimumRevenue, minimumTransactions, successRateThreshold, benefits } = req.body;
                // Validate required fields
                if (!name) {
                    return res.badRequest("Tier name is required");
                }
                if (minimumRevenue === undefined || minimumRevenue === null) {
                    return res.badRequest("Minimum revenue is required");
                }
                if (minimumTransactions === undefined || minimumTransactions === null) {
                    return res.badRequest("Minimum transactions is required");
                }
                // Create tier
                const tier = await this.merchantSegmentationService.createPerformanceTier(name, description || "", parseFloat(minimumRevenue), parseInt(minimumTransactions), successRateThreshold ? parseFloat(successRateThreshold) : undefined, benefits);
                return res.success("Merchant performance tier created", tier);
            }
            catch (error) {
                logger_1.logger.error("Error creating merchant performance tier:", error);
                return res.serverError("Failed to create merchant performance tier");
            }
        });
        /**
       * Apply performance tier to qualifying merchants
       */
        this.applyPerformanceTier = this.asyncHandler(async (req, res) => {
            try {
                const { tierId } = req.params;
                // Apply tier
                const merchantCount = await this.merchantSegmentationService.applyPerformanceTierToMerchants(tierId);
                return res.success("Performance tier applied to merchants", { merchantCount });
            }
            catch (error) {
                logger_1.logger.error("Error applying performance tier to merchants:", error);
                return res.serverError("Failed to apply performance tier to merchants");
            }
        });
        this.merchantSegmentationService = new merchant_segmentation_service_1.MerchantSegmentationService();
    }
}
exports.MerchantSegmentationController = MerchantSegmentationController;
//# sourceMappingURL=merchant-segmentation.controller.js.map