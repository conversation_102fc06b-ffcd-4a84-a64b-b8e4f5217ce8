// jscpd:ignore-file

import { ModuleFactory, ModuleRegistry, Container, Module } from '../../core/module';
import { Merchant } from '@prisma/client';
import { Prisma } from '@prisma/client';
import { logger, ErrorFactory, CryptoUtils } from "../../utils";
import { authMiddleware } from '../../middlewares/auth.middleware';
import { Merchant } from '@prisma/client';
import { Prisma } from '@prisma/client';
import { logger, ErrorFactory, CryptoUtils } from "../../utils";
import { authMiddleware } from '../../middlewares/auth.middleware';

// Extend the Express Request type to include user property
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role: string;
        merchantId?: string;
      };
    }
  }
}

export class MerchantModule {
  private moduleFactory: ModuleFactory<Merchant, Prisma.MerchantCreateInput, Prisma.MerchantUpdateInput>;
  private moduleRegistry: ModuleRegistry;
  private container: Container;
  private module: Module;

  /**
   * Create a new merchant module
   */
  constructor() {
    this.moduleRegistry = new ModuleRegistry();
    this.container = new Container();

    // Create module factory
    this.moduleFactory = new ModuleFactory<Merchant, Prisma.MerchantCreateInput, Prisma.MerchantUpdateInput>(
      'merchant',
      'Merchant'
    );

    // Get router, repository, service, and controller from factory
    const { router, repository, service, controller } = this.moduleFactory.build();

    // Configure router
    router
      .addRoute('get', '/stats', controller.getStats)
      .addRoute('get', '/payments', controller.getPayments)
      .addRoute('post', '/verify', controller.verifyMerchant)
      .addRoute('post', '/api-key', controller.generateApiKey)
      .addRoute('delete', '/api-key/:id', controller.revokeApiKey)
      .addMiddleware(authMiddleware);

    // Add custom repository methods
    this.moduleFactory.addRepositoryMethod(
      'findByUserId',
      async (userId: string) => {
        try {
          return await repository.findByField('userId', userId);
        } catch (error) {
          logger.error(`Error finding merchant by user ID ${userId}:`, error);
          throw error;
        }
      }
    );

    this.moduleFactory.addRepositoryMethod(
      'findByApiKey',
      async (apiKey: string) => {
        try {
          return await repository.findByField('apiKey', apiKey);
        } catch (error) {
          logger.error(`Error finding merchant by API key:`, error);
          throw error;
        }
      }
    );

    // Add custom service methods
    this.moduleFactory.addServiceMethod(
      'verifyMerchant',
      async (id: string, verificationData: any) => {
        try {
          // Get merchant
          const merchant: any =await service.getById(id);

          // Check if merchant exists
          if (!merchant) {
            throw ErrorFactory.notFound('Merchant', id);
          }

          // Verify merchant
          // Implementation would depend on the specific verification process
          const isVerified: boolean =true; // Placeholder

          // Update merchant status
          const updatedMerchant: any =await service.update(id, {
            isVerified,
            verifiedAt: new Date(),
            updatedAt: new Date()
          } as Prisma.MerchantUpdateInput);

          logger.info(`Merchant verified: ${id}`, {
            merchantId: id,
            isVerified
          });

          return {
            success: true,
            merchant: updatedMerchant,
            isVerified
          };
        } catch (error) {
          logger.error(`Error verifying merchant ${id}:`, error);
          throw ErrorFactory.handle(error);
        }
      }
    );

    this.moduleFactory.addServiceMethod(
      'generateApiKey',
      async (id: string) => {
        try {
          // Get merchant
          const merchant: any =await service.getById(id);

          // Check if merchant exists
          if (!merchant) {
            throw ErrorFactory.notFound('Merchant', id);
          }

          // Check if merchant is verified
          if (!merchant.isVerified) {
            throw ErrorFactory.validation('Merchant must be verified to generate an API key');
          }

          // Generate API key
          const apiKey: any =await CryptoUtils.generateApiKey('mch', 32);

          // Update merchant with API key
          const updatedMerchant: any =await service.update(id, {
            apiKey,
            updatedAt: new Date()
          } as Prisma.MerchantUpdateInput);

          logger.info(`API key generated for merchant: ${id}`, {
            merchantId: id
          });

          return {
            success: true,
            merchant: updatedMerchant,
            apiKey
          };
        } catch (error) {
          logger.error(`Error generating API key for merchant ${id}:`, error);
          throw ErrorFactory.handle(error);
        }
      }
    );

    this.moduleFactory.addServiceMethod(
      'revokeApiKey',
      async (id: string) => {
        try {
          // Get merchant
          const merchant: any =await service.getById(id);

          // Check if merchant exists
          if (!merchant) {
            throw ErrorFactory.notFound('Merchant', id);
          }

          // Update merchant to remove API key
          const updatedMerchant: any =await service.update(id, {
            apiKey: null,
            updatedAt: new Date()
          } as Prisma.MerchantUpdateInput);

          logger.info(`API key revoked for merchant: ${id}`, {
            merchantId: id
          });

          return {
            success: true,
            merchant: updatedMerchant
          };
        } catch (error) {
          logger.error(`Error revoking API key for merchant ${id}:`, error);
          throw ErrorFactory.handle(error);
        }
      }
    );

    this.moduleFactory.addServiceMethod(
      'getDashboardData',
      async (id: string) => {
        try {
          // Get merchant
          const merchant: any =await service.getById(id);

          // Check if merchant exists
          if (!merchant) {
            throw ErrorFactory.notFound('Merchant', id);
          }

          // Get payment service
          const paymentService: any =this.container.resolve('paymentService');

          // Get merchant payments
          const payments: any =await paymentService.getByMerchantId(id, { limit: 5 });

          // Get payment stats
          const stats: any =await paymentService.getPaymentStats(id);

          // Return dashboard data
          return {
            merchant,
            recentPayments: payments.data,
            stats
          };
        } catch (error) {
          logger.error(`Error getting dashboard data for merchant ${id}:`, error);
          throw ErrorFactory.handle(error);
        }
      }
    );

    // Add custom controller methods
    this.moduleFactory.addControllerMethod(
      'getDashboard',
      async (req, res) => {
        try {
          // Check authorization
          const { userRole, userId, merchantId } = req.user;

          // Only merchants can view their dashboard
          if (userRole !== 'MERCHANT' && userRole !== 'ADMIN') {
            throw ErrorFactory.authorization('You do not have permission to view this dashboard');
          }

          // Get dashboard data
          const dashboardData: any =await service.getDashboardData(merchantId);

          // Send success response
          return res.status(200).json({
            success: true,
            data: dashboardData
          });
        } catch (error) {
          logger.error(`Error getting merchant dashboard:`, error);
          return res.status(500).json({
            success: false,
            error: (error as Error).message || 'An error occurred while getting merchant dashboard'
          });
        }
      }
    );

    this.moduleFactory.addControllerMethod(
      'getStats',
      async (req, res) => {
        try {
          // Check authorization
          const { userRole, userId, merchantId } = req.user;

          // Only merchants can view their stats
          if (userRole !== 'MERCHANT' && userRole !== 'ADMIN') {
            throw ErrorFactory.authorization('You do not have permission to view these stats');
          }

          // Get payment service
          const paymentService: any =this.container.resolve('paymentService');

          // Get payment stats
          const stats: any =await paymentService.getPaymentStats(merchantId);

          // Send success response
          return res.status(200).json({
            success: true,
            data: stats
          });
        } catch (error) {
          logger.error(`Error getting merchant stats:`, error);
          return res.status(500).json({
            success: false,
            error: (error as Error).message || 'An error occurred while getting merchant stats'
          });
        }
      }
    );

    this.moduleFactory.addControllerMethod(
      'getPayments',
      async (req, res) => {
        try {
          // Check authorization
          const { userRole, userId, merchantId } = req.user;

          // Only merchants can view their payments
          if (userRole !== 'MERCHANT' && userRole !== 'ADMIN') {
            throw ErrorFactory.authorization('You do not have permission to view these payments');
          }

          // Get payment service
          const paymentService: any =this.container.resolve('paymentService');

          // Parse pagination parameters
          const limit: any =parseInt(req.query.limit as string) || 10;
          const page: any =parseInt(req.query.page as string) || 1;
          const offset: any =(page - 1) * limit;

          // Get merchant payments
          const payments: any =await paymentService.getByMerchantId(merchantId, { limit, offset });

          // Send success response
          return res.status(200).json({
            success: true,
            data: payments
          });
        } catch (error) {
          logger.error(`Error getting merchant payments:`, error);
          return res.status(500).json({
            success: false,
            error: (error as Error).message || 'An error occurred while getting merchant payments'
          });
        }
      }
    );

    this.moduleFactory.addControllerMethod(
      'verifyMerchant',
      async (req, res) => {
        try {
          // Check authorization
          const { userRole } = req.user;

          // Only admins can verify merchants
          if (userRole !== 'ADMIN') {
            throw ErrorFactory.authorization('You do not have permission to verify merchants');
          }

          // Get merchant ID from request body
          const { merchantId, verificationData } = req.body;

          if (!merchantId) {
            return res.status(400).json({
              success: false,
              error: 'Merchant ID is required'
            });
          }

          // Verify merchant
          const result: any =await service.verifyMerchant(merchantId, verificationData);

          // Send success response
          return res.status(200).json({
            success: true,
            data: result
          });
        } catch (error) {
          logger.error(`Error verifying merchant:`, error);
          return res.status(500).json({
            success: false,
            error: (error as Error).message || 'An error occurred while verifying merchant'
          });
        }
      }
    );

    this.moduleFactory.addControllerMethod(
      'generateApiKey',
      async (req, res) => {
        try {
          // Check authorization
          const { userRole, merchantId } = req.user;

          // Only merchants can generate API keys for their own account
          if (userRole !== 'MERCHANT' && userRole !== 'ADMIN') {
            throw ErrorFactory.authorization('You do not have permission to generate API keys');
          }

          // Generate API key
          const result: any =await service.generateApiKey(merchantId);

          // Send success response
          return res.status(200).json({
            success: true,
            data: result
          });
        } catch (error) {
          logger.error(`Error generating API key:`, error);
          return res.status(500).json({
            success: false,
            error: (error as Error).message || 'An error occurred while generating API key'
          });
        }
      }
    );

    this.moduleFactory.addControllerMethod(
      'revokeApiKey',
      async (req, res) => {
        try {
          // Check authorization
          const { userRole, merchantId } = req.user;

          // Only merchants can revoke API keys for their own account
          if (userRole !== 'MERCHANT' && userRole !== 'ADMIN') {
            throw ErrorFactory.authorization('You do not have permission to revoke API keys');
          }

          // Revoke API key
          const result: any =await service.revokeApiKey(merchantId);

          // Send success response
          return res.status(200).json({
            success: true,
            data: result
          });
        } catch (error) {
          logger.error(`Error revoking API key:`, error);
          return res.status(500).json({
            success: false,
            error: (error as Error).message || 'An error occurred while revoking API key'
          });
        }
      }
    );
    // Create module
    this.module = {
      name: 'merchant',
      router,
      repository,
      service,
      controller,
      dependencies: ['payment', 'user'],
      initialize: async () => {
        logger.info('Initializing merchant module');

        // Register dependencies
        this.container.registerSingleton('merchantRepository', () => repository);
        this.container.registerSingleton('merchantService', () => service);
        this.container.registerSingleton('merchantController', () => controller);

        logger.info('Merchant module initialized');
      }
    };

    // Register the module
    this.moduleRegistry.registerModule(this.module);
  }

  /**
   * Get the module
   * @returns Merchant module
   */
  getModule(): Module {
    return this.module;
  }
}