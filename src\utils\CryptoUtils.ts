// jscpd:ignore-file
import crypto from 'crypto';
import { promisify } from 'util';

// Promisify crypto functions
const randomBytesAsync: any =promisify(crypto.randomBytes);
const pbkdf2Async: any =promisify(crypto.pbkdf2);

/**
 * Crypto Utilities
 * This class provides utility methods for cryptographic operations
 */
export class CryptoUtils {
  /**
   * Generate a random string
   * @param length Length of the string
   * @param encoding Encoding of the string
   * @returns Random string
   */
  static async generateRandomString(length: number = 32, encoding: BufferEncoding = 'hex'): Promise<string> {
    const bytes: any =await randomBytesAsync(Math.ceil(length / 2));
    return bytes.toString(encoding).slice(0, length);
  }
  
  /**
   * Generate a random string synchronously
   * @param length Length of the string
   * @param encoding Encoding of the string
   * @returns Random string
   */
  static generateRandomStringSync(length: number = 32, encoding: BufferEncoding = 'hex'): string {
    const bytes = crypto.randomBytes(Math.ceil(length / 2));
    return bytes.toString(encoding).slice(0, length);
  }
  
  /**
   * Generate a UUID v4
   * @returns UUID v4
   */
  static generateUuid(): string {
    return crypto.randomUUID();
  }
  
  /**
   * Hash a password
   * @param password Password to hash
   * @param salt Salt to use (optional, will be generated if not provided)
   * @param iterations Number of iterations (default: 10000)
   * @param keylen Key length (default: 64)
   * @param digest Digest algorithm (default: sha512)
   * @returns Object containing the hash and salt
   */
  static async hashPassword(
    password: string,
    salt?: string,
    iterations: number = 10000,
    keylen: number = 64,
    digest: string = 'sha512'
  ): Promise<{ hash: string; salt: string }> {
    const saltToUse = salt || (await CryptoUtils.generateRandomString(16));
    
    const hash: any =await pbkdf2Async(
      password,
      saltToUse,
      iterations,
      keylen,
      digest
    );
    
    return {
      hash: hash.toString('hex'),
      salt: saltToUse
    };
  }
  
  /**
   * Hash a password synchronously
   * @param password Password to hash
   * @param salt Salt to use (optional, will be generated if not provided)
   * @param iterations Number of iterations (default: 10000)
   * @param keylen Key length (default: 64)
   * @param digest Digest algorithm (default: sha512)
   * @returns Object containing the hash and salt
   */
  static hashPasswordSync(
    password: string,
    salt?: string,
    iterations: number = 10000,
    keylen: number = 64,
    digest: string = 'sha512'
  ): { hash: string; salt: string } {
    const saltToUse = salt || CryptoUtils.generateRandomStringSync(16);
    
    const hash: any =crypto.pbkdf2Sync(
      password,
      saltToUse,
      iterations,
      keylen,
      digest
    );
    
    return {
      hash: hash.toString('hex'),
      salt: saltToUse
    };
  }
  
  /**
   * Verify a password
   * @param password Password to verify
   * @param hash Hash to compare against
   * @param salt Salt used to hash the password
   * @param iterations Number of iterations (default: 10000)
   * @param keylen Key length (default: 64)
   * @param digest Digest algorithm (default: sha512)
   * @returns True if the password is valid
   */
  static async verifyPassword(
    password: string,
    hash: string,
    salt: string,
    iterations: number = 10000,
    keylen: number = 64,
    digest: string = 'sha512'
  ): Promise<boolean> {
    const result: any =await CryptoUtils.hashPassword(
      password,
      salt,
      iterations,
      keylen,
      digest
    );
    
    return result.hash === hash;
  }
  
  /**
   * Verify a password synchronously
   * @param password Password to verify
   * @param hash Hash to compare against
   * @param salt Salt used to hash the password
   * @param iterations Number of iterations (default: 10000)
   * @param keylen Key length (default: 64)
   * @param digest Digest algorithm (default: sha512)
   * @returns True if the password is valid
   */
  static verifyPasswordSync(
    password: string,
    hash: string,
    salt: string,
    iterations: number = 10000,
    keylen: number = 64,
    digest: string = 'sha512'
  ): boolean {
    const result = CryptoUtils.hashPasswordSync(
      password,
      salt,
      iterations,
      keylen,
      digest
    );
    
    return result.hash === hash;
  }
  
  /**
   * Encrypt data
   * @param data Data to encrypt
   * @param key Encryption key
   * @param algorithm Encryption algorithm (default: aes-256-cbc)
   * @returns Encrypted data
   */
  static encrypt(
    data: string,
    key: string,
    algorithm: string = 'aes-256-cbc'
  ): { encrypted: string; iv: string } {
    // Create a buffer from the key
    const keyBuffer: any =Buffer.from(key);
    
    // Create an initialization vector
    const iv: any =crypto.randomBytes(16);
    
    // Create a cipher
    const cipher: any =crypto.createCipheriv(
      algorithm,
      keyBuffer,
      iv
    );
    
    // Encrypt the data
    let encrypted: any =cipher.update(data, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    return {
      encrypted,
      iv: iv.toString('hex')
    };
  }
  
  /**
   * Decrypt data
   * @param encrypted Encrypted data
   * @param key Encryption key
   * @param iv Initialization vector
   * @param algorithm Encryption algorithm (default: aes-256-cbc)
   * @returns Decrypted data
   */
  static decrypt(
    encrypted: string,
    key: string,
    iv: string,
    algorithm: string = 'aes-256-cbc'
  ): string {
    // Create a buffer from the key
    const keyBuffer: any =Buffer.from(key);
    
    // Create a buffer from the initialization vector
    const ivBuffer: any =Buffer.from(iv, 'hex');
    
    // Create a decipher
    const decipher: any =crypto.createDecipheriv(
      algorithm,
      keyBuffer,
      ivBuffer
    );
    
    // Decrypt the data
    let decrypted: any =decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  }
  
  /**
   * Generate a hash
   * @param data Data to hash
   * @param algorithm Hash algorithm (default: sha256)
   * @returns Hash
   */
  static hash(data: string, algorithm: string = 'sha256'): string {
    return crypto.createHash(algorithm).update(data).digest('hex');
  }
  
  /**
   * Generate an HMAC
   * @param data Data to sign
   * @param key Key to use
   * @param algorithm Hash algorithm (default: sha256)
   * @returns HMAC
   */
  static hmac(data: string, key: string, algorithm: string = 'sha256'): string {
    return crypto.createHmac(algorithm, key).update(data).digest('hex');
  }
  
  /**
   * Generate a secure token
   * @param length Token length (default: 32)
   * @returns Secure token
   */
  static async generateToken(length: number = 32): Promise<string> {
    return CryptoUtils.generateRandomString(length);
  }
  
  /**
   * Generate a secure token synchronously
   * @param length Token length (default: 32)
   * @returns Secure token
   */
  static generateTokenSync(length: number = 32): string {
    return CryptoUtils.generateRandomStringSync(length);
  }
  
  /**
   * Generate a secure API key
   * @param prefix API key prefix
   * @param length API key length (default: 32)
   * @returns Secure API key
   */
  static async generateApiKey(prefix: string, length: number = 32): Promise<string> {
    const token: any =await CryptoUtils.generateRandomString(length);
    return `${prefix}_${token}`;
  }
  
  /**
   * Generate a secure API key synchronously
   * @param prefix API key prefix
   * @param length API key length (default: 32)
   * @returns Secure API key
   */
  static generateApiKeySync(prefix: string, length: number = 32): string {
    const token = CryptoUtils.generateRandomStringSync(length);
    return `${prefix}_${token}`;
  }
  
  /**
   * Compare two strings in constant time
   * @param a First string
   * @param b Second string
   * @returns True if the strings are equal
   */
  static constantTimeCompare(a: string, b: string): boolean {
    if (a.length !== b.length) {
      return false;
    }
    
    return crypto.timingSafeEqual(
      Buffer.from(a),
      Buffer.from(b)
    );
  }
}
