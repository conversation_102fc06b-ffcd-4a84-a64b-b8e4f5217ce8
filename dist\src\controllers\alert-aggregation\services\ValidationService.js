"use strict";
/**
 * Validation Service
 *
 * Handles input validation for alert aggregation operations.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ValidationService = void 0;
const types_1 = require("../../../types");
const AppError_1 = require("../../../utils/errors/AppError");
/**
 * Validation service for alert aggregation
 */
class ValidationService {
    /**
     * Validate aggregation rule creation request
     */
    validateCreateAggregationRule(data) {
        const errors = [];
        // Validate required fields using helper methods
        this.validateNameField(data.name, errors, true);
        this.validateDescriptionField(data.description, errors, true);
        this.validateTypeField(data.type, errors, true);
        this.validateSeverityField(data.severity, errors, true);
        this.validateTimeWindowField(data.timeWindow, errors, true);
        this.validateThresholdField(data.threshold, errors, true);
        this.validateGroupByField(data.groupBy, errors, true);
        this.validateEnabledField(data.enabled, errors);
        if (errors.length > 0) {
            throw new AppError_1.AppError({
                message: 'Validation failed',
                type: AppError_1.ErrorType.VALIDATION,
                code: AppError_1.ErrorCode.INVALID_INPUT,
                details: { errors },
            });
        }
        return {
            name: data.name.trim(),
            description: data.description.trim(),
            type: data.type,
            severity: data.severity,
            timeWindow: data.timeWindow,
            threshold: data.threshold,
            groupBy: data.groupBy,
            enabled: data.enabled !== undefined ? data.enabled : true,
        };
    }
    /**
     * Validate aggregation rule update request
     */
    validateUpdateAggregationRule(data) {
        const errors = [];
        // Validate optional fields using helper methods
        if (data.name !== undefined)
            this.validateNameField(data.name, errors, false);
        if (data.description !== undefined)
            this.validateDescriptionField(data.description, errors, false);
        if (data.type !== undefined)
            this.validateTypeField(data.type, errors, false);
        if (data.severity !== undefined)
            this.validateSeverityField(data.severity, errors, false);
        if (data.timeWindow !== undefined)
            this.validateTimeWindowField(data.timeWindow, errors, false);
        if (data.threshold !== undefined)
            this.validateThresholdField(data.threshold, errors, false);
        if (data.groupBy !== undefined)
            this.validateGroupByField(data.groupBy, errors, false);
        if (data.enabled !== undefined)
            this.validateEnabledField(data.enabled, errors);
        if (errors.length > 0) {
            throw new AppError_1.AppError({
                message: 'Validation failed',
                type: AppError_1.ErrorType.VALIDATION,
                code: AppError_1.ErrorCode.INVALID_INPUT,
                details: { errors },
            });
        }
        const result = {};
        if (data.name !== undefined)
            result.name = data.name.trim();
        if (data.description !== undefined)
            result.description = data.description.trim();
        if (data.type !== undefined)
            result.type = data.type;
        if (data.severity !== undefined)
            result.severity = data.severity;
        if (data.timeWindow !== undefined)
            result.timeWindow = data.timeWindow;
        if (data.threshold !== undefined)
            result.threshold = data.threshold;
        if (data.groupBy !== undefined)
            result.groupBy = data.groupBy;
        if (data.enabled !== undefined)
            result.enabled = data.enabled;
        return result;
    }
    /**
     * Validate correlation condition
     */
    validateCorrelationCondition(condition, index) {
        const errors = [];
        if (!condition.alertType || !Object.values(types_1.AlertType).includes(condition.alertType)) {
            errors.push({
                field: `conditions[${index}].alertType`,
                message: `Invalid alert type. Must be one of: ${Object.values(types_1.AlertType).join(', ')}`,
                value: condition.alertType,
            });
        }
        if (!condition.severity || !Object.values(types_1.AlertSeverity).includes(condition.severity)) {
            errors.push({
                field: `conditions[${index}].severity`,
                message: `Invalid alert severity. Must be one of: ${Object.values(types_1.AlertSeverity).join(', ')}`,
                value: condition.severity,
            });
        }
        if (typeof condition.count !== 'number' || condition.count <= 0) {
            errors.push({
                field: `conditions[${index}].count`,
                message: 'Count must be a positive number',
                value: condition.count,
            });
        }
        const validOperators = [
            'EQUALS',
            'GREATER_THAN',
            'LESS_THAN',
            'GREATER_THAN_OR_EQUAL',
            'LESS_THAN_OR_EQUAL',
        ];
        if (!condition.operator || !validOperators.includes(condition.operator)) {
            errors.push({
                field: `conditions[${index}].operator`,
                message: `Invalid operator. Must be one of: ${validOperators.join(', ')}`,
                value: condition.operator,
            });
        }
        if (errors.length > 0) {
            throw new AppError_1.AppError({
                message: 'Validation failed',
                type: AppError_1.ErrorType.VALIDATION,
                code: AppError_1.ErrorCode.INVALID_INPUT,
                details: { errors },
            });
        }
        return {
            alertType: condition.alertType,
            severity: condition.severity,
            count: condition.count,
            operator: condition.operator,
        };
    }
    /**
     * Validate ID parameter
     */
    validateId(id, fieldName = 'id') {
        if (!id) {
            throw new AppError_1.AppError({
                message: `${fieldName} is required`,
                type: AppError_1.ErrorType.VALIDATION,
                code: AppError_1.ErrorCode.MISSING_REQUIRED_FIELD,
            });
        }
        if (typeof id !== 'string' || id.trim().length === 0) {
            throw new AppError_1.AppError({
                message: `${fieldName} must be a non-empty string`,
                type: AppError_1.ErrorType.VALIDATION,
                code: AppError_1.ErrorCode.INVALID_INPUT,
            });
        }
        // Basic UUID validation (can be enhanced)
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
        if (!uuidRegex.test(id)) {
            throw new AppError_1.AppError({
                message: `${fieldName} must be a valid UUID`,
                type: AppError_1.ErrorType.VALIDATION,
                code: AppError_1.ErrorCode.INVALID_INPUT,
            });
        }
        return id.trim();
    }
    /**
     * Validate pagination parameters
     */
    validatePaginationParams(query) {
        const page = query.page ? parseInt(query.page, 10) : 1;
        const limit = query.limit ? parseInt(query.limit, 10) : 10;
        if (isNaN(page) || page < 1) {
            throw new AppError_1.AppError({
                message: 'Page must be a positive integer',
                type: AppError_1.ErrorType.VALIDATION,
                code: AppError_1.ErrorCode.INVALID_INPUT,
            });
        }
        if (isNaN(limit) || limit < 1 || limit > 100) {
            throw new AppError_1.AppError({
                message: 'Limit must be between 1 and 100',
                type: AppError_1.ErrorType.VALIDATION,
                code: AppError_1.ErrorCode.INVALID_INPUT,
            });
        }
        const result = { page, limit };
        if (query.sortBy) {
            const validSortFields = ['name', 'createdAt', 'updatedAt', 'enabled'];
            if (!validSortFields.includes(query.sortBy)) {
                throw new AppError_1.AppError({
                    message: `Invalid sort field. Must be one of: ${validSortFields.join(', ')}`,
                    type: AppError_1.ErrorType.VALIDATION,
                    code: AppError_1.ErrorCode.INVALID_INPUT,
                });
            }
            result.sortBy = query.sortBy;
        }
        if (query.sortOrder) {
            if (!['asc', 'desc'].includes(query.sortOrder)) {
                throw new AppError_1.AppError({
                    message: 'Sort order must be either "asc" or "desc"',
                    type: AppError_1.ErrorType.VALIDATION,
                    code: AppError_1.ErrorCode.INVALID_INPUT,
                });
            }
            result.sortOrder = query.sortOrder;
        }
        return result;
    }
    /**
     * Helper validation methods
     */
    validateNameField(name, errors, required) {
        if (required && !name) {
            errors.push({ field: 'name', message: 'Name is required' });
            return;
        }
        if (name !== undefined) {
            if (typeof name !== 'string' || name.trim().length === 0) {
                errors.push({ field: 'name', message: 'Name must be a non-empty string' });
            }
            else if (name.length > 100) {
                errors.push({ field: 'name', message: 'Name must be less than 100 characters' });
            }
        }
    }
    validateDescriptionField(description, errors, required) {
        if (required && !description) {
            errors.push({ field: 'description', message: 'Description is required' });
            return;
        }
        if (description !== undefined) {
            if (typeof description !== 'string' || description.trim().length === 0) {
                errors.push({ field: 'description', message: 'Description must be a non-empty string' });
            }
            else if (description.length > 500) {
                errors.push({
                    field: 'description',
                    message: 'Description must be less than 500 characters',
                });
            }
        }
    }
    validateTypeField(type, errors, required) {
        if (required && !type) {
            errors.push({ field: 'type', message: 'Type is required' });
            return;
        }
        if (type !== undefined && type !== 'ANY' && !Object.values(types_1.AlertType).includes(type)) {
            errors.push({
                field: 'type',
                message: `Invalid alert type. Must be one of: ${Object.values(types_1.AlertType).join(', ')}, ANY`,
                value: type,
            });
        }
    }
    validateSeverityField(severity, errors, required) {
        if (required && !severity) {
            errors.push({ field: 'severity', message: 'Severity is required' });
            return;
        }
        if (severity !== undefined &&
            severity !== 'ANY' &&
            !Object.values(types_1.AlertSeverity).includes(severity)) {
            errors.push({
                field: 'severity',
                message: `Invalid alert severity. Must be one of: ${Object.values(types_1.AlertSeverity).join(', ')}, ANY`,
                value: severity,
            });
        }
    }
    validateTimeWindowField(timeWindow, errors, required) {
        if (required && (timeWindow === undefined || timeWindow === null)) {
            errors.push({ field: 'timeWindow', message: 'Time window is required' });
            return;
        }
        if (timeWindow !== undefined) {
            if (typeof timeWindow !== 'number' || timeWindow <= 0) {
                errors.push({ field: 'timeWindow', message: 'Time window must be a positive number' });
            }
            else if (timeWindow > 86400) {
                errors.push({
                    field: 'timeWindow',
                    message: 'Time window cannot exceed 24 hours (86400 seconds)',
                });
            }
        }
    }
    validateThresholdField(threshold, errors, required) {
        if (required && (threshold === undefined || threshold === null)) {
            errors.push({ field: 'threshold', message: 'Threshold is required' });
            return;
        }
        if (threshold !== undefined) {
            if (typeof threshold !== 'number' || threshold <= 0) {
                errors.push({ field: 'threshold', message: 'Threshold must be a positive number' });
            }
            else if (threshold > 10000) {
                errors.push({ field: 'threshold', message: 'Threshold cannot exceed 10000' });
            }
        }
    }
    validateGroupByField(groupBy, errors, required) {
        if (required && !groupBy) {
            errors.push({ field: 'groupBy', message: 'Group by is required' });
            return;
        }
        if (groupBy !== undefined) {
            if (!Array.isArray(groupBy)) {
                errors.push({ field: 'groupBy', message: 'Group by must be an array' });
            }
            else if (groupBy.length === 0) {
                errors.push({ field: 'groupBy', message: 'Group by must contain at least one field' });
            }
            else {
                const validGroupByFields = ['type', 'severity', 'source', 'merchantId', 'userId'];
                const invalidFields = groupBy.filter((field) => !validGroupByFields.includes(field));
                if (invalidFields.length > 0) {
                    errors.push({
                        field: 'groupBy',
                        message: `Invalid group by fields: ${invalidFields.join(', ')}. Valid fields: ${validGroupByFields.join(', ')}`,
                        value: invalidFields,
                    });
                }
            }
        }
    }
    validateEnabledField(enabled, errors) {
        if (enabled !== undefined && typeof enabled !== 'boolean') {
            errors.push({ field: 'enabled', message: 'Enabled must be a boolean' });
        }
    }
}
exports.ValidationService = ValidationService;
//# sourceMappingURL=ValidationService.js.map