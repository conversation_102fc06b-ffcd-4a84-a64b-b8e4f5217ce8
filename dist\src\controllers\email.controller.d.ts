import { BaseController } from "./base.controller";
declare global {
    namespace Express {
        interface Request {
            user?: {
                id: string;
                role: string;
                merchantId?: string;
            };
        }
    }
}
/**
 * EmailController
 * Controller for handling email operations
 */
export declare class EmailController extends BaseController {
    constructor();
    /**
     * Test the email service by sending a test email
     */
    testEmailService: any;
    /**
     * Send a custom email
     */
    sendCustomEmail: any;
    /**
     * Get admin email addresses
     */
    getAdminEmails: any;
}
declare const _default: EmailController;
export default _default;
//# sourceMappingURL=email.controller.d.ts.map