"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RouteTestHelper = void 0;
const supertest_1 = __importDefault(require("supertest"));
const RouteRegistry_1 = require("../../core/RouteRegistry");
const RouteVersionManager_1 = require("../../core/RouteVersionManager");
const RouteMonitor_1 = require("../../core/RouteMonitor");
const logger_1 = require("../../lib/logger");
/**
 * Route test helper
 * This class helps with testing routes
 */
class RouteTestHelper {
    /**
     * Create a new route test helper
     * @param app Express application
     */
    constructor(app) {
        this.app = app;
        this.routeRegistry = RouteRegistry_1.RouteRegistry.getInstance();
        this.routeVersionManager = RouteVersionManager_1.RouteVersionManager.getInstance();
        this.routeMonitor = RouteMonitor_1.RouteMonitor.getInstance();
    }
    /**
     * Create a mock router
     * @param key Router key
     * @param path Router path
     * @param handler Request handler
     * @returns Express router
     */
    createMockRouter(key, path, handler) {
        const router = Router();
        // Add handler
        router.get("/", handler);
        // Register router
        this.routeRegistry.register(key, router, { path });
        return router;
    }
    /**
     * Create a mock versioned router
     * @param version Version
     * @param key Router key
     * @param path Router path
     * @param handler Request handler
     * @returns Express router
     */
    createMockVersionedRouter(version, key, path, handler) {
        const router = Router();
        // Add handler
        router.get("/", handler);
        // Register router
        this.routeVersionManager.registerVersionedRoute(version, key, router, { path });
        return router;
    }
    /**
     * Create a mock middleware
     * @param name Middleware name
     * @param handler Middleware handler
     * @returns Express middleware
     */
    createMockMiddleware(name, handler) {
        return (req, res, next) => {
            logger_1.logger.debug(`Mock middleware ${name} called`);
            handler(req, res, next);
        };
    }
    /**
     * Test a route
     * @param method HTTP method
     * @param path Route path
     * @param expectedStatus Expected status code
     * @param expectedBody Expected response body
     * @param token Authentication token
     * @param body Request body
     * @returns Test result
     */
    async testRoute(method, path, expectedStatus, expectedBody, token, body) {
        // Create request
        let req = (0, supertest_1.default)(this.app)[method](path);
        // Add token if provided
        if (token) {
            req = req.set("Authorization", `Bearer ${token}`);
        }
        // Add body if provided
        if (body) {
            req = req.send(body);
        }
        // Send request
        const res = await req;
        // Check status
        expect(res.status).toBe(expectedStatus);
        // Check body if provided
        if (expectedBody) {
            expect(res.body).toMatchObject(expectedBody);
        }
        return res;
    }
    /**
     * Test a route with authentication
     * @param method HTTP method
     * @param path Route path
     * @param token Authentication token
     * @param expectedStatus Expected status code
     * @param expectedBody Expected response body
     * @param body Request body
     * @returns Test result
     */
    async testAuthenticatedRoute(method, path, token, expectedStatus, expectedBody, body) {
        return this.testRoute(method, path, expectedStatus, expectedBody, token, body);
    }
    /**
     * Test a route without authentication
     * @param method HTTP method
     * @param path Route path
     * @param expectedStatus Expected status code
     * @param expectedBody Expected response body
     * @param body Request body
     * @returns Test result
     */
    async testUnauthenticatedRoute(method, path, expectedStatus, expectedBody, body) {
        return this.testRoute(method, path, expectedStatus, expectedBody, undefined, body);
    }
    /**
     * Reset route metrics
     */
    resetRouteMetrics() {
        this.routeMonitor.resetAllMetrics();
    }
}
exports.RouteTestHelper = RouteTestHelper;
exports.default = RouteTestHelper;
//# sourceMappingURL=RouteTestHelper.js.map