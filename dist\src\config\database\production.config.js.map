{"version": 3, "file": "production.config.js", "sourceRoot": "", "sources": ["../../../../src/config/database/production.config.ts"], "names": [], "mappings": ";AAAA,oBAAoB;AACpB;;;;;GAKG;;;AAEH,6CAA0C;AAE1C;;GAEG;AACU,QAAA,wBAAwB,GAAQ;IACzC;;;KAGC;IACD,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,EAAE;IAEnC;;KAEC;IACD,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,WAAW;IAExC;;KAEC;IACD,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,MAAM,EAAE,EAAE,CAAC;IAEjD;;KAEC;IACD,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,UAAU;IAE/C;;KAEC;IACD,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,EAAE;IAEvC;;KAEC;IACD,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,YAAY;IAE7C;;KAEC;IACD,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,MAAM,KAAK,MAAM;IAElC;;KAEC;IACD,IAAI,EAAE;QACN;;WAEG;QACC,GAAG,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,GAAG,EAAE,EAAE,CAAC;QAE5D;;OAED;QACC,GAAG,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,IAAI,EAAE,EAAE,CAAC;QAE7D;;OAED;QACC,IAAI,EAAE,KAAK;QAEX;;OAED;QACC,OAAO,EAAE,KAAK;KACjB;IAED;;KAEC;IACD,gBAAgB,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI,OAAO,EAAE,EAAE,CAAC;IAE3E;;KAEC;IACD,OAAO,EAAE;QACT;;WAEG;QACC,OAAO,EAAE,KAAK;QAEd;;OAED;QACC,MAAM,EAAE,IAAI;QAEZ;;OAED;QACC,QAAQ,EAAE,IAAI;QAEd;;OAED;QACC,WAAW,EAAE,IAAI;QAEjB;;OAED;QACC,kBAAkB,EAAE,IAAI;KAC3B;CACJ,CAAC;AAEF;;;GAGG;AACI,MAAM,8BAA8B,GAAO,GAAW,EAAE;IAC3D,yDAAyD;IACzD,IAAI,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE,CAAC;QAC3B,OAAO,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;IACpC,CAAC;IAED,0DAA0D;IAC1D,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,EAAE,GAAG,gCAAwB,CAAC;IAEnF,6BAA6B;IAC7B,MAAM,GAAG,GAAO,gBAAgB,QAAQ,IAAI,QAAQ,IAAI,IAAI,IAAI,IAAI,IAAI,QAAQ,iBAAiB,GAAG,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IAEjI,eAAM,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC;IAE7D,OAAO,GAAG,CAAC;AACf,CAAC,CAAC;AAfW,QAAA,8BAA8B,kCAezC;AAEF;;;GAGG;AACI,MAAM,0BAA0B,GAAO,GAAG,EAAE;IAC/C,OAAO;QACH,WAAW,EAAE,EAAG,EAAE,EAAE;gBACZ,GAAG,EAAE,IAAA,sCAA8B,GAAE;aACxC;SACJ;QACD,GAAG,EAAE,CAAC,OAAO,CAAC;QACd,WAAW,EAAE,SAAS;KACzB,CAAC;AACN,CAAC,CAAC;AATW,QAAA,0BAA0B,8BASrC;AAEF,kBAAe,gCAAwB,CAAC"}