/**
 * Report Types and Interfaces
 * 
 * Centralized type definitions for the reporting system.
 */

/**
 * Supported report types
 */
export enum ReportType {
  TRANSACTION = 'TRANSACTION',
  CUSTOMER = 'CUSTOMER',
  PAYMENT_METHOD = 'PAYMENT_METHOD',
  SUBSCRIPTION = 'SUBSCRIPTION'
}

/**
 * Supported export formats
 */
export enum ExportFormat {
  CSV = 'CSV',
  PDF = 'PDF',
  EXCEL = 'EXCEL',
  JSON = 'JSON'
}

/**
 * Report status
 */
export enum ReportStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  SUCCESS = 'SUCCESS',
  FAILED = 'FAILED'
}

/**
 * Base report parameters
 */
export interface BaseReportParams {
  startDate?: string;
  endDate?: string;
  merchantId?: string;
  userId?: string;
  userRole?: string;
  limit?: number;
  offset?: number;
}

/**
 * Transaction report parameters
 */
export interface TransactionReportParams extends BaseReportParams {
  status?: string;
  minAmount?: number;
  maxAmount?: number;
  currency?: string;
  paymentMethod?: string;
}

/**
 * Customer report parameters
 */
export interface CustomerReportParams extends BaseReportParams {
  status?: string;
  country?: string;
  city?: string;
}

/**
 * Payment method report parameters
 */
export interface PaymentMethodReportParams extends BaseReportParams {
  type?: string;
  isActive?: boolean;
  isDefault?: boolean;
}

/**
 * Subscription report parameters
 */
export interface SubscriptionReportParams extends BaseReportParams {
  status?: string;
  planId?: string;
  interval?: string;
}

/**
 * Report generation request
 */
export interface ReportGenerationRequest {
  type: ReportType;
  format: ExportFormat;
  parameters: BaseReportParams;
  name?: string;
  templateId?: string;
}

/**
 * Report generation result
 */
export interface ReportGenerationResult {
  id: string;
  filePath: string;
  rowCount: number;
  format: ExportFormat;
  fileSize: number;
  metadata?: any;
}

/**
 * Report template
 */
export interface ReportTemplate {
  id: string;
  name: string;
  type: ReportType;
  description?: string;
  parameters: any;
  columns: ReportColumn[];
  filters: ReportFilter[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Report column definition
 */
export interface ReportColumn {
  key: string;
  label: string;
  type: 'string' | 'number' | 'date' | 'boolean';
  format?: string;
  width?: number;
  sortable?: boolean;
  filterable?: boolean;
}

/**
 * Report filter definition
 */
export interface ReportFilter {
  key: string;
  label: string;
  type: 'text' | 'number' | 'date' | 'select' | 'multiselect';
  options?: { value: any; label: string }[];
  required?: boolean;
  defaultValue?: any;
}

/**
 * Scheduled report configuration
 */
export interface ScheduledReportConfig {
  id?: string;
  name: string;
  templateId: string;
  cronExpression: string;
  exportFormat: ExportFormat;
  parameters: any;
  recipients: string[];
  isActive: boolean;
  lastRunAt?: Date;
  nextRunAt?: Date;
}

/**
 * Report run record
 */
export interface ReportRun {
  id: string;
  scheduledReportId: string;
  status: ReportStatus;
  startedAt: Date;
  completedAt?: Date;
  filePath?: string;
  fileType?: string;
  fileSize?: number;
  rowCount?: number;
  error?: string;
  metadata?: any;
}

/**
 * Report size estimation
 */
export interface ReportSizeEstimate {
  recordCount: number;
  estimatedSizeBytes: number;
  recommendStreaming: boolean;
  estimatedDuration: number;
}

/**
 * Export options
 */
export interface ExportOptions {
  filename?: string;
  includeHeaders?: boolean;
  dateFormat?: string;
  numberFormat?: string;
  compression?: boolean;
  password?: string;
}

/**
 * Email delivery options
 */
export interface EmailDeliveryOptions {
  recipients: string[];
  subject: string;
  body?: string;
  attachmentName?: string;
  deleteAfterSend?: boolean;
}

/**
 * Report statistics
 */
export interface ReportStats {
  totalReports: number;
  successfulReports: number;
  failedReports: number;
  averageGenerationTime: number;
  reportsByType: Record<ReportType, number>;
  reportsByFormat: Record<ExportFormat, number>;
  totalFileSize: number;
}

/**
 * Report data row interface
 */
export interface ReportDataRow {
  [key: string]: any;
}

/**
 * Report generator interface
 */
export interface IReportGenerator {
  getType(): ReportType;
  generateData(parameters: BaseReportParams): Promise<ReportDataRow[]>;
  validateParameters(parameters: BaseReportParams): void;
  getDefaultColumns(): ReportColumn[];
}

/**
 * Report exporter interface
 */
export interface IReportExporter {
  getFormat(): ExportFormat;
  export(data: ReportDataRow[], filePath: string, options?: ExportOptions): Promise<void>;
  getMimeType(): string;
  getFileExtension(): string;
}

/**
 * Report error types
 */
export enum ReportErrorCode {
  INVALID_PARAMETERS = 'INVALID_PARAMETERS',
  UNSUPPORTED_TYPE = 'UNSUPPORTED_TYPE',
  UNSUPPORTED_FORMAT = 'UNSUPPORTED_FORMAT',
  GENERATION_FAILED = 'GENERATION_FAILED',
  EXPORT_FAILED = 'EXPORT_FAILED',
  TEMPLATE_NOT_FOUND = 'TEMPLATE_NOT_FOUND',
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  FILE_NOT_FOUND = 'FILE_NOT_FOUND',
  EMAIL_DELIVERY_FAILED = 'EMAIL_DELIVERY_FAILED'
}

/**
 * Report error class
 */
export class ReportError extends Error {
  code: ReportErrorCode;
  statusCode: number;

  constructor(message: string, code: ReportErrorCode, statusCode: number = 400) {
    super(message);
    this.name = 'ReportError';
    this.code = code;
    this.statusCode = statusCode;
  }
}
