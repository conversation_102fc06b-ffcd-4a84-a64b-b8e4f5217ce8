"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.verifyTrc20Deposit = exports.getDepositHistory = exports.getAccountInfo = exports.testConnection = void 0;
const binance_service_1 = require("../services/binance.service");
const error_middleware_1 = require("../middlewares/error.middleware");
// Test Binance API connection
exports.testConnection = (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const { apiKey, apiSecret } = req.body;
    // Validate required fields
    if (!apiKey || !apiSecret) {
        throw new error_middleware_1.AppError({
            message: "API key and secret are required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }
    // Test connection
    const result = await binance_service_1.BinanceService.testConnection(apiKey, apiSecret);
    if (!result.success) {
        return res.status(400).json({
            success: false,
            message: result.message
        });
    }
    res.status(200).json({
        success: true,
        message: "Connection successful"
    });
});
// Get account information
exports.getAccountInfo = (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const { apiKey, apiSecret } = req.body;
    // Validate required fields
    if (!apiKey || !apiSecret) {
        throw new error_middleware_1.AppError({
            message: "API key and secret are required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }
    // Get account information
    const accountInfo = await binance_service_1.BinanceService.getAccountInfo(apiKey, apiSecret);
    res.status(200).json(accountInfo);
});
// Get deposit history
exports.getDepositHistory = (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const { apiKey, apiSecret, coin, status, startTime, endTime } = req.body;
    // Validate required fields
    if (!apiKey || !apiSecret) {
        throw new error_middleware_1.AppError({
            message: "API key and secret are required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }
    // Get deposit history
    const depositHistory = await binance_service_1.BinanceService.getDepositHistory(apiKey, apiSecret, coin, status, startTime, endTime);
    res.status(200).json(depositHistory);
});
// Verify TRC20 deposit
exports.verifyTrc20Deposit = (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const { apiKey, apiSecret, txHash, amount, coin, timeWindow } = req.body;
    // Validate required fields
    if (!apiKey || !apiSecret || !txHash || !amount) {
        throw new error_middleware_1.AppError({
            message: "API key, secret, transaction hash, and amount are required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }
    // Verify TRC20 deposit
    const result = await binance_service_1.BinanceService.verifyTrc20Deposit(apiKey, apiSecret, txHash, amount, coin, timeWindow);
    res.status(200).json(result);
});
//# sourceMappingURL=binance.controller.js.map