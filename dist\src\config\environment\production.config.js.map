{"version": 3, "file": "production.config.js", "sourceRoot": "", "sources": ["../../../../src/config/environment/production.config.ts"], "names": [], "mappings": ";AAAA,oBAAoB;AACpB;;;;;GAKG;;;AAEH,6CAA0C;AAC1C,yFAA+E;AAG/E;;GAEG;AACI,MAAM,yBAAyB,GAAO,GAAS,EAAE;IACpD,eAAM,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;IAExD,MAAM,OAAO,GAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,iBAAiB,CAAC,CAAC;IAEnE,IAAI,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;QACzB,MAAM,MAAM,GAAO,MAAM,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC;QAEpD,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;QACtE,CAAC;aAAM,CAAC;YACJ,eAAM,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;QACrE,CAAC;IACL,CAAC;SAAM,CAAC;QACJ,eAAM,CAAC,IAAI,CAAC,sEAAsE,CAAC,CAAC;IACxF,CAAC;AACL,CAAC,CAAC;AAhBW,QAAA,yBAAyB,6BAgBpC;AAEF;;GAEG;AACI,MAAM,0BAA0B,GAAO,KAAK,IAAmB,EAAE;IACpE,eAAM,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;IAEjE,wCAAwC;IACxC,IAAA,iCAAyB,GAAE,CAAC;IAE5B,gCAAgC;IAChC,mBAAmB,CAAC,UAAU,EAAE,CAAC;IAEjC,qCAAqC;IACrC,wBAAwB,CAAC,UAAU,EAAE,CAAC;IAEtC,qCAAqC;IACrC,OAAO,CAAC,GAAG,CAAC,gBAAgB,GAAG,wCAAe,CAAC,UAAU,CAAC;IAC1D,OAAO,CAAC,GAAG,CAAC,cAAc,GAAG,MAAM,CAAC;IAEpC,eAAM,CAAC,IAAI,CAAC,+DAA+D,CAAC,CAAC;AACjF,CAAC,CAAC;AAjBW,QAAA,0BAA0B,8BAiBrC;AAEF;;;GAGG;AACI,MAAM,6BAA6B,GAAO,GAG/C,EAAE;IACA,eAAM,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;IAEjD,MAAM,MAAM,GAAa,EAAE,CAAC;IAE5B,+BAA+B;IAC/B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE,CAAC;QAC5B,MAAM,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;IAC3C,CAAC;IAED,0BAA0B;IAC1B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,OAAO,CAAC,GAAG,CAAC,UAAU,KAAK,sCAAsC,EAAE,CAAC;QAC/F,MAAM,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;IAChE,CAAC;IAED,wBAAwB;IACxB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE,CAAC;QAClE,MAAM,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;IACvD,CAAC;IAED,4BAA4B;IAC5B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;QACjF,MAAM,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;IACrD,CAAC;IAED,6BAA6B;IAC7B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC;QACjD,MAAM,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;IACtD,CAAC;IAED,uCAAuC;IACvC,IAAI,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC;QAC9E,eAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;IAC7C,CAAC;SAAM,CAAC;QACJ,MAAM,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;IAC7D,CAAC;IAED,wCAAwC;IACxC,IAAI,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC;QACzB,eAAM,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;IAC9C,CAAC;SAAM,CAAC;QACJ,MAAM,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;IAC9D,CAAC;IAED,MAAM,KAAK,GAAO,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC;IAEtC,IAAI,KAAK,EAAE,CAAC;QACR,eAAM,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;IAC5D,CAAC;SAAM,CAAC;QACJ,eAAM,CAAC,IAAI,CAAC,uDAAuD,EAAE,MAAM,CAAC,CAAC;IACjF,CAAC;IAED,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;AAC7B,CAAC,CAAC;AAxDW,QAAA,6BAA6B,iCAwDxC;AAEF;;GAEG;AACU,QAAA,gBAAgB,GAAQ;IACjC,QAAQ,EAAE,wBAAwB;IAClC,GAAG,EAAE,mBAAmB;IACxB,QAAQ,EAAE,wBAAwB;IAClC,UAAU,EAAE,kCAA0B;IACtC,QAAQ,EAAE,qCAA6B;IACvC,eAAe,EAAE,iCAAyB;CAC7C,CAAC;AAEF,kBAAe,wBAAgB,CAAC"}