"use strict";
/**
 * Admin Response Mapper
 *
 * Handles response formatting for admin operations.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminResponseMapper = void 0;
const AppError_1 = require("../../../utils/errors/AppError");
/**
 * Response mapper for admin operations
 */
class AdminResponseMapper {
    /**
     * Send success response
     */
    static sendSuccess(res, data, message, statusCode = 200, pagination) {
        const response = {
            success: true,
            data,
            message,
            pagination,
            timestamp: new Date(),
            requestId: res.locals.requestId || 'unknown',
        };
        res.status(statusCode).json(response);
    }
    /**
     * Send error response
     */
    static sendError(res, error, statusCode) {
        let errorResponse;
        if (error instanceof AppError_1.AppError) {
            errorResponse = {
                success: false,
                error: {
                    message: error.message,
                    code: error.code,
                    type: error.type,
                    details: error.details,
                },
                timestamp: new Date(),
                requestId: res.locals.requestId || 'unknown',
            };
            statusCode = statusCode || error.statusCode || 400;
        }
        else {
            errorResponse = {
                success: false,
                error: {
                    message: error.message || 'Internal server error',
                    code: 'INTERNAL_SERVER_ERROR',
                    type: 'INTERNAL',
                },
                timestamp: new Date(),
                requestId: res.locals.requestId || 'unknown',
            };
            statusCode = statusCode || 500;
        }
        res.status(statusCode).json(errorResponse);
    }
    /**
     * Send dashboard data response
     */
    static sendDashboardData(res, data) {
        this.sendSuccess(res, data, 'Dashboard data retrieved successfully');
    }
    /**
     * Send dashboard statistics response
     */
    static sendDashboardStatistics(res, stats) {
        this.sendSuccess(res, stats, 'Dashboard statistics retrieved successfully');
    }
    /**
     * Send admin users list response
     */
    static sendAdminUsersList(res, users, total, page = 1, limit = 10) {
        const totalPages = Math.ceil(total / limit);
        this.sendSuccess(res, users, `Retrieved ${users.length} admin users`, 200, {
            page,
            limit,
            total,
            totalPages,
        });
    }
    /**
     * Send single admin user response
     */
    static sendAdminUser(res, user, message) {
        this.sendSuccess(res, user, message || 'Admin user retrieved successfully');
    }
    /**
     * Send admin user created response
     */
    static sendAdminUserCreated(res, user) {
        this.sendSuccess(res, user, 'Admin user created successfully', 201);
    }
    /**
     * Send admin user updated response
     */
    static sendAdminUserUpdated(res, user) {
        this.sendSuccess(res, user, 'Admin user updated successfully');
    }
    /**
     * Send admin user deleted response
     */
    static sendAdminUserDeleted(res) {
        this.sendSuccess(res, null, 'Admin user deleted successfully');
    }
    /**
     * Send roles list response
     */
    static sendRolesList(res, roles, total, page = 1, limit = 10) {
        const totalPages = Math.ceil(total / limit);
        this.sendSuccess(res, roles, `Retrieved ${roles.length} roles`, 200, {
            page,
            limit,
            total,
            totalPages,
        });
    }
    /**
     * Send single role response
     */
    static sendRole(res, role, message) {
        this.sendSuccess(res, role, message || 'Role retrieved successfully');
    }
    /**
     * Send role created response
     */
    static sendRoleCreated(res, role) {
        this.sendSuccess(res, role, 'Role created successfully', 201);
    }
    /**
     * Send role updated response
     */
    static sendRoleUpdated(res, role) {
        this.sendSuccess(res, role, 'Role updated successfully');
    }
    /**
     * Send role deleted response
     */
    static sendRoleDeleted(res) {
        this.sendSuccess(res, null, 'Role deleted successfully');
    }
    /**
     * Send permissions list response
     */
    static sendPermissionsList(res, permissions, total, page = 1, limit = 10) {
        const totalPages = Math.ceil(total / limit);
        this.sendSuccess(res, permissions, `Retrieved ${permissions.length} permissions`, 200, {
            page,
            limit,
            total,
            totalPages,
        });
    }
    /**
     * Send single permission response
     */
    static sendPermission(res, permission, message) {
        this.sendSuccess(res, permission, message || 'Permission retrieved successfully');
    }
    /**
     * Send permission created response
     */
    static sendPermissionCreated(res, permission) {
        this.sendSuccess(res, permission, 'Permission created successfully', 201);
    }
    /**
     * Send permission updated response
     */
    static sendPermissionUpdated(res, permission) {
        this.sendSuccess(res, permission, 'Permission updated successfully');
    }
    /**
     * Send permission deleted response
     */
    static sendPermissionDeleted(res) {
        this.sendSuccess(res, null, 'Permission deleted successfully');
    }
    /**
     * Send system health response
     */
    static sendSystemHealth(res, health) {
        const statusCode = health.status === 'healthy' ? 200 : health.status === 'degraded' ? 200 : 503;
        this.sendSuccess(res, health, `System is ${health.status}`, statusCode);
    }
    /**
     * Send validation error response
     */
    static sendValidationError(res, errors, message = 'Validation failed') {
        const error = new AppError_1.AppError({
            message,
            type: 'VALIDATION',
            code: 'INVALID_INPUT',
            details: { errors },
        });
        this.sendError(res, error, 400);
    }
    /**
     * Send authorization error response
     */
    static sendAuthorizationError(res, message = 'Access denied', requiredRole) {
        const error = new AppError_1.AppError({
            message,
            type: 'AUTHENTICATION',
            code: 'INVALID_CREDENTIALS',
            details: { requiredRole },
        });
        this.sendError(res, error, 403);
    }
    /**
     * Send not found error response
     */
    static sendNotFoundError(res, resource = 'Resource') {
        const error = new AppError_1.AppError({
            message: `${resource} not found`,
            type: 'NOT_FOUND',
            code: 'RESOURCE_NOT_FOUND',
        });
        this.sendError(res, error, 404);
    }
    /**
     * Send internal server error response
     */
    static sendInternalServerError(res, message = 'Internal server error') {
        const error = new AppError_1.AppError({
            message,
            type: 'INTERNAL',
            code: 'INTERNAL_SERVER_ERROR',
        });
        this.sendError(res, error, 500);
    }
    /**
     * Handle async controller method
     */
    static asyncHandler(fn) {
        return (req, res, next) => {
            Promise.resolve(fn(req, res, next)).catch((error) => next(error));
        };
    }
    /**
     * Set response headers for API
     */
    static setApiHeaders(res) {
        res.setHeader('Content-Type', 'application/json');
        res.setHeader('X-API-Version', '1.0');
        res.setHeader('X-Response-Time', Date.now());
    }
}
exports.AdminResponseMapper = AdminResponseMapper;
//# sourceMappingURL=AdminResponseMapper.js.map