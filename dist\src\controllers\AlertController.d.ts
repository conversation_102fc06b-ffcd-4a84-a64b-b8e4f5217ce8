import { BaseController } from "../base/BaseController";
/**
 * Alert controller
 */
export declare class AlertController extends BaseController {
    private alertService;
    constructor();
    /**
     * Get alerts
     * @route GET /api/alerts
     */
    getAlerts: any;
    /**
     * Get alert by ID
     * @route GET /api/alerts/:id
     */
    getAlert: any;
    /**
     * Update alert status
     * @route PUT /api/alerts/:id/status
     */
    updateAlertStatus: any;
    /**
     * Create a test alert
     * @route POST /api/alerts/test
     */
    createTestAlert: any;
    /**
     * Get alert count
     * @route GET /api/alerts/count
     */
    getAlertCount: any;
}
//# sourceMappingURL=AlertController.d.ts.map