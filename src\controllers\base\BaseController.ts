// jscpd:ignore-file
import { Request, Response } from 'express';

// Mock imports for now
const logger = {
  error: (message: string, error?: unknown) => console.error(message, error),
};

class AppError extends Error {
  statusCode: number;
  constructor(message: string, statusCode: number = 500) {
    super(message);
    this.statusCode = statusCode;
  }
}

class ServiceError extends Error {
  statusCode: number;
  code: string;
  details: unknown;
  constructor(options: { message: string; statusCode: number; code: string; details?: unknown }) {
    super(options.message);
    this.statusCode = options.statusCode;
    this.code = options.code;
    this.details = options.details;
  }
}

enum ErrorCode {
  INVALID_INPUT = 'INVALID_INPUT',
}

const asyncHandler = (fn: (...args: any[]) => any) => fn;

const ControllerUtils = {
  checkAuth: (req: Request) => ({ userRole: 'USER', userId: '1' }),
  checkAdmin: (req: Request) => ({ userId: '1' }),
  checkMerchant: (req: Request) => ({ userId: '1', merchantId: '1' }),
  parseDateRange: (req: Request, start: string, end: string) => ({
    startDate: new Date(),
    endDate: new Date(),
  }),
  parsePagination: (req: Request) => ({ page: 1, limit: 10, offset: 0 }),
  validateRequiredFields: (req: Request, fields: string[]) => {},
  validateEnum: (value: any, enumType: unknown, fieldName: string) => {},
  formatSuccessResponse: (data: any) => ({ success: true, data }),
  formatMessageResponse: (message: string) => ({ success: true, message }),
  formatPaginatedResponse: (data: any, total: number, page: number, limit: number) => ({
    success: true,
    data,
    pagination: { total, page, limit },
  }),
  formatErrorResponse: (message: string) => ({ success: false, message }),
};

export class BaseController {
  /**
   * Async handler for controller methods
   * @param fn (...args: any[]) => any to handle
   * @returns Express handler
   */
  protected asyncHandler(fn: (req: Request, res: Response) => Promise<unknown>) {
    return asyncHandler(fn);
  }

  /**
   * Check if user is authorized
   * @param req Express request
   * @throws AppError if user is not authorized
   */
  protected checkAuthorization(req: Request): {
    userRole: string;
    userId: string;
    merchantId?: string;
  } {
    return ControllerUtils.checkAuth(req);
  }

  /**
   * Check if user is admin
   * @param req Express request
   * @throws AppError if user is not admin
   */
  protected checkAdminRole(req: Request): { userId: string } {
    return ControllerUtils.checkAdmin(req);
  }

  /**
   * Check if user is merchant
   * @param req Express request
   * @throws AppError if user is not merchant
   */
  protected checkMerchantRole(req: Request): { userId: string; merchantId: string } {
    return ControllerUtils.checkMerchant(req);
  }

  /**
   * Parse date range from request
   * @param req Express request
   * @param startDateField Start date field name
   * @param endDateField End date field name
   * @returns Date range
   */
  protected parseDateRange(
    req: Request,
    startDateField = 'startDate',
    endDateField = 'endDate'
  ): { startDate: Date; endDate: Date } {
    return ControllerUtils.parseDateRange(req, startDateField, endDateField);
  }

  /**
   * Parse pagination parameters from request
   * @param req Express request
   * @returns Pagination parameters
   */
  protected parsePagination(req: Request): { page: number; limit: number; offset: number } {
    return ControllerUtils.parsePagination(req);
  }

  /**
   * Validate required fields
   * @param req Express request
   * @param fields Required fields
   * @throws AppError if unknown required field is missing
   */
  protected validateRequiredFields(req: Request, fields: string[]): any {
    ControllerUtils.validateRequiredFields(req, fields);
  }

  /**
   * Validate enum value
   * @param value Value to validate
   * @param enumType Enum type
   * @param fieldName Field name for error message
   * @throws AppError if value is not in enum
   */
  protected validateEnum<T extends object>(value: any, enumType: T, fieldName: string): any {
    ControllerUtils.validateEnum(value, enumType, fieldName);
  }

  /**
   * Send success response
   * @param res Express response
   * @param data Response data
   * @param statusCode HTTP status code
   */
  protected sendSuccess(res: Response, data: any, statusCode = 200): Response {
    return res.status(statusCode).json(ControllerUtils.formatSuccessResponse(data));
  }

  /**
   * Send message response
   * @param res Express response
   * @param message Response message
   * @param statusCode HTTP status code
   */
  protected sendMessage(res: Response, message: string, statusCode = 200): Response {
    return res.status(statusCode).json(ControllerUtils.formatMessageResponse(message));
  }

  /**
   * Send paginated success response
   * @param res Express response
   * @param data Response data
   * @param total Total number of items
   * @param page Current page
   * @param limit Items per page
   * @param statusCode HTTP status code
   */
  protected sendPaginatedSuccess(
    res: Response,
    data: any,
    total: number,
    page: number,
    limit: number,
    statusCode = 200
  ): Response {
    return res
      .status(statusCode)
      .json(ControllerUtils.formatPaginatedResponse(data, total, page, limit));
  }

  /**
   * Handle error
   * @param error Error
   * @param res Response
   */
  protected handleError(error: Error, res: Response): Response {
    logger.error(`Controller error:`, error);

    if (error instanceof ServiceError) {
      return res.status(error.statusCode).json({
        success: false,
        error: {
          message: (error as Error).message,
          code: error.code,
          details: error.details,
        },
      });
    }

    if (error instanceof AppError) {
      return res
        .status(error.statusCode)
        .json(ControllerUtils.formatErrorResponse((error as Error).message));
    }

    return res.status(500).json(ControllerUtils.formatErrorResponse('Internal server error'));
  }

  /**
   * Send error response
   * @param res Express response
   * @param error Error
   * @returns Response
   */
  protected sendError(
    res: Response,
    error: Error | AppError | ServiceError | string,
    statusCode = 500
  ): Response {
    if (typeof error === 'string') {
      return res.status(statusCode).json(ControllerUtils.formatErrorResponse(error));
    }

    return this.handleError(error, res);
  }

  /**
   * Send not found response
   * @param res Express response
   * @param resource Resource name
   * @param id Resource ID
   * @returns Response
   */
  protected sendNotFound(res: Response, resource: string, id: string): Response {
    const error = new AppError(`${resource} with ID ${id} not found`, 404);
    return this.handleError(error, res);
  }

  /**
   * Send validation error response
   * @param res Express response
   * @param errors Validation errors
   * @returns Response
   */
  protected sendValidationError(res: Response, errors: Record<string, string[]>): Response {
    const error = new ServiceError({
      message: 'Validation failed',
      statusCode: 400,
      code: ErrorCode.INVALID_INPUT,
      details: errors,
    });
    return this.handleError(error, res);
  }
}
