// jscpd:ignore-file

import { Request, Response } from "express";
import paymentService from "../services/payment.service";

class PaymentController {
    /**
     * Get all payments
     */
    async getAllPayments(req: Request, res: Response): Promise<Response> {
        try {
            const payments: any =await paymentService.getAllPayments();

            return res.status(200).json({
                status: "success",
                data: payments
            });
        } catch (error) {
            return res.status(500).json({
                status: "error",
                message: error instanceof Error ? (error as Error).message : "Failed to retrieve payments"
            });
        }
    }

    /**
     * Get payment by ID
     */
    async getPaymentById(req: Request, res: Response): Promise<Response> {
        try {
            const { id } = req.params;
            const payment: any =await paymentService.getPaymentById(id);

            if (!payment) {
                return res.status(404).json({
                    status: "error",
                    message: "Payment not found"
                });
            }

            return res.status(200).json({
                status: "success",
                data: payment
            });
        } catch (error) {
            return res.status(500).json({
                status: "error",
                message: error instanceof Error ? (error as Error).message : "Failed to retrieve payment"
            });
        }
    }

    /**
     * Get payments by merchant ID
     */
    async getPaymentsByMerchant(req: Request, res: Response): Promise<Response> {
        try {
            const { merchantId } = req.params;

            const payments: any =await paymentService.getPaymentsByMerchant(merchantId);

            return res.status(200).json({
                status: "success",
                data: payments
            });
        } catch (error) {
            return res.status(500).json({
                status: "error",
                message: error instanceof Error ? (error as Error).message : "Failed to get payments"
            });
        }
    }

    /**
     * Create a new payment
     */
    async createPayment(req: Request, res: Response): Promise<Response> {
        try {
            const {
                merchantId,
                merchantName,
                amount,
                currency,
                method,
                customerEmail,
                verificationMethod,
                verificationDetails
            } = req.body;

            // Basic validation
            if (!merchantId || !amount || !currency || !method || !verificationMethod) {
                return res.status(400).json({
                    status: "error",
                    message: "MerchantId, amount, currency, method, and verificationMethod are required"
                });
            }

            const newPayment: any =await paymentService.createPayment({
                merchantId,
                merchantName,
                amount: Number(amount),
                currency,
                status: "pending",
                method,
                customerEmail,
                date: new Date(),
                verificationMethod,
                verificationDetails
            });

            return res.status(201).json({
                status: "success",
                data: newPayment
            });
        } catch (error) {
            return res.status(500).json({
                status: "error",
                message: error instanceof Error ? (error as Error).message : "Failed to create payment"
            });
        }
    }

    /**
     * Update an existing payment
     */
    async updatePayment(req: Request, res: Response): Promise<Response> {
        try {
            const { id } = req.params;
            const updateData: any =req.body;

            // Prevent updating the ID
            delete updateData.id;

            // Update date if it exists and is a string
            if (updateData.date && typeof updateData.date === "string") {
                updateData.date = new Date(updateData.date);
            }

            const updatedPayment: any =await paymentService.updatePayment(id, updateData);

            if (!updatedPayment) {
                return res.status(404).json({
                    status: "error",
                    message: "Payment not found"
                });
            }

            return res.status(200).json({
                status: "success",
                data: updatedPayment
            });
        } catch (error) {
            return res.status(500).json({
                status: "error",
                message: error instanceof Error ? (error as Error).message : "Failed to update payment"
            });
        }
    }

    /**
     * Delete a payment
     */
    async deletePayment(req: Request, res: Response): Promise<Response> {
        try {
            const { id } = req.params;

            const deleted: any =await paymentService.deletePayment(id);

            if (!deleted) {
                return res.status(404).json({
                    status: "error",
                    message: "Payment not found"
                });
            }

            return res.status(200).json({
                status: "success",
                message: "Payment deleted successfully"
            });
        } catch (error) {
            return res.status(500).json({
                status: "error",
                message: error instanceof Error ? (error as Error).message : "Failed to delete payment"
            });
        }
    }
}

export default new PaymentController();
