// jscpd:ignore-file
import express from 'express';
import { FraudDetectionController } from '../controllers/fraud-detection';
import { authMiddleware } from '../middlewares/auth.middleware';
import { roleMiddleware } from '../middlewares/role.middleware';
import { Merchant } from '../types';

const router: any = express.Router();
const fraudDetectionController: any = new FraudDetectionController();

/**
 * @route POST /api/fraud-detection/assess
 * @desc Assess transaction risk
 * @access Private (Merchant, Admin)
 */
router.post(
  '/assess',
  authMiddleware,
  roleMiddleware(['MERCHANT', 'ADMIN']),
  fraudDetectionController.assessTransactionRisk
);

/**
 * @route GET /api/fraud-detection/transaction/:transactionId
 * @desc Get risk assessment for a transaction
 * @access Private (Merchant, Admin)
 */
router.get(
  '/transaction/:transactionId',
  authMiddleware,
  roleMiddleware(['MERCHANT', 'ADMIN']),
  fraudDetectionController.getTransactionRiskAssessment
);

/**
 * @route GET /api/fraud-detection/config/:merchantId
 * @desc Get fraud detection configuration for a merchant
 * @access Private (Merchant, Admin)
 */
router.get(
  '/config/:merchantId',
  authMiddleware,
  roleMiddleware(['MERCHANT', 'ADMIN']),
  fraudDetectionController.getMerchantFraudConfig
);

/**
 * @route PUT /api/fraud-detection/config/:merchantId
 * @desc Update fraud detection configuration for a merchant
 * @access Private (Admin)
 */
router.put(
  '/config/:merchantId',
  authMiddleware,
  roleMiddleware(['ADMIN']),
  fraudDetectionController.updateMerchantFraudConfig
);

/**
 * @route GET /api/fraud-detection/flagged
 * @desc Get flagged transactions
 * @access Private (Admin)
 */
router.get(
  '/flagged',
  authMiddleware,
  roleMiddleware(['ADMIN']),
  fraudDetectionController.getFlaggedTransactions
);

/**
 * @route GET /api/fraud-detection/statistics
 * @desc Get fraud statistics
 * @access Private (Admin)
 */
router.get(
  '/statistics',
  authMiddleware,
  roleMiddleware(['ADMIN']),
  fraudDetectionController.getFraudStatistics
);

export default router;
