"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AlertController = void 0;
const base_controller_1 = require("./base.controller");
const asyncHandler_1 = require("../utils/asyncHandler");
const AppError_1 = require("../utils/errors/AppError");
const types_1 = require("../types");
const alert_service_1 = require("../services/alert.service");
const client_1 = require("@prisma/client");
const prisma = new client_1.PrismaClient();
/**
 * AlertController
 * Controller for handling alert operations
 */
class AlertController extends base_controller_1.BaseController {
    constructor() {
        super();
        /**
         * Get alerts with filtering and pagination
         */
        this.getAlerts = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Get user role and ID
                const { userRole, userId, merchantId } = this.checkAuthorization(req);
                // Get query parameters
                const status = req.query.status;
                const severity = req.query.severity;
                const type = req.query.type;
                const startDate = req.query.startDate ? new Date(req.query.startDate) : undefined;
                const endDate = req.query.endDate ? new Date(req.query.endDate) : undefined;
                const search = req.query.search;
                const limit = parseInt(req.query.limit) || 10;
                const offset = parseInt(req.query.offset) || 0;
                const sortBy = req.query.sortBy || "createdAt";
                const sortOrder = req.query.sortOrder || "desc";
                // Create alert service
                const alertService = new alert_service_1.AlertService();
                // Get alerts based on user role
                let alerts;
                if (userRole === "ADMIN") {
                    // Admins can see all alerts
                    alerts = await alertService.getAlerts({
                        merchantId: req.query.merchantId,
                        status,
                        severity,
                        type,
                        startDate,
                        endDate,
                        search,
                        limit,
                        offset,
                        sortBy,
                        sortOrder
                    });
                }
                else {
                    // Regular users can only see their merchant's alerts
                    alerts = await alertService.getAlerts({
                        merchantId,
                        status,
                        severity,
                        type,
                        startDate,
                        endDate,
                        search,
                        limit,
                        offset,
                        sortBy,
                        sortOrder
                    });
                }
                // Return alerts
                return res.status(200).json({
                    success: true,
                    data: alerts
                });
            }
            catch (error) {
                if (error instanceof AppError_1.AppError) {
                    throw error;
                }
                throw new AppError_1.AppError({
                    message: "Failed to get alerts",
                    type: ErrorType.INTERNAL,
                    code: ErrorCode.INTERNAL_SERVER_ERROR
                });
            }
        });
        /**
         * Get a single alert by ID
         */
        this.getAlert = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Get user role and ID
                const { userRole, userId, merchantId } = this.checkAuthorization(req);
                // Get alert ID from params
                const alertId = req.params.id;
                if (!alertId) {
                    throw new AppError_1.AppError({
                        message: "Alert ID is required",
                        type: ErrorType.VALIDATION,
                        code: ErrorCode.MISSING_REQUIRED_FIELD
                    });
                }
                // Create alert service
                const alertService = new alert_service_1.AlertService();
                // Get alert
                const alert = await alertService.getAlert(alertId);
                // Check if user is authorized to view this alert
                if (userRole !== "ADMIN" && alert.merchantId !== merchantId) {
                    throw new AppError_1.AppError({
                        message: "Unauthorized",
                        type: ErrorType.AUTHENTICATION,
                        code: ErrorCode.INVALID_CREDENTIALS
                    });
                }
                // Return alert
                return res.status(200).json({
                    success: true,
                    data: alert
                });
            }
            catch (error) {
                if (error instanceof AppError_1.AppError) {
                    throw error;
                }
                throw new AppError_1.AppError({
                    message: "Failed to get alert",
                    type: ErrorType.INTERNAL,
                    code: ErrorCode.INTERNAL_SERVER_ERROR
                });
            }
        });
        /**
         * Update the status of an alert
         */
        this.updateAlertStatus = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Get user role and ID
                const { userRole, userId, merchantId } = this.checkAuthorization(req);
                // Get alert ID from params
                const alertId = req.params.id;
                if (!alertId) {
                    throw new AppError_1.AppError({
                        message: "Alert ID is required",
                        type: ErrorType.VALIDATION,
                        code: ErrorCode.MISSING_REQUIRED_FIELD
                    });
                }
                // Get status from body
                const { status } = req.body;
                if (!status || !Object.values(types_1.AlertStatus).includes(status)) {
                    throw new AppError_1.AppError({
                        message: "Valid status is required",
                        type: ErrorType.VALIDATION,
                        code: ErrorCode.MISSING_REQUIRED_FIELD
                    });
                }
                // Create alert service
                const alertService = new alert_service_1.AlertService();
                // Get alert to check authorization
                const alert = await alertService.getAlert(alertId);
                // Check if user is authorized to update this alert
                if (userRole !== "ADMIN" && alert.merchantId !== merchantId) {
                    throw new AppError_1.AppError({
                        message: "Unauthorized",
                        type: ErrorType.AUTHENTICATION,
                        code: ErrorCode.INVALID_CREDENTIALS
                    });
                }
                // Update alert status
                const updatedAlert = await alertService.updateAlertStatus(alertId, status, userId);
                // Return updated alert
                return res.status(200).json({
                    success: true,
                    data: updatedAlert
                });
            }
            catch (error) {
                if (error instanceof AppError_1.AppError) {
                    throw error;
                }
                throw new AppError_1.AppError({
                    message: "Failed to update alert status",
                    type: ErrorType.INTERNAL,
                    code: ErrorCode.INTERNAL_SERVER_ERROR
                });
            }
        });
        /**
         * Create a test alert for testing purposes
         */
        this.createTestAlert = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Get user role and ID
                const { userRole, userId, merchantId } = this.checkAuthorization(req);
                // Only admins can create test alerts
                this.checkAdminRole(userRole);
                // Get alert data from body
                const { type, severity, title, message, details, targetMerchantId } = req.body;
                // Validate required fields
                if (!type || !severity || !title || !message) {
                    throw new AppError_1.AppError({
                        message: "Type, severity, title, and message are required",
                        type: ErrorType.VALIDATION,
                        code: ErrorCode.MISSING_REQUIRED_FIELD
                    });
                }
                // Validate type and severity
                if (!Object.values(types_1.AlertType).includes(type)) {
                    throw new AppError_1.AppError({
                        message: "Invalid alert type",
                        type: ErrorType.VALIDATION,
                        code: ErrorCode.INVALID_INPUT
                    });
                }
                if (!Object.values(types_1.AlertSeverity).includes(severity)) {
                    throw new AppError_1.AppError({
                        message: "Invalid alert severity",
                        type: ErrorType.VALIDATION,
                        code: ErrorCode.INVALID_INPUT
                    });
                }
                // If targetMerchantId is provided, check if it exists
                if (targetMerchantId) {
                    const merchant = await prisma.merchant.findUnique({
                        where: { id: targetMerchantId }
                    });
                    if (!merchant) {
                        throw new AppError_1.AppError({
                            message: "Target merchant not found",
                            type: ErrorType.NOT_FOUND,
                            code: ErrorCode.RESOURCE_NOT_FOUND
                        });
                    }
                }
                // Create alert service
                const alertService = new alert_service_1.AlertService();
                // Create test alert
                const alert = await alertService.createAlert({
                    type: type,
                    severity: severity,
                    title,
                    message,
                    details: details || {},
                    merchantId: targetMerchantId || null,
                    source: "TEST",
                    createdBy: userId
                });
                // Return created alert
                return res.status(201).json({
                    success: true,
                    data: alert
                });
            }
            catch (error) {
                if (error instanceof AppError_1.AppError) {
                    throw error;
                }
                throw new AppError_1.AppError({
                    message: "Failed to create test alert",
                    type: ErrorType.INTERNAL,
                    code: ErrorCode.INTERNAL_SERVER_ERROR
                });
            }
        });
        /**
         * Get the count of alerts with filtering
         */
        this.getAlertCount = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Get user role and ID
                const { userRole, userId, merchantId } = this.checkAuthorization(req);
                // Get query parameters
                const status = req.query.status;
                const severity = req.query.severity;
                const type = req.query.type;
                const startDate = req.query.startDate ? new Date(req.query.startDate) : undefined;
                const endDate = req.query.endDate ? new Date(req.query.endDate) : undefined;
                const search = req.query.search;
                // Create where clause based on user role
                const where = {};
                // Add status filter
                if (status) {
                    where.status = status;
                }
                // Add severity filter
                if (severity) {
                    where.severity = severity;
                }
                // Add type filter
                if (type) {
                    where.type = type;
                }
                // Add date range filter
                if (startDate || endDate) {
                    where.createdAt = {};
                    if (startDate) {
                        where.createdAt.gte = startDate;
                    }
                    if (endDate) {
                        // Set end date to end of day
                        const endOfDay = new Date(endDate);
                        endOfDay.setHours(23, 59, 59, 999);
                        where.createdAt.lte = endOfDay;
                    }
                }
                // Add search filter
                if (search) {
                    where.OR = [
                        { title: { contains: search, mode: "insensitive" } },
                        { message: { contains: search, mode: "insensitive" } },
                        { source: { contains: search, mode: "insensitive" } }
                    ];
                }
                // Add merchant filter based on user role
                if (userRole !== "ADMIN" && merchantId) {
                    where.merchantId = merchantId;
                }
                else if (userRole === "ADMIN" && req.query.merchantId) {
                    where.merchantId = req.query.merchantId;
                }
                // Get alert count
                const count = await prisma.alert.count({ where });
                // Return count
                return res.status(200).json({
                    success: true,
                    data: { count }
                });
            }
            catch (error) {
                if (error instanceof AppError_1.AppError) {
                    throw error;
                }
                throw new AppError_1.AppError({
                    message: "Failed to get alert count",
                    type: ErrorType.INTERNAL,
                    code: ErrorCode.INTERNAL_SERVER_ERROR
                });
            }
        });
    }
    /**
     * Helper method to check admin role
     */
    checkAdminRole(userRole) {
        if (!userRole || userRole !== "ADMIN") {
            throw new AppError_1.AppError({
                message: "Unauthorized. Admin role required.",
                type: ErrorType.AUTHENTICATION,
                code: ErrorCode.INVALID_CREDENTIALS
            });
        }
    }
    /**
     * Helper method to check authorization
     */
    checkAuthorization(req) {
        const userRole = req.user?.role;
        const userId = req.user?.id;
        const merchantId = req.user?.merchantId;
        if (!userRole || !userId) {
            throw new AppError_1.AppError({
                message: "Unauthorized",
                type: ErrorType.AUTHENTICATION,
                code: ErrorCode.INVALID_CREDENTIALS
            });
        }
        return { userRole, userId, merchantId };
    }
}
exports.AlertController = AlertController;
exports.default = new AlertController();
//# sourceMappingURL=alert.controller.js.map