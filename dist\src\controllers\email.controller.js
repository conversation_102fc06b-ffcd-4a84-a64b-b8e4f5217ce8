"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmailController = void 0;
const base_controller_1 = require("./base.controller");
const asyncHandler_1 = require("../utils/asyncHandler");
const AppError_1 = require("../utils/errors/AppError");
const email_service_1 = require("../services/email.service");
/**
 * EmailController
 * Controller for handling email operations
 */
class EmailController extends base_controller_1.BaseController {
    constructor() {
        super();
        /**
         * Test the email service by sending a test email
         */
        this.testEmailService = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Get user role
                const userRole = req.user?.role;
                // Check if user is authorized
                if (!userRole || userRole !== "ADMIN") {
                    throw new AppError_1.AppError({
                        message: "Unauthorized",
                        type: ErrorType.AUTHENTICATION,
                        code: ErrorCode.INVALID_CREDENTIALS
                    });
                }
                // Create email service
                const emailService = new email_service_1.EmailService();
                // Test email service
                const success = await emailService.testEmailService();
                if (success) {
                    return res.status(200).json({
                        success: true,
                        message: "Test email sent successfully"
                    });
                }
                else {
                    throw new AppError_1.AppError({
                        message: "Failed to send test email",
                        type: ErrorType.INTERNAL,
                        code: ErrorCode.INTERNAL_SERVER_ERROR
                    });
                }
            }
            catch (error) {
                if (error instanceof AppError_1.AppError) {
                    throw error;
                }
                throw new AppError_1.AppError({
                    message: "Failed to test email service",
                    type: ErrorType.INTERNAL,
                    code: ErrorCode.INTERNAL_SERVER_ERROR
                });
            }
        });
        /**
         * Send a custom email
         */
        this.sendCustomEmail = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Get user role
                const userRole = req.user?.role;
                // Check if user is authorized
                if (!userRole || userRole !== "ADMIN") {
                    throw new AppError_1.AppError({
                        message: "Unauthorized",
                        type: ErrorType.AUTHENTICATION,
                        code: ErrorCode.INVALID_CREDENTIALS
                    });
                }
                // Get email data from body
                const { to, subject, html, text } = req.body;
                // Validate required fields
                if (!to || !subject || !html) {
                    throw new AppError_1.AppError({
                        message: "To, subject, and html are required",
                        type: ErrorType.VALIDATION,
                        code: ErrorCode.MISSING_REQUIRED_FIELD
                    });
                }
                // Create email service
                const emailService = new email_service_1.EmailService();
                // Send email
                const success = await emailService.sendEmail(to, subject, html, text);
                if (success) {
                    return res.status(200).json({
                        success: true,
                        message: "Email sent successfully"
                    });
                }
                else {
                    throw new AppError_1.AppError({
                        message: "Failed to send email",
                        type: ErrorType.INTERNAL,
                        code: ErrorCode.INTERNAL_SERVER_ERROR
                    });
                }
            }
            catch (error) {
                if (error instanceof AppError_1.AppError) {
                    throw error;
                }
                throw new AppError_1.AppError({
                    message: "Failed to send custom email",
                    type: ErrorType.INTERNAL,
                    code: ErrorCode.INTERNAL_SERVER_ERROR
                });
            }
        });
        /**
         * Get admin email addresses
         */
        this.getAdminEmails = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Get user role
                const userRole = req.user?.role;
                // Check if user is authorized
                if (!userRole || userRole !== "ADMIN") {
                    throw new AppError_1.AppError({
                        message: "Unauthorized",
                        type: ErrorType.AUTHENTICATION,
                        code: ErrorCode.INVALID_CREDENTIALS
                    });
                }
                // Create email service
                const emailService = new email_service_1.EmailService();
                // Get admin emails
                const emails = await emailService.getAdminEmails();
                return res.status(200).json({
                    success: true,
                    data: emails
                });
            }
            catch (error) {
                if (error instanceof AppError_1.AppError) {
                    throw error;
                }
                throw new AppError_1.AppError({
                    message: "Failed to get admin emails",
                    type: ErrorType.INTERNAL,
                    code: ErrorCode.INTERNAL_SERVER_ERROR
                });
            }
        });
    }
}
exports.EmailController = EmailController;
exports.default = new EmailController();
//# sourceMappingURL=email.controller.js.map