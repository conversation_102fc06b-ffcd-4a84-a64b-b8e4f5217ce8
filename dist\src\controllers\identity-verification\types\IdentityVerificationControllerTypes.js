"use strict";
/**
 * Identity Verification Controller Types
 *
 * Type definitions for identity verification controller components.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserRole = exports.SupportedNetwork = exports.VerificationStatus = exports.VerificationMethod = void 0;
/**
 * Verification method types
 */
var VerificationMethod;
(function (VerificationMethod) {
    VerificationMethod["ETHEREUM_SIGNATURE"] = "ETHEREUM_SIGNATURE";
    VerificationMethod["ERC1484"] = "ERC1484";
    VerificationMethod["ERC725"] = "ERC725";
    VerificationMethod["ENS"] = "ENS";
    VerificationMethod["POLYGON_ID"] = "POLYGON_ID";
    VerificationMethod["WORLDCOIN"] = "WORLDCOIN";
    VerificationMethod["UNSTOPPABLE_DOMAINS"] = "UNSTOPPABLE_DOMAINS";
    VerificationMethod["BLOCKCHAIN_VERIFICATION"] = "BLOCKCHAIN_VERIFICATION";
    VerificationMethod["ENS_DOMAIN"] = "ENS_DOMAIN";
})(VerificationMethod || (exports.VerificationMethod = VerificationMethod = {}));
/**
 * Verification status enum
 */
var VerificationStatus;
(function (VerificationStatus) {
    VerificationStatus["PENDING"] = "PENDING";
    VerificationStatus["VERIFIED"] = "VERIFIED";
    VerificationStatus["FAILED"] = "FAILED";
    VerificationStatus["EXPIRED"] = "EXPIRED";
})(VerificationStatus || (exports.VerificationStatus = VerificationStatus = {}));
/**
 * Supported blockchain networks
 */
var SupportedNetwork;
(function (SupportedNetwork) {
    SupportedNetwork["ETHEREUM"] = "ethereum";
    SupportedNetwork["POLYGON"] = "polygon";
    SupportedNetwork["BSC"] = "bsc";
    SupportedNetwork["ARBITRUM"] = "arbitrum";
    SupportedNetwork["OPTIMISM"] = "optimism";
    SupportedNetwork["AVALANCHE"] = "avalanche";
})(SupportedNetwork || (exports.SupportedNetwork = SupportedNetwork = {}));
/**
 * User role enum
 */
var UserRole;
(function (UserRole) {
    UserRole["USER"] = "USER";
    UserRole["MERCHANT"] = "MERCHANT";
    UserRole["ADMIN"] = "ADMIN";
})(UserRole || (exports.UserRole = UserRole = {}));
//# sourceMappingURL=IdentityVerificationControllerTypes.js.map