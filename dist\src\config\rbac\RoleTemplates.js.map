{"version": 3, "file": "RoleTemplates.js", "sourceRoot": "", "sources": ["../../../../src/config/rbac/RoleTemplates.ts"], "names": [], "mappings": ";;;AACA,oBAAoB;AACpB;;;;GAIG;AAEH,yDAc4B;AAa5B;;GAEG;AACU,QAAA,oBAAoB,GAAiB;IAC9C,IAAI,EAAE,aAAa;IACnB,IAAI,EAAE,aAAa;IACnB,WAAW,EAAE,oCAAoC;IACjD,WAAW,EAAE,kCAAe;IAC5B,QAAQ,EAAE,IAAI;CACjB,CAAC;AAEF;;GAEG;AACU,QAAA,cAAc,GAAiB;IACxC,IAAI,EAAE,OAAO;IACb,IAAI,EAAE,OAAO;IACb,WAAW,EAAE,+CAA+C;IAC5D,WAAW,EAAE;QACT,GAAG,oCAAiB;QACpB,GAAG,uCAAoB,CAAC,GAAG;QAC3B,GAAG,sCAAmB,CAAC,IAAI;QAC3B,GAAG,sCAAmB,CAAC,KAAK;QAC5B,GAAG,2CAAwB,CAAC,IAAI;QAChC,GAAG,2CAAwB,CAAC,IAAI;QAChC,GAAG,wCAAqB,CAAC,IAAI;QAC7B,GAAG,yCAAsB,CAAC,IAAI;QAC9B,GAAG,uCAAoB,CAAC,IAAI;QAC5B,GAAG,uCAAoB,CAAC,IAAI;QAC5B,GAAG,2CAAwB,CAAC,GAAG;KAClC;IACD,QAAQ,EAAE,IAAI;CACjB,CAAC;AAEF;;GAEG;AACU,QAAA,wBAAwB,GAAiB;IAClD,IAAI,EAAE,iBAAiB;IACvB,IAAI,EAAE,iBAAiB;IACvB,WAAW,EAAE,0CAA0C;IACvD,WAAW,EAAE;QACT,GAAG,oCAAiB;QACpB,GAAG,uCAAoB,CAAC,IAAI;QAC5B,GAAG,sCAAmB,CAAC,GAAG;QAC1B,GAAG,2CAAwB,CAAC,GAAG;QAC/B,GAAG,wCAAqB,CAAC,IAAI;QAC7B,GAAG,wCAAqB,CAAC,KAAK;KACjC;IACD,QAAQ,EAAE,IAAI;CACjB,CAAC;AAEF;;GAEG;AACU,QAAA,uBAAuB,GAAiB;IACjD,IAAI,EAAE,gBAAgB;IACtB,IAAI,EAAE,gBAAgB;IACtB,WAAW,EAAE,yCAAyC;IACtD,WAAW,EAAE;QACT,GAAG,oCAAiB;QACpB,GAAG,uCAAoB,CAAC,GAAG;QAC3B,GAAG,2CAAwB,CAAC,IAAI;QAChC,GAAG,wCAAqB,CAAC,IAAI;KAChC;IACD,QAAQ,EAAE,IAAI;CACjB,CAAC;AAEF;;GAEG;AACU,QAAA,uBAAuB,GAAiB;IACjD,IAAI,EAAE,gBAAgB;IACtB,IAAI,EAAE,gBAAgB;IACtB,WAAW,EAAE,oDAAoD;IACjE,WAAW,EAAE;QACT,GAAG,oCAAiB;QACpB,GAAG,2CAAwB,CAAC,GAAG;QAC/B,GAAG,uCAAoB,CAAC,GAAG;QAC3B,GAAG,yCAAsB,CAAC,GAAG;KAChC;IACD,QAAQ,EAAE,IAAI;CACjB,CAAC;AAEF;;GAEG;AACU,QAAA,sBAAsB,GAAiB;IAChD,IAAI,EAAE,eAAe;IACrB,IAAI,EAAE,eAAe;IACrB,WAAW,EAAE,+CAA+C;IAC5D,WAAW,EAAE;QACT,GAAG,oCAAiB;QACpB,GAAG,uCAAoB,CAAC,IAAI;QAC5B,GAAG,sCAAmB,CAAC,IAAI;QAC3B,GAAG,2CAAwB,CAAC,IAAI;QAChC,GAAG,yCAAsB,CAAC,IAAI;QAC9B,GAAG,2CAAwB,CAAC,IAAI;KACnC;IACD,QAAQ,EAAE,IAAI;CACjB,CAAC;AAEF;;GAEG;AACU,QAAA,2BAA2B,GAAiB;IACrD,IAAI,EAAE,oBAAoB;IAC1B,IAAI,EAAE,oBAAoB;IAC1B,WAAW,EAAE,mDAAmD;IAChE,WAAW,EAAE;QACT,GAAG,oCAAiB;QACpB,GAAG,uCAAoB,CAAC,IAAI;QAC5B,GAAG,uCAAoB,CAAC,OAAO;QAC/B,GAAG,2CAAwB,CAAC,IAAI;QAChC,GAAG,yCAAsB,CAAC,IAAI;QAC9B,GAAG,uCAAoB,CAAC,IAAI;KAC/B;IACD,QAAQ,EAAE,IAAI;CACjB,CAAC;AAEF;;GAEG;AACU,QAAA,0BAA0B,GAAiB;IACpD,IAAI,EAAE,mBAAmB;IACzB,IAAI,EAAE,mBAAmB;IACzB,WAAW,EAAE,iCAAiC;IAC9C,WAAW,EAAE;QACT,GAAG,oCAAiB;QACpB,GAAG,wCAAqB,CAAC,GAAG;QAC5B,GAAG,uCAAoB,CAAC,IAAI;QAC5B,GAAG,sCAAmB,CAAC,IAAI;KAC9B;IACD,QAAQ,EAAE,IAAI;CACjB,CAAC;AAEF;;GAEG;AACU,QAAA,6BAA6B,GAAiB;IACvD,IAAI,EAAE,sBAAsB;IAC5B,IAAI,EAAE,sBAAsB;IAC5B,WAAW,EAAE,4CAA4C;IACzD,WAAW,EAAE;QACT,GAAG,oCAAiB;QACpB,GAAG,uCAAoB,CAAC,GAAG;QAC3B,GAAG,uCAAoB,CAAC,GAAG;QAC3B,GAAG,mCAAgB,CAAC,GAAG;QACvB,GAAG,yCAAsB,CAAC,GAAG;KAChC;IACD,QAAQ,EAAE,IAAI;CACjB,CAAC;AAEF;;GAEG;AACU,QAAA,gBAAgB,GAAiB;IAC1C,IAAI,EAAE,SAAS;IACf,IAAI,EAAE,SAAS;IACf,WAAW,EAAE,2DAA2D;IACxE,WAAW,EAAE;QACT,GAAG,oCAAiB;QACpB,GAAG,uCAAoB,CAAC,IAAI;QAC5B,GAAG,sCAAmB,CAAC,IAAI;QAC3B,GAAG,2CAAwB,CAAC,IAAI;QAChC,GAAG,2CAAwB,CAAC,IAAI;QAChC,GAAG,wCAAqB,CAAC,IAAI;QAC7B,GAAG,yCAAsB,CAAC,IAAI;QAC9B,GAAG,uCAAoB,CAAC,IAAI;QAC5B,GAAG,uCAAoB,CAAC,IAAI;QAC5B,GAAG,2CAAwB,CAAC,IAAI;QAChC,GAAG,mCAAgB,CAAC,IAAI;QACxB,GAAG,yCAAsB,CAAC,IAAI;KACjC;IACD,QAAQ,EAAE,IAAI;CACjB,CAAC;AAEF;;GAEG;AACU,QAAA,cAAc,GAAiC;IACxD,WAAW,EAAE,4BAAoB;IACjC,KAAK,EAAE,sBAAc;IACrB,eAAe,EAAE,gCAAwB;IACzC,cAAc,EAAE,+BAAuB;IACvC,cAAc,EAAE,+BAAuB;IACvC,aAAa,EAAE,8BAAsB;IACrC,kBAAkB,EAAE,mCAA2B;IAC/C,iBAAiB,EAAE,kCAA0B;IAC7C,oBAAoB,EAAE,qCAA6B;IACnD,OAAO,EAAE,wBAAgB;CAC5B,CAAC"}