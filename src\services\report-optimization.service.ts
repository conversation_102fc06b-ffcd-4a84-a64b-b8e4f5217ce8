import { PrismaClient } from '@prisma/client';
import { createReadStream, createWriteStream } from 'fs';
import { Transform } from 'stream';
import { Parser } from 'json2csv';
import * as ExcelJS from 'exceljs';

const prisma = new PrismaClient();

export class ReportOptimizationService {
  private readonly BATCH_SIZE = 1000;
  private readonly MAX_MEMORY_USAGE = 100 * 1024 * 1024; // 100MB

  /**
   * Generate large reports using streaming to optimize memory usage
   */
  public async generateLargeReport(
    reportType: string,
    parameters: any,
    format: string,
    filePath: string
  ): Promise<void> {
    switch (format.toUpperCase()) {
      case 'CSV':
        await this.generateLargeCSVReport(reportType, parameters, filePath);
        break;
      case 'EXCEL':
        await this.generateLargeExcelReport(reportType, parameters, filePath);
        break;
      case 'JSON':
        await this.generateLargeJSONReport(reportType, parameters, filePath);
        break;
      default:
        throw new Error(`Streaming not supported for format: ${format}`);
    }
  }

  /**
   * Generate large CSV report using streaming
   */
  private async generateLargeCSVReport(
    reportType: string,
    parameters: any,
    filePath: string
  ): Promise<void> {
    const writeStream = createWriteStream(filePath);
    let isFirstBatch = true;
    let offset = 0;
    let hasMoreData = true;

    while (hasMoreData) {
      const batch = await this.getDataBatch(reportType, parameters, offset, this.BATCH_SIZE);
      
      if (batch.length === 0) {
        hasMoreData = false;
        break;
      }

      if (isFirstBatch && batch.length > 0) {
        // Write headers for the first batch
        const fields = Object.keys(batch[0]);
        const parser = new Parser({ fields, header: true });
        const csv = parser.parse(batch);
        writeStream.write(csv);
        isFirstBatch = false;
      } else if (batch.length > 0) {
        // Write data without headers for subsequent batches
        const fields = Object.keys(batch[0]);
        const parser = new Parser({ fields, header: false });
        const csv = parser.parse(batch);
        writeStream.write('\n' + csv);
      }

      offset += this.BATCH_SIZE;
      
      // Check if we got fewer records than batch size (last batch)
      if (batch.length < this.BATCH_SIZE) {
        hasMoreData = false;
      }
    }

    writeStream.end();
    
    return new Promise((resolve, reject) => {
      writeStream.on('finish', resolve);
      writeStream.on('error', reject);
    });
  }

  /**
   * Generate large Excel report using streaming
   */
  private async generateLargeExcelReport(
    reportType: string,
    parameters: any,
    filePath: string
  ): Promise<void> {
    const workbook = new ExcelJS.stream.xlsx.WorkbookWriter({
      filename: filePath,
      useStyles: true,
      useSharedStrings: true
    });

    const worksheet = workbook.addWorksheet('Report');
    
    let offset = 0;
    let hasMoreData = true;
    let isFirstBatch = true;

    while (hasMoreData) {
      const batch = await this.getDataBatch(reportType, parameters, offset, this.BATCH_SIZE);
      
      if (batch.length === 0) {
        hasMoreData = false;
        break;
      }

      if (isFirstBatch && batch.length > 0) {
        // Add headers for the first batch
        const headers = Object.keys(batch[0]);
        worksheet.addRow(headers);
        
        // Style the header row
        const headerRow = worksheet.getRow(1);
        headerRow.font = { bold: true };
        headerRow.commit();
        
        isFirstBatch = false;
      }

      // Add data rows
      for (const row of batch) {
        const values = Object.values(row);
        const worksheetRow = worksheet.addRow(values);
        worksheetRow.commit();
      }

      offset += this.BATCH_SIZE;
      
      // Check if we got fewer records than batch size (last batch)
      if (batch.length < this.BATCH_SIZE) {
        hasMoreData = false;
      }
    }

    await workbook.commit();
  }

  /**
   * Generate large JSON report using streaming
   */
  private async generateLargeJSONReport(
    reportType: string,
    parameters: any,
    filePath: string
  ): Promise<void> {
    const writeStream = createWriteStream(filePath);
    
    writeStream.write('[');
    
    let offset = 0;
    let hasMoreData = true;
    let isFirstBatch = true;

    while (hasMoreData) {
      const batch = await this.getDataBatch(reportType, parameters, offset, this.BATCH_SIZE);
      
      if (batch.length === 0) {
        hasMoreData = false;
        break;
      }

      for (let i = 0; i < batch.length; i++) {
        const record = batch[i];
        
        if (!isFirstBatch || i > 0) {
          writeStream.write(',');
        }
        
        writeStream.write(JSON.stringify(record));
      }

      isFirstBatch = false;
      offset += this.BATCH_SIZE;
      
      // Check if we got fewer records than batch size (last batch)
      if (batch.length < this.BATCH_SIZE) {
        hasMoreData = false;
      }
    }

    writeStream.write(']');
    writeStream.end();
    
    return new Promise((resolve, reject) => {
      writeStream.on('finish', resolve);
      writeStream.on('error', reject);
    });
  }

  /**
   * Get a batch of data for the specified report type
   */
  private async getDataBatch(
    reportType: string,
    parameters: any,
    offset: number,
    limit: number
  ): Promise<any[]> {
    switch (reportType) {
      case 'TRANSACTION':
        return this.getTransactionBatch(parameters, offset, limit);
      case 'CUSTOMER':
        return this.getCustomerBatch(parameters, offset, limit);
      case 'PAYMENT_METHOD':
        return this.getPaymentMethodBatch(parameters, offset, limit);
      case 'SUBSCRIPTION':
        return this.getSubscriptionBatch(parameters, offset, limit);
      default:
        throw new Error(`Unsupported report type: ${reportType}`);
    }
  }

  /**
   * Get transaction data batch
   */
  private async getTransactionBatch(
    parameters: any,
    offset: number,
    limit: number
  ): Promise<any[]> {
    const { startDate, endDate, merchantId, status } = parameters;
    
    const transactions = await prisma.transaction.findMany({
      where: {
        ...(startDate && { createdAt: { gte: new Date(startDate) } }),
        ...(endDate && { createdAt: { lte: new Date(endDate) } }),
        ...(merchantId && { merchantId }),
        ...(status && { status })
      },
      include: {
        merchant: {
          select: {
            businessName: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip: offset,
      take: limit
    });

    return transactions.map(transaction => ({
      id: transaction.id,
      reference: transaction.reference,
      amount: transaction.amount,
      currency: transaction.currency,
      status: transaction.status,
      paymentMethod: transaction.paymentMethod,
      merchantName: transaction.merchant?.businessName || 'Unknown',
      createdAt: transaction.createdAt.toISOString(),
      updatedAt: transaction.updatedAt.toISOString()
    }));
  }

  /**
   * Get customer data batch
   */
  private async getCustomerBatch(
    parameters: any,
    offset: number,
    limit: number
  ): Promise<any[]> {
    const { startDate, endDate, status } = parameters;
    
    const customers = await prisma.customer.findMany({
      where: {
        ...(startDate && { createdAt: { gte: new Date(startDate) } }),
        ...(endDate && { createdAt: { lte: new Date(endDate) } }),
        ...(status && { status })
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip: offset,
      take: limit
    });

    return customers.map(customer => ({
      id: customer.id,
      email: customer.email,
      firstName: customer.firstName,
      lastName: customer.lastName,
      status: customer.status,
      createdAt: customer.createdAt.toISOString(),
      updatedAt: customer.updatedAt.toISOString()
    }));
  }

  /**
   * Get payment method data batch
   */
  private async getPaymentMethodBatch(
    parameters: any,
    offset: number,
    limit: number
  ): Promise<any[]> {
    const { startDate, endDate, type } = parameters;
    
    const paymentMethods = await prisma.paymentMethod.findMany({
      where: {
        ...(startDate && { createdAt: { gte: new Date(startDate) } }),
        ...(endDate && { createdAt: { lte: new Date(endDate) } }),
        ...(type && { type })
      },
      include: {
        customer: {
          select: {
            email: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip: offset,
      take: limit
    });

    return paymentMethods.map(pm => ({
      id: pm.id,
      type: pm.type,
      last4: pm.last4,
      expiryMonth: pm.expiryMonth,
      expiryYear: pm.expiryYear,
      isDefault: pm.isDefault,
      customerEmail: pm.customer?.email || 'Unknown',
      createdAt: pm.createdAt.toISOString(),
      updatedAt: pm.updatedAt.toISOString()
    }));
  }

  /**
   * Get subscription data batch
   */
  private async getSubscriptionBatch(
    parameters: any,
    offset: number,
    limit: number
  ): Promise<any[]> {
    const { startDate, endDate, status } = parameters;
    
    const subscriptions = await prisma.subscription.findMany({
      where: {
        ...(startDate && { createdAt: { gte: new Date(startDate) } }),
        ...(endDate && { createdAt: { lte: new Date(endDate) } }),
        ...(status && { status })
      },
      include: {
        customer: {
          select: {
            email: true
          }
        },
        plan: {
          select: {
            name: true,
            amount: true,
            currency: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip: offset,
      take: limit
    });

    return subscriptions.map(subscription => ({
      id: subscription.id,
      status: subscription.status,
      startDate: subscription.startDate?.toISOString(),
      endDate: subscription.endDate?.toISOString(),
      planName: subscription.plan?.name || 'Unknown',
      amount: subscription.plan?.amount || 0,
      currency: subscription.plan?.currency || 'USD',
      customerEmail: subscription.customer?.email || 'Unknown',
      createdAt: subscription.createdAt.toISOString(),
      updatedAt: subscription.updatedAt.toISOString()
    }));
  }

  /**
   * Estimate report size before generation
   */
  public async estimateReportSize(reportType: string, parameters: any): Promise<{
    recordCount: number;
    estimatedSizeBytes: number;
    recommendStreaming: boolean;
  }> {
    const recordCount = await this.getRecordCount(reportType, parameters);
    const avgRecordSize = this.getAverageRecordSize(reportType);
    const estimatedSizeBytes = recordCount * avgRecordSize;
    const recommendStreaming = estimatedSizeBytes > this.MAX_MEMORY_USAGE;

    return {
      recordCount,
      estimatedSizeBytes,
      recommendStreaming
    };
  }

  /**
   * Get total record count for a report
   */
  private async getRecordCount(reportType: string, parameters: any): Promise<number> {
    switch (reportType) {
      case 'TRANSACTION':
        return this.getTransactionCount(parameters);
      case 'CUSTOMER':
        return this.getCustomerCount(parameters);
      case 'PAYMENT_METHOD':
        return this.getPaymentMethodCount(parameters);
      case 'SUBSCRIPTION':
        return this.getSubscriptionCount(parameters);
      default:
        return 0;
    }
  }

  private async getTransactionCount(parameters: any): Promise<number> {
    const { startDate, endDate, merchantId, status } = parameters;
    
    return prisma.transaction.count({
      where: {
        ...(startDate && { createdAt: { gte: new Date(startDate) } }),
        ...(endDate && { createdAt: { lte: new Date(endDate) } }),
        ...(merchantId && { merchantId }),
        ...(status && { status })
      }
    });
  }

  private async getCustomerCount(parameters: any): Promise<number> {
    const { startDate, endDate, status } = parameters;
    
    return prisma.customer.count({
      where: {
        ...(startDate && { createdAt: { gte: new Date(startDate) } }),
        ...(endDate && { createdAt: { lte: new Date(endDate) } }),
        ...(status && { status })
      }
    });
  }

  private async getPaymentMethodCount(parameters: any): Promise<number> {
    const { startDate, endDate, type } = parameters;
    
    return prisma.paymentMethod.count({
      where: {
        ...(startDate && { createdAt: { gte: new Date(startDate) } }),
        ...(endDate && { createdAt: { lte: new Date(endDate) } }),
        ...(type && { type })
      }
    });
  }

  private async getSubscriptionCount(parameters: any): Promise<number> {
    const { startDate, endDate, status } = parameters;
    
    return prisma.subscription.count({
      where: {
        ...(startDate && { createdAt: { gte: new Date(startDate) } }),
        ...(endDate && { createdAt: { lte: new Date(endDate) } }),
        ...(status && { status })
      }
    });
  }

  /**
   * Get average record size in bytes for different report types
   */
  private getAverageRecordSize(reportType: string): number {
    switch (reportType) {
      case 'TRANSACTION':
        return 200; // Estimated average size per transaction record
      case 'CUSTOMER':
        return 150; // Estimated average size per customer record
      case 'PAYMENT_METHOD':
        return 120; // Estimated average size per payment method record
      case 'SUBSCRIPTION':
        return 180; // Estimated average size per subscription record
      default:
        return 100; // Default estimate
    }
  }
}
