/**
 * Admin Response Mapper
 *
 * Handles response formatting for admin operations.
 */
import { Response } from 'express';
import { AdminUserResponse, RoleResponse, PermissionResponse, DashboardDataResponse, DashboardStatistics, SystemHealthStatus } from '../types/AdminControllerTypes';
import { AppError } from '../../../utils/errors/AppError';
/**
 * Response mapper for admin operations
 */
export declare class AdminResponseMapper {
    /**
     * Send success response
     */
    static sendSuccess<T>(res: Response, data: T, message?: string, statusCode?: number, pagination?: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
    }): void;
    /**
     * Send error response
     */
    static sendError(res: Response, error: AppError | Error, statusCode?: number): void;
    /**
     * Send dashboard data response
     */
    static sendDashboardData(res: Response, data: DashboardDataResponse): void;
    /**
     * Send dashboard statistics response
     */
    static sendDashboardStatistics(res: Response, stats: DashboardStatistics): void;
    /**
     * Send admin users list response
     */
    static sendAdminUsersList(res: Response, users: AdminUserResponse[], total: number, page?: number, limit?: number): void;
    /**
     * Send single admin user response
     */
    static sendAdminUser(res: Response, user: AdminUserResponse, message?: string): void;
    /**
     * Send admin user created response
     */
    static sendAdminUserCreated(res: Response, user: AdminUserResponse): void;
    /**
     * Send admin user updated response
     */
    static sendAdminUserUpdated(res: Response, user: AdminUserResponse): void;
    /**
     * Send admin user deleted response
     */
    static sendAdminUserDeleted(res: Response): void;
    /**
     * Send roles list response
     */
    static sendRolesList(res: Response, roles: RoleResponse[], total: number, page?: number, limit?: number): void;
    /**
     * Send single role response
     */
    static sendRole(res: Response, role: RoleResponse, message?: string): void;
    /**
     * Send role created response
     */
    static sendRoleCreated(res: Response, role: RoleResponse): void;
    /**
     * Send role updated response
     */
    static sendRoleUpdated(res: Response, role: RoleResponse): void;
    /**
     * Send role deleted response
     */
    static sendRoleDeleted(res: Response): void;
    /**
     * Send permissions list response
     */
    static sendPermissionsList(res: Response, permissions: PermissionResponse[], total: number, page?: number, limit?: number): void;
    /**
     * Send single permission response
     */
    static sendPermission(res: Response, permission: PermissionResponse, message?: string): void;
    /**
     * Send permission created response
     */
    static sendPermissionCreated(res: Response, permission: PermissionResponse): void;
    /**
     * Send permission updated response
     */
    static sendPermissionUpdated(res: Response, permission: PermissionResponse): void;
    /**
     * Send permission deleted response
     */
    static sendPermissionDeleted(res: Response): void;
    /**
     * Send system health response
     */
    static sendSystemHealth(res: Response, health: SystemHealthStatus): void;
    /**
     * Send validation error response
     */
    static sendValidationError(res: Response, errors: any[], message?: string): void;
    /**
     * Send authorization error response
     */
    static sendAuthorizationError(res: Response, message?: string, requiredRole?: string): void;
    /**
     * Send not found error response
     */
    static sendNotFoundError(res: Response, resource?: string): void;
    /**
     * Send internal server error response
     */
    static sendInternalServerError(res: Response, message?: string): void;
    /**
     * Handle async controller method
     */
    static asyncHandler(fn: Function): (req: any, res: Response, next: Function) => void;
    /**
     * Set response headers for API
     */
    static setApiHeaders(res: Response): void;
}
//# sourceMappingURL=AdminResponseMapper.d.ts.map