"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const logger_1 = require("../utils/logger");
const payment_analytics_service_1 = __importStar(require("../services/analytics/payment-analytics.service"));
/**
 * Analytics controller
 */
const analyticsController = {
    /**
     * Get payment analytics
     */
    getPaymentAnalytics: async (req, res) => {
        try {
            // Parse filter parameters
            const filter = {};
            if (req.query.merchantId) {
                filter.merchantId = req.query.merchantId;
            }
            if (req.query.startDate) {
                filter.startDate = new Date(req.query.startDate);
            }
            if (req.query.endDate) {
                filter.endDate = new Date(req.query.endDate);
            }
            if (req.query.period) {
                filter.period = req.query.period;
            }
            else {
                filter.period = payment_analytics_service_1.AnalyticsPeriod.MONTH; // Default to month
            }
            const analytics = await payment_analytics_service_1.default.getPaymentAnalytics(filter);
            return res.status(200).json(analytics);
        }
        catch (error) {
            logger_1.logger.error("Error getting payment analytics:", error);
            return res.status(500).json({ error: "Failed to get payment analytics" });
        }
    },
    /**
     * Get payment method analytics
     */
    getPaymentMethodAnalytics: async (req, res) => {
        try {
            const { paymentMethodType } = req.params;
            if (!paymentMethodType) {
                return res.status(400).json({ error: "Payment method type is required" });
            }
            // Parse filter parameters
            const filter = {};
            if (req.query.merchantId) {
                filter.merchantId = req.query.merchantId;
            }
            if (req.query.startDate) {
                filter.startDate = new Date(req.query.startDate);
            }
            if (req.query.endDate) {
                filter.endDate = new Date(req.query.endDate);
            }
            if (req.query.period) {
                filter.period = req.query.period;
            }
            else {
                filter.period = payment_analytics_service_1.AnalyticsPeriod.MONTH; // Default to month
            }
            const analytics = await payment_analytics_service_1.default.getPaymentMethodAnalytics(paymentMethodType, filter);
            return res.status(200).json(analytics);
        }
        catch (error) {
            logger_1.logger.error("Error getting payment method analytics:", error);
            return res.status(500).json({ error: "Failed to get payment method analytics" });
        }
    },
    /**
     * Get merchant analytics
     */
    getMerchantAnalytics: async (req, res) => {
        try {
            const { merchantId } = req.params;
            if (!merchantId) {
                return res.status(400).json({ error: "Merchant ID is required" });
            }
            // Parse filter parameters
            const filter = {
                merchantId
            };
            if (req.query.startDate) {
                filter.startDate = new Date(req.query.startDate);
            }
            if (req.query.endDate) {
                filter.endDate = new Date(req.query.endDate);
            }
            if (req.query.period) {
                filter.period = req.query.period;
            }
            else {
                filter.period = payment_analytics_service_1.AnalyticsPeriod.MONTH; // Default to month
            }
            const analytics = await payment_analytics_service_1.default.getMerchantAnalytics(merchantId, filter);
            return res.status(200).json(analytics);
        }
        catch (error) {
            logger_1.logger.error("Error getting merchant analytics:", error);
            return res.status(500).json({ error: "Failed to get merchant analytics" });
        }
    },
    /**
     * Get dashboard analytics
     */
    getDashboardAnalytics: async (req, res) => {
        try {
            // Get user role and ID from request
            const { role, id } = req.user;
            // Parse filter parameters
            const filter = {};
            if (req.query.startDate) {
                filter.startDate = new Date(req.query.startDate);
            }
            if (req.query.endDate) {
                filter.endDate = new Date(req.query.endDate);
            }
            if (req.query.period) {
                filter.period = req.query.period;
            }
            else {
                filter.period = payment_analytics_service_1.AnalyticsPeriod.MONTH; // Default to month
            }
            // Get analytics based on user role
            let analytics;
            if (role === "admin") {
                // Admin sees all analytics
                analytics = await payment_analytics_service_1.default.getPaymentAnalytics(filter);
            }
            else if (role === "merchant") {
                // Merchant sees only their analytics
                analytics = await payment_analytics_service_1.default.getMerchantAnalytics(id, filter);
            }
            else {
                return res.status(403).json({ error: "Unauthorized" });
            }
            return res.status(200).json(analytics);
        }
        catch (error) {
            logger_1.logger.error("Error getting dashboard analytics:", error);
            return res.status(500).json({ error: "Failed to get dashboard analytics" });
        }
    }
};
exports.default = analyticsController;
//# sourceMappingURL=analytics.controller.js.map