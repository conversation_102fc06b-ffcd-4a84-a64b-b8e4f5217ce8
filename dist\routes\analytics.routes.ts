// jscpd:ignore-file
import express from "express";
import analyticsController from "../controllers/analytics.controller";
import { authenticateJWT, isAdmin, isMerchantOrAdmin } from '../middlewares/auth';
import { Merchant } from '../types';
import { Merchant } from '../types';


const router: any =express.Router();

/**
 * @route   GET /api/analytics/payments
 * @desc    Get payment analytics
 * @access  Admin
 */
router.get(
    "/payments",
    authenticateJWT,
    isAdmin,
    analyticsController.getPaymentAnalytics
);

/**
 * @route   GET /api/analytics/merchants/:merchantId
 * @desc    Get merchant analytics
 * @access  Merchant or Admin
 */
router.get(
    "/merchants/:merchantId",
    authenticateJWT,
    isMerchantOrAdmin,
    analyticsController.getMerchantAnalytics
);

/**
 * @route   GET /api/analytics/payment-methods/:paymentMethodType
 * @desc    Get payment method analytics
 * @access  Admin
 */
router.get(
    "/payment-methods/:paymentMethodType",
    authenticateJWT,
    isAdmin,
    analyticsController.getPaymentMethodAnalytics
);

/**
 * @route   GET /api/analytics/dashboard
 * @desc    Get dashboard analytics
 * @access  Merchant or Admin
 */
router.get(
    "/dashboard",
    authenticateJWT,
    isMerchantOrAdmin,
    analyticsController.getDashboardAnalytics
);

export default router;
