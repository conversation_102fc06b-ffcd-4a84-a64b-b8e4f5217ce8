/**
 * Fraud Configuration Validator
 *
 * Focused validator for fraud detection configuration operations.
 */
import { UpdateFraudConfigRequest } from '../types/FraudDetectionControllerTypes';
import { BaseValidator } from './BaseValidator';
/**
 * Validator for fraud configuration operations
 */
export declare class FraudConfigValidator extends BaseValidator {
    /**
     * Validate fraud configuration update request
     */
    validateUpdateFraudConfig(data: any): UpdateFraudConfigRequest;
    /**
     * Validate factor weights
     */
    private validateFactorWeights;
    /**
     * Validate high risk countries
     */
    private validateHighRiskCountries;
    /**
     * Validate high risk IP ranges
     */
    private validateHighRiskIpRanges;
    /**
     * Validate transaction limits
     */
    private validateTransactionLimits;
    /**
     * Build update request object
     */
    private buildUpdateRequest;
}
//# sourceMappingURL=FraudConfigValidator.d.ts.map