// jscpd:ignore-file

import { ModuleFactory, ModuleRegistry, Container, Module } from '../../core/module';
import { Webhook } from '@prisma/client';
import { Prisma } from '@prisma/client';
import { logger, ErrorFactory } from "../../utils";
import { authMiddleware } from '../../middlewares/auth.middleware';
import { Webhook } from '@prisma/client';
import { Prisma } from '@prisma/client';
import { logger, ErrorFactory } from "../../utils";
import { authMiddleware } from '../../middlewares/auth.middleware';

// Extend the Express Request type to include user property
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role: string;
        merchantId?: string;
      };
    }
  }
}

/**
 * Webhook Module
 * This module provides webhook functionality with zero duplication
 */
export class WebhookModule {
  private moduleFactory: ModuleFactory<Webhook, Prisma.WebhookCreateInput, Prisma.WebhookUpdateInput>;
  private moduleRegistry: ModuleRegistry;
  private container: Container;
  private module: Module;

  /**
   * Create a new webhook module
   */
  constructor() {
    this.moduleRegistry = new ModuleRegistry();
    this.container = new Container();

    // Create module factory
    this.moduleFactory = new ModuleFactory<Webhook, Prisma.WebhookCreateInput, Prisma.WebhookUpdateInput>(
      'webhook',
      'Webhook'
    );

    // Get router, repository, service, and controller from factory
    const { router, repository, service, controller } = this.moduleFactory.build();

    // Configure router
    router
      .addRoute('get', '/:id', controller.getById)
      .addRoute('post', '/', controller.create)
      .addRoute('put', '/:id', controller.update)
      .addRoute('delete', '/:id', controller.delete)
      .addRoute('get', '/merchant/:merchantId', controller.getWebhooksByMerchantId)
      .addRoute('post', '/:id/trigger', controller.triggerWebhook)
      .addMiddleware(authMiddleware);

    // Add custom repository methods
    this.moduleFactory.addRepositoryMethod(
      'findByMerchantId',
      async (merchantId: string, options: { limit?: number; offset?: number } = {}) => {
        try {
          return await repository.findByFieldWithPagination('merchantId', merchantId, options);
        } catch (error) {
          logger.error(`Error finding webhooks by merchant ID ${merchantId}:`, error);
          throw ErrorFactory.handle(error);
        }
      }
    );

    this.moduleFactory.addServiceMethod(
      'triggerWebhook',
      async (webhookId: string, payload: any) => {
        try {
          // Get webhook
          const webhook: any =await service.getById(webhookId);

          // Check if webhook exists
// jscpd:ignore-end
          if (!webhook) {
            throw ErrorFactory.notFound('Webhook', webhookId);
          }

          // Deliver webhook
          // Implementation would depend on the specific delivery process
          // This is a placeholder implementation
          const deliveryId: any =`del_${Date.now()}`;

          logger.info(`Webhook triggered: ${webhookId}`, {
            webhookId,
            deliveryId,
            url: webhook.url
          });

          return {
            success: true,
            message: 'Webhook delivered successfully',
            deliveryId
          };
        } catch (error) {
          logger.error(`Error triggering webhook ${webhookId}:`, error);
          throw ErrorFactory.handle(error);
        }
      }
    );

    // Add custom controller methods
    this.moduleFactory.addControllerMethod(
      'getWebhooksByMerchantId',
      async (req, res) => {
        try {
          // Check authorization
          const { userRole, merchantId } = req.user;

          // Get merchant ID from params
          const { merchantId: requestedMerchantId } = req.params;

          // Check if user has permission to view these webhooks
          if (userRole !== 'ADMIN' && merchantId !== requestedMerchantId) {
            throw ErrorFactory.authorization('You do not have permission to view these webhooks');
          }

          // Parse pagination parameters
          const limit: any =parseInt(req.query.limit as string) || 10;
          const page: any =parseInt(req.query.page as string) || 1;
          const offset: any =(page - 1) * limit;

          // Get webhooks
          const webhooks: any =await service.findByMerchantId(requestedMerchantId, { limit, offset });

          // Send success response
          return res.status(200).json({
            success: true,
            data: webhooks
          });
        } catch (error) {
          logger.error(`Error getting webhooks by merchant ID:`, error);
          return res.status(500).json({
            success: false,
            error: (error as Error).message || 'An error occurred while getting webhooks'
          });
        }
      }
    );

    this.moduleFactory.addControllerMethod(
      'triggerWebhook',
      async (req, res) => {
        try {
          // Check authorization
          const { userRole } = req.user;

          // Only admins and merchants can trigger webhooks
          if (userRole !== 'ADMIN' && userRole !== 'MERCHANT') {
            throw ErrorFactory.authorization('You do not have permission to trigger webhooks');
          }

          // Get webhook ID from params
          const { id } = req.params;

          // Get payload from request body
          const payload: any =req.body;

          // Trigger webhook
          const result: any =await service.triggerWebhook(id, payload);

          // Send success response
          return res.status(200).json({
            success: true,
            data: result
          });
        } catch (error) {
          logger.error(`Error triggering webhook:`, error);
          return res.status(500).json({
            success: false,
            error: (error as Error).message || 'An error occurred while triggering webhook'
          });
        }
      }
    );

    // Create module
    this.module = {
      name: 'webhook',
      router,
      repository,
      service,
      controller,
      dependencies: [],
      initialize: async () => {
        logger.info('Initializing webhook module');

        // Register dependencies
        this.container.registerSingleton('webhookRepository', () => repository);
        this.container.registerSingleton('webhookService', () => service);
        this.container.registerSingleton('webhookController', () => controller);

        logger.info('Webhook module initialized');
      }
    };

    // Register the module
    this.moduleRegistry.registerModule(this.module);
  }

  /**
   * Get the module
   * @returns Webhook module
   */
  getModule(): Module {
    return this.module;
  }
}