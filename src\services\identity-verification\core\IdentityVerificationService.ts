import { PrismaClient } from '@prisma/client';
/**
 * Identity Verification Service
 *
 * Main orchestrator for all identity verification methods.
 */

import {
  PrismaClient,
  IdentityVerificationMethodEnum,
  IdentityVerificationStatusEnum,
} from '@prisma/client';
import {
  IdentityVerificationResult,
  VerificationFilters,
  VerificationStats,
  VerificationConfig,
  EthereumSignatureParams,
} from './IdentityVerificationTypes';
import { IdentityVerificationError } from './IdentityVerificationError';
import { EthereumSignatureVerification } from '../methods/EthereumSignatureVerification';
import { logger } from '../../../lib/logger';

/**
 * Main identity verification service
 */
export class IdentityVerificationService {
  private prisma: PrismaClient;
  private config: VerificationConfig;
  private ethereumSignatureVerification: EthereumSignatureVerification;

  constructor(prisma: PrismaClient, config?: Partial<VerificationConfig>) {
    this.prisma = prisma;
    this.config = {
      ethereumRpcUrl:
        process.env.ETHEREUM_RPC_URL || 'https://mainnet.infura.io/v3/your-infura-key',
      enabledMethods: [
        IdentityVerificationMethodEnum.ETHEREUM_SIGNATURE,
        IdentityVerificationMethodEnum.ERC1484,
        IdentityVerificationMethodEnum.ERC725,
        IdentityVerificationMethodEnum.ENS,
        IdentityVerificationMethodEnum.POLYGON_ID,
        IdentityVerificationMethodEnum.WORLDCOIN,
        IdentityVerificationMethodEnum.BRIGHTID,
      ],
      ...config,
    };

    // Initialize verification methods
    this.ethereumSignatureVerification = new EthereumSignatureVerification(this.prisma);
  }

  /**
   * Verify identity using Ethereum signature
   */
  async verifyEthereumSignature(
    params: EthereumSignatureParams
  ): Promise<IdentityVerificationResult> {
    if (!this.isMethodEnabled(IdentityVerificationMethodEnum.ETHEREUM_SIGNATURE)) {
      throw IdentityVerificationError.invalidParameters(
        'Ethereum signature verification is not enabled'
      );
    }

    return await this.ethereumSignatureVerification.verify(params);
  }

  /**
   * Get verification by ID
   */
  async getVerificationById(id: string) {
    try {
      const verification = await this.prisma.identityVerification.findUnique({
        where: { id },
        include: { claims: true },
      });

      if (!verification) {
        throw IdentityVerificationError.verificationNotFound();
      }

      return verification;
    } catch (error) {
      if (error instanceof IdentityVerificationError) {
        throw error;
      }

      logger.error('Error getting verification by ID:', error);
      throw IdentityVerificationError.internalError('Failed to retrieve identity verification');
    }
  }

  /**
   * Get verifications with filters
   */
  async getVerifications(filters: VerificationFilters = {}) {
    try {
      const where: any = {};

      if (filters.userId) where.userId = filters.userId;
      if (filters.merchantId) where.merchantId = filters.merchantId;
      if (filters.method) where.method = filters.method;
      if (filters.status) where.status = filters.status;

      if (filters.dateFrom || filters.dateTo) {
        where.createdAt = {};
        if (filters.dateFrom) where.createdAt.gte = filters.dateFrom;
        if (filters.dateTo) where.createdAt.lte = filters.dateTo;
      }

      return await this.prisma.identityVerification.findMany({
        where,
        include: { claims: true },
        orderBy: { createdAt: 'desc' },
        take: filters.limit || 50,
        skip: filters.offset || 0,
      });
    } catch (error) {
      logger.error('Error getting verifications:', error);
      throw IdentityVerificationError.internalError('Failed to retrieve identity verifications');
    }
  }

  /**
   * Get verifications for user
   */
  async getVerificationsForUser(userId: string) {
    if (!userId) {
      throw IdentityVerificationError.invalidParameters('User ID is required');
    }

    return await this.getVerifications({ userId });
  }

  /**
   * Get verifications for merchant
   */
  async getVerificationsForMerchant(merchantId: string) {
    if (!merchantId) {
      throw IdentityVerificationError.invalidParameters('Merchant ID is required');
    }

    return await this.getVerifications({ merchantId });
  }

  /**
   * Get verification statistics
   */
  async getVerificationStats(filters: VerificationFilters = {}): Promise<VerificationStats> {
    try {
      const where: any = {};

      if (filters.userId) where.userId = filters.userId;
      if (filters.merchantId) where.merchantId = filters.merchantId;
      if (filters.dateFrom || filters.dateTo) {
        where.createdAt = {};
        if (filters.dateFrom) where.createdAt.gte = filters.dateFrom;
        if (filters.dateTo) where.createdAt.lte = filters.dateTo;
      }

      const [
        totalVerifications,
        successfulVerifications,
        failedVerifications,
        pendingVerifications,
        verificationsByMethod,
      ] = await Promise.all([
        this.prisma.identityVerification.count({ where }),
        this.prisma.identityVerification.count({
          where: { ...where, status: IdentityVerificationStatusEnum.VERIFIED },
        }),
        this.prisma.identityVerification.count({
          where: { ...where, status: IdentityVerificationStatusEnum.REJECTED },
        }),
        this.prisma.identityVerification.count({
          where: { ...where, status: IdentityVerificationStatusEnum.PENDING },
        }),
        this.prisma.identityVerification.groupBy({
          by: ['method'],
          where,
          _count: { method: true },
        }),
      ]);

      const methodStats: Record<string, number> = {};
      verificationsByMethod.forEach((item: any) => {
        methodStats[item.method] = item._count.method;
      });

      // Calculate average verification time (simplified)
      const averageVerificationTime = 5000; // 5 seconds average

      return {
        totalVerifications,
        successfulVerifications,
        failedVerifications,
        pendingVerifications,
        verificationsByMethod: methodStats,
        averageVerificationTime,
      };
    } catch (error) {
      logger.error('Error getting verification stats:', error);
      throw IdentityVerificationError.internalError('Failed to retrieve verification statistics');
    }
  }

  /**
   * Check if verification method is enabled
   */
  private isMethodEnabled(method: IdentityVerificationMethodEnum): boolean {
    return this.config.enabledMethods.includes(method);
  }

  /**
   * Get enabled verification methods
   */
  getEnabledMethods(): IdentityVerificationMethodEnum[] {
    return [...this.config.enabledMethods];
  }

  /**
   * Update configuration
   */
  updateConfig(config: Partial<VerificationConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * Health check for verification service
   */
  async healthCheck(): Promise<{ status: string; methods: string[]; errors: string[] }> {
    const errors: string[] = [];
    const methods: string[] = [];

    // Check each enabled method
    for (const method of this.config.enabledMethods) {
      try {
        switch (method) {
          case IdentityVerificationMethodEnum.ETHEREUM_SIGNATURE:
            // Test Ethereum connection
            methods.push('ethereum_signature');
            break;
          // Add other method checks here
          default:
            methods.push(method);
        }
      } catch (error) {
        errors.push(`${method}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    return {
      status: errors.length === 0 ? 'healthy' : 'degraded',
      methods,
      errors,
    };
  }
}
