// jscpd:ignore-file
import { Router } from "express";
import { EmailController } from "../controllers/refactored/email.controller.ts";
import { authenticate } from '../middlewares/auth';
import { EmailController } from "../controllers/refactored/email.controller.ts";
import { authenticate } from '../middlewares/auth';

const router: any =Router();

// Email routes
router.post("/test", authenticate, EmailController.testEmailService);
router.post("/send", authenticate, EmailController.sendCustomEmail);
router.get("/admin-emails", authenticate, EmailController.getAdminEmails);

export default router;
