/**
 * Test Suite Builders
 *
 * Functions to create comprehensive test suites for different component types.
 */

import { BaseController } from '../../../controllers/base.controller';
import { BaseService } from '../../../services/base.service';
// BaseRepository not available - using unknown type
import {
  ControllerTestOptions,
  ServiceTestOptions,
  RepositoryTestOptions,
  TestSuiteConfig,
  TestScenario,
  TestStep,
} from '../core/TestTypes';
import { testController, testService, testRepository } from '../runners/TestRunners';

/**
 * Create a test suite for a controller
 */
export function createControllerTestSuite(
  name: string,
  controllerClass: new (...args: any[]) => BaseController,
  tests: Record<string, ControllerTestOptions>,
  config: TestSuiteConfig = { name: 'default' }
): void {
  describe(name, () => {
    let controller: BaseController;

    // Global setup
    beforeAll(async () => {
      if (config.setup) {
        await config.setup();
      }
    });

    // Global teardown
    afterAll(async () => {
      if (config.teardown) {
        await config.teardown();
      }
    });

    // Setup before each test
    beforeEach(async () => {
      controller = new controllerClass();

      if (config.beforeEach) {
        await config.beforeEach();
      }
    });

    // Cleanup after each test
    afterEach(async () => {
      if (config.afterEach) {
        await config.afterEach();
      }
    });

    // Create individual test cases
    Object.entries(tests).forEach(([method, testOptions]) => {
      const testName = testOptions.description || `should test ${method}`;
      const timeout = testOptions.timeout ?? config.timeout ?? 10000;

      if (testOptions.skip) {
        it.skip(testName, () => {});
        return;
      }

      const testFn = testOptions.only ? it.only : it;

      testFn(
        testName,
        async () => {
          await testController(controller, method as any, testOptions);
        },
        timeout
      );
    });
  });
}

/**
 * Create a test suite for a service
 */
export function createServiceTestSuite(
  name: string,
  serviceClass: new (...args: any[]) => BaseService,
  tests: Record<string, ServiceTestOptions>,
  config: TestSuiteConfig = { name: 'default' },
  setupFn?: (service: BaseService) => void | Promise<void>
): void {
  describe(name, () => {
    let service: BaseService;

    // Global setup
    beforeAll(async () => {
      if (config.setup) {
        await config.setup();
      }
    });

    // Global teardown
    afterAll(async () => {
      if (config.teardown) {
        await config.teardown();
      }
    });

    // Setup before each test
    beforeEach(async () => {
      service = new serviceClass();

      if (setupFn) {
        await setupFn(service);
      }

      if (config.beforeEach) {
        await config.beforeEach();
      }
    });

    // Cleanup after each test
    afterEach(async () => {
      if (config.afterEach) {
        await config.afterEach();
      }
    });

    // Create individual test cases
    Object.entries(tests).forEach(([method, testOptions]) => {
      const testName = testOptions.description || `should test ${method}`;
      const timeout = testOptions.timeout ?? config.timeout ?? 10000;

      if (testOptions.skip) {
        it.skip(testName, () => {});
        return;
      }

      const testFn = testOptions.only ? it.only : it;

      testFn(
        testName,
        async () => {
          await testService(service, method as any, testOptions);
        },
        timeout
      );
    });
  });
}

/**
 * Create a test suite for a repository
 */
export function createRepositoryTestSuite(
  name: string,
  repositoryClass: new (...args: any[]) => unknown,
  tests: Record<string, RepositoryTestOptions>,
  config: TestSuiteConfig = { name: 'default' }
): void {
  describe(name, () => {
    let repository: unknown;

    // Global setup
    beforeAll(async () => {
      if (config.setup) {
        await config.setup();
      }
    });

    // Global teardown
    afterAll(async () => {
      if (config.teardown) {
        await config.teardown();
      }
    });

    // Setup before each test
    beforeEach(async () => {
      repository = new repositoryClass();

      if (config.beforeEach) {
        await config.beforeEach();
      }
    });

    // Cleanup after each test
    afterEach(async () => {
      if (config.afterEach) {
        await config.afterEach();
      }
    });

    // Create individual test cases
    Object.entries(tests).forEach(([method, testOptions]) => {
      const testName = testOptions.description || `should test ${method}`;
      const timeout = testOptions.timeout ?? config.timeout ?? 10000;

      if (testOptions.skip) {
        it.skip(testName, () => {});
        return;
      }

      const testFn = testOptions.only ? it.only : it;

      testFn(
        testName,
        async () => {
          await testRepository(repository, method as any, testOptions);
        },
        timeout
      );
    });
  });
}

/**
 * Create an integration test suite
 */
export function createIntegrationTestSuite(
  name: string,
  scenarios: TestScenario[],
  config: TestSuiteConfig = { name: 'default' }
): void {
  describe(`Integration: ${name}`, () => {
    // Global setup
    beforeAll(async () => {
      if (config.setup) {
        await config.setup();
      }
    });

    // Global teardown
    afterAll(async () => {
      if (config.teardown) {
        await config.teardown();
      }
    });

    // Setup before each test
    beforeEach(async () => {
      if (config.beforeEach) {
        await config.beforeEach();
      }
    });

    // Cleanup after each test
    afterEach(async () => {
      if (config.afterEach) {
        await config.afterEach();
      }
    });

    // Create scenario tests
    scenarios.forEach((scenario) => {
      it(
        scenario.name,
        async () => {
          // Scenario setup
          if (scenario.setup) {
            await scenario.setup();
          }

          try {
            // Execute steps
            for (const step of scenario.steps) {
              await executeTestStep(step);
            }

            // Validate expected outcome
            if (scenario.expectedOutcome) {
              expect(scenario.expectedOutcome).toBeDefined();
            }
          } finally {
            // Scenario teardown
            if (scenario.teardown) {
              await scenario.teardown();
            }
          }
        },
        config.timeout ?? 30000
      );
    });
  });
}

/**
 * Create a performance test suite
 */
export function createPerformanceTestSuite(
  name: string,
  tests: Record<
    string,
    {
      fn: Function;
      args?: any[];
      maxExecutionTime?: number;
      iterations?: number;
      warmupIterations?: number;
    }
  >,
  config: TestSuiteConfig = { name: 'default' }
): void {
  describe(`Performance: ${name}`, () => {
    // Global setup
    beforeAll(async () => {
      if (config.setup) {
        await config.setup();
      }
    });

    // Global teardown
    afterAll(async () => {
      if (config.teardown) {
        await config.teardown();
      }
    });

    // Create performance tests
    Object.entries(tests).forEach(([testName, testConfig]) => {
      it(
        `should perform ${testName} within acceptable time`,
        async () => {
          const iterations = testConfig.iterations ?? 100;
          const warmupIterations = testConfig.warmupIterations ?? 10;
          const maxExecutionTime = testConfig.maxExecutionTime ?? 1000; // 1 second
          const args = testConfig.args ?? [];

          // Warmup
          for (let i = 0; i < warmupIterations; i++) {
            await testConfig.fn(...args);
          }

          // Measure performance
          const startTime = process.hrtime.bigint();

          for (let i = 0; i < iterations; i++) {
            await testConfig.fn(...args);
          }

          const endTime = process.hrtime.bigint();
          const executionTime = Number(endTime - startTime) / 1000000; // Convert to milliseconds
          const averageTime = executionTime / iterations;

          expect(averageTime).toBeLessThan(maxExecutionTime);

          console.log(`Performance: ${testName}`);
          console.log(`  Total time: ${executionTime.toFixed(2)}ms`);
          console.log(`  Average time: ${averageTime.toFixed(2)}ms`);
          console.log(`  Iterations: ${iterations}`);
        },
        config.timeout ?? 60000
      );
    });
  });
}

/**
 * Create an API test suite
 */
export function createApiTestSuite(
  name: string,
  baseUrl: string,
  endpoints: Record<
    string,
    {
      method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
      path: string;
      headers?: Record<string, string>;
      body?: unknown;
      expectedStatus?: number;
      expectedResponse?: unknown;
      auth?: unknown;
    }
  >,
  config: TestSuiteConfig = { name: 'default' }
): void {
  describe(`API: ${name}`, () => {
    // Global setup
    beforeAll(async () => {
      if (config.setup) {
        await config.setup();
      }
    });

    // Global teardown
    afterAll(async () => {
      if (config.teardown) {
        await config.teardown();
      }
    });

    // Create API tests
    Object.entries(endpoints).forEach(([endpointName, endpointConfig]) => {
      it(`should handle ${endpointConfig.method} ${endpointConfig.path}`, async () => {
        // This would integrate with a real HTTP client like supertest
        // For now, it's a placeholder structure

        const url = `${baseUrl}${endpointConfig.path}`;
        const options = {
          method: endpointConfig.method,
          headers: endpointConfig.headers ?? {},
          body: endpointConfig.body,
        };

        // Mock API call result
        const mockResponse = {
          status: endpointConfig.expectedStatus ?? 200,
          data: endpointConfig.expectedResponse,
        };

        expect(mockResponse.status).toBe(endpointConfig.expectedStatus ?? 200);

        if (endpointConfig.expectedResponse) {
          expect(mockResponse.data).toEqual(endpointConfig.expectedResponse);
        }
      });
    });
  });
}

/**
 * Execute a test step
 */
async function executeTestStep(step: TestStep): Promise<void> {
  const startTime = Date.now();

  try {
    await step.action();

    if (step.validation) {
      await step.validation();
    }
  } catch (error) {
    const executionTime = Date.now() - startTime;

    if (step.timeout && executionTime > step.timeout) {
      throw new Error(`Test step "${step.name}" timed out after ${executionTime}ms`);
    }

    throw error;
  }
}

/**
 * Create a custom test matcher
 */
export function createCustomMatcher(
  name: string,
  matcher: (received: unknown, ...args: any[]) => jest.CustomMatcherResult
): void {
  expect.extend({
    [name]: matcher,
  });
}

/**
 * Create a test data builder
 */
export function createTestDataBuilder<T>(defaultData: T, overrides?: Partial<T>): T {
  return {
    ...defaultData,
    ...overrides,
  };
}

/**
 * Create a test scenario builder
 */
export function createTestScenarioBuilder(name: string): {
  withDescription: (description: string) => unknown;
  withSetup: (setup: () => void | Promise<void>) => unknown;
  withTeardown: (teardown: () => void | Promise<void>) => unknown;
  withSteps: (steps: TestStep[]) => unknown;
  withExpectedOutcome: (outcome: unknown) => unknown;
  build: () => TestScenario;
} {
  let scenario: Partial<TestScenario> = { name };

  return {
    withDescription: (description: string) => {
      scenario.description = description;
      return this;
    },
    withSetup: (setup: () => void | Promise<void>) => {
      scenario.setup = setup;
      return this;
    },
    withTeardown: (teardown: () => void | Promise<void>) => {
      scenario.teardown = teardown;
      return this;
    },
    withSteps: (steps: TestStep[]) => {
      scenario.steps = steps;
      return this;
    },
    withExpectedOutcome: (outcome: unknown) => {
      scenario.expectedOutcome = outcome;
      return this;
    },
    build: () => scenario as TestScenario,
  };
}
