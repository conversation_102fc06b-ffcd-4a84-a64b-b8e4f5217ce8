// jscpd:ignore-file
import { Request, Response, NextFunction } from 'express';
import { BinanceService } from "../services/binance.service";
import { AppError, asyncHandler } from '../middlewares/error.middleware';
import { BinanceService } from "../services/binance.service";
import { AppError, asyncHandler } from '../middlewares/error.middleware';

// Test Binance API connection
export const testConnection: unknown =asyncHandler(async (req: Request, res: Response) => {
    const { apiKey, apiSecret } = req.body;
  
    // Validate required fields
    if (!apiKey || !apiSecret) {
        throw new AppError({
            message: "API key and secret are required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }
  
    // Test connection
    const result: any =await BinanceService.testConnection(apiKey, apiSecret);
  
    if (!result.success) {
        return res.status(400).json({
            success: false,
            message: result.message
        });
    }
  
    res.status(200).json({
        success: true,
        message: "Connection successful"
    });
});

// Get account information
export const getAccountInfo: unknown =asyncHandler(async (req: Request, res: Response) => {
    const { apiKey, apiSecret } = req.body;
  
    // Validate required fields
    if (!apiKey || !apiSecret) {
        throw new AppError({
            message: "API key and secret are required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }
  
    // Get account information
    const accountInfo: unknown =await BinanceService.getAccountInfo(apiKey, apiSecret);
  
    res.status(200).json(accountInfo);
});

// Get deposit history
export const getDepositHistory: unknown =asyncHandler(async (req: Request, res: Response) => {
    const { apiKey, apiSecret, coin, status, startTime, endTime } = req.body;
  
    // Validate required fields
    if (!apiKey || !apiSecret) {
        throw new AppError({
            message: "API key and secret are required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }
  
    // Get deposit history
    const depositHistory: unknown =await BinanceService.getDepositHistory(
        apiKey,
        apiSecret,
        coin,
        status,
        startTime,
        endTime
    );
  
    res.status(200).json(depositHistory);
});

// Verify TRC20 deposit
export const verifyTrc20Deposit: unknown =asyncHandler(async (req: Request, res: Response) => {
    const { apiKey, apiSecret, txHash, amount, coin, timeWindow } = req.body;
  
    // Validate required fields
    if (!apiKey || !apiSecret || !txHash || !amount) {
        throw new AppError({
            message: "API key, secret, transaction hash, and amount are required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }
  
    // Verify TRC20 deposit
    const result: any =await BinanceService.verifyTrc20Deposit(
        apiKey,
        apiSecret,
        txHash,
        amount,
        coin,
        timeWindow
    );
  
    res.status(200).json(result);
});
