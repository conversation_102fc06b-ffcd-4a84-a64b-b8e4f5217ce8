{"version": 3, "file": "IdentityVerificationResponseMapper.js", "sourceRoot": "", "sources": ["../../../../../src/controllers/identity-verification/mappers/IdentityVerificationResponseMapper.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;AAWH,6DAA0D;AAE1D;;GAEG;AACH,MAAa,kCAAkC;IAC7C;;OAEG;IACH,MAAM,CAAC,WAAW,CAChB,GAAa,EACb,IAAO,EACP,OAAgB,EAChB,aAAqB,GAAG,EACxB,UAKC;QAED,MAAM,QAAQ,GAAuB;YACnC,OAAO,EAAE,IAAI;YACb,IAAI;YACJ,OAAO;YACP,UAAU;YACV,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,SAAS,EAAE,GAAG,CAAC,MAAM,CAAC,SAAS,IAAI,SAAS;SAC7C,CAAC;QAEF,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,SAAS,CAAC,GAAa,EAAE,KAAuB,EAAE,UAAmB;QAC1E,IAAI,aAA4B,CAAC;QAEjC,IAAI,KAAK,YAAY,mBAAQ,EAAE,CAAC;YAC9B,aAAa,GAAG;gBACd,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE;oBACL,OAAO,EAAE,KAAK,CAAC,OAAO;oBACtB,IAAI,EAAE,KAAK,CAAC,IAAI;oBAChB,IAAI,EAAE,KAAK,CAAC,IAAI;oBAChB,OAAO,EAAE,KAAK,CAAC,OAAO;iBACvB;gBACD,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,SAAS,EAAE,GAAG,CAAC,MAAM,CAAC,SAAS,IAAI,SAAS;aAC7C,CAAC;YAEF,UAAU,GAAG,UAAU,IAAI,KAAK,CAAC,UAAU,IAAI,GAAG,CAAC;QACrD,CAAC;aAAM,CAAC;YACN,aAAa,GAAG;gBACd,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE;oBACL,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI,uBAAuB;oBACjD,IAAI,EAAE,uBAAuB;oBAC7B,IAAI,EAAE,UAAU;iBACjB;gBACD,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,SAAS,EAAE,GAAG,CAAC,MAAM,CAAC,SAAS,IAAI,SAAS;aAC7C,CAAC;YAEF,UAAU,GAAG,UAAU,IAAI,GAAG,CAAC;QACjC,CAAC;QAED,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,sBAAsB,CAAC,GAAa,EAAE,MAAW,EAAE,OAAgB;QACxE,MAAM,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QAE9C,IAAI,CAAC,WAAW,CACd,GAAG,EACH,MAAM,EACN,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,qCAAqC,CAAC,CAAC,CAAC,qBAAqB,CAAC,EAC3F,UAAU,CACX,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,gBAAgB,CACrB,GAAa,EACb,YAAkC,EAClC,OAAgB;QAEhB,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,YAAY,EAAE,OAAO,IAAI,qCAAqC,CAAC,CAAC;IACxF,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,qBAAqB,CAC1B,GAAa,EACb,aAAqC,EACrC,KAAa,EACb,OAAe,CAAC,EAChB,QAAgB,EAAE;QAElB,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;QAE5C,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,aAAa,EAAE,aAAa,aAAa,CAAC,MAAM,gBAAgB,EAAE,GAAG,EAAE;YAC3F,IAAI;YACJ,KAAK;YACL,KAAK;YACL,UAAU;SACX,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,SAAS,CACd,GAAa,EACb,KAAoB,EACpB,OAAgB,EAChB,aAAqB,GAAG;QAExB,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,KAAK,EAAE,OAAO,IAAI,8BAA8B,EAAE,UAAU,CAAC,CAAC;IACtF,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,gBAAgB,CAAC,GAAa,EAAE,KAAoB;QACzD,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,KAAK,EAAE,0BAA0B,EAAE,GAAG,CAAC,CAAC;IAC9D,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,qBAAqB,CAAC,GAAa,EAAE,KAAgC;QAC1E,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,KAAK,EAAE,gDAAgD,CAAC,CAAC;IACjF,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,qBAAqB,CAAC,GAAa,EAAE,QAAmC;QAC7E,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,QAAQ,EAAE,2CAA2C,CAAC,CAAC;IAC/E,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,wCAAwC,CAAC,GAAa,EAAE,OAAY;QACzE,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,OAAO,EAAE,sDAAsD,EAAE,GAAG,CAAC,CAAC;IAC9F,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,iCAAiC,CAAC,GAAa;QACpD,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE,2CAA2C,CAAC,CAAC;IACzF,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,iCAAiC,CAAC,GAAa;QACpD,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,EAAE,yCAAyC,EAAE,GAAG,CAAC,CAAC;IAC7F,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,+BAA+B,CAAC,GAAa,EAAE,KAAa;QACjE,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,EAAE,SAAS,KAAK,wBAAwB,CAAC,CAAC;IAC3E,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,6BAA6B,CAAC,GAAa,EAAE,YAAkC;QACpF,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,YAAY,EAAE,0CAA0C,CAAC,CAAC;IAClF,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,yBAAyB,CAAC,GAAa,EAAE,MAAW;QACzD,IAAI,CAAC,sBAAsB,CAAC,GAAG,EAAE,MAAM,EAAE,mCAAmC,CAAC,CAAC;IAChF,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,mBAAmB,CACxB,GAAa,EACb,MAAa,EACb,UAAkB,mBAAmB;QAErC,MAAM,KAAK,GAAG,IAAI,mBAAQ,CAAC;YACzB,OAAO;YACP,IAAI,EAAE,YAAmB;YACzB,IAAI,EAAE,eAAsB;YAC5B,OAAO,EAAE,EAAE,MAAM,EAAE;SACpB,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,sBAAsB,CAC3B,GAAa,EACb,UAAkB,eAAe,EACjC,YAAqB;QAErB,MAAM,KAAK,GAAG,IAAI,mBAAQ,CAAC;YACzB,OAAO;YACP,IAAI,EAAE,gBAAuB;YAC7B,IAAI,EAAE,qBAA4B;YAClC,OAAO,EAAE,EAAE,YAAY,EAAE;SAC1B,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,iBAAiB,CAAC,GAAa,EAAE,WAAmB,UAAU;QACnE,MAAM,KAAK,GAAG,IAAI,mBAAQ,CAAC;YACzB,OAAO,EAAE,GAAG,QAAQ,YAAY;YAChC,IAAI,EAAE,WAAkB;YACxB,IAAI,EAAE,oBAA2B;SAClC,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,uBAAuB,CAAC,GAAa,EAAE,UAAkB,uBAAuB;QACrF,MAAM,KAAK,GAAG,IAAI,mBAAQ,CAAC;YACzB,OAAO;YACP,IAAI,EAAE,UAAiB;YACvB,IAAI,EAAE,uBAA8B;SACrC,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,gBAAgB,CACrB,IAAY,EACZ,KAAa,EACb,KAAa;QASb,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;QAE5C,OAAO;YACL,IAAI;YACJ,KAAK;YACL,KAAK;YACL,UAAU;YACV,OAAO,EAAE,IAAI,GAAG,UAAU;YAC1B,OAAO,EAAE,IAAI,GAAG,CAAC;SAClB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,YAAY,CAAC,EAAY;QAC9B,OAAO,CAAC,GAAQ,EAAE,GAAa,EAAE,IAAc,EAAE,EAAE;YACjD,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;QACpE,CAAC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,aAAa,CAAC,GAAa;QAChC,GAAG,CAAC,SAAS,CAAC,cAAc,EAAE,kBAAkB,CAAC,CAAC;QAClD,GAAG,CAAC,SAAS,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;QACtC,GAAG,CAAC,SAAS,CAAC,iBAAiB,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,WAAW,CAAC,MAAc,EAAE,GAAW,EAAE,UAAkB,EAAE,YAAoB;QACtF,OAAO,CAAC,GAAG,CAAC,GAAG,MAAM,IAAI,GAAG,MAAM,UAAU,MAAM,YAAY,IAAI,CAAC,CAAC;IACtE,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,qBAAqB,CAAC,YAAiB;QAC5C,OAAO;YACL,EAAE,EAAE,YAAY,CAAC,EAAE;YACnB,IAAI,EAAE,YAAY,CAAC,IAAI;YACvB,MAAM,EAAE,YAAY,CAAC,MAAM;YAC3B,OAAO,EAAE,YAAY,CAAC,OAAO;YAC7B,MAAM,EAAE,YAAY,CAAC,MAAM;YAC3B,UAAU,EAAE,YAAY,CAAC,UAAU;YACnC,QAAQ,EAAE,YAAY,CAAC,QAAQ;YAC/B,SAAS,EAAE,YAAY,CAAC,SAAS;YACjC,SAAS,EAAE,YAAY,CAAC,SAAS;YACjC,SAAS,EAAE,YAAY,CAAC,SAAS;SAClC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,cAAc,CAAC,KAAU;QAC9B,OAAO;YACL,EAAE,EAAE,KAAK,CAAC,EAAE;YACZ,cAAc,EAAE,KAAK,CAAC,cAAc;YACpC,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,KAAK,EAAE,KAAK,CAAC,KAAK;YAClB,MAAM,EAAE,KAAK,CAAC,MAAM;YACpB,SAAS,EAAE,KAAK,CAAC,SAAS;YAC1B,SAAS,EAAE,KAAK,CAAC,SAAS;YAC1B,SAAS,EAAE,KAAK,CAAC,SAAS;SAC3B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,0BAA0B,CAAC,KAAU;QAC1C,OAAO;YACL,kBAAkB,EAAE,KAAK,CAAC,kBAAkB,IAAI,CAAC;YACjD,aAAa,EAAE,KAAK,CAAC,aAAa,IAAI,CAAC;YACvC,YAAY,EAAE,KAAK,CAAC,YAAY,IAAI,CAAC;YACrC,WAAW,EAAE,KAAK,CAAC,WAAW,IAAI,CAAC;YACnC,YAAY,EAAE,KAAK,CAAC,YAAY,IAAI,CAAC;YACrC,mBAAmB,EAAE,KAAK,CAAC,mBAAmB,IAAI,EAAE;YACpD,sBAAsB,EAAE,KAAK,CAAC,sBAAsB,IAAI,EAAE;SAC3D,CAAC;IACJ,CAAC;CACF;AA5VD,gFA4VC"}