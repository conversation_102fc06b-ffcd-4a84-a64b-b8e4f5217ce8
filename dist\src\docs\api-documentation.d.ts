/**
 * API Documentation Generator
 *
 * Generates comprehensive API documentation for all endpoints
 */
export interface APIEndpoint {
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
    path: string;
    description: string;
    parameters?: APIParameter[];
    requestBody?: APIRequestBody;
    responses: APIResponse[];
    authentication?: string[];
    authorization?: string[];
    examples?: APIExample[];
}
export interface APIParameter {
    name: string;
    type: 'string' | 'number' | 'boolean' | 'object' | 'array';
    location: 'path' | 'query' | 'header';
    required: boolean;
    description: string;
    example?: any;
}
export interface APIRequestBody {
    contentType: string;
    schema: any;
    description: string;
    example?: any;
}
export interface APIResponse {
    statusCode: number;
    description: string;
    schema?: any;
    example?: any;
}
export interface APIExample {
    title: string;
    description: string;
    request: any;
    response: any;
}
export declare class APIDocumentationGenerator {
    private readonly endpoints;
    constructor();
    /**
     * Initialize all API endpoints
     */
    private initializeEndpoints;
    /**
     * Add Identity Verification endpoints
     */
    private addIdentityVerificationEndpoints;
    /**
     * Add Fraud Detection endpoints
     */
    private addFraudDetectionEndpoints;
    /**
     * Add Admin endpoints
     */
    private addAdminEndpoints;
    /**
     * Add Alert Aggregation endpoints
     */
    private addAlertAggregationEndpoints;
    /**
     * Generate OpenAPI specification
     */
    generateOpenAPISpec(): any;
    /**
     * Generate markdown documentation
     */
    generateMarkdownDocs(): string;
    /**
     * Get all endpoints
     */
    getEndpoints(): APIEndpoint[];
    /**
     * Generate Postman collection
     */
    generatePostmanCollection(): any;
}
//# sourceMappingURL=api-documentation.d.ts.map