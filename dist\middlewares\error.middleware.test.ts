// jscpd:ignore-file
import { Request, Response, NextFunction } from "express";
import { 
    AppError, 
    errorHand<PERSON>, 
    createBadRequestError,
    createUnauthorizedError,
    createForbiddenError,
    createNotFoundError,
    createConflictError,
    createValidationError,
    createServerError,
    createTooManyRequestsError
} from "./error.middleware";
import { logger } from "../lib/logger";
import { Middleware } from '../types/express';
import { 
    AppError, 
    errorHandler, 
    createBadRequestError,
    createUnauthorizedError,
    createForbiddenError,
    createNotFoundError,
    createConflictError,
    createValidationError,
    createServerError,
    createTooManyRequestsError
} from "./error.middleware";
import { logger } from "../lib/logger";
import { Middleware } from '../types/express';


// Mock the logger
jest.mock("../lib/logger", () => ({
    logger: {, error: jest.fn(),
        warn: jest.fn(),
        info: jest.fn(),
        debug: jest.fn()
    }
}));

describe("Error Middleware", () => {
    let mockRequest: Partial<Request>;
    let mockResponse: Partial<Response>;
    let nextFunction: NextFunction;

    beforeEach(() => {
        mockRequest = {
            method: "GET",
            path: "/test",
            ip: "127.0.0.1",
            requestId: "test-request-id",
            user: {, userId: "user123",
                role: "MERCHANT"
            }
        };
    
        mockResponse = {
            status: jest.fn().mockReturnThis(),
            json: jest.fn(),
            setHeader: jest.fn()
        };
    
        nextFunction = jest.fn();
    
        jest.clearAllMocks();
    });

    describe("AppError", () => {
        it("should create an AppError with default values", () => {
            const error: any =new AppError("Test error");
      
            expect(error).toBeInstanceOf(Error);
            expect((error as Error).message).toBe("Test error");
            expect(error.statusCode).toBe(500);
            expect(error.isOperational).toBe(true);
            expect(error.code).toBe("INTERNAL_SERVER_ERROR");
            expect(error.details).toBeUndefined();
        });

        it("should create an AppError with custom values", () => {
            const error: any =new AppError("Test error", 400, true, "CUSTOM_ERROR", { field: "test" });
      
            expect((error as Error).message).toBe("Test error");
            expect(error.statusCode).toBe(400);
            expect(error.isOperational).toBe(true);
            expect(error.code).toBe("CUSTOM_ERROR");
            expect(error.details).toEqual({ field: "test" });
        });
    });

    describe("Error Factory Functions", () => {
        it("should create a bad request error", () => {
            const error: any =createBadRequestError("Bad request");
      
            expect(error).toBeInstanceOf(AppError);
            expect((error as Error).message).toBe("Bad request");
            expect(error.statusCode).toBe(400);
            expect(error.isOperational).toBe(true);
            expect(error.code).toBe("BAD_REQUEST");
        });

        it("should create an unauthorized error", () => {
            const error: any =createUnauthorizedError("Unauthorized");
      
            expect(error).toBeInstanceOf(AppError);
            expect((error as Error).message).toBe("Unauthorized");
            expect(error.statusCode).toBe(401);
            expect(error.isOperational).toBe(true);
            expect(error.code).toBe("UNAUTHORIZED");
        });

        it("should create a forbidden error", () => {
            const error: any =createForbiddenError("Forbidden");
      
            expect(error).toBeInstanceOf(AppError);
            expect((error as Error).message).toBe("Forbidden");
            expect(error.statusCode).toBe(403);
            expect(error.isOperational).toBe(true);
            expect(error.code).toBe("FORBIDDEN");
        });

        it("should create a not found error", () => {
            const error: any =createNotFoundError("Not found");
      
            expect(error).toBeInstanceOf(AppError);
            expect((error as Error).message).toBe("Not found");
            expect(error.statusCode).toBe(404);
            expect(error.isOperational).toBe(true);
            expect(error.code).toBe("NOT_FOUND");
        });

        it("should create a conflict error", () => {
            const error: any =createConflictError("Conflict");
      
            expect(error).toBeInstanceOf(AppError);
            expect((error as Error).message).toBe("Conflict");
            expect(error.statusCode).toBe(409);
            expect(error.isOperational).toBe(true);
            expect(error.code).toBe("CONFLICT");
        });

        it("should create a validation error", () => {
            const error: any =createValidationError("Validation error");
      
            expect(error).toBeInstanceOf(AppError);
            expect((error as Error).message).toBe("Validation error");
            expect(error.statusCode).toBe(422);
            expect(error.isOperational).toBe(true);
            expect(error.code).toBe("VALIDATION_ERROR");
        });

        it("should create a server error", () => {
            const error: any =createServerError("Internal server error");
      
            expect(error).toBeInstanceOf(AppError);
            expect((error as Error).message).toBe("Internal server error");
            expect(error.statusCode).toBe(500);
            expect(error.isOperational).toBe(false);
            expect(error.code).toBe("INTERNAL_SERVER_ERROR");
        });

        it("should create a too many requests error", () => {
            const error: any =createTooManyRequestsError("Too many requests");
      
            expect(error).toBeInstanceOf(AppError);
            expect((error as Error).message).toBe("Too many requests");
            expect(error.statusCode).toBe(429);
            expect(error.isOperational).toBe(true);
            expect(error.code).toBe("RATE_LIMIT_EXCEEDED");
        });
    });

    describe("errorHandler", () => {
        it("should handle AppError", () => {
            const appError: any =new AppError("Test error", 400, true, "TEST_ERROR", { field: "test" });
      
            errorHandler(appError, mockRequest as Request, mockResponse as Response, nextFunction);
      
            expect(mockResponse.status).toHaveBeenCalledWith(400);
            expect(mockResponse.json).toHaveBeenCalledWith({
                status: "error",
                code: "TEST_ERROR",
                message: "Test error",
                details: {, field: "test" },
                requestId: "test-request-id"
            });
            expect(logger.warn).toHaveBeenCalled();
        });

        it("should handle validation error", () => {
            const validationError: any =new Error("Validation error");
            validationError.name = "ValidationError";
      
            errorHandler(validationError, mockRequest as Request, mockResponse as Response, nextFunction);
      
            expect(mockResponse.status).toHaveBeenCalledWith(400);
            expect(mockResponse.json).toHaveBeenCalledWith({
                status: "error",
                code: "VALIDATION_ERROR",
                message: "Validation error",
                details: null,
                requestId: "test-request-id"
            });
            expect(logger.warn).toHaveBeenCalled();
        });

        it("should handle JWT error", () => {
            const jwtError: any =new Error("Invalid token");
            jwtError.name = "JsonWebTokenError";
      
            errorHandler(jwtError, mockRequest as Request, mockResponse as Response, nextFunction);
      
            expect(mockResponse.status).toHaveBeenCalledWith(401);
            expect(mockResponse.json).toHaveBeenCalledWith({
                status: "error",
                code: "INVALID_TOKEN",
                message: "Invalid authentication token",
                details: null,
                requestId: "test-request-id"
            });
            expect(logger.warn).toHaveBeenCalled();
        });

        it("should handle JWT expiration error", () => {
            const jwtError: any =new Error("Token expired");
            jwtError.name = "TokenExpiredError";
      
            errorHandler(jwtError, mockRequest as Request, mockResponse as Response, nextFunction);
      
            expect(mockResponse.status).toHaveBeenCalledWith(401);
            expect(mockResponse.json).toHaveBeenCalledWith({
                status: "error",
                code: "TOKEN_EXPIRED",
                message: "Authentication token expired",
                details: null,
                requestId: "test-request-id"
            });
            expect(logger.warn).toHaveBeenCalled();
        });

        it("should handle unknown errors", () => {
            const unknownError: any =new Error("Unknown error");
      
            errorHandler(unknownError, mockRequest as Request, mockResponse as Response, nextFunction);
      
            expect(mockResponse.status).toHaveBeenCalledWith(500);
            expect(mockResponse.json).toHaveBeenCalledWith({
                status: "error",
                code: "INTERNAL_SERVER_ERROR",
                message: "Internal Server Error",
                details: null,
                requestId: "test-request-id"
            });
            expect(logger.error).toHaveBeenCalled();
        });
    });
});
