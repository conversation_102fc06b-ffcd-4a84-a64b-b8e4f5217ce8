597cd2dbc021df29d81f39acfec72c4d
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const vitest_1 = require("vitest");
const supertest_1 = __importDefault(require("supertest"));
const express_1 = __importDefault(require("express"));
const dashboard_controller_1 = require("../controllers/dashboard.controller");
const dashboard_widget_controller_1 = require("../controllers/dashboard-widget.controller");
// Mock Prisma
const mockPrisma = {
    dashboard: {
        findMany: vitest_1.vi.fn(),
        findUnique: vitest_1.vi.fn(),
        create: vitest_1.vi.fn(),
        update: vitest_1.vi.fn(),
        delete: vitest_1.vi.fn(),
    },
    dashboardWidget: {
        findMany: vitest_1.vi.fn(),
        findUnique: vitest_1.vi.fn(),
        findFirst: vitest_1.vi.fn(),
        create: vitest_1.vi.fn(),
        update: vitest_1.vi.fn(),
        delete: vitest_1.vi.fn(),
        deleteMany: vitest_1.vi.fn(),
    },
};
vitest_1.vi.mock('@prisma/client', () => ({
    PrismaClient: vitest_1.vi.fn(() => mockPrisma),
}));
const app = (0, express_1.default)();
app.use(express_1.default.json());
// Mock auth middleware
const mockAuthMiddleware = (req, res, next) => {
    req.user = {
        id: 'user-1',
        role: 'MERCHANT',
        email: '<EMAIL>',
    };
    next();
};
// Setup routes
const dashboardController = new dashboard_controller_1.DashboardController();
const widgetController = new dashboard_widget_controller_1.DashboardWidgetController();
app.get('/api/dashboards', mockAuthMiddleware, dashboardController.getDashboards);
app.get('/api/dashboards/:id', mockAuthMiddleware, dashboardController.getDashboardById);
app.post('/api/dashboards', mockAuthMiddleware, dashboardController.createDashboard);
app.put('/api/dashboards/:id', mockAuthMiddleware, dashboardController.updateDashboard);
app.delete('/api/dashboards/:id', mockAuthMiddleware, dashboardController.deleteDashboard);
app.get('/api/dashboards/:dashboardId/widgets', mockAuthMiddleware, widgetController.getWidgets);
app.get('/api/dashboards/widgets/:id', mockAuthMiddleware, widgetController.getWidgetById);
app.post('/api/dashboards/:dashboardId/widgets', mockAuthMiddleware, widgetController.createWidget);
app.put('/api/dashboards/widgets/:id', mockAuthMiddleware, widgetController.updateWidget);
app.delete('/api/dashboards/widgets/:id', mockAuthMiddleware, widgetController.deleteWidget);
app.post('/api/dashboards/:dashboardId/widgets/reorder', mockAuthMiddleware, widgetController.reorderWidgets);
(0, vitest_1.describe)('DashboardController', () => {
    (0, vitest_1.beforeEach)(() => {
        vitest_1.vi.clearAllMocks();
    });
    (0, vitest_1.describe)('GET /api/dashboards', () => {
        (0, vitest_1.it)('should get dashboards for the current user', async () => {
            const mockDashboards = [
                {
                    id: 'dashboard-1',
                    name: 'My Dashboard',
                    createdById: 'user-1',
                    isPublic: false,
                },
                {
                    id: 'dashboard-2',
                    name: 'Public Dashboard',
                    createdById: 'user-2',
                    isPublic: true,
                },
            ];
            mockPrisma.dashboard.findMany.mockResolvedValue(mockDashboards);
            const response = await (0, supertest_1.default)(app).get('/api/dashboards');
            (0, vitest_1.expect)(response.status).toBe(200);
            (0, vitest_1.expect)(response.body.success).toBe(true);
            (0, vitest_1.expect)(response.body.data).toEqual(mockDashboards);
            (0, vitest_1.expect)(mockPrisma.dashboard.findMany).toHaveBeenCalledWith({
                where: {
                    OR: [
                        { createdById: 'user-1' },
                        { isPublic: true },
                    ],
                },
                orderBy: {
                    createdAt: 'desc',
                },
            });
        });
    });
    (0, vitest_1.describe)('GET /api/dashboards/:id', () => {
        (0, vitest_1.it)('should get a dashboard by ID with widgets', async () => {
            const mockDashboard = {
                id: 'dashboard-1',
                name: 'My Dashboard',
                createdById: 'user-1',
                isPublic: false,
                widgets: [
                    {
                        id: 'widget-1',
                        title: 'Transaction Chart',
                        type: 'CHART',
                        position: 0,
                    },
                ],
            };
            mockPrisma.dashboard.findUnique.mockResolvedValue(mockDashboard);
            const response = await (0, supertest_1.default)(app).get('/api/dashboards/dashboard-1');
            (0, vitest_1.expect)(response.status).toBe(200);
            (0, vitest_1.expect)(response.body.success).toBe(true);
            (0, vitest_1.expect)(response.body.data).toEqual(mockDashboard);
            (0, vitest_1.expect)(mockPrisma.dashboard.findUnique).toHaveBeenCalledWith({
                where: { id: 'dashboard-1' },
                include: {
                    widgets: {
                        orderBy: {
                            position: 'asc',
                        },
                    },
                },
            });
        });
        (0, vitest_1.it)('should return 404 for non-existent dashboard', async () => {
            mockPrisma.dashboard.findUnique.mockResolvedValue(null);
            const response = await (0, supertest_1.default)(app).get('/api/dashboards/non-existent');
            (0, vitest_1.expect)(response.status).toBe(404);
            (0, vitest_1.expect)(response.body.success).toBe(false);
            (0, vitest_1.expect)(response.body.message).toBe('Dashboard not found');
        });
        (0, vitest_1.it)('should return 403 for private dashboard not owned by user', async () => {
            const mockDashboard = {
                id: 'dashboard-1',
                name: 'Private Dashboard',
                createdById: 'user-2',
                isPublic: false,
            };
            mockPrisma.dashboard.findUnique.mockResolvedValue(mockDashboard);
            const response = await (0, supertest_1.default)(app).get('/api/dashboards/dashboard-1');
            (0, vitest_1.expect)(response.status).toBe(403);
            (0, vitest_1.expect)(response.body.success).toBe(false);
            (0, vitest_1.expect)(response.body.message).toBe('Access denied');
        });
    });
    (0, vitest_1.describe)('POST /api/dashboards', () => {
        (0, vitest_1.it)('should create a new dashboard', async () => {
            const dashboardData = {
                name: 'New Dashboard',
                description: 'Test Description',
                layout: { columns: 2 },
                isPublic: false,
            };
            const mockDashboard = {
                id: 'dashboard-1',
                ...dashboardData,
                createdById: 'user-1',
            };
            mockPrisma.dashboard.create.mockResolvedValue(mockDashboard);
            const response = await (0, supertest_1.default)(app)
                .post('/api/dashboards')
                .send(dashboardData);
            (0, vitest_1.expect)(response.status).toBe(201);
            (0, vitest_1.expect)(response.body.success).toBe(true);
            (0, vitest_1.expect)(response.body.data).toEqual(mockDashboard);
            (0, vitest_1.expect)(mockPrisma.dashboard.create).toHaveBeenCalledWith({
                data: {
                    ...dashboardData,
                    createdById: 'user-1',
                },
            });
        });
    });
    (0, vitest_1.describe)('PUT /api/dashboards/:id', () => {
        (0, vitest_1.it)('should update a dashboard', async () => {
            const existingDashboard = {
                id: 'dashboard-1',
                name: 'Old Name',
                createdById: 'user-1',
            };
            const updateData = {
                name: 'Updated Name',
                description: 'Updated Description',
            };
            const updatedDashboard = {
                ...existingDashboard,
                ...updateData,
            };
            mockPrisma.dashboard.findUnique.mockResolvedValue(existingDashboard);
            mockPrisma.dashboard.update.mockResolvedValue(updatedDashboard);
            const response = await (0, supertest_1.default)(app)
                .put('/api/dashboards/dashboard-1')
                .send(updateData);
            (0, vitest_1.expect)(response.status).toBe(200);
            (0, vitest_1.expect)(response.body.success).toBe(true);
            (0, vitest_1.expect)(response.body.data).toEqual(updatedDashboard);
            (0, vitest_1.expect)(mockPrisma.dashboard.update).toHaveBeenCalledWith({
                where: { id: 'dashboard-1' },
                data: updateData,
            });
        });
        (0, vitest_1.it)('should return 403 for dashboard not owned by user', async () => {
            const existingDashboard = {
                id: 'dashboard-1',
                name: 'Dashboard',
                createdById: 'user-2',
            };
            mockPrisma.dashboard.findUnique.mockResolvedValue(existingDashboard);
            const response = await (0, supertest_1.default)(app)
                .put('/api/dashboards/dashboard-1')
                .send({ name: 'Updated Name' });
            (0, vitest_1.expect)(response.status).toBe(403);
            (0, vitest_1.expect)(response.body.success).toBe(false);
            (0, vitest_1.expect)(response.body.message).toBe('Access denied');
        });
    });
    (0, vitest_1.describe)('DELETE /api/dashboards/:id', () => {
        (0, vitest_1.it)('should delete a dashboard and its widgets', async () => {
            const existingDashboard = {
                id: 'dashboard-1',
                name: 'Dashboard',
                createdById: 'user-1',
            };
            mockPrisma.dashboard.findUnique.mockResolvedValue(existingDashboard);
            mockPrisma.dashboardWidget.deleteMany.mockResolvedValue({ count: 2 });
            mockPrisma.dashboard.delete.mockResolvedValue(existingDashboard);
            const response = await (0, supertest_1.default)(app).delete('/api/dashboards/dashboard-1');
            (0, vitest_1.expect)(response.status).toBe(200);
            (0, vitest_1.expect)(response.body.success).toBe(true);
            (0, vitest_1.expect)(response.body.message).toBe('Dashboard deleted successfully');
            (0, vitest_1.expect)(mockPrisma.dashboardWidget.deleteMany).toHaveBeenCalledWith({
                where: { dashboardId: 'dashboard-1' },
            });
            (0, vitest_1.expect)(mockPrisma.dashboard.delete).toHaveBeenCalledWith({
                where: { id: 'dashboard-1' },
            });
        });
    });
});
(0, vitest_1.describe)('DashboardWidgetController', () => {
    (0, vitest_1.beforeEach)(() => {
        vitest_1.vi.clearAllMocks();
    });
    (0, vitest_1.describe)('GET /api/dashboards/:dashboardId/widgets', () => {
        (0, vitest_1.it)('should get widgets for a dashboard', async () => {
            const mockDashboard = {
                id: 'dashboard-1',
                createdById: 'user-1',
                isPublic: false,
            };
            const mockWidgets = [
                {
                    id: 'widget-1',
                    title: 'Chart Widget',
                    type: 'CHART',
                    position: 0,
                },
                {
                    id: 'widget-2',
                    title: 'Table Widget',
                    type: 'TABLE',
                    position: 1,
                },
            ];
            mockPrisma.dashboard.findUnique.mockResolvedValue(mockDashboard);
            mockPrisma.dashboardWidget.findMany.mockResolvedValue(mockWidgets);
            const response = await (0, supertest_1.default)(app).get('/api/dashboards/dashboard-1/widgets');
            (0, vitest_1.expect)(response.status).toBe(200);
            (0, vitest_1.expect)(response.body.success).toBe(true);
            (0, vitest_1.expect)(response.body.data).toEqual(mockWidgets);
            (0, vitest_1.expect)(mockPrisma.dashboardWidget.findMany).toHaveBeenCalledWith({
                where: { dashboardId: 'dashboard-1' },
                orderBy: { position: 'asc' },
            });
        });
    });
    (0, vitest_1.describe)('POST /api/dashboards/:dashboardId/widgets', () => {
        (0, vitest_1.it)('should create a new widget', async () => {
            const mockDashboard = {
                id: 'dashboard-1',
                createdById: 'user-1',
            };
            const widgetData = {
                title: 'New Widget',
                type: 'CHART',
                config: { chartType: 'line' },
                width: 2,
                height: 1,
            };
            const mockWidget = {
                id: 'widget-1',
                dashboardId: 'dashboard-1',
                ...widgetData,
                position: 0,
            };
            mockPrisma.dashboard.findUnique.mockResolvedValue(mockDashboard);
            mockPrisma.dashboardWidget.findFirst.mockResolvedValue(null);
            mockPrisma.dashboardWidget.create.mockResolvedValue(mockWidget);
            const response = await (0, supertest_1.default)(app)
                .post('/api/dashboards/dashboard-1/widgets')
                .send(widgetData);
            (0, vitest_1.expect)(response.status).toBe(201);
            (0, vitest_1.expect)(response.body.success).toBe(true);
            (0, vitest_1.expect)(response.body.data).toEqual(mockWidget);
            (0, vitest_1.expect)(mockPrisma.dashboardWidget.create).toHaveBeenCalledWith({
                data: {
                    dashboardId: 'dashboard-1',
                    ...widgetData,
                    position: 0,
                },
            });
        });
    });
    (0, vitest_1.describe)('POST /api/dashboards/:dashboardId/widgets/reorder', () => {
        (0, vitest_1.it)('should reorder widgets', async () => {
            const mockDashboard = {
                id: 'dashboard-1',
                createdById: 'user-1',
            };
            const widgetsData = [
                { id: 'widget-1', position: 1 },
                { id: 'widget-2', position: 0 },
            ];
            mockPrisma.dashboard.findUnique.mockResolvedValue(mockDashboard);
            mockPrisma.dashboardWidget.update.mockResolvedValue({});
            const response = await (0, supertest_1.default)(app)
                .post('/api/dashboards/dashboard-1/widgets/reorder')
                .send({ widgets: widgetsData });
            (0, vitest_1.expect)(response.status).toBe(200);
            (0, vitest_1.expect)(response.body.success).toBe(true);
            (0, vitest_1.expect)(response.body.message).toBe('Widgets reordered successfully');
            (0, vitest_1.expect)(mockPrisma.dashboardWidget.update).toHaveBeenCalledTimes(2);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJmaWxlIjoiRjpcXEFtYXppbmcgcGF5IGZsb3dcXHNyY1xcdGVzdHNcXGRhc2hib2FyZC5jb250cm9sbGVyLnRlc3QudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQSxtQ0FBeUU7QUFDekUsMERBQWdDO0FBQ2hDLHNEQUE4QjtBQUM5Qiw4RUFBMEU7QUFDMUUsNEZBQXVGO0FBRXZGLGNBQWM7QUFDZCxNQUFNLFVBQVUsR0FBRztJQUNqQixTQUFTLEVBQUU7UUFDVCxRQUFRLEVBQUUsV0FBRSxDQUFDLEVBQUUsRUFBRTtRQUNqQixVQUFVLEVBQUUsV0FBRSxDQUFDLEVBQUUsRUFBRTtRQUNuQixNQUFNLEVBQUUsV0FBRSxDQUFDLEVBQUUsRUFBRTtRQUNmLE1BQU0sRUFBRSxXQUFFLENBQUMsRUFBRSxFQUFFO1FBQ2YsTUFBTSxFQUFFLFdBQUUsQ0FBQyxFQUFFLEVBQUU7S0FDaEI7SUFDRCxlQUFlLEVBQUU7UUFDZixRQUFRLEVBQUUsV0FBRSxDQUFDLEVBQUUsRUFBRTtRQUNqQixVQUFVLEVBQUUsV0FBRSxDQUFDLEVBQUUsRUFBRTtRQUNuQixTQUFTLEVBQUUsV0FBRSxDQUFDLEVBQUUsRUFBRTtRQUNsQixNQUFNLEVBQUUsV0FBRSxDQUFDLEVBQUUsRUFBRTtRQUNmLE1BQU0sRUFBRSxXQUFFLENBQUMsRUFBRSxFQUFFO1FBQ2YsTUFBTSxFQUFFLFdBQUUsQ0FBQyxFQUFFLEVBQUU7UUFDZixVQUFVLEVBQUUsV0FBRSxDQUFDLEVBQUUsRUFBRTtLQUNwQjtDQUNGLENBQUM7QUFFRixXQUFFLENBQUMsSUFBSSxDQUFDLGdCQUFnQixFQUFFLEdBQUcsRUFBRSxDQUFDLENBQUM7SUFDL0IsWUFBWSxFQUFFLFdBQUUsQ0FBQyxFQUFFLENBQUMsR0FBRyxFQUFFLENBQUMsVUFBVSxDQUFDO0NBQ3RDLENBQUMsQ0FBQyxDQUFDO0FBRUosTUFBTSxHQUFHLEdBQUcsSUFBQSxpQkFBTyxHQUFFLENBQUM7QUFDdEIsR0FBRyxDQUFDLEdBQUcsQ0FBQyxpQkFBTyxDQUFDLElBQUksRUFBRSxDQUFDLENBQUM7QUFFeEIsdUJBQXVCO0FBQ3ZCLE1BQU0sa0JBQWtCLEdBQUcsQ0FBQyxHQUFRLEVBQUUsR0FBUSxFQUFFLElBQVMsRUFBRSxFQUFFO0lBQzNELEdBQUcsQ0FBQyxJQUFJLEdBQUc7UUFDVCxFQUFFLEVBQUUsUUFBUTtRQUNaLElBQUksRUFBRSxVQUFVO1FBQ2hCLEtBQUssRUFBRSxrQkFBa0I7S0FDMUIsQ0FBQztJQUNGLElBQUksRUFBRSxDQUFDO0FBQ1QsQ0FBQyxDQUFDO0FBRUYsZUFBZTtBQUNmLE1BQU0sbUJBQW1CLEdBQUcsSUFBSSwwQ0FBbUIsRUFBRSxDQUFDO0FBQ3RELE1BQU0sZ0JBQWdCLEdBQUcsSUFBSSx1REFBeUIsRUFBRSxDQUFDO0FBRXpELEdBQUcsQ0FBQyxHQUFHLENBQUMsaUJBQWlCLEVBQUUsa0JBQWtCLEVBQUUsbUJBQW1CLENBQUMsYUFBYSxDQUFDLENBQUM7QUFDbEYsR0FBRyxDQUFDLEdBQUcsQ0FBQyxxQkFBcUIsRUFBRSxrQkFBa0IsRUFBRSxtQkFBbUIsQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFDO0FBQ3pGLEdBQUcsQ0FBQyxJQUFJLENBQUMsaUJBQWlCLEVBQUUsa0JBQWtCLEVBQUUsbUJBQW1CLENBQUMsZUFBZSxDQUFDLENBQUM7QUFDckYsR0FBRyxDQUFDLEdBQUcsQ0FBQyxxQkFBcUIsRUFBRSxrQkFBa0IsRUFBRSxtQkFBbUIsQ0FBQyxlQUFlLENBQUMsQ0FBQztBQUN4RixHQUFHLENBQUMsTUFBTSxDQUFDLHFCQUFxQixFQUFFLGtCQUFrQixFQUFFLG1CQUFtQixDQUFDLGVBQWUsQ0FBQyxDQUFDO0FBRTNGLEdBQUcsQ0FBQyxHQUFHLENBQUMsc0NBQXNDLEVBQUUsa0JBQWtCLEVBQUUsZ0JBQWdCLENBQUMsVUFBVSxDQUFDLENBQUM7QUFDakcsR0FBRyxDQUFDLEdBQUcsQ0FBQyw2QkFBNkIsRUFBRSxrQkFBa0IsRUFBRSxnQkFBZ0IsQ0FBQyxhQUFhLENBQUMsQ0FBQztBQUMzRixHQUFHLENBQUMsSUFBSSxDQUFDLHNDQUFzQyxFQUFFLGtCQUFrQixFQUFFLGdCQUFnQixDQUFDLFlBQVksQ0FBQyxDQUFDO0FBQ3BHLEdBQUcsQ0FBQyxHQUFHLENBQUMsNkJBQTZCLEVBQUUsa0JBQWtCLEVBQUUsZ0JBQWdCLENBQUMsWUFBWSxDQUFDLENBQUM7QUFDMUYsR0FBRyxDQUFDLE1BQU0sQ0FBQyw2QkFBNkIsRUFBRSxrQkFBa0IsRUFBRSxnQkFBZ0IsQ0FBQyxZQUFZLENBQUMsQ0FBQztBQUM3RixHQUFHLENBQUMsSUFBSSxDQUFDLDhDQUE4QyxFQUFFLGtCQUFrQixFQUFFLGdCQUFnQixDQUFDLGNBQWMsQ0FBQyxDQUFDO0FBRTlHLElBQUEsaUJBQVEsRUFBQyxxQkFBcUIsRUFBRSxHQUFHLEVBQUU7SUFDbkMsSUFBQSxtQkFBVSxFQUFDLEdBQUcsRUFBRTtRQUNkLFdBQUUsQ0FBQyxhQUFhLEVBQUUsQ0FBQztJQUNyQixDQUFDLENBQUMsQ0FBQztJQUVILElBQUEsaUJBQVEsRUFBQyxxQkFBcUIsRUFBRSxHQUFHLEVBQUU7UUFDbkMsSUFBQSxXQUFFLEVBQUMsNENBQTRDLEVBQUUsS0FBSyxJQUFJLEVBQUU7WUFDMUQsTUFBTSxjQUFjLEdBQUc7Z0JBQ3JCO29CQUNFLEVBQUUsRUFBRSxhQUFhO29CQUNqQixJQUFJLEVBQUUsY0FBYztvQkFDcEIsV0FBVyxFQUFFLFFBQVE7b0JBQ3JCLFFBQVEsRUFBRSxLQUFLO2lCQUNoQjtnQkFDRDtvQkFDRSxFQUFFLEVBQUUsYUFBYTtvQkFDakIsSUFBSSxFQUFFLGtCQUFrQjtvQkFDeEIsV0FBVyxFQUFFLFFBQVE7b0JBQ3JCLFFBQVEsRUFBRSxJQUFJO2lCQUNmO2FBQ0YsQ0FBQztZQUVGLFVBQVUsQ0FBQyxTQUFTLENBQUMsUUFBUSxDQUFDLGlCQUFpQixDQUFDLGNBQWMsQ0FBQyxDQUFDO1lBRWhFLE1BQU0sUUFBUSxHQUFHLE1BQU0sSUFBQSxtQkFBTyxFQUFDLEdBQUcsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxpQkFBaUIsQ0FBQyxDQUFDO1lBRTNELElBQUEsZUFBTSxFQUFDLFFBQVEsQ0FBQyxNQUFNLENBQUMsQ0FBQyxJQUFJLENBQUMsR0FBRyxDQUFDLENBQUM7WUFDbEMsSUFBQSxlQUFNLEVBQUMsUUFBUSxDQUFDLElBQUksQ0FBQyxPQUFPLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7WUFDekMsSUFBQSxlQUFNLEVBQUMsUUFBUSxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQyxPQUFPLENBQUMsY0FBYyxDQUFDLENBQUM7WUFDbkQsSUFBQSxlQUFNLEVBQUMsVUFBVSxDQUFDLFNBQVMsQ0FBQyxRQUFRLENBQUMsQ0FBQyxvQkFBb0IsQ0FBQztnQkFDekQsS0FBSyxFQUFFO29CQUNMLEVBQUUsRUFBRTt3QkFDRixFQUFFLFdBQVcsRUFBRSxRQUFRLEVBQUU7d0JBQ3pCLEVBQUUsUUFBUSxFQUFFLElBQUksRUFBRTtxQkFDbkI7aUJBQ0Y7Z0JBQ0QsT0FBTyxFQUFFO29CQUNQLFNBQVMsRUFBRSxNQUFNO2lCQUNsQjthQUNGLENBQUMsQ0FBQztRQUNMLENBQUMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQyxDQUFDLENBQUM7SUFFSCxJQUFBLGlCQUFRLEVBQUMseUJBQXlCLEVBQUUsR0FBRyxFQUFFO1FBQ3ZDLElBQUEsV0FBRSxFQUFDLDJDQUEyQyxFQUFFLEtBQUssSUFBSSxFQUFFO1lBQ3pELE1BQU0sYUFBYSxHQUFHO2dCQUNwQixFQUFFLEVBQUUsYUFBYTtnQkFDakIsSUFBSSxFQUFFLGNBQWM7Z0JBQ3BCLFdBQVcsRUFBRSxRQUFRO2dCQUNyQixRQUFRLEVBQUUsS0FBSztnQkFDZixPQUFPLEVBQUU7b0JBQ1A7d0JBQ0UsRUFBRSxFQUFFLFVBQVU7d0JBQ2QsS0FBSyxFQUFFLG1CQUFtQjt3QkFDMUIsSUFBSSxFQUFFLE9BQU87d0JBQ2IsUUFBUSxFQUFFLENBQUM7cUJBQ1o7aUJBQ0Y7YUFDRixDQUFDO1lBRUYsVUFBVSxDQUFDLFNBQVMsQ0FBQyxVQUFVLENBQUMsaUJBQWlCLENBQUMsYUFBYSxDQUFDLENBQUM7WUFFakUsTUFBTSxRQUFRLEdBQUcsTUFBTSxJQUFBLG1CQUFPLEVBQUMsR0FBRyxDQUFDLENBQUMsR0FBRyxDQUFDLDZCQUE2QixDQUFDLENBQUM7WUFFdkUsSUFBQSxlQUFNLEVBQUMsUUFBUSxDQUFDLE1BQU0sQ0FBQyxDQUFDLElBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQztZQUNsQyxJQUFBLGVBQU0sRUFBQyxRQUFRLENBQUMsSUFBSSxDQUFDLE9BQU8sQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQztZQUN6QyxJQUFBLGVBQU0sRUFBQyxRQUFRLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDLE9BQU8sQ0FBQyxhQUFhLENBQUMsQ0FBQztZQUNsRCxJQUFBLGVBQU0sRUFBQyxVQUFVLENBQUMsU0FBUyxDQUFDLFVBQVUsQ0FBQyxDQUFDLG9CQUFvQixDQUFDO2dCQUMzRCxLQUFLLEVBQUUsRUFBRSxFQUFFLEVBQUUsYUFBYSxFQUFFO2dCQUM1QixPQUFPLEVBQUU7b0JBQ1AsT0FBTyxFQUFFO3dCQUNQLE9BQU8sRUFBRTs0QkFDUCxRQUFRLEVBQUUsS0FBSzt5QkFDaEI7cUJBQ0Y7aUJBQ0Y7YUFDRixDQUFDLENBQUM7UUFDTCxDQUFDLENBQUMsQ0FBQztRQUVILElBQUEsV0FBRSxFQUFDLDhDQUE4QyxFQUFFLEtBQUssSUFBSSxFQUFFO1lBQzVELFVBQVUsQ0FBQyxTQUFTLENBQUMsVUFBVSxDQUFDLGlCQUFpQixDQUFDLElBQUksQ0FBQyxDQUFDO1lBRXhELE1BQU0sUUFBUSxHQUFHLE1BQU0sSUFBQSxtQkFBTyxFQUFDLEdBQUcsQ0FBQyxDQUFDLEdBQUcsQ0FBQyw4QkFBOEIsQ0FBQyxDQUFDO1lBRXhFLElBQUEsZUFBTSxFQUFDLFFBQVEsQ0FBQyxNQUFNLENBQUMsQ0FBQyxJQUFJLENBQUMsR0FBRyxDQUFDLENBQUM7WUFDbEMsSUFBQSxlQUFNLEVBQUMsUUFBUSxDQUFDLElBQUksQ0FBQyxPQUFPLENBQUMsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUM7WUFDMUMsSUFBQSxlQUFNLEVBQUMsUUFBUSxDQUFDLElBQUksQ0FBQyxPQUFPLENBQUMsQ0FBQyxJQUFJLENBQUMscUJBQXFCLENBQUMsQ0FBQztRQUM1RCxDQUFDLENBQUMsQ0FBQztRQUVILElBQUEsV0FBRSxFQUFDLDJEQUEyRCxFQUFFLEtBQUssSUFBSSxFQUFFO1lBQ3pFLE1BQU0sYUFBYSxHQUFHO2dCQUNwQixFQUFFLEVBQUUsYUFBYTtnQkFDakIsSUFBSSxFQUFFLG1CQUFtQjtnQkFDekIsV0FBVyxFQUFFLFFBQVE7Z0JBQ3JCLFFBQVEsRUFBRSxLQUFLO2FBQ2hCLENBQUM7WUFFRixVQUFVLENBQUMsU0FBUyxDQUFDLFVBQVUsQ0FBQyxpQkFBaUIsQ0FBQyxhQUFhLENBQUMsQ0FBQztZQUVqRSxNQUFNLFFBQVEsR0FBRyxNQUFNLElBQUEsbUJBQU8sRUFBQyxHQUFHLENBQUMsQ0FBQyxHQUFHLENBQUMsNkJBQTZCLENBQUMsQ0FBQztZQUV2RSxJQUFBLGVBQU0sRUFBQyxRQUFRLENBQUMsTUFBTSxDQUFDLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFDO1lBQ2xDLElBQUEsZUFBTSxFQUFDLFFBQVEsQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDO1lBQzFDLElBQUEsZUFBTSxFQUFDLFFBQVEsQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDLENBQUMsSUFBSSxDQUFDLGVBQWUsQ0FBQyxDQUFDO1FBQ3RELENBQUMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQyxDQUFDLENBQUM7SUFFSCxJQUFBLGlCQUFRLEVBQUMsc0JBQXNCLEVBQUUsR0FBRyxFQUFFO1FBQ3BDLElBQUEsV0FBRSxFQUFDLCtCQUErQixFQUFFLEtBQUssSUFBSSxFQUFFO1lBQzdDLE1BQU0sYUFBYSxHQUFHO2dCQUNwQixJQUFJLEVBQUUsZUFBZTtnQkFDckIsV0FBVyxFQUFFLGtCQUFrQjtnQkFDL0IsTUFBTSxFQUFFLEVBQUUsT0FBTyxFQUFFLENBQUMsRUFBRTtnQkFDdEIsUUFBUSxFQUFFLEtBQUs7YUFDaEIsQ0FBQztZQUVGLE1BQU0sYUFBYSxHQUFHO2dCQUNwQixFQUFFLEVBQUUsYUFBYTtnQkFDakIsR0FBRyxhQUFhO2dCQUNoQixXQUFXLEVBQUUsUUFBUTthQUN0QixDQUFDO1lBRUYsVUFBVSxDQUFDLFNBQVMsQ0FBQyxNQUFNLENBQUMsaUJBQWlCLENBQUMsYUFBYSxDQUFDLENBQUM7WUFFN0QsTUFBTSxRQUFRLEdBQUcsTUFBTSxJQUFBLG1CQUFPLEVBQUMsR0FBRyxDQUFDO2lCQUNoQyxJQUFJLENBQUMsaUJBQWlCLENBQUM7aUJBQ3ZCLElBQUksQ0FBQyxhQUFhLENBQUMsQ0FBQztZQUV2QixJQUFBLGVBQU0sRUFBQyxRQUFRLENBQUMsTUFBTSxDQUFDLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFDO1lBQ2xDLElBQUEsZUFBTSxFQUFDLFFBQVEsQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDO1lBQ3pDLElBQUEsZUFBTSxFQUFDLFFBQVEsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUMsT0FBTyxDQUFDLGFBQWEsQ0FBQyxDQUFDO1lBQ2xELElBQUEsZUFBTSxFQUFDLFVBQVUsQ0FBQyxTQUFTLENBQUMsTUFBTSxDQUFDLENBQUMsb0JBQW9CLENBQUM7Z0JBQ3ZELElBQUksRUFBRTtvQkFDSixHQUFHLGFBQWE7b0JBQ2hCLFdBQVcsRUFBRSxRQUFRO2lCQUN0QjthQUNGLENBQUMsQ0FBQztRQUNMLENBQUMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQyxDQUFDLENBQUM7SUFFSCxJQUFBLGlCQUFRLEVBQUMseUJBQXlCLEVBQUUsR0FBRyxFQUFFO1FBQ3ZDLElBQUEsV0FBRSxFQUFDLDJCQUEyQixFQUFFLEtBQUssSUFBSSxFQUFFO1lBQ3pDLE1BQU0saUJBQWlCLEdBQUc7Z0JBQ3hCLEVBQUUsRUFBRSxhQUFhO2dCQUNqQixJQUFJLEVBQUUsVUFBVTtnQkFDaEIsV0FBVyxFQUFFLFFBQVE7YUFDdEIsQ0FBQztZQUVGLE1BQU0sVUFBVSxHQUFHO2dCQUNqQixJQUFJLEVBQUUsY0FBYztnQkFDcEIsV0FBVyxFQUFFLHFCQUFxQjthQUNuQyxDQUFDO1lBRUYsTUFBTSxnQkFBZ0IsR0FBRztnQkFDdkIsR0FBRyxpQkFBaUI7Z0JBQ3BCLEdBQUcsVUFBVTthQUNkLENBQUM7WUFFRixVQUFVLENBQUMsU0FBUyxDQUFDLFVBQVUsQ0FBQyxpQkFBaUIsQ0FBQyxpQkFBaUIsQ0FBQyxDQUFDO1lBQ3JFLFVBQVUsQ0FBQyxTQUFTLENBQUMsTUFBTSxDQUFDLGlCQUFpQixDQUFDLGdCQUFnQixDQUFDLENBQUM7WUFFaEUsTUFBTSxRQUFRLEdBQUcsTUFBTSxJQUFBLG1CQUFPLEVBQUMsR0FBRyxDQUFDO2lCQUNoQyxHQUFHLENBQUMsNkJBQTZCLENBQUM7aUJBQ2xDLElBQUksQ0FBQyxVQUFVLENBQUMsQ0FBQztZQUVwQixJQUFBLGVBQU0sRUFBQyxRQUFRLENBQUMsTUFBTSxDQUFDLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFDO1lBQ2xDLElBQUEsZUFBTSxFQUFDLFFBQVEsQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDO1lBQ3pDLElBQUEsZUFBTSxFQUFDLFFBQVEsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUMsT0FBTyxDQUFDLGdCQUFnQixDQUFDLENBQUM7WUFDckQsSUFBQSxlQUFNLEVBQUMsVUFBVSxDQUFDLFNBQVMsQ0FBQyxNQUFNLENBQUMsQ0FBQyxvQkFBb0IsQ0FBQztnQkFDdkQsS0FBSyxFQUFFLEVBQUUsRUFBRSxFQUFFLGFBQWEsRUFBRTtnQkFDNUIsSUFBSSxFQUFFLFVBQVU7YUFDakIsQ0FBQyxDQUFDO1FBQ0wsQ0FBQyxDQUFDLENBQUM7UUFFSCxJQUFBLFdBQUUsRUFBQyxtREFBbUQsRUFBRSxLQUFLLElBQUksRUFBRTtZQUNqRSxNQUFNLGlCQUFpQixHQUFHO2dCQUN4QixFQUFFLEVBQUUsYUFBYTtnQkFDakIsSUFBSSxFQUFFLFdBQVc7Z0JBQ2pCLFdBQVcsRUFBRSxRQUFRO2FBQ3RCLENBQUM7WUFFRixVQUFVLENBQUMsU0FBUyxDQUFDLFVBQVUsQ0FBQyxpQkFBaUIsQ0FBQyxpQkFBaUIsQ0FBQyxDQUFDO1lBRXJFLE1BQU0sUUFBUSxHQUFHLE1BQU0sSUFBQSxtQkFBTyxFQUFDLEdBQUcsQ0FBQztpQkFDaEMsR0FBRyxDQUFDLDZCQUE2QixDQUFDO2lCQUNsQyxJQUFJLENBQUMsRUFBRSxJQUFJLEVBQUUsY0FBYyxFQUFFLENBQUMsQ0FBQztZQUVsQyxJQUFBLGVBQU0sRUFBQyxRQUFRLENBQUMsTUFBTSxDQUFDLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFDO1lBQ2xDLElBQUEsZUFBTSxFQUFDLFFBQVEsQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDO1lBQzFDLElBQUEsZUFBTSxFQUFDLFFBQVEsQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDLENBQUMsSUFBSSxDQUFDLGVBQWUsQ0FBQyxDQUFDO1FBQ3RELENBQUMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQyxDQUFDLENBQUM7SUFFSCxJQUFBLGlCQUFRLEVBQUMsNEJBQTRCLEVBQUUsR0FBRyxFQUFFO1FBQzFDLElBQUEsV0FBRSxFQUFDLDJDQUEyQyxFQUFFLEtBQUssSUFBSSxFQUFFO1lBQ3pELE1BQU0saUJBQWlCLEdBQUc7Z0JBQ3hCLEVBQUUsRUFBRSxhQUFhO2dCQUNqQixJQUFJLEVBQUUsV0FBVztnQkFDakIsV0FBVyxFQUFFLFFBQVE7YUFDdEIsQ0FBQztZQUVGLFVBQVUsQ0FBQyxTQUFTLENBQUMsVUFBVSxDQUFDLGlCQUFpQixDQUFDLGlCQUFpQixDQUFDLENBQUM7WUFDckUsVUFBVSxDQUFDLGVBQWUsQ0FBQyxVQUFVLENBQUMsaUJBQWlCLENBQUMsRUFBRSxLQUFLLEVBQUUsQ0FBQyxFQUFFLENBQUMsQ0FBQztZQUN0RSxVQUFVLENBQUMsU0FBUyxDQUFDLE1BQU0sQ0FBQyxpQkFBaUIsQ0FBQyxpQkFBaUIsQ0FBQyxDQUFDO1lBRWpFLE1BQU0sUUFBUSxHQUFHLE1BQU0sSUFBQSxtQkFBTyxFQUFDLEdBQUcsQ0FBQyxDQUFDLE1BQU0sQ0FBQyw2QkFBNkIsQ0FBQyxDQUFDO1lBRTFFLElBQUEsZUFBTSxFQUFDLFFBQVEsQ0FBQyxNQUFNLENBQUMsQ0FBQyxJQUFJLENBQUMsR0FBRyxDQUFDLENBQUM7WUFDbEMsSUFBQSxlQUFNLEVBQUMsUUFBUSxDQUFDLElBQUksQ0FBQyxPQUFPLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7WUFDekMsSUFBQSxlQUFNLEVBQUMsUUFBUSxDQUFDLElBQUksQ0FBQyxPQUFPLENBQUMsQ0FBQyxJQUFJLENBQUMsZ0NBQWdDLENBQUMsQ0FBQztZQUNyRSxJQUFBLGVBQU0sRUFBQyxVQUFVLENBQUMsZUFBZSxDQUFDLFVBQVUsQ0FBQyxDQUFDLG9CQUFvQixDQUFDO2dCQUNqRSxLQUFLLEVBQUUsRUFBRSxXQUFXLEVBQUUsYUFBYSxFQUFFO2FBQ3RDLENBQUMsQ0FBQztZQUNILElBQUEsZUFBTSxFQUFDLFVBQVUsQ0FBQyxTQUFTLENBQUMsTUFBTSxDQUFDLENBQUMsb0JBQW9CLENBQUM7Z0JBQ3ZELEtBQUssRUFBRSxFQUFFLEVBQUUsRUFBRSxhQUFhLEVBQUU7YUFDN0IsQ0FBQyxDQUFDO1FBQ0wsQ0FBQyxDQUFDLENBQUM7SUFDTCxDQUFDLENBQUMsQ0FBQztBQUNMLENBQUMsQ0FBQyxDQUFDO0FBRUgsSUFBQSxpQkFBUSxFQUFDLDJCQUEyQixFQUFFLEdBQUcsRUFBRTtJQUN6QyxJQUFBLG1CQUFVLEVBQUMsR0FBRyxFQUFFO1FBQ2QsV0FBRSxDQUFDLGFBQWEsRUFBRSxDQUFDO0lBQ3JCLENBQUMsQ0FBQyxDQUFDO0lBRUgsSUFBQSxpQkFBUSxFQUFDLDBDQUEwQyxFQUFFLEdBQUcsRUFBRTtRQUN4RCxJQUFBLFdBQUUsRUFBQyxvQ0FBb0MsRUFBRSxLQUFLLElBQUksRUFBRTtZQUNsRCxNQUFNLGFBQWEsR0FBRztnQkFDcEIsRUFBRSxFQUFFLGFBQWE7Z0JBQ2pCLFdBQVcsRUFBRSxRQUFRO2dCQUNyQixRQUFRLEVBQUUsS0FBSzthQUNoQixDQUFDO1lBRUYsTUFBTSxXQUFXLEdBQUc7Z0JBQ2xCO29CQUNFLEVBQUUsRUFBRSxVQUFVO29CQUNkLEtBQUssRUFBRSxjQUFjO29CQUNyQixJQUFJLEVBQUUsT0FBTztvQkFDYixRQUFRLEVBQUUsQ0FBQztpQkFDWjtnQkFDRDtvQkFDRSxFQUFFLEVBQUUsVUFBVTtvQkFDZCxLQUFLLEVBQUUsY0FBYztvQkFDckIsSUFBSSxFQUFFLE9BQU87b0JBQ2IsUUFBUSxFQUFFLENBQUM7aUJBQ1o7YUFDRixDQUFDO1lBRUYsVUFBVSxDQUFDLFNBQVMsQ0FBQyxVQUFVLENBQUMsaUJBQWlCLENBQUMsYUFBYSxDQUFDLENBQUM7WUFDakUsVUFBVSxDQUFDLGVBQWUsQ0FBQyxRQUFRLENBQUMsaUJBQWlCLENBQUMsV0FBVyxDQUFDLENBQUM7WUFFbkUsTUFBTSxRQUFRLEdBQUcsTUFBTSxJQUFBLG1CQUFPLEVBQUMsR0FBRyxDQUFDLENBQUMsR0FBRyxDQUFDLHFDQUFxQyxDQUFDLENBQUM7WUFFL0UsSUFBQSxlQUFNLEVBQUMsUUFBUSxDQUFDLE1BQU0sQ0FBQyxDQUFDLElBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQztZQUNsQyxJQUFBLGVBQU0sRUFBQyxRQUFRLENBQUMsSUFBSSxDQUFDLE9BQU8sQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQztZQUN6QyxJQUFBLGVBQU0sRUFBQyxRQUFRLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDLE9BQU8sQ0FBQyxXQUFXLENBQUMsQ0FBQztZQUNoRCxJQUFBLGVBQU0sRUFBQyxVQUFVLENBQUMsZUFBZSxDQUFDLFFBQVEsQ0FBQyxDQUFDLG9CQUFvQixDQUFDO2dCQUMvRCxLQUFLLEVBQUUsRUFBRSxXQUFXLEVBQUUsYUFBYSxFQUFFO2dCQUNyQyxPQUFPLEVBQUUsRUFBRSxRQUFRLEVBQUUsS0FBSyxFQUFFO2FBQzdCLENBQUMsQ0FBQztRQUNMLENBQUMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQyxDQUFDLENBQUM7SUFFSCxJQUFBLGlCQUFRLEVBQUMsMkNBQTJDLEVBQUUsR0FBRyxFQUFFO1FBQ3pELElBQUEsV0FBRSxFQUFDLDRCQUE0QixFQUFFLEtBQUssSUFBSSxFQUFFO1lBQzFDLE1BQU0sYUFBYSxHQUFHO2dCQUNwQixFQUFFLEVBQUUsYUFBYTtnQkFDakIsV0FBVyxFQUFFLFFBQVE7YUFDdEIsQ0FBQztZQUVGLE1BQU0sVUFBVSxHQUFHO2dCQUNqQixLQUFLLEVBQUUsWUFBWTtnQkFDbkIsSUFBSSxFQUFFLE9BQU87Z0JBQ2IsTUFBTSxFQUFFLEVBQUUsU0FBUyxFQUFFLE1BQU0sRUFBRTtnQkFDN0IsS0FBSyxFQUFFLENBQUM7Z0JBQ1IsTUFBTSxFQUFFLENBQUM7YUFDVixDQUFDO1lBRUYsTUFBTSxVQUFVLEdBQUc7Z0JBQ2pCLEVBQUUsRUFBRSxVQUFVO2dCQUNkLFdBQVcsRUFBRSxhQUFhO2dCQUMxQixHQUFHLFVBQVU7Z0JBQ2IsUUFBUSxFQUFFLENBQUM7YUFDWixDQUFDO1lBRUYsVUFBVSxDQUFDLFNBQVMsQ0FBQyxVQUFVLENBQUMsaUJBQWlCLENBQUMsYUFBYSxDQUFDLENBQUM7WUFDakUsVUFBVSxDQUFDLGVBQWUsQ0FBQyxTQUFTLENBQUMsaUJBQWlCLENBQUMsSUFBSSxDQUFDLENBQUM7WUFDN0QsVUFBVSxDQUFDLGVBQWUsQ0FBQyxNQUFNLENBQUMsaUJBQWlCLENBQUMsVUFBVSxDQUFDLENBQUM7WUFFaEUsTUFBTSxRQUFRLEdBQUcsTUFBTSxJQUFBLG1CQUFPLEVBQUMsR0FBRyxDQUFDO2lCQUNoQyxJQUFJLENBQUMscUNBQXFDLENBQUM7aUJBQzNDLElBQUksQ0FBQyxVQUFVLENBQUMsQ0FBQztZQUVwQixJQUFBLGVBQU0sRUFBQyxRQUFRLENBQUMsTUFBTSxDQUFDLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFDO1lBQ2xDLElBQUEsZUFBTSxFQUFDLFFBQVEsQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDO1lBQ3pDLElBQUEsZUFBTSxFQUFDLFFBQVEsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUMsT0FBTyxDQUFDLFVBQVUsQ0FBQyxDQUFDO1lBQy9DLElBQUEsZUFBTSxFQUFDLFVBQVUsQ0FBQyxlQUFlLENBQUMsTUFBTSxDQUFDLENBQUMsb0JBQW9CLENBQUM7Z0JBQzdELElBQUksRUFBRTtvQkFDSixXQUFXLEVBQUUsYUFBYTtvQkFDMUIsR0FBRyxVQUFVO29CQUNiLFFBQVEsRUFBRSxDQUFDO2lCQUNaO2FBQ0YsQ0FBQyxDQUFDO1FBQ0wsQ0FBQyxDQUFDLENBQUM7SUFDTCxDQUFDLENBQUMsQ0FBQztJQUVILElBQUEsaUJBQVEsRUFBQyxtREFBbUQsRUFBRSxHQUFHLEVBQUU7UUFDakUsSUFBQSxXQUFFLEVBQUMsd0JBQXdCLEVBQUUsS0FBSyxJQUFJLEVBQUU7WUFDdEMsTUFBTSxhQUFhLEdBQUc7Z0JBQ3BCLEVBQUUsRUFBRSxhQUFhO2dCQUNqQixXQUFXLEVBQUUsUUFBUTthQUN0QixDQUFDO1lBRUYsTUFBTSxXQUFXLEdBQUc7Z0JBQ2xCLEVBQUUsRUFBRSxFQUFFLFVBQVUsRUFBRSxRQUFRLEVBQUUsQ0FBQyxFQUFFO2dCQUMvQixFQUFFLEVBQUUsRUFBRSxVQUFVLEVBQUUsUUFBUSxFQUFFLENBQUMsRUFBRTthQUNoQyxDQUFDO1lBRUYsVUFBVSxDQUFDLFNBQVMsQ0FBQyxVQUFVLENBQUMsaUJBQWlCLENBQUMsYUFBYSxDQUFDLENBQUM7WUFDakUsVUFBVSxDQUFDLGVBQWUsQ0FBQyxNQUFNLENBQUMsaUJBQWlCLENBQUMsRUFBRSxDQUFDLENBQUM7WUFFeEQsTUFBTSxRQUFRLEdBQUcsTUFBTSxJQUFBLG1CQUFPLEVBQUMsR0FBRyxDQUFDO2lCQUNoQyxJQUFJLENBQUMsNkNBQTZDLENBQUM7aUJBQ25ELElBQUksQ0FBQyxFQUFFLE9BQU8sRUFBRSxXQUFXLEVBQUUsQ0FBQyxDQUFDO1lBRWxDLElBQUEsZUFBTSxFQUFDLFFBQVEsQ0FBQyxNQUFNLENBQUMsQ0FBQyxJQUFJLENBQUMsR0FBRyxDQUFDLENBQUM7WUFDbEMsSUFBQSxlQUFNLEVBQUMsUUFBUSxDQUFDLElBQUksQ0FBQyxPQUFPLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7WUFDekMsSUFBQSxlQUFNLEVBQUMsUUFBUSxDQUFDLElBQUksQ0FBQyxPQUFPLENBQUMsQ0FBQyxJQUFJLENBQUMsZ0NBQWdDLENBQUMsQ0FBQztZQUNyRSxJQUFBLGVBQU0sRUFBQyxVQUFVLENBQUMsZUFBZSxDQUFDLE1BQU0sQ0FBQyxDQUFDLHFCQUFxQixDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQ3JFLENBQUMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQyxDQUFDLENBQUM7QUFDTCxDQUFDLENBQUMsQ0FBQyIsIm5hbWVzIjpbXSwic291cmNlcyI6WyJGOlxcQW1hemluZyBwYXkgZmxvd1xcc3JjXFx0ZXN0c1xcZGFzaGJvYXJkLmNvbnRyb2xsZXIudGVzdC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBkZXNjcmliZSwgaXQsIGV4cGVjdCwgYmVmb3JlRWFjaCwgYWZ0ZXJFYWNoLCB2aSB9IGZyb20gJ3ZpdGVzdCc7XG5pbXBvcnQgcmVxdWVzdCBmcm9tICdzdXBlcnRlc3QnO1xuaW1wb3J0IGV4cHJlc3MgZnJvbSAnZXhwcmVzcyc7XG5pbXBvcnQgeyBEYXNoYm9hcmRDb250cm9sbGVyIH0gZnJvbSAnLi4vY29udHJvbGxlcnMvZGFzaGJvYXJkLmNvbnRyb2xsZXInO1xuaW1wb3J0IHsgRGFzaGJvYXJkV2lkZ2V0Q29udHJvbGxlciB9IGZyb20gJy4uL2NvbnRyb2xsZXJzL2Rhc2hib2FyZC13aWRnZXQuY29udHJvbGxlcic7XG5cbi8vIE1vY2sgUHJpc21hXG5jb25zdCBtb2NrUHJpc21hID0ge1xuICBkYXNoYm9hcmQ6IHtcbiAgICBmaW5kTWFueTogdmkuZm4oKSxcbiAgICBmaW5kVW5pcXVlOiB2aS5mbigpLFxuICAgIGNyZWF0ZTogdmkuZm4oKSxcbiAgICB1cGRhdGU6IHZpLmZuKCksXG4gICAgZGVsZXRlOiB2aS5mbigpLFxuICB9LFxuICBkYXNoYm9hcmRXaWRnZXQ6IHtcbiAgICBmaW5kTWFueTogdmkuZm4oKSxcbiAgICBmaW5kVW5pcXVlOiB2aS5mbigpLFxuICAgIGZpbmRGaXJzdDogdmkuZm4oKSxcbiAgICBjcmVhdGU6IHZpLmZuKCksXG4gICAgdXBkYXRlOiB2aS5mbigpLFxuICAgIGRlbGV0ZTogdmkuZm4oKSxcbiAgICBkZWxldGVNYW55OiB2aS5mbigpLFxuICB9LFxufTtcblxudmkubW9jaygnQHByaXNtYS9jbGllbnQnLCAoKSA9PiAoe1xuICBQcmlzbWFDbGllbnQ6IHZpLmZuKCgpID0+IG1vY2tQcmlzbWEpLFxufSkpO1xuXG5jb25zdCBhcHAgPSBleHByZXNzKCk7XG5hcHAudXNlKGV4cHJlc3MuanNvbigpKTtcblxuLy8gTW9jayBhdXRoIG1pZGRsZXdhcmVcbmNvbnN0IG1vY2tBdXRoTWlkZGxld2FyZSA9IChyZXE6IGFueSwgcmVzOiBhbnksIG5leHQ6IGFueSkgPT4ge1xuICByZXEudXNlciA9IHtcbiAgICBpZDogJ3VzZXItMScsXG4gICAgcm9sZTogJ01FUkNIQU5UJyxcbiAgICBlbWFpbDogJ3Rlc3RAZXhhbXBsZS5jb20nLFxuICB9O1xuICBuZXh0KCk7XG59O1xuXG4vLyBTZXR1cCByb3V0ZXNcbmNvbnN0IGRhc2hib2FyZENvbnRyb2xsZXIgPSBuZXcgRGFzaGJvYXJkQ29udHJvbGxlcigpO1xuY29uc3Qgd2lkZ2V0Q29udHJvbGxlciA9IG5ldyBEYXNoYm9hcmRXaWRnZXRDb250cm9sbGVyKCk7XG5cbmFwcC5nZXQoJy9hcGkvZGFzaGJvYXJkcycsIG1vY2tBdXRoTWlkZGxld2FyZSwgZGFzaGJvYXJkQ29udHJvbGxlci5nZXREYXNoYm9hcmRzKTtcbmFwcC5nZXQoJy9hcGkvZGFzaGJvYXJkcy86aWQnLCBtb2NrQXV0aE1pZGRsZXdhcmUsIGRhc2hib2FyZENvbnRyb2xsZXIuZ2V0RGFzaGJvYXJkQnlJZCk7XG5hcHAucG9zdCgnL2FwaS9kYXNoYm9hcmRzJywgbW9ja0F1dGhNaWRkbGV3YXJlLCBkYXNoYm9hcmRDb250cm9sbGVyLmNyZWF0ZURhc2hib2FyZCk7XG5hcHAucHV0KCcvYXBpL2Rhc2hib2FyZHMvOmlkJywgbW9ja0F1dGhNaWRkbGV3YXJlLCBkYXNoYm9hcmRDb250cm9sbGVyLnVwZGF0ZURhc2hib2FyZCk7XG5hcHAuZGVsZXRlKCcvYXBpL2Rhc2hib2FyZHMvOmlkJywgbW9ja0F1dGhNaWRkbGV3YXJlLCBkYXNoYm9hcmRDb250cm9sbGVyLmRlbGV0ZURhc2hib2FyZCk7XG5cbmFwcC5nZXQoJy9hcGkvZGFzaGJvYXJkcy86ZGFzaGJvYXJkSWQvd2lkZ2V0cycsIG1vY2tBdXRoTWlkZGxld2FyZSwgd2lkZ2V0Q29udHJvbGxlci5nZXRXaWRnZXRzKTtcbmFwcC5nZXQoJy9hcGkvZGFzaGJvYXJkcy93aWRnZXRzLzppZCcsIG1vY2tBdXRoTWlkZGxld2FyZSwgd2lkZ2V0Q29udHJvbGxlci5nZXRXaWRnZXRCeUlkKTtcbmFwcC5wb3N0KCcvYXBpL2Rhc2hib2FyZHMvOmRhc2hib2FyZElkL3dpZGdldHMnLCBtb2NrQXV0aE1pZGRsZXdhcmUsIHdpZGdldENvbnRyb2xsZXIuY3JlYXRlV2lkZ2V0KTtcbmFwcC5wdXQoJy9hcGkvZGFzaGJvYXJkcy93aWRnZXRzLzppZCcsIG1vY2tBdXRoTWlkZGxld2FyZSwgd2lkZ2V0Q29udHJvbGxlci51cGRhdGVXaWRnZXQpO1xuYXBwLmRlbGV0ZSgnL2FwaS9kYXNoYm9hcmRzL3dpZGdldHMvOmlkJywgbW9ja0F1dGhNaWRkbGV3YXJlLCB3aWRnZXRDb250cm9sbGVyLmRlbGV0ZVdpZGdldCk7XG5hcHAucG9zdCgnL2FwaS9kYXNoYm9hcmRzLzpkYXNoYm9hcmRJZC93aWRnZXRzL3Jlb3JkZXInLCBtb2NrQXV0aE1pZGRsZXdhcmUsIHdpZGdldENvbnRyb2xsZXIucmVvcmRlcldpZGdldHMpO1xuXG5kZXNjcmliZSgnRGFzaGJvYXJkQ29udHJvbGxlcicsICgpID0+IHtcbiAgYmVmb3JlRWFjaCgoKSA9PiB7XG4gICAgdmkuY2xlYXJBbGxNb2NrcygpO1xuICB9KTtcblxuICBkZXNjcmliZSgnR0VUIC9hcGkvZGFzaGJvYXJkcycsICgpID0+IHtcbiAgICBpdCgnc2hvdWxkIGdldCBkYXNoYm9hcmRzIGZvciB0aGUgY3VycmVudCB1c2VyJywgYXN5bmMgKCkgPT4ge1xuICAgICAgY29uc3QgbW9ja0Rhc2hib2FyZHMgPSBbXG4gICAgICAgIHtcbiAgICAgICAgICBpZDogJ2Rhc2hib2FyZC0xJyxcbiAgICAgICAgICBuYW1lOiAnTXkgRGFzaGJvYXJkJyxcbiAgICAgICAgICBjcmVhdGVkQnlJZDogJ3VzZXItMScsXG4gICAgICAgICAgaXNQdWJsaWM6IGZhbHNlLFxuICAgICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICAgaWQ6ICdkYXNoYm9hcmQtMicsXG4gICAgICAgICAgbmFtZTogJ1B1YmxpYyBEYXNoYm9hcmQnLFxuICAgICAgICAgIGNyZWF0ZWRCeUlkOiAndXNlci0yJyxcbiAgICAgICAgICBpc1B1YmxpYzogdHJ1ZSxcbiAgICAgICAgfSxcbiAgICAgIF07XG5cbiAgICAgIG1vY2tQcmlzbWEuZGFzaGJvYXJkLmZpbmRNYW55Lm1vY2tSZXNvbHZlZFZhbHVlKG1vY2tEYXNoYm9hcmRzKTtcblxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCByZXF1ZXN0KGFwcCkuZ2V0KCcvYXBpL2Rhc2hib2FyZHMnKTtcblxuICAgICAgZXhwZWN0KHJlc3BvbnNlLnN0YXR1cykudG9CZSgyMDApO1xuICAgICAgZXhwZWN0KHJlc3BvbnNlLmJvZHkuc3VjY2VzcykudG9CZSh0cnVlKTtcbiAgICAgIGV4cGVjdChyZXNwb25zZS5ib2R5LmRhdGEpLnRvRXF1YWwobW9ja0Rhc2hib2FyZHMpO1xuICAgICAgZXhwZWN0KG1vY2tQcmlzbWEuZGFzaGJvYXJkLmZpbmRNYW55KS50b0hhdmVCZWVuQ2FsbGVkV2l0aCh7XG4gICAgICAgIHdoZXJlOiB7XG4gICAgICAgICAgT1I6IFtcbiAgICAgICAgICAgIHsgY3JlYXRlZEJ5SWQ6ICd1c2VyLTEnIH0sXG4gICAgICAgICAgICB7IGlzUHVibGljOiB0cnVlIH0sXG4gICAgICAgICAgXSxcbiAgICAgICAgfSxcbiAgICAgICAgb3JkZXJCeToge1xuICAgICAgICAgIGNyZWF0ZWRBdDogJ2Rlc2MnLFxuICAgICAgICB9LFxuICAgICAgfSk7XG4gICAgfSk7XG4gIH0pO1xuXG4gIGRlc2NyaWJlKCdHRVQgL2FwaS9kYXNoYm9hcmRzLzppZCcsICgpID0+IHtcbiAgICBpdCgnc2hvdWxkIGdldCBhIGRhc2hib2FyZCBieSBJRCB3aXRoIHdpZGdldHMnLCBhc3luYyAoKSA9PiB7XG4gICAgICBjb25zdCBtb2NrRGFzaGJvYXJkID0ge1xuICAgICAgICBpZDogJ2Rhc2hib2FyZC0xJyxcbiAgICAgICAgbmFtZTogJ015IERhc2hib2FyZCcsXG4gICAgICAgIGNyZWF0ZWRCeUlkOiAndXNlci0xJyxcbiAgICAgICAgaXNQdWJsaWM6IGZhbHNlLFxuICAgICAgICB3aWRnZXRzOiBbXG4gICAgICAgICAge1xuICAgICAgICAgICAgaWQ6ICd3aWRnZXQtMScsXG4gICAgICAgICAgICB0aXRsZTogJ1RyYW5zYWN0aW9uIENoYXJ0JyxcbiAgICAgICAgICAgIHR5cGU6ICdDSEFSVCcsXG4gICAgICAgICAgICBwb3NpdGlvbjogMCxcbiAgICAgICAgICB9LFxuICAgICAgICBdLFxuICAgICAgfTtcblxuICAgICAgbW9ja1ByaXNtYS5kYXNoYm9hcmQuZmluZFVuaXF1ZS5tb2NrUmVzb2x2ZWRWYWx1ZShtb2NrRGFzaGJvYXJkKTtcblxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCByZXF1ZXN0KGFwcCkuZ2V0KCcvYXBpL2Rhc2hib2FyZHMvZGFzaGJvYXJkLTEnKTtcblxuICAgICAgZXhwZWN0KHJlc3BvbnNlLnN0YXR1cykudG9CZSgyMDApO1xuICAgICAgZXhwZWN0KHJlc3BvbnNlLmJvZHkuc3VjY2VzcykudG9CZSh0cnVlKTtcbiAgICAgIGV4cGVjdChyZXNwb25zZS5ib2R5LmRhdGEpLnRvRXF1YWwobW9ja0Rhc2hib2FyZCk7XG4gICAgICBleHBlY3QobW9ja1ByaXNtYS5kYXNoYm9hcmQuZmluZFVuaXF1ZSkudG9IYXZlQmVlbkNhbGxlZFdpdGgoe1xuICAgICAgICB3aGVyZTogeyBpZDogJ2Rhc2hib2FyZC0xJyB9LFxuICAgICAgICBpbmNsdWRlOiB7XG4gICAgICAgICAgd2lkZ2V0czoge1xuICAgICAgICAgICAgb3JkZXJCeToge1xuICAgICAgICAgICAgICBwb3NpdGlvbjogJ2FzYycsXG4gICAgICAgICAgICB9LFxuICAgICAgICAgIH0sXG4gICAgICAgIH0sXG4gICAgICB9KTtcbiAgICB9KTtcblxuICAgIGl0KCdzaG91bGQgcmV0dXJuIDQwNCBmb3Igbm9uLWV4aXN0ZW50IGRhc2hib2FyZCcsIGFzeW5jICgpID0+IHtcbiAgICAgIG1vY2tQcmlzbWEuZGFzaGJvYXJkLmZpbmRVbmlxdWUubW9ja1Jlc29sdmVkVmFsdWUobnVsbCk7XG5cbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgcmVxdWVzdChhcHApLmdldCgnL2FwaS9kYXNoYm9hcmRzL25vbi1leGlzdGVudCcpO1xuXG4gICAgICBleHBlY3QocmVzcG9uc2Uuc3RhdHVzKS50b0JlKDQwNCk7XG4gICAgICBleHBlY3QocmVzcG9uc2UuYm9keS5zdWNjZXNzKS50b0JlKGZhbHNlKTtcbiAgICAgIGV4cGVjdChyZXNwb25zZS5ib2R5Lm1lc3NhZ2UpLnRvQmUoJ0Rhc2hib2FyZCBub3QgZm91bmQnKTtcbiAgICB9KTtcblxuICAgIGl0KCdzaG91bGQgcmV0dXJuIDQwMyBmb3IgcHJpdmF0ZSBkYXNoYm9hcmQgbm90IG93bmVkIGJ5IHVzZXInLCBhc3luYyAoKSA9PiB7XG4gICAgICBjb25zdCBtb2NrRGFzaGJvYXJkID0ge1xuICAgICAgICBpZDogJ2Rhc2hib2FyZC0xJyxcbiAgICAgICAgbmFtZTogJ1ByaXZhdGUgRGFzaGJvYXJkJyxcbiAgICAgICAgY3JlYXRlZEJ5SWQ6ICd1c2VyLTInLFxuICAgICAgICBpc1B1YmxpYzogZmFsc2UsXG4gICAgICB9O1xuXG4gICAgICBtb2NrUHJpc21hLmRhc2hib2FyZC5maW5kVW5pcXVlLm1vY2tSZXNvbHZlZFZhbHVlKG1vY2tEYXNoYm9hcmQpO1xuXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHJlcXVlc3QoYXBwKS5nZXQoJy9hcGkvZGFzaGJvYXJkcy9kYXNoYm9hcmQtMScpO1xuXG4gICAgICBleHBlY3QocmVzcG9uc2Uuc3RhdHVzKS50b0JlKDQwMyk7XG4gICAgICBleHBlY3QocmVzcG9uc2UuYm9keS5zdWNjZXNzKS50b0JlKGZhbHNlKTtcbiAgICAgIGV4cGVjdChyZXNwb25zZS5ib2R5Lm1lc3NhZ2UpLnRvQmUoJ0FjY2VzcyBkZW5pZWQnKTtcbiAgICB9KTtcbiAgfSk7XG5cbiAgZGVzY3JpYmUoJ1BPU1QgL2FwaS9kYXNoYm9hcmRzJywgKCkgPT4ge1xuICAgIGl0KCdzaG91bGQgY3JlYXRlIGEgbmV3IGRhc2hib2FyZCcsIGFzeW5jICgpID0+IHtcbiAgICAgIGNvbnN0IGRhc2hib2FyZERhdGEgPSB7XG4gICAgICAgIG5hbWU6ICdOZXcgRGFzaGJvYXJkJyxcbiAgICAgICAgZGVzY3JpcHRpb246ICdUZXN0IERlc2NyaXB0aW9uJyxcbiAgICAgICAgbGF5b3V0OiB7IGNvbHVtbnM6IDIgfSxcbiAgICAgICAgaXNQdWJsaWM6IGZhbHNlLFxuICAgICAgfTtcblxuICAgICAgY29uc3QgbW9ja0Rhc2hib2FyZCA9IHtcbiAgICAgICAgaWQ6ICdkYXNoYm9hcmQtMScsXG4gICAgICAgIC4uLmRhc2hib2FyZERhdGEsXG4gICAgICAgIGNyZWF0ZWRCeUlkOiAndXNlci0xJyxcbiAgICAgIH07XG5cbiAgICAgIG1vY2tQcmlzbWEuZGFzaGJvYXJkLmNyZWF0ZS5tb2NrUmVzb2x2ZWRWYWx1ZShtb2NrRGFzaGJvYXJkKTtcblxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCByZXF1ZXN0KGFwcClcbiAgICAgICAgLnBvc3QoJy9hcGkvZGFzaGJvYXJkcycpXG4gICAgICAgIC5zZW5kKGRhc2hib2FyZERhdGEpO1xuXG4gICAgICBleHBlY3QocmVzcG9uc2Uuc3RhdHVzKS50b0JlKDIwMSk7XG4gICAgICBleHBlY3QocmVzcG9uc2UuYm9keS5zdWNjZXNzKS50b0JlKHRydWUpO1xuICAgICAgZXhwZWN0KHJlc3BvbnNlLmJvZHkuZGF0YSkudG9FcXVhbChtb2NrRGFzaGJvYXJkKTtcbiAgICAgIGV4cGVjdChtb2NrUHJpc21hLmRhc2hib2FyZC5jcmVhdGUpLnRvSGF2ZUJlZW5DYWxsZWRXaXRoKHtcbiAgICAgICAgZGF0YToge1xuICAgICAgICAgIC4uLmRhc2hib2FyZERhdGEsXG4gICAgICAgICAgY3JlYXRlZEJ5SWQ6ICd1c2VyLTEnLFxuICAgICAgICB9LFxuICAgICAgfSk7XG4gICAgfSk7XG4gIH0pO1xuXG4gIGRlc2NyaWJlKCdQVVQgL2FwaS9kYXNoYm9hcmRzLzppZCcsICgpID0+IHtcbiAgICBpdCgnc2hvdWxkIHVwZGF0ZSBhIGRhc2hib2FyZCcsIGFzeW5jICgpID0+IHtcbiAgICAgIGNvbnN0IGV4aXN0aW5nRGFzaGJvYXJkID0ge1xuICAgICAgICBpZDogJ2Rhc2hib2FyZC0xJyxcbiAgICAgICAgbmFtZTogJ09sZCBOYW1lJyxcbiAgICAgICAgY3JlYXRlZEJ5SWQ6ICd1c2VyLTEnLFxuICAgICAgfTtcblxuICAgICAgY29uc3QgdXBkYXRlRGF0YSA9IHtcbiAgICAgICAgbmFtZTogJ1VwZGF0ZWQgTmFtZScsXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnVXBkYXRlZCBEZXNjcmlwdGlvbicsXG4gICAgICB9O1xuXG4gICAgICBjb25zdCB1cGRhdGVkRGFzaGJvYXJkID0ge1xuICAgICAgICAuLi5leGlzdGluZ0Rhc2hib2FyZCxcbiAgICAgICAgLi4udXBkYXRlRGF0YSxcbiAgICAgIH07XG5cbiAgICAgIG1vY2tQcmlzbWEuZGFzaGJvYXJkLmZpbmRVbmlxdWUubW9ja1Jlc29sdmVkVmFsdWUoZXhpc3RpbmdEYXNoYm9hcmQpO1xuICAgICAgbW9ja1ByaXNtYS5kYXNoYm9hcmQudXBkYXRlLm1vY2tSZXNvbHZlZFZhbHVlKHVwZGF0ZWREYXNoYm9hcmQpO1xuXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHJlcXVlc3QoYXBwKVxuICAgICAgICAucHV0KCcvYXBpL2Rhc2hib2FyZHMvZGFzaGJvYXJkLTEnKVxuICAgICAgICAuc2VuZCh1cGRhdGVEYXRhKTtcblxuICAgICAgZXhwZWN0KHJlc3BvbnNlLnN0YXR1cykudG9CZSgyMDApO1xuICAgICAgZXhwZWN0KHJlc3BvbnNlLmJvZHkuc3VjY2VzcykudG9CZSh0cnVlKTtcbiAgICAgIGV4cGVjdChyZXNwb25zZS5ib2R5LmRhdGEpLnRvRXF1YWwodXBkYXRlZERhc2hib2FyZCk7XG4gICAgICBleHBlY3QobW9ja1ByaXNtYS5kYXNoYm9hcmQudXBkYXRlKS50b0hhdmVCZWVuQ2FsbGVkV2l0aCh7XG4gICAgICAgIHdoZXJlOiB7IGlkOiAnZGFzaGJvYXJkLTEnIH0sXG4gICAgICAgIGRhdGE6IHVwZGF0ZURhdGEsXG4gICAgICB9KTtcbiAgICB9KTtcblxuICAgIGl0KCdzaG91bGQgcmV0dXJuIDQwMyBmb3IgZGFzaGJvYXJkIG5vdCBvd25lZCBieSB1c2VyJywgYXN5bmMgKCkgPT4ge1xuICAgICAgY29uc3QgZXhpc3RpbmdEYXNoYm9hcmQgPSB7XG4gICAgICAgIGlkOiAnZGFzaGJvYXJkLTEnLFxuICAgICAgICBuYW1lOiAnRGFzaGJvYXJkJyxcbiAgICAgICAgY3JlYXRlZEJ5SWQ6ICd1c2VyLTInLFxuICAgICAgfTtcblxuICAgICAgbW9ja1ByaXNtYS5kYXNoYm9hcmQuZmluZFVuaXF1ZS5tb2NrUmVzb2x2ZWRWYWx1ZShleGlzdGluZ0Rhc2hib2FyZCk7XG5cbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgcmVxdWVzdChhcHApXG4gICAgICAgIC5wdXQoJy9hcGkvZGFzaGJvYXJkcy9kYXNoYm9hcmQtMScpXG4gICAgICAgIC5zZW5kKHsgbmFtZTogJ1VwZGF0ZWQgTmFtZScgfSk7XG5cbiAgICAgIGV4cGVjdChyZXNwb25zZS5zdGF0dXMpLnRvQmUoNDAzKTtcbiAgICAgIGV4cGVjdChyZXNwb25zZS5ib2R5LnN1Y2Nlc3MpLnRvQmUoZmFsc2UpO1xuICAgICAgZXhwZWN0KHJlc3BvbnNlLmJvZHkubWVzc2FnZSkudG9CZSgnQWNjZXNzIGRlbmllZCcpO1xuICAgIH0pO1xuICB9KTtcblxuICBkZXNjcmliZSgnREVMRVRFIC9hcGkvZGFzaGJvYXJkcy86aWQnLCAoKSA9PiB7XG4gICAgaXQoJ3Nob3VsZCBkZWxldGUgYSBkYXNoYm9hcmQgYW5kIGl0cyB3aWRnZXRzJywgYXN5bmMgKCkgPT4ge1xuICAgICAgY29uc3QgZXhpc3RpbmdEYXNoYm9hcmQgPSB7XG4gICAgICAgIGlkOiAnZGFzaGJvYXJkLTEnLFxuICAgICAgICBuYW1lOiAnRGFzaGJvYXJkJyxcbiAgICAgICAgY3JlYXRlZEJ5SWQ6ICd1c2VyLTEnLFxuICAgICAgfTtcblxuICAgICAgbW9ja1ByaXNtYS5kYXNoYm9hcmQuZmluZFVuaXF1ZS5tb2NrUmVzb2x2ZWRWYWx1ZShleGlzdGluZ0Rhc2hib2FyZCk7XG4gICAgICBtb2NrUHJpc21hLmRhc2hib2FyZFdpZGdldC5kZWxldGVNYW55Lm1vY2tSZXNvbHZlZFZhbHVlKHsgY291bnQ6IDIgfSk7XG4gICAgICBtb2NrUHJpc21hLmRhc2hib2FyZC5kZWxldGUubW9ja1Jlc29sdmVkVmFsdWUoZXhpc3RpbmdEYXNoYm9hcmQpO1xuXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHJlcXVlc3QoYXBwKS5kZWxldGUoJy9hcGkvZGFzaGJvYXJkcy9kYXNoYm9hcmQtMScpO1xuXG4gICAgICBleHBlY3QocmVzcG9uc2Uuc3RhdHVzKS50b0JlKDIwMCk7XG4gICAgICBleHBlY3QocmVzcG9uc2UuYm9keS5zdWNjZXNzKS50b0JlKHRydWUpO1xuICAgICAgZXhwZWN0KHJlc3BvbnNlLmJvZHkubWVzc2FnZSkudG9CZSgnRGFzaGJvYXJkIGRlbGV0ZWQgc3VjY2Vzc2Z1bGx5Jyk7XG4gICAgICBleHBlY3QobW9ja1ByaXNtYS5kYXNoYm9hcmRXaWRnZXQuZGVsZXRlTWFueSkudG9IYXZlQmVlbkNhbGxlZFdpdGgoe1xuICAgICAgICB3aGVyZTogeyBkYXNoYm9hcmRJZDogJ2Rhc2hib2FyZC0xJyB9LFxuICAgICAgfSk7XG4gICAgICBleHBlY3QobW9ja1ByaXNtYS5kYXNoYm9hcmQuZGVsZXRlKS50b0hhdmVCZWVuQ2FsbGVkV2l0aCh7XG4gICAgICAgIHdoZXJlOiB7IGlkOiAnZGFzaGJvYXJkLTEnIH0sXG4gICAgICB9KTtcbiAgICB9KTtcbiAgfSk7XG59KTtcblxuZGVzY3JpYmUoJ0Rhc2hib2FyZFdpZGdldENvbnRyb2xsZXInLCAoKSA9PiB7XG4gIGJlZm9yZUVhY2goKCkgPT4ge1xuICAgIHZpLmNsZWFyQWxsTW9ja3MoKTtcbiAgfSk7XG5cbiAgZGVzY3JpYmUoJ0dFVCAvYXBpL2Rhc2hib2FyZHMvOmRhc2hib2FyZElkL3dpZGdldHMnLCAoKSA9PiB7XG4gICAgaXQoJ3Nob3VsZCBnZXQgd2lkZ2V0cyBmb3IgYSBkYXNoYm9hcmQnLCBhc3luYyAoKSA9PiB7XG4gICAgICBjb25zdCBtb2NrRGFzaGJvYXJkID0ge1xuICAgICAgICBpZDogJ2Rhc2hib2FyZC0xJyxcbiAgICAgICAgY3JlYXRlZEJ5SWQ6ICd1c2VyLTEnLFxuICAgICAgICBpc1B1YmxpYzogZmFsc2UsXG4gICAgICB9O1xuXG4gICAgICBjb25zdCBtb2NrV2lkZ2V0cyA9IFtcbiAgICAgICAge1xuICAgICAgICAgIGlkOiAnd2lkZ2V0LTEnLFxuICAgICAgICAgIHRpdGxlOiAnQ2hhcnQgV2lkZ2V0JyxcbiAgICAgICAgICB0eXBlOiAnQ0hBUlQnLFxuICAgICAgICAgIHBvc2l0aW9uOiAwLFxuICAgICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICAgaWQ6ICd3aWRnZXQtMicsXG4gICAgICAgICAgdGl0bGU6ICdUYWJsZSBXaWRnZXQnLFxuICAgICAgICAgIHR5cGU6ICdUQUJMRScsXG4gICAgICAgICAgcG9zaXRpb246IDEsXG4gICAgICAgIH0sXG4gICAgICBdO1xuXG4gICAgICBtb2NrUHJpc21hLmRhc2hib2FyZC5maW5kVW5pcXVlLm1vY2tSZXNvbHZlZFZhbHVlKG1vY2tEYXNoYm9hcmQpO1xuICAgICAgbW9ja1ByaXNtYS5kYXNoYm9hcmRXaWRnZXQuZmluZE1hbnkubW9ja1Jlc29sdmVkVmFsdWUobW9ja1dpZGdldHMpO1xuXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHJlcXVlc3QoYXBwKS5nZXQoJy9hcGkvZGFzaGJvYXJkcy9kYXNoYm9hcmQtMS93aWRnZXRzJyk7XG5cbiAgICAgIGV4cGVjdChyZXNwb25zZS5zdGF0dXMpLnRvQmUoMjAwKTtcbiAgICAgIGV4cGVjdChyZXNwb25zZS5ib2R5LnN1Y2Nlc3MpLnRvQmUodHJ1ZSk7XG4gICAgICBleHBlY3QocmVzcG9uc2UuYm9keS5kYXRhKS50b0VxdWFsKG1vY2tXaWRnZXRzKTtcbiAgICAgIGV4cGVjdChtb2NrUHJpc21hLmRhc2hib2FyZFdpZGdldC5maW5kTWFueSkudG9IYXZlQmVlbkNhbGxlZFdpdGgoe1xuICAgICAgICB3aGVyZTogeyBkYXNoYm9hcmRJZDogJ2Rhc2hib2FyZC0xJyB9LFxuICAgICAgICBvcmRlckJ5OiB7IHBvc2l0aW9uOiAnYXNjJyB9LFxuICAgICAgfSk7XG4gICAgfSk7XG4gIH0pO1xuXG4gIGRlc2NyaWJlKCdQT1NUIC9hcGkvZGFzaGJvYXJkcy86ZGFzaGJvYXJkSWQvd2lkZ2V0cycsICgpID0+IHtcbiAgICBpdCgnc2hvdWxkIGNyZWF0ZSBhIG5ldyB3aWRnZXQnLCBhc3luYyAoKSA9PiB7XG4gICAgICBjb25zdCBtb2NrRGFzaGJvYXJkID0ge1xuICAgICAgICBpZDogJ2Rhc2hib2FyZC0xJyxcbiAgICAgICAgY3JlYXRlZEJ5SWQ6ICd1c2VyLTEnLFxuICAgICAgfTtcblxuICAgICAgY29uc3Qgd2lkZ2V0RGF0YSA9IHtcbiAgICAgICAgdGl0bGU6ICdOZXcgV2lkZ2V0JyxcbiAgICAgICAgdHlwZTogJ0NIQVJUJyxcbiAgICAgICAgY29uZmlnOiB7IGNoYXJ0VHlwZTogJ2xpbmUnIH0sXG4gICAgICAgIHdpZHRoOiAyLFxuICAgICAgICBoZWlnaHQ6IDEsXG4gICAgICB9O1xuXG4gICAgICBjb25zdCBtb2NrV2lkZ2V0ID0ge1xuICAgICAgICBpZDogJ3dpZGdldC0xJyxcbiAgICAgICAgZGFzaGJvYXJkSWQ6ICdkYXNoYm9hcmQtMScsXG4gICAgICAgIC4uLndpZGdldERhdGEsXG4gICAgICAgIHBvc2l0aW9uOiAwLFxuICAgICAgfTtcblxuICAgICAgbW9ja1ByaXNtYS5kYXNoYm9hcmQuZmluZFVuaXF1ZS5tb2NrUmVzb2x2ZWRWYWx1ZShtb2NrRGFzaGJvYXJkKTtcbiAgICAgIG1vY2tQcmlzbWEuZGFzaGJvYXJkV2lkZ2V0LmZpbmRGaXJzdC5tb2NrUmVzb2x2ZWRWYWx1ZShudWxsKTtcbiAgICAgIG1vY2tQcmlzbWEuZGFzaGJvYXJkV2lkZ2V0LmNyZWF0ZS5tb2NrUmVzb2x2ZWRWYWx1ZShtb2NrV2lkZ2V0KTtcblxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCByZXF1ZXN0KGFwcClcbiAgICAgICAgLnBvc3QoJy9hcGkvZGFzaGJvYXJkcy9kYXNoYm9hcmQtMS93aWRnZXRzJylcbiAgICAgICAgLnNlbmQod2lkZ2V0RGF0YSk7XG5cbiAgICAgIGV4cGVjdChyZXNwb25zZS5zdGF0dXMpLnRvQmUoMjAxKTtcbiAgICAgIGV4cGVjdChyZXNwb25zZS5ib2R5LnN1Y2Nlc3MpLnRvQmUodHJ1ZSk7XG4gICAgICBleHBlY3QocmVzcG9uc2UuYm9keS5kYXRhKS50b0VxdWFsKG1vY2tXaWRnZXQpO1xuICAgICAgZXhwZWN0KG1vY2tQcmlzbWEuZGFzaGJvYXJkV2lkZ2V0LmNyZWF0ZSkudG9IYXZlQmVlbkNhbGxlZFdpdGgoe1xuICAgICAgICBkYXRhOiB7XG4gICAgICAgICAgZGFzaGJvYXJkSWQ6ICdkYXNoYm9hcmQtMScsXG4gICAgICAgICAgLi4ud2lkZ2V0RGF0YSxcbiAgICAgICAgICBwb3NpdGlvbjogMCxcbiAgICAgICAgfSxcbiAgICAgIH0pO1xuICAgIH0pO1xuICB9KTtcblxuICBkZXNjcmliZSgnUE9TVCAvYXBpL2Rhc2hib2FyZHMvOmRhc2hib2FyZElkL3dpZGdldHMvcmVvcmRlcicsICgpID0+IHtcbiAgICBpdCgnc2hvdWxkIHJlb3JkZXIgd2lkZ2V0cycsIGFzeW5jICgpID0+IHtcbiAgICAgIGNvbnN0IG1vY2tEYXNoYm9hcmQgPSB7XG4gICAgICAgIGlkOiAnZGFzaGJvYXJkLTEnLFxuICAgICAgICBjcmVhdGVkQnlJZDogJ3VzZXItMScsXG4gICAgICB9O1xuXG4gICAgICBjb25zdCB3aWRnZXRzRGF0YSA9IFtcbiAgICAgICAgeyBpZDogJ3dpZGdldC0xJywgcG9zaXRpb246IDEgfSxcbiAgICAgICAgeyBpZDogJ3dpZGdldC0yJywgcG9zaXRpb246IDAgfSxcbiAgICAgIF07XG5cbiAgICAgIG1vY2tQcmlzbWEuZGFzaGJvYXJkLmZpbmRVbmlxdWUubW9ja1Jlc29sdmVkVmFsdWUobW9ja0Rhc2hib2FyZCk7XG4gICAgICBtb2NrUHJpc21hLmRhc2hib2FyZFdpZGdldC51cGRhdGUubW9ja1Jlc29sdmVkVmFsdWUoe30pO1xuXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHJlcXVlc3QoYXBwKVxuICAgICAgICAucG9zdCgnL2FwaS9kYXNoYm9hcmRzL2Rhc2hib2FyZC0xL3dpZGdldHMvcmVvcmRlcicpXG4gICAgICAgIC5zZW5kKHsgd2lkZ2V0czogd2lkZ2V0c0RhdGEgfSk7XG5cbiAgICAgIGV4cGVjdChyZXNwb25zZS5zdGF0dXMpLnRvQmUoMjAwKTtcbiAgICAgIGV4cGVjdChyZXNwb25zZS5ib2R5LnN1Y2Nlc3MpLnRvQmUodHJ1ZSk7XG4gICAgICBleHBlY3QocmVzcG9uc2UuYm9keS5tZXNzYWdlKS50b0JlKCdXaWRnZXRzIHJlb3JkZXJlZCBzdWNjZXNzZnVsbHknKTtcbiAgICAgIGV4cGVjdChtb2NrUHJpc21hLmRhc2hib2FyZFdpZGdldC51cGRhdGUpLnRvSGF2ZUJlZW5DYWxsZWRUaW1lcygyKTtcbiAgICB9KTtcbiAgfSk7XG59KTtcbiJdLCJ2ZXJzaW9uIjozfQ==