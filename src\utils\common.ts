// jscpd:ignore-file
/**
 * Common Utility Functions
 *
 * This file contains common utility functions used throughout the application.
 * It helps eliminate duplication by providing a single source of truth for utility functions.
 */

import { Request, Response } from 'express';
import { AppError, ErrorType, ErrorCode } from '../utils/errors';
import { AppError, ErrorType, ErrorCode } from '../utils/errors';

/**
 * Check if a value is defined
 * @param value Value to check
 * @returns True if value is defined
 */
export function isDefined<T>(value: T | undefined | null): value is T {
  return value !== undefined && value !== null;
}

/**
 * Check if a value is undefined or null
 * @param value Value to check
 * @returns True if value is undefined or null
 */
export function isUndefined<T>(value: T | undefined | null): value is undefined | null {
  return value === undefined ?? value === null;
}

/**
 * Check if a string is empty
 * @param value String to check
 * @returns True if string is empty
 */
export function isEmpty(value: string | undefined | null): boolean {
  return isUndefined(value) || value.trim() === '';
}

/**
 * Check if a string is not empty
 * @param value String to check
 * @returns True if string is not empty
 */
export function isNotEmpty(value: string | undefined | null): boolean {
  return !isEmpty(value);
}

/**
 * Format currency amount
 * @param amount Amount to format
 * @param currency Currency code
 * @returns Formatted currency amount
 */
export function formatCurrency(amount: number, currency: string): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency
  }).format(amount);
}

/**
 * Format date
 * @param date Date to format
 * @param options Intl.DateTimeFormatOptions
 * @returns Formatted date
 */
export function formatDate(date: Date, options?: Intl.DateTimeFormatOptions): string {
  return new Intl.DateTimeFormat('en-US', options).format(date);
}

/**
 * Generate a random string
 * @param length Length of the string
 * @returns Random string
 */
export function generateRandomString(length: number): string {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result: string ='';

  for (let i: number =0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }

  return result;
}

/**
 * Truncate a string
 * @param str String to truncate
 * @param maxLength Maximum length
 * @param suffix Suffix to add if truncated
 * @returns Truncated string
 */
export function truncate(str: string, maxLength: number, suffix: string = '...'): string {
  if (str.length <= maxLength) {
    return str;
  }

  return str.substring(0, maxLength) + suffix;
}
