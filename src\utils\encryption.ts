// jscpd:ignore-file
import crypto from 'crypto';
import { PaymentMethodType } from '../types';

// Environment variables for encryption
const ENCRYPTION_KEY: any = process.env.ENCRYPTION_KEY || 'amazingpay-encryption-key-32bytes';
const ENCRYPTION_IV: any = process.env.ENCRYPTION_IV || 'amazingpay-iv-16b';

/**
 * Encrypt sensitive data in payment method configuration
 * @param config Configuration object
 * @param type Payment method type
 */
export function encryptSensitiveData(
  config: Record<string, any>,
  type: PaymentMethodType
): Record<string, any> {
  if (!config) return {};

  const encryptedConfig: any = { ...config };

  // Encrypt sensitive data based on payment method type
  switch (type) {
    case PaymentMethodType.BINANCE_PAY:
      if (encryptedConfig.apiSecret) {
        encryptedConfig.apiSecret = encrypt(encryptedConfig.apiSecret);
      }
      break;
    case PaymentMethodType.BINANCE_C2C:
      // No sensitive data to encrypt
      break;
    case PaymentMethodType.BINANCE_TRC20:
    case PaymentMethodType.BINANCE_TRC20_DIRECT:
      if (encryptedConfig.apiSecret) {
        encryptedConfig.apiSecret = encrypt(encryptedConfig.apiSecret);
      }
      break;
    case PaymentMethodType.CRYPTO_TRANSFER:
      // No sensitive data to encrypt
      break;
    default:
      // For unknown types, encrypt common sensitive fields
      if (encryptedConfig.apiSecret) {
        encryptedConfig.apiSecret = encrypt(encryptedConfig.apiSecret);
      }
      if (encryptedConfig.privateKey) {
        encryptedConfig.privateKey = encrypt(encryptedConfig.privateKey);
      }
      if (encryptedConfig.password) {
        encryptedConfig.password = encrypt(encryptedConfig.password);
      }
  }

  return encryptedConfig;
}

/**
 * Decrypt sensitive data in payment method configuration
 * @param config Configuration object
 * @param type Payment method type
 */
export function decryptSensitiveData(
  config: Record<string, any>,
  type: PaymentMethodType
): Record<string, any> {
  if (!config) return {};

  const decryptedConfig: any = { ...config };

  // Decrypt sensitive data based on payment method type
  switch (type) {
    case PaymentMethodType.BINANCE_PAY:
      if (decryptedConfig.apiSecret) {
        decryptedConfig.apiSecret = decrypt(decryptedConfig.apiSecret);
      }
      break;
    case PaymentMethodType.BINANCE_C2C:
      // No sensitive data to decrypt
      break;
    case PaymentMethodType.BINANCE_TRC20:
    case PaymentMethodType.BINANCE_TRC20_DIRECT:
      if (decryptedConfig.apiSecret) {
        decryptedConfig.apiSecret = decrypt(decryptedConfig.apiSecret);
      }
      break;
    case PaymentMethodType.CRYPTO_TRANSFER:
      // No sensitive data to decrypt
      break;
    default:
      // For unknown types, decrypt common sensitive fields
      if (decryptedConfig.apiSecret) {
        decryptedConfig.apiSecret = decrypt(decryptedConfig.apiSecret);
      }
      if (decryptedConfig.privateKey) {
        decryptedConfig.privateKey = decrypt(decryptedConfig.privateKey);
      }
      if (decryptedConfig.password) {
        decryptedConfig.password = decrypt(decryptedConfig.password);
      }
  }

  return decryptedConfig;
}

/**
 * Encrypt a string
 * @param text Text to encrypt
 */
export function encrypt(text: string): string {
  try {
    // Create key and iv from environment variables
    const key: any = crypto.scryptSync(ENCRYPTION_KEY, 'salt', 32);
    const iv: any = Buffer.from(ENCRYPTION_IV.slice(0, 16), 'utf8');

    const cipher: any = crypto.createCipheriv('aes-256-cbc', key, iv);

    // Encrypt the text
    let encrypted: any = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    return encrypted;
  } catch (error) {
    console.error('Encryption error:', error);
    return text; // Return original text on error
  }
}

/**
 * Decrypt a string
 * @param encryptedText Encrypted text
 */
export function decrypt(encryptedText: string): string {
  try {
    // Create key and iv from environment variables
    const key: any = crypto.scryptSync(ENCRYPTION_KEY, 'salt', 32);
    const iv: any = Buffer.from(ENCRYPTION_IV.slice(0, 16), 'utf8');

    const decipher: any = crypto.createDecipheriv('aes-256-cbc', key, iv);

    // Decrypt the text
    let decrypted: any = decipher.update(encryptedText, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    return decrypted;
  } catch (error) {
    console.error('Decryption error:', error);
    return encryptedText; // Return encrypted text on error
  }
}
