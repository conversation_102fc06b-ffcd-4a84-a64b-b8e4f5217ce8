// jscpd:ignore-file
import { Router } from "express";
import { SmsController } from "../controllers/refactored/sms.controller.ts";
import { authenticate } from '../middlewares/auth';
import { SmsController } from "../controllers/refactored/sms.controller.ts";
import { authenticate } from '../middlewares/auth';

const router: any =Router();

// SMS routes
router.post("/test", authenticate, SmsController.testSmsService);
router.post("/send", authenticate, SmsController.sendCustomSms);
router.get("/admin-phone-numbers", authenticate, SmsController.getAdminPhoneNumbers);

export default router;
