"use strict";
/**
 * Identity Verification Authorization Service
 *
 * Handles authorization logic for identity verification operations.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.IdentityVerificationAuthService = void 0;
const AppError_1 = require("../../../utils/errors/AppError");
const IdentityVerificationControllerTypes_1 = require("../types/IdentityVerificationControllerTypes");
/**
 * Authorization service for identity verification
 */
class IdentityVerificationAuthService {
    constructor() {
        this.adminRoles = [IdentityVerificationControllerTypes_1.UserRole.ADMIN];
        this.merchantRoles = [IdentityVerificationControllerTypes_1.UserRole.MERCHANT, IdentityVerificationControllerTypes_1.UserRole.ADMIN];
        this.userRoles = [IdentityVerificationControllerTypes_1.UserRole.USER, IdentityVerificationControllerTypes_1.UserRole.MERCHANT, IdentityVerificationControllerTypes_1.UserRole.ADMIN];
    }
    /**
     * Check if user is authorized for the given action
     */
    async checkPermission(context) {
        const { user, resource, action } = context;
        if (!user) {
            return {
                allowed: false,
                reason: 'User not authenticated',
            };
        }
        // Check role-based permissions
        const rolePermission = this.checkRolePermission(user.role, resource, action);
        if (!rolePermission.allowed) {
            return rolePermission;
        }
        // Check resource-specific permissions
        const resourcePermission = await this.checkResourcePermission(context);
        if (!resourcePermission.allowed) {
            return resourcePermission;
        }
        return { allowed: true };
    }
    /**
     * Check role-based permissions
     */
    checkRolePermission(userRole, resource, action) {
        switch (resource) {
            case 'verification':
                return this.checkVerificationPermission(userRole, action);
            case 'verification-stats':
                return this.checkStatsPermission(userRole, action);
            case 'verification-admin':
                return this.checkAdminPermission(userRole, action);
            default:
                return {
                    allowed: false,
                    reason: `Unknown resource: ${resource}`,
                };
        }
    }
    /**
     * Check verification permissions
     */
    checkVerificationPermission(userRole, action) {
        switch (action) {
            case 'create':
            case 'verify':
                if (this.userRoles.includes(userRole)) {
                    return { allowed: true };
                }
                return {
                    allowed: false,
                    reason: 'User role required for verification operations',
                    requiredRole: 'USER',
                };
            case 'read':
                if (this.userRoles.includes(userRole)) {
                    return { allowed: true };
                }
                return {
                    allowed: false,
                    reason: 'User role required for reading verifications',
                    requiredRole: 'USER',
                };
            case 'update':
            case 'delete':
                if (this.adminRoles.includes(userRole)) {
                    return { allowed: true };
                }
                return {
                    allowed: false,
                    reason: 'Admin role required for modification operations',
                    requiredRole: 'ADMIN',
                };
            default:
                return {
                    allowed: false,
                    reason: `Unknown action: ${action}`,
                };
        }
    }
    /**
     * Check statistics permissions
     */
    checkStatsPermission(userRole, action) {
        if (action === 'read') {
            if (this.merchantRoles.includes(userRole)) {
                return { allowed: true };
            }
            return {
                allowed: false,
                reason: 'Merchant role required for statistics',
                requiredRole: 'MERCHANT',
            };
        }
        return {
            allowed: false,
            reason: `Unknown action: ${action}`,
        };
    }
    /**
     * Check admin permissions
     */
    checkAdminPermission(userRole, action) {
        if (this.adminRoles.includes(userRole)) {
            return { allowed: true };
        }
        return {
            allowed: false,
            reason: 'Admin role required for administrative operations',
            requiredRole: 'ADMIN',
        };
    }
    /**
     * Check resource-specific permissions
     */
    async checkResourcePermission(context) {
        const { user, resource, action } = context;
        // Check if user can access their own verifications
        if (resource === 'verification' && action === 'read') {
            // Users can read their own verifications
            // Merchants can read verifications for their merchant account
            // Admins can read all verifications
            if (user.role === IdentityVerificationControllerTypes_1.UserRole.ADMIN) {
                return { allowed: true };
            }
            // For specific verification access, additional checks would be needed
            // This is a simplified implementation
            return { allowed: true };
        }
        return { allowed: true };
    }
    /**
     * Require admin role
     */
    requireAdmin(userRole) {
        if (!userRole || !this.adminRoles.includes(userRole)) {
            throw new AppError_1.AppError({
                message: 'Admin role required',
                type: AppError_1.ErrorType.AUTHENTICATION,
                code: AppError_1.ErrorCode.INVALID_CREDENTIALS,
            });
        }
    }
    /**
     * Require merchant role or higher
     */
    requireMerchant(userRole) {
        if (!userRole || !this.merchantRoles.includes(userRole)) {
            throw new AppError_1.AppError({
                message: 'Merchant role required',
                type: AppError_1.ErrorType.AUTHENTICATION,
                code: AppError_1.ErrorCode.INVALID_CREDENTIALS,
            });
        }
    }
    /**
     * Require authenticated user
     */
    requireAuthenticated(userRole) {
        if (!userRole || !this.userRoles.includes(userRole)) {
            throw new AppError_1.AppError({
                message: 'Authentication required',
                type: AppError_1.ErrorType.AUTHENTICATION,
                code: AppError_1.ErrorCode.INVALID_CREDENTIALS,
            });
        }
    }
    /**
     * Check if user has specific role
     */
    hasRole(userRole, requiredRole) {
        const roleHierarchy = {
            [IdentityVerificationControllerTypes_1.UserRole.USER]: 1,
            [IdentityVerificationControllerTypes_1.UserRole.MERCHANT]: 2,
            [IdentityVerificationControllerTypes_1.UserRole.ADMIN]: 3,
        };
        const userLevel = roleHierarchy[userRole] || 0;
        const requiredLevel = roleHierarchy[requiredRole] || 0;
        return userLevel >= requiredLevel;
    }
    /**
     * Get user permissions for a resource
     */
    getUserPermissions(userRole, resource) {
        const permissions = [];
        switch (resource) {
            case 'verification':
                if (this.userRoles.includes(userRole)) {
                    permissions.push('create', 'verify', 'read');
                }
                if (this.adminRoles.includes(userRole)) {
                    permissions.push('update', 'delete');
                }
                break;
            case 'verification-stats':
                if (this.merchantRoles.includes(userRole)) {
                    permissions.push('read');
                }
                break;
            case 'verification-admin':
                if (this.adminRoles.includes(userRole)) {
                    permissions.push('read', 'create', 'update', 'delete');
                }
                break;
        }
        return permissions;
    }
    /**
     * Validate authorization context
     */
    validateAuthorizationContext(context) {
        if (!context.user) {
            throw new AppError_1.AppError({
                message: 'User context is required',
                type: AppError_1.ErrorType.AUTHENTICATION,
                code: AppError_1.ErrorCode.INVALID_CREDENTIALS,
            });
        }
        if (!context.resource) {
            throw new AppError_1.AppError({
                message: 'Resource is required',
                type: AppError_1.ErrorType.VALIDATION,
                code: AppError_1.ErrorCode.MISSING_REQUIRED_FIELD,
            });
        }
        if (!context.action) {
            throw new AppError_1.AppError({
                message: 'Action is required',
                type: AppError_1.ErrorType.VALIDATION,
                code: AppError_1.ErrorCode.MISSING_REQUIRED_FIELD,
            });
        }
    }
    /**
     * Create authorization context from request
     */
    createAuthorizationContext(user, resource, action, resourceId) {
        return {
            user: {
                id: user?.id,
                role: user?.role,
                merchantId: user?.merchantId,
            },
            resource,
            action,
            resourceId,
        };
    }
    /**
     * Handle authorization error
     */
    handleAuthorizationError(result) {
        const message = result.reason ?? 'Access denied';
        throw new AppError_1.AppError({
            message,
            type: AppError_1.ErrorType.AUTHENTICATION,
            code: AppError_1.ErrorCode.INVALID_CREDENTIALS,
            details: {
                requiredRole: result.requiredRole,
                requiredPermissions: result.requiredPermissions,
            },
        });
    }
    /**
     * Check if user can access verification
     */
    canAccessVerification(user, verification) {
        // Admin can access all verifications
        if (user.role === IdentityVerificationControllerTypes_1.UserRole.ADMIN) {
            return true;
        }
        // Users can access their own verifications
        if (user.role === IdentityVerificationControllerTypes_1.UserRole.USER && verification.userId === user.id) {
            return true;
        }
        // Merchants can access verifications for their merchant account
        if (user.role === IdentityVerificationControllerTypes_1.UserRole.MERCHANT && verification.merchantId === user.id) {
            return true;
        }
        return false;
    }
    /**
     * Extract user context from request
     */
    extractUserContext(req) {
        const userId = req.user?.role === IdentityVerificationControllerTypes_1.UserRole.USER ? req.user.id : undefined;
        const merchantId = req.user?.role === IdentityVerificationControllerTypes_1.UserRole.MERCHANT ? req.user.id : undefined;
        return { userId, merchantId };
    }
}
exports.IdentityVerificationAuthService = IdentityVerificationAuthService;
//# sourceMappingURL=IdentityVerificationAuthService.js.map