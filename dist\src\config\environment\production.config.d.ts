/**
 * Production Environment Configuration
 *
 * This file loads and configures all production environment settings.
 * It serves as the central point for loading all production-specific configurations.
 */
/**
 * Load production environment variables from .env.production file
 */
export declare const loadProductionEnvironment: any;
/**
 * Initialize all production configurations
 */
export declare const initializeProductionConfig: any;
/**
 * Validate production environment
 * @returns Validation result
 */
export declare const validateProductionEnvironment: any;
/**
 * Production environment configuration
 */
export declare const productionConfig: any;
export default productionConfig;
//# sourceMappingURL=production.config.d.ts.map