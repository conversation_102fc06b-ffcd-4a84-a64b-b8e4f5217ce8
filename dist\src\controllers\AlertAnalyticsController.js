"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AlertAnalyticsController = void 0;
const BaseController_1 = require("../base/BaseController");
const alert_analytics_service_1 = require("../../services/alert-analytics.service");
const asyncHandler_1 = require("../../utils/asyncHandler");
/**
 * Alert analytics controller
 */
class AlertAnalyticsController extends BaseController_1.BaseController {
    constructor() {
        super();
        /**
         * Get alert count by status
         * @route GET /api/alerts/analytics/count-by-status
         */
        this.getAlertCountByStatus = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            // Check authorization
            const { userRole, merchantId } = this.checkAuthorization(req);
            // Parse date range
            const { startDate, endDate } = this.parseDateRange(req.query.startDate, req.query.endDate);
            // Determine target merchant ID
            const targetMerchantId = this.determineTargetMerchantId(userRole, merchantId, req.query.merchantId);
            // Get alert count by status
            const data = await this.analyticsService.getAlertCountByStatus(startDate, endDate, targetMerchantId);
            // Return data
            return this.sendSuccess(res, data);
        });
        /**
         * Get alert count by severity
         * @route GET /api/alerts/analytics/count-by-severity
         */
        this.getAlertCountBySeverity = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            // Check authorization
            const { userRole, merchantId } = this.checkAuthorization(req);
            // Parse date range
            const { startDate, endDate } = this.parseDateRange(req.query.startDate, req.query.endDate);
            // Determine target merchant ID
            const targetMerchantId = this.determineTargetMerchantId(userRole, merchantId, req.query.merchantId);
            // Get alert count by severity
            const data = await this.analyticsService.getAlertCountBySeverity(startDate, endDate, targetMerchantId);
            // Return data
            return this.sendSuccess(res, data);
        });
        /**
         * Get alert count by type
         * @route GET /api/alerts/analytics/count-by-type
         */
        this.getAlertCountByType = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            // Check authorization
            const { userRole, merchantId } = this.checkAuthorization(req);
            // Parse date range
            const { startDate, endDate } = this.parseDateRange(req.query.startDate, req.query.endDate);
            // Determine target merchant ID
            const targetMerchantId = this.determineTargetMerchantId(userRole, merchantId, req.query.merchantId);
            // Get alert count by type
            const data = await this.analyticsService.getAlertCountByType(startDate, endDate, targetMerchantId);
            // Return data
            return this.sendSuccess(res, data);
        });
        /**
         * Get alert count by day
         * @route GET /api/alerts/analytics/count-by-day
         */
        this.getAlertCountByDay = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            // Check authorization
            const { userRole, merchantId } = this.checkAuthorization(req);
            // Parse date range
            const { startDate, endDate } = this.parseDateRange(req.query.startDate, req.query.endDate);
            // Determine target merchant ID
            const targetMerchantId = this.determineTargetMerchantId(userRole, merchantId, req.query.merchantId);
            // Get alert count by day
            const data = await this.analyticsService.getAlertCountByDay(startDate, endDate, targetMerchantId);
            // Return data
            return this.sendSuccess(res, data);
        });
        /**
         * Get alert count by hour
         * @route GET /api/alerts/analytics/count-by-hour
         */
        this.getAlertCountByHour = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            // Check authorization
            const { userRole, merchantId } = this.checkAuthorization(req);
            // Parse date
            const date = this.parseDate(req.query.date, "date");
            // Determine target merchant ID
            const targetMerchantId = this.determineTargetMerchantId(userRole, merchantId, req.query.merchantId);
            // Get alert count by hour
            const data = await this.analyticsService.getAlertCountByHour(date, targetMerchantId);
            // Return data
            return this.sendSuccess(res, data);
        });
        /**
         * Get top merchants by alert count
         * @route GET /api/alerts/analytics/top-merchants
         */
        this.getTopMerchantsByAlertCount = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            // Check authorization
            const { userRole } = this.checkAuthorization(req);
            // Only admins can access this endpoint
            this.checkAdminRole(userRole);
            // Parse date range
            const { startDate, endDate } = this.parseDateRange(req.query.startDate, req.query.endDate);
            // Parse limit
            const limit = this.parseInteger(req.query.limit, "limit", 10);
            // Get top merchants by alert count
            const data = await this.analyticsService.getTopMerchantsByAlertCount(startDate, endDate, limit);
            // Return data
            return this.sendSuccess(res, data);
        });
        /**
         * Get alert resolution time statistics
         * @route GET /api/alerts/analytics/resolution-time
         */
        this.getAlertResolutionTimeStats = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            // Check authorization
            const { userRole, merchantId } = this.checkAuthorization(req);
            // Parse date range
            const { startDate, endDate } = this.parseDateRange(req.query.startDate, req.query.endDate);
            // Determine target merchant ID
            const targetMerchantId = this.determineTargetMerchantId(userRole, merchantId, req.query.merchantId);
            // Get alert resolution time statistics
            const data = await this.analyticsService.getAlertResolutionTimeStats(startDate, endDate, targetMerchantId);
            // Return data
            return this.sendSuccess(res, data);
        });
        /**
         * Get alert trends
         * @route GET /api/alerts/analytics/trends
         */
        this.getAlertTrends = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            // Check authorization
            const { userRole, merchantId } = this.checkAuthorization(req);
            // Parse days
            const days = this.parseInteger(req.query.days, "days", 30);
            // Determine target merchant ID
            const targetMerchantId = this.determineTargetMerchantId(userRole, merchantId, req.query.merchantId);
            // Get alert trends
            const data = await this.analyticsService.getAlertTrends(days, targetMerchantId);
            // Return data
            return this.sendSuccess(res, data);
        });
        this.analyticsService = new alert_analytics_service_1.AlertAnalyticsService();
    }
}
exports.AlertAnalyticsController = AlertAnalyticsController;
//# sourceMappingURL=AlertAnalyticsController.js.map