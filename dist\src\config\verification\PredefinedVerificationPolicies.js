"use strict";
// jscpd:ignore-file
/**
 * Predefined Verification Policies
 *
 * Defines predefined verification policies.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.PREDEFINED_VERIFICATION_POLICIES = exports.BINANCE_TRC20_POLICY = exports.BINANCE_PAY_POLICY = exports.HIGH_RISK_COUNTRY_POLICY = exports.NEW_MERCHANT_POLICY = exports.CRYPTO_TRANSACTION_POLICY = exports.MEDIUM_VALUE_POLICY = exports.HIGH_VALUE_POLICY = void 0;
const VerificationPolicy_1 = require("../../services/verification/policy/VerificationPolicy");
/**
 * High-value transaction policy
 *
 * Requires document and phone verification for transactions over $10,000.
 */
exports.HIGH_VALUE_POLICY = new VerificationPolicy_1.VerificationPolicy("high_value_policy")
    .setDescription("Requires document and phone verification for transactions over $10,000")
    .requireMethods(["document_verification", "phone_verification"])
    .whenAmountExceeds(10000);
/**
 * Medium-value transaction policy
 *
 * Requires phone verification for transactions over $1,000.
 */
exports.MEDIUM_VALUE_POLICY = new VerificationPolicy_1.VerificationPolicy("medium_value_policy")
    .setDescription("Requires phone verification for transactions over $1,000")
    .requireMethod("phone_verification")
    .whenAmountExceeds(1000)
    .whenAmountBelow(10000);
/**
 * Crypto transaction policy
 *
 * Requires blockchain verification for crypto transactions.
 */
exports.CRYPTO_TRANSACTION_POLICY = new VerificationPolicy_1.VerificationPolicy("crypto_transaction_policy")
    .setDescription("Requires blockchain verification for crypto transactions")
    .requireMethod("blockchain_verification")
    .addCondition(request => request.paymentMethodType.includes("crypto") ||
    request.paymentMethodType.includes("binance"));
/**
 * New merchant policy
 *
 * Requires additional verification for new merchants.
 */
exports.NEW_MERCHANT_POLICY = new VerificationPolicy_1.VerificationPolicy("new_merchant_policy")
    .setDescription("Requires additional verification for new merchants")
    .requireMethods(["email_verification", "phone_verification"])
    .addCondition(request);
{
    // In a real implementation, this would check if the merchant is new
    // For now, we'll use a metadata flag
    return request.metadata?.isNewMerchant === true;
}
;
/**
 * High-risk country policy
 *
 * Requires additional verification for transactions from high-risk countries.
 */
exports.HIGH_RISK_COUNTRY_POLICY = new VerificationPolicy_1.VerificationPolicy("high_risk_country_policy")
    .setDescription("Requires additional verification for transactions from high-risk countries")
    .requireMethods(["document_verification", "phone_verification"])
    .addCondition(request);
{
    const highRiskCountries = ["XX", "YY", "ZZ"]; // Example country codes
    return highRiskCountries.includes(request.metadata?.country);
}
;
/**
 * Binance Pay policy
 *
 * Specific verification requirements for Binance Pay.
 */
exports.BINANCE_PAY_POLICY = new VerificationPolicy_1.VerificationPolicy("binance_pay_policy")
    .setDescription("Specific verification requirements for Binance Pay")
    .requireMethod("binance_pay_verification")
    .forPaymentMethod("binance_pay");
/**
 * Binance TRC20 policy
 *
 * Specific verification requirements for Binance TRC20.
 */
exports.BINANCE_TRC20_POLICY = new VerificationPolicy_1.VerificationPolicy("binance_trc20_policy")
    .setDescription("Specific verification requirements for Binance TRC20")
    .requireMethod("binance_trc20_verification")
    .forPaymentMethod("binance_trc20");
/**
 * All predefined policies
 */
exports.PREDEFINED_VERIFICATION_POLICIES = [
    exports.HIGH_VALUE_POLICY,
    exports.MEDIUM_VALUE_POLICY,
    exports.CRYPTO_TRANSACTION_POLICY,
    exports.NEW_MERCHANT_POLICY,
    exports.HIGH_RISK_COUNTRY_POLICY,
    exports.BINANCE_PAY_POLICY,
    exports.BINANCE_TRC20_POLICY
];
//# sourceMappingURL=PredefinedVerificationPolicies.js.map