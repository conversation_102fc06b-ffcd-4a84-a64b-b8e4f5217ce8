/**
 * Initialize JWT configuration
 * This must be called before using any JWT functions
 */
export declare const initializeJwtConfig: any;
export declare const generateToken: any;
export declare const generateRefreshToken: any;
export declare const verifyToken: any;
declare const _default: {
    JWT_EXPIRES_IN: string;
    JWT_REFRESH_EXPIRES_IN: any;
    initializeJwtConfig: any;
    generateToken: any;
    generateRefreshToken: any;
    verifyToken: any;
    getJwtSecret: () => string;
};
export default _default;
//# sourceMappingURL=auth.d.ts.map