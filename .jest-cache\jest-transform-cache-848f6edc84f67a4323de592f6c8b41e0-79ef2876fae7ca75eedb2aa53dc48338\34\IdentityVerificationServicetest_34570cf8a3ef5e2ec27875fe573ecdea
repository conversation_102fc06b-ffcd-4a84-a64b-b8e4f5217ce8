6605b31567fe82409b3b73a7da64b119
"use strict";
/**
 * Unit Tests for Identity Verification Service
 *
 * Comprehensive test suite covering all functionality of the IdentityVerificationService
 */
Object.defineProperty(exports, "__esModule", { value: true });
const globals_1 = require("@jest/globals");
// Mock dependencies
globals_1.jest.mock('@prisma/client');
globals_1.jest.mock('ethers');
const IdentityVerificationService_1 = require("../core/IdentityVerificationService");
const IdentityVerificationError_1 = require("../core/IdentityVerificationError");
(0, globals_1.describe)('IdentityVerificationService', () => {
    let service;
    let mockPrisma;
    (0, globals_1.beforeEach)(() => {
        // Create mock Prisma client
        mockPrisma = {
            identityVerification: {
                create: globals_1.jest.fn(),
                findUnique: globals_1.jest.fn(),
                findMany: globals_1.jest.fn(),
                update: globals_1.jest.fn(),
                delete: globals_1.jest.fn(),
                count: globals_1.jest.fn(),
                groupBy: globals_1.jest.fn(),
            },
            user: {
                findUnique: globals_1.jest.fn(),
                create: globals_1.jest.fn(),
                update: globals_1.jest.fn(),
            },
            merchant: {
                findUnique: globals_1.jest.fn(),
            },
        };
        // Initialize service with mock
        service = new IdentityVerificationService_1.IdentityVerificationService(mockPrisma);
    });
    (0, globals_1.afterEach)(() => {
        globals_1.jest.clearAllMocks();
    });
    (0, globals_1.describe)('verifyEthereumSignature', () => {
        const validVerificationData = {
            address: '******************************************', // Valid Ethereum address format
            message: 'Verify identity for AmazingPay',
            signature: '0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890ab1c',
            userId: 'user-123-456-789',
            merchantId: 'merchant-123-456-789',
        };
        (0, globals_1.it)('should successfully verify a valid Ethereum signature', async () => {
            // Arrange
            const mockVerificationResult = {
                id: 'verification-123-456-789',
                userId: validVerificationData.userId,
                merchantId: validVerificationData.merchantId,
                method: 'ETHEREUM_SIGNATURE',
                status: 'VERIFIED',
                address: validVerificationData.address,
                createdAt: new Date(),
            };
            // Mock Prisma calls
            mockPrisma.identityVerification.create.mockResolvedValue(mockVerificationResult);
            // Act
            const result = await service.verifyEthereumSignature(validVerificationData);
            // Assert
            (0, globals_1.expect)(result).toBeDefined();
            (0, globals_1.expect)(result.success).toBe(true);
            (0, globals_1.expect)(result.verificationId).toBe(mockVerificationResult.id);
            (0, globals_1.expect)(result.method).toBe('ETHEREUM_SIGNATURE');
            (0, globals_1.expect)(result.status).toBe('VERIFIED');
            // Verify Prisma calls
            (0, globals_1.expect)(mockPrisma.identityVerification.create).toHaveBeenCalledWith({
                data: globals_1.expect.objectContaining({
                    userId: validVerificationData.userId,
                    merchantId: validVerificationData.merchantId,
                    method: 'ETHEREUM_SIGNATURE',
                    status: 'VERIFIED',
                    address: globals_1.expect.any(String),
                }),
            });
        });
        (0, globals_1.it)('should return error for invalid Ethereum address', async () => {
            // Arrange
            const invalidData = {
                ...validVerificationData,
                address: 'invalid-address',
            };
            // Act
            const result = await service.verifyEthereumSignature(invalidData);
            // Assert
            (0, globals_1.expect)(result.success).toBe(false);
            (0, globals_1.expect)(result.error).toContain('Invalid Ethereum address format');
            // Verify no database calls were made
            (0, globals_1.expect)(mockPrisma.identityVerification.create).not.toHaveBeenCalled();
        });
        (0, globals_1.it)('should return error for invalid signature', async () => {
            // Arrange
            const invalidSignatureData = {
                ...validVerificationData,
                signature: '0xinvalid', // Invalid signature format
            };
            // Act
            const result = await service.verifyEthereumSignature(invalidSignatureData);
            // Assert
            (0, globals_1.expect)(result.success).toBe(false);
            (0, globals_1.expect)(result.error).toContain('Invalid signature format');
            (0, globals_1.expect)(mockPrisma.identityVerification.create).not.toHaveBeenCalled();
        });
        (0, globals_1.it)('should handle database errors gracefully', async () => {
            // Arrange
            mockPrisma.identityVerification.create.mockRejectedValue(new Error('Database error'));
            // Act
            const result = await service.verifyEthereumSignature(validVerificationData);
            // Assert
            (0, globals_1.expect)(result.success).toBe(false);
            (0, globals_1.expect)(result.error).toContain('Database error');
        });
    });
    (0, globals_1.describe)('getVerificationById', () => {
        (0, globals_1.it)('should return verification details for valid ID', async () => {
            // Arrange
            const verificationId = 'verification-123-456-789';
            const mockVerification = {
                id: verificationId,
                userId: 'user-123-456-789',
                merchantId: 'merchant-123-456-789',
                method: 'ethereum_signature',
                status: 'verified',
                confidence: 0.95,
                address: '******************************************',
                createdAt: new Date(),
                updatedAt: new Date(),
            };
            mockPrisma.identityVerification.findUnique.mockResolvedValue(mockVerification);
            // Act
            const result = await service.getVerificationById(verificationId);
            // Assert
            (0, globals_1.expect)(result).toEqual(mockVerification);
            (0, globals_1.expect)(mockPrisma.identityVerification.findUnique).toHaveBeenCalledWith({
                where: { id: verificationId },
                include: { claims: true },
            });
        });
        (0, globals_1.it)('should throw error for non-existent verification', async () => {
            // Arrange
            const verificationId = 'non-existent-verification-id';
            mockPrisma.identityVerification.findUnique.mockResolvedValue(null);
            // Act & Assert
            await (0, globals_1.expect)(service.getVerificationById(verificationId)).rejects.toThrow(IdentityVerificationError_1.IdentityVerificationError);
        });
    });
    (0, globals_1.describe)('getVerificationsForUser', () => {
        (0, globals_1.it)('should return user verifications', async () => {
            // Arrange
            const userId = 'user-123-456-789';
            const mockVerifications = [
                {
                    id: 'verification-1-123-456',
                    userId,
                    method: 'ethereum_signature',
                    status: 'verified',
                    createdAt: new Date(),
                },
                {
                    id: 'verification-2-123-456',
                    userId,
                    method: 'ethereum_signature',
                    status: 'verified',
                    createdAt: new Date(),
                },
            ];
            mockPrisma.identityVerification.findMany.mockResolvedValue(mockVerifications);
            // Act
            const result = await service.getVerificationsForUser(userId);
            // Assert
            (0, globals_1.expect)(result).toEqual(mockVerifications);
            (0, globals_1.expect)(mockPrisma.identityVerification.findMany).toHaveBeenCalledWith({
                where: { userId },
                include: { claims: true },
                orderBy: { createdAt: 'desc' },
                take: 50,
                skip: 0,
            });
        });
        (0, globals_1.it)('should handle empty results', async () => {
            // Arrange
            const userId = 'empty-user-123-456';
            mockPrisma.identityVerification.findMany.mockResolvedValue([]);
            // Act
            const result = await service.getVerificationsForUser(userId);
            // Assert
            (0, globals_1.expect)(result).toEqual([]);
        });
    });
    (0, globals_1.describe)('getVerificationStats', () => {
        (0, globals_1.it)('should return verification statistics', async () => {
            // Arrange
            const filters = {
                merchantId: 'merchant-stats-123',
                dateFrom: new Date('2024-01-01'),
                dateTo: new Date('2024-01-31'),
            };
            mockPrisma.identityVerification.count
                .mockResolvedValueOnce(100) // total
                .mockResolvedValueOnce(85) // successful
                .mockResolvedValueOnce(5) // failed
                .mockResolvedValueOnce(10); // pending
            mockPrisma.identityVerification.groupBy.mockResolvedValue([
                { method: 'ETHEREUM_SIGNATURE', _count: { method: 50 } },
                { method: 'ERC1484', _count: { method: 30 } },
            ]);
            // Act
            const result = await service.getVerificationStats(filters);
            // Assert
            (0, globals_1.expect)(result).toEqual({
                totalVerifications: 100,
                successfulVerifications: 85,
                failedVerifications: 5,
                pendingVerifications: 10,
                verificationsByMethod: {
                    ETHEREUM_SIGNATURE: 50,
                    ERC1484: 30,
                },
                averageVerificationTime: 5000,
            });
            // Verify database calls
            (0, globals_1.expect)(mockPrisma.identityVerification.count).toHaveBeenCalledTimes(4);
            (0, globals_1.expect)(mockPrisma.identityVerification.groupBy).toHaveBeenCalledTimes(1);
        });
        (0, globals_1.it)('should handle zero verifications', async () => {
            // Arrange
            mockPrisma.identityVerification.count.mockResolvedValue(0);
            mockPrisma.identityVerification.groupBy.mockResolvedValue([]);
            // Act
            const result = await service.getVerificationStats();
            // Assert
            (0, globals_1.expect)(result.totalVerifications).toBe(0);
            (0, globals_1.expect)(result.successfulVerifications).toBe(0);
        });
    });
    (0, globals_1.describe)('Error Handling', () => {
        (0, globals_1.it)('should handle network errors gracefully', async () => {
            // Arrange
            const testData = {
                address: '******************************************',
                message: 'Test message',
                signature: '0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890ab1c',
                userId: 'user-123',
                merchantId: 'merchant-123',
            };
            mockPrisma.identityVerification.create.mockRejectedValue(new Error('Network error'));
            // Act & Assert
            const result = await service.verifyEthereumSignature(testData);
            (0, globals_1.expect)(result.success).toBe(false);
            (0, globals_1.expect)(result.error).toContain('Network error');
        });
        (0, globals_1.it)('should handle timeout errors', async () => {
            // Arrange
            const testData = {
                address: '******************************************',
                message: 'Test message',
                signature: '0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890ab1c',
                userId: 'user-123',
                merchantId: 'merchant-123',
            };
            mockPrisma.identityVerification.create.mockImplementation(() => new Promise((_, reject) => setTimeout(() => reject(new Error('Timeout')), 100)));
            // Act & Assert
            const result = await service.verifyEthereumSignature(testData);
            (0, globals_1.expect)(result.success).toBe(false);
        });
    });
    (0, globals_1.describe)('Input Validation', () => {
        (0, globals_1.it)('should validate required fields', async () => {
            // Test missing address
            const result1 = await service.verifyEthereumSignature({
                address: '',
                message: 'test',
                signature: '0xtest',
                userId: 'user-validation-123',
                merchantId: 'merchant-validation-123',
            });
            (0, globals_1.expect)(result1.success).toBe(false);
            (0, globals_1.expect)(result1.error).toContain('Address is required');
            // Test missing message
            const result2 = await service.verifyEthereumSignature({
                address: '******************************************',
                message: '',
                signature: '0xtest',
                userId: 'user-validation-123',
                merchantId: 'merchant-validation-123',
            });
            (0, globals_1.expect)(result2.success).toBe(false);
            (0, globals_1.expect)(result2.error).toContain('Message is required');
            // Test missing signature
            const result3 = await service.verifyEthereumSignature({
                address: '******************************************',
                message: 'test',
                signature: '',
                userId: 'user-validation-123',
                merchantId: 'merchant-validation-123',
            });
            (0, globals_1.expect)(result3.success).toBe(false);
            (0, globals_1.expect)(result3.error).toContain('Signature is required');
        });
        (0, globals_1.it)('should validate address format', async () => {
            const result = await service.verifyEthereumSignature({
                address: 'invalid-address',
                message: 'test',
                signature: '0xtest',
                userId: 'user-validation-123',
                merchantId: 'merchant-validation-123',
            });
            (0, globals_1.expect)(result.success).toBe(false);
            (0, globals_1.expect)(result.error).toContain('Invalid Ethereum address format');
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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