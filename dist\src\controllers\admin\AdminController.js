"use strict";
/**
 * Admin Controller
 *
 * Modular controller for admin operations.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminController = void 0;
const base_controller_1 = require("../base.controller");
const asyncHandler_1 = require("../../utils/asyncHandler");
const prisma = __importStar(require("../../lib/prisma"));
const AdminAuthorizationService_1 = require("./services/AdminAuthorizationService");
const AdminValidationService_1 = require("./services/AdminValidationService");
const AdminBusinessService_1 = require("./services/AdminBusinessService");
const AdminResponseMapper_1 = require("./mappers/AdminResponseMapper");
/**
 * Modular Admin Controller
 */
class AdminController extends base_controller_1.BaseController {
    constructor() {
        super();
        /**
         * Get dashboard data
         */
        this.getDashboardData = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Authorization
                const authContext = this.authService.createAuthorizationContext(req.user, 'dashboard', 'read');
                const permission = await this.authService.checkPermission(authContext);
                if (!permission.allowed) {
                    this.authService.handleAuthorizationError(permission);
                }
                // Business logic
                const dashboardData = await this.businessService.getDashboardData();
                // Response
                AdminResponseMapper_1.AdminResponseMapper.sendDashboardData(res, dashboardData);
            }
            catch (error) {
                AdminResponseMapper_1.AdminResponseMapper.sendError(res, error);
            }
        });
        /**
         * Get dashboard statistics
         */
        this.getDashboardStatistics = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Authorization
                const authContext = this.authService.createAuthorizationContext(req.user, 'dashboard', 'read');
                const permission = await this.authService.checkPermission(authContext);
                if (!permission.allowed) {
                    this.authService.handleAuthorizationError(permission);
                }
                // Business logic
                const statistics = await this.businessService.getDashboardStatistics();
                // Response
                AdminResponseMapper_1.AdminResponseMapper.sendDashboardStatistics(res, statistics);
            }
            catch (error) {
                AdminResponseMapper_1.AdminResponseMapper.sendError(res, error);
            }
        });
        /**
         * Get all admin users
         */
        this.getAdminUsers = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Authorization
                const authContext = this.authService.createAuthorizationContext(req.user, 'admin-users', 'read');
                const permission = await this.authService.checkPermission(authContext);
                if (!permission.allowed) {
                    this.authService.handleAuthorizationError(permission);
                }
                // Validation
                const pagination = this.validationService.validatePaginationParams(req.query);
                // Build filters
                const filters = {};
                if (req.query.status)
                    filters.status = req.query.status;
                if (req.query.roleId)
                    filters.roleId = req.query.roleId;
                if (req.query.search)
                    filters.search = req.query.search;
                if (req.query.dateFrom)
                    filters.dateFrom = new Date(req.query.dateFrom);
                if (req.query.dateTo)
                    filters.dateTo = new Date(req.query.dateTo);
                // Business logic
                const result = await this.businessService.getAdminUsers(filters, pagination);
                // Response
                AdminResponseMapper_1.AdminResponseMapper.sendAdminUsersList(res, result.users, result.total, pagination.page, pagination.limit);
            }
            catch (error) {
                AdminResponseMapper_1.AdminResponseMapper.sendError(res, error);
            }
        });
        /**
         * Get admin user by ID
         */
        this.getAdminUserById = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Authorization
                const authContext = this.authService.createAuthorizationContext(req.user, 'admin-users', 'read', req.params.id);
                const permission = await this.authService.checkPermission(authContext);
                if (!permission.allowed) {
                    this.authService.handleAuthorizationError(permission);
                }
                // Validation
                const userId = this.validationService.validateId(req.params.id, 'User ID');
                // Business logic
                const user = await this.businessService.getAdminUserById(userId);
                // Response
                AdminResponseMapper_1.AdminResponseMapper.sendAdminUser(res, user);
            }
            catch (error) {
                AdminResponseMapper_1.AdminResponseMapper.sendError(res, error);
            }
        });
        /**
         * Create admin user
         */
        this.createAdminUser = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Authorization
                const authContext = this.authService.createAuthorizationContext(req.user, 'admin-users', 'create');
                const permission = await this.authService.checkPermission(authContext);
                if (!permission.allowed) {
                    this.authService.handleAuthorizationError(permission);
                }
                // Validation
                const validatedData = this.validationService.validateCreateAdminUser(req.body);
                // Business logic
                const user = await this.businessService.createAdminUser(validatedData, req.user.id);
                // Response
                AdminResponseMapper_1.AdminResponseMapper.sendAdminUserCreated(res, user);
            }
            catch (error) {
                AdminResponseMapper_1.AdminResponseMapper.sendError(res, error);
            }
        });
        /**
         * Update admin user
         */
        this.updateAdminUser = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Authorization
                const authContext = this.authService.createAuthorizationContext(req.user, 'admin-users', 'update', req.params.id);
                const permission = await this.authService.checkPermission(authContext);
                if (!permission.allowed) {
                    this.authService.handleAuthorizationError(permission);
                }
                // Validation
                const userId = this.validationService.validateId(req.params.id, 'User ID');
                const validatedData = this.validationService.validateUpdateAdminUser(req.body);
                // Business logic
                const user = await this.businessService.updateAdminUser(userId, validatedData);
                // Response
                AdminResponseMapper_1.AdminResponseMapper.sendAdminUserUpdated(res, user);
            }
            catch (error) {
                AdminResponseMapper_1.AdminResponseMapper.sendError(res, error);
            }
        });
        /**
         * Delete admin user
         */
        this.deleteAdminUser = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Authorization
                const authContext = this.authService.createAuthorizationContext(req.user, 'admin-users', 'delete', req.params.id);
                const permission = await this.authService.checkPermission(authContext);
                if (!permission.allowed) {
                    this.authService.handleAuthorizationError(permission);
                }
                // Validation
                const userId = this.validationService.validateId(req.params.id, 'User ID');
                // Business logic
                await this.businessService.deleteAdminUser(userId);
                // Response
                AdminResponseMapper_1.AdminResponseMapper.sendAdminUserDeleted(res);
            }
            catch (error) {
                AdminResponseMapper_1.AdminResponseMapper.sendError(res, error);
            }
        });
        /**
         * Get system health
         */
        this.getSystemHealth = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Authorization
                const authContext = this.authService.createAuthorizationContext(req.user, 'system', 'read');
                const permission = await this.authService.checkPermission(authContext);
                if (!permission.allowed) {
                    this.authService.handleAuthorizationError(permission);
                }
                // Business logic - simple health check
                const health = {
                    status: 'healthy',
                    timestamp: new Date(),
                    services: {
                        database: 'connected',
                        cache: 'connected',
                        external: 'connected',
                    },
                    metrics: {
                        uptime: process.uptime(),
                        memoryUsage: process.memoryUsage().heapUsed / 1024 / 1024, // MB
                        cpuUsage: process.cpuUsage().user / 1000000, // seconds
                    },
                };
                // Response
                AdminResponseMapper_1.AdminResponseMapper.sendSystemHealth(res, health);
            }
            catch (error) {
                AdminResponseMapper_1.AdminResponseMapper.sendError(res, error);
            }
        });
        /**
         * Health check endpoint
         */
        this.healthCheck = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                AdminResponseMapper_1.AdminResponseMapper.sendSuccess(res, {
                    status: 'healthy',
                    timestamp: new Date(),
                    version: '1.0.0',
                    services: {
                        authorization: 'active',
                        validation: 'active',
                        business: 'active',
                        database: 'connected',
                    },
                }, 'Admin Controller is healthy');
            }
            catch (error) {
                AdminResponseMapper_1.AdminResponseMapper.sendError(res, error);
            }
        });
        this.authService = new AdminAuthorizationService_1.AdminAuthorizationService();
        this.validationService = new AdminValidationService_1.AdminValidationService();
        this.businessService = new AdminBusinessService_1.AdminBusinessService(prisma);
    }
}
exports.AdminController = AdminController;
//# sourceMappingURL=AdminController.js.map