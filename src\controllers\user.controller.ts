// jscpd:ignore-file
import { Request, Response, NextFunction } from 'express';
import { BaseController } from '../../core/BaseController';
import { UserService } from '../../services/refactored/user.service';
import { ErrorFactory } from '../../utils/errors/ErrorFactory';
import { logger } from '../../lib/logger';
import bcrypt from 'bcryptjs';
import { BaseController } from '../../core/BaseController';
import { UserService } from '../../services/refactored/user.service';
import { ErrorFactory } from '../../utils/errors/ErrorFactory';
import { logger } from '../../lib/logger';

/**
 * User controller
 * This controller handles user-related operations
 */
export class UserController extends BaseController {
  private userService: UserService;

  /**
   * Create a new user controller
   */
  constructor() {
    super();
    this.userService = new UserService();
  }

  /**
   * Get all users
   */
  getUsers = this.createHandler(async (req: Request, res: Response) => {
    // Check authorization
    const { userRole } = this.checkAuthorization(req);

    // Only admins can get all users
    this.checkAdminRole(userRole);

    // Parse pagination parameters
    const { limit, offset } = this.parsePagination(req);

    // Get users
    const result: unknown = await this.userService.getUsers({
      limit,
      offset,
    });

    // Send paginated response
    return this.sendPaginatedSuccess(res, result.data, result.total, limit, offset);
  });

  /**
   * Get a user by ID
   */
  getUser = this.createHandler(async (req: Request, res: Response) => {
    // Check authorization
    const { userRole, userId } = this.checkAuthorization(req);

    // Get user ID from params
    const { id } = req.params;

    // Check if user has permission to view this user
    if (userRole !== 'ADMIN' && userId !== id) {
      throw ErrorFactory.authorization('You do not have permission to view this user');
    }

    // Get user
    const user: unknown = await this.userService.getUserById(id);

    // Check if user exists
    if (!user) {
      throw ErrorFactory.notFound('User', id);
    }

    // Send success response
    return this.sendSuccess(res, user);
  });

  /**
   * Create a new user
   */
  createUser = this.createHandler(async (req: Request, res: Response) => {
    // Check authorization
    const { userRole } = this.checkAuthorization(req);

    // Only admins can create users
    this.checkAdminRole(userRole);

    // Get request body
    const { email, password, name, role, merchantId } = req.body;

    // Validate required fields
    if (!email || !password || !name) {
      throw ErrorFactory.validation('Email, password, and name are required');
    }

    // Check if email is valid
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      throw ErrorFactory.validation('Invalid email format');
    }

    // Check if password is strong enough
    if (password.length < 8) {
      throw ErrorFactory.validation('Password must be at least 8 characters long');
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create user
    const user = await this.userService.createUser({
      email,
      hashedPassword,
      name,
      role: role || 'USER',
      merchantId,
    });

    // Send success response
    return this.sendSuccess(res, user, 201);
  });

  /**
   * Update a user
   */
  updateUser = this.createHandler(async (req: Request, res: Response) => {
    // Check authorization
    const { userRole, userId } = this.checkAuthorization(req);

    // Get user ID from params
    const { id } = req.params;

    // Check if user has permission to update this user
    if (userRole !== 'ADMIN' && userId !== id) {
      throw ErrorFactory.authorization('You do not have permission to update this user');
    }

    // Get request body
    const { name, email, role, merchantId, password } = req.body;

    // Get user
    const user = await this.userService.getUserById(id);

    // Check if user exists
    if (!user) {
      throw ErrorFactory.notFound('User', id);
    }

    // Prepare update data
    const updateData: any = {};

    if (name) updateData.name = name;
    if (email) {
      // Check if email is valid
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        throw ErrorFactory.validation('Invalid email format');
      }
      updateData.email = email;
    }

    // Only admins can update role and merchantId
    if (userRole === 'ADMIN') {
      if (role) updateData.role = role;
      if (merchantId !== undefined) updateData.merchantId = merchantId;
    }

    // Update password if provided
    if (password) {
      // Check if password is strong enough
      if (password.length < 8) {
        throw ErrorFactory.validation('Password must be at least 8 characters long');
      }

      // Hash password
      updateData.hashedPassword = await bcrypt.hash(password, 10);
    }

    // Update user
    const updatedUser = await this.userService.updateUser(id, updateData);

    // Send success response
    return this.sendSuccess(res, updatedUser);
  });

  /**
   * Delete a user
   */
  deleteUser = this.createHandler(async (req: Request, res: Response) => {
    // Check authorization
    const { userRole } = this.checkAuthorization(req);

    // Only admins can delete users
    this.checkAdminRole(userRole);

    // Get user ID from params
    const { id } = req.params;

    // Delete user
    await this.userService.deleteUser(id);

    // Send success response
    return this.sendSuccess(res, { message: 'User deleted successfully' });
  });

  /**
   * Get current user
   */
  getCurrentUser = this.createHandler(async (req: Request, res: Response) => {
    // Check authorization
    const { userId } = this.checkAuthorization(req);

    // Get user
    const user = await this.userService.getUserById(userId);

    // Check if user exists
    if (!user) {
      throw ErrorFactory.notFound('User', userId);
    }

    // Send success response
    return this.sendSuccess(res, user);
  });

  /**
   * Update current user
   */
  updateCurrentUser = this.createHandler(async (req: Request, res: Response) => {
    // Check authorization
    const { userId } = this.checkAuthorization(req);

    // Get request body
    const { name, email, password } = req.body;

    // Prepare update data
    const updateData: any = {};

    if (name) updateData.name = name;
    if (email) {
      // Check if email is valid
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        throw ErrorFactory.validation('Invalid email format');
      }
      updateData.email = email;
    }

    // Update password if provided
    if (password) {
      // Check if password is strong enough
      if (password.length < 8) {
        throw ErrorFactory.validation('Password must be at least 8 characters long');
      }

      // Hash password
      updateData.hashedPassword = await bcrypt.hash(password, 10);
    }

    // Update user
    const updatedUser = await this.userService.updateUser(userId, updateData);

    // Send success response
    return this.sendSuccess(res, updatedUser);
  });
}
