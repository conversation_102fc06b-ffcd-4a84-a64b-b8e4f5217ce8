266b9e6687df560bbb08242005b7b05f
"use strict";
/**
 * Response Utilities
 *
 * This module provides utility functions for handling API responses.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.createApiResponse = exports.sendError = exports.sendSuccess = void 0;
/**
 * Send a success response
 */
const sendSuccess = (res, data = {}, message = 'Success', statusCode = 200) => {
    return res.status(statusCode).json({
        success: true,
        message,
        data
    });
};
exports.sendSuccess = sendSuccess;
/**
 * Send an error response
 */
const sendError = (res, message = 'Error', statusCode = 500, error = null) => {
    return res.status(statusCode).json({
        success: false,
        message,
        error: error ? (error.message || error) : null
    });
};
exports.sendError = sendError;
/**
 * Create a standard API response
 */
const createApiResponse = (success, message, data = null, error = null) => {
    return {
        success,
        message,
        data,
        error
    };
};
exports.createApiResponse = createApiResponse;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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