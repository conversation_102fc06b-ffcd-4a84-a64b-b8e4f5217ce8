// jscpd:ignore-file
import axios from 'axios';
import crypto from 'crypto';
import { logger } from '../utils/logger';
import { config } from '../config';
import { getEnvironment } from '../config/environment';
import { config } from '../config';
import { getEnvironment } from '../config/environment';
import { getApiKey, getApiSecret, getApiUrl, logServiceConfig } from '../utils/service-config';
import { Transaction } from '../types';
import { BinanceApiConfig, BinanceApiResponse, BinanceTransaction } from '@amazingpay/shared';
import { getApiKey, getApiSecret, getApiUrl, logServiceConfig } from '../utils/service-config';
import { Transaction } from '../types';

import { BinanceApiConfig, BinanceApiResponse, BinanceTransaction } from '@amazingpay/shared';

/**
 * Service for interacting with the Binance API
 */
export class BinanceApiService {
  private apiKey: string;
  private apiSecret: string;
  private baseUrl: string;

  /**
   * Create a new BinanceApiService instance
   * @param apiConfig Binance API configuration
   */
  constructor(apiConfig?: BinanceApiConfig) {
    // Get environment-specific configuration
    const env: unknown = getEnvironment();

    // Use provided config, environment-specific config, or default
    this.apiKey = apiConfig?.apiKey ?? getApiKey('binance', config.binance?.apiKey ?? '');
    this.apiSecret =
      apiConfig?.secretKey ?? getApiSecret('binance', config.binance?.secretKey ?? '');
    this.baseUrl =
      apiConfig?.baseUrl ||
      getApiUrl('binance', config.binance?.apiUrl ?? 'https://api.binance.com');

    // Log service configuration (masked)
    logServiceConfig('binance');

    logger.debug(
      `Initialized Binance API service for ${env} environment with URL: ${this.baseUrl}`
    );
  }

  /**
   * Create a BinanceApiService instance from merchant configuration
   * @param apiKey Binance API key
   * @param secretKey Binance API secret
   * @returns BinanceApiService instance
   */
  static createFromMerchantConfig(apiKey: string, secretKey: string): BinanceApiService {
    return new BinanceApiService({
      apiKey,
      secretKey,
    });
  }

  /**
   * Generate signature for Binance API requests
   * @param queryString Query string to sign
   * @returns Signature
   */
  private async makeRequest<T>(
    endpoint: string,
    method: 'GET' | 'POST' = 'GET',
    params: Record<string, any> = {}
  ): Promise<T> {
    try {
      // Add timestamp to params
      const timestamp: unknown = Date.now();
      params.timestamp = timestamp;

      // Convert params to query string
      const queryString: unknown = Object.entries(params)
        .map(([key, value]) => `${key}=${value}`)
        .join('&');

      // Generate signature
      const signature: unknown = this.generateSignature(queryString);

      // Add signature to query string
      const queryStringWithSignature: unknown = `${queryString}&signature=${signature}`;

      // Make request
      const url: unknown = `${this.baseUrl}${endpoint}?${queryStringWithSignature}`;

      logger.debug(`Making ${method} request to ${url}`);

      const response: any = await axios({
        method,
        url,
        headers: this.createHeaders(),
      });

      return response.data;
    } catch (error) {
      logger.error(`Binance API error: ${error}`);
      throw error;
    }
  }

  /**
   * Test API connection
   * @returns Connection status
   */
  async testConnection(): Promise<BinanceApiResponse<{ success: boolean }>> {
    try {
      await this.makeRequest('/api/v3/ping');
      return {
        success: true,
        data: { success: true },
        message: 'Connection successful',
      };
    } catch (error) {
      logger.error('Error testing Binance API connection', { error });
      return {
        success: false,
        error: error instanceof Error ? (error as Error).message : 'Unknown error',
        message: 'Connection failed',
      };
    }
  }

  /**
   * Get account information
   * @returns Account information
   */
  async getAccountInfo(): Promise<BinanceApiResponse<any>> {
    try {
      const data: any = await this.makeRequest('/api/v3/account');
      return {
        success: true,
        data,
      };
    } catch (error) {
      logger.error('Error getting account information from Binance API', { error });
      return {
        success: false,
        error: error instanceof Error ? (error as Error).message : 'Unknown error',
        message: 'Failed to get account information',
      };
    }
  }

  /**
   * Get deposit history
   * @param coin Coin symbol (e.g., USDT)
   * @param network Network (e.g., TRC20)
   * @param startTime Start time in milliseconds
   * @param endTime End time in milliseconds
   * @param status Deposit status (0: pending, 1: success, etc.)
   * @returns Deposit history
   */
  async getDepositHistory(
    coin?: string,
    network?: string,
    startTime?: number,
    endTime?: number,
    status?: number
  ): Promise<BinanceApiResponse<BinanceTransaction[]>> {
    try {
      const params: Record<string, any> = {};

      if (coin) params.coin = coin;
      if (network) params.network = network;
      if (startTime) params.startTime = startTime;
      if (endTime) params.endTime = endTime;
      if (status !== undefined) params.status = status;

      const data: any = await this.makeRequest<BinanceTransaction[]>(
        '/sapi/v1/capital/deposit/hisrec',
        'GET',
        params
      );
      return {
        success: true,
        data,
      };
    } catch (error) {
      logger.error('Error getting deposit history from Binance API', { error });
      return {
        success: false,
        error: error instanceof Error ? (error as Error).message : 'Unknown error',
        message: 'Failed to get deposit history',
      };
    }
  }

  /**
   * Verify a TRC20 transaction
   * @param walletAddress Wallet address
   * @param amount Expected amount
   * @param txHash Transaction hash (optional)
   * @param coin Coin symbol (e.g., USDT)
   * @returns Verification result
   */
  async verifyTRC20Transaction(
    walletAddress: string,
    amount: number,
    txHash?: string,
    coin: string = 'USDT'
  ): Promise<
    BinanceApiResponse<{
      verified: boolean;
      transaction?: BinanceTransaction;
    }>
  > {
    try {
      logger.info(
        `Verifying TRC20 transaction for wallet: ${walletAddress}, amount: ${amount}, txHash: ${
          txHash ?? 'not provided'
        }`
      );

      // Get deposit history for the last 24 hours
      const endTime: unknown = Date.now();
      const startTime: unknown = endTime - 24 * 60 * 60 * 1000; // 24 hours ago

      const depositsResponse: unknown = await this.getDepositHistory(
        coin,
        'TRC20',
        startTime,
        endTime,
        1
      ); // Status 1 = success

      if (!depositsResponse.success || !depositsResponse.data) {
        return {
          success: false,
          error: depositsResponse.error ?? 'Failed to get deposit history',
          message: 'Failed to verify transaction: could not retrieve deposit history',
        };
      }

      const deposits: unknown = depositsResponse.data;
      logger.debug(`Found ${deposits.length} deposits for ${coin} on TRC20 network`);

      // If txHash is provided, find the specific transaction
      if (txHash) {
        const matchingDeposit: unknown = deposits.find(
          (deposit) =>
            deposit.txId === txHash &&
            deposit.address === walletAddress &&
            Math.abs(deposit.amount - amount) < 0.01 // Allow small difference due to fees
        );

        if (matchingDeposit) {
          logger.info(`Found matching deposit for txHash: ${txHash}`);
          return {
            success: true,
            data: { verified: true, transaction: matchingDeposit },
            message: 'Transaction verified successfully',
          };
        } else {
          logger.warn(`No matching deposit found for txHash: ${txHash}`);
          return {
            success: false,
            message: 'Transaction not found or details do not match',
          };
        }
      } else {
        // If no txHash is provided, find unknown transaction with matching amount and address
        const matchingDeposits: unknown = deposits.filter(
          (deposit) => deposit.address === walletAddress && Math.abs(deposit.amount - amount) < 0.01
        );

        if (matchingDeposits.length > 0) {
          // Sort by timestamp (newest first) and take the first one
          matchingDeposits.sort((a, b) => b.insertTime - a.insertTime);
          const latestDeposit: unknown = matchingDeposits[0];

          logger.info(`Found matching deposit without txHash: ${latestDeposit.txId}`);
          return {
            success: true,
            data: { verified: true, transaction: latestDeposit },
            message: 'Transaction verified successfully',
          };
        } else {
          logger.warn(
            `No matching deposit found for address: ${walletAddress} and amount: ${amount}`
          );
          return {
            success: false,
            message: 'No matching transaction found',
          };
        }
      }
    } catch (error) {
      logger.error('Error verifying TRC20 transaction', { error, walletAddress, amount, txHash });
      return {
        success: false,
        error: error instanceof Error ? (error as Error).message : 'Unknown error',
        message: 'Failed to verify transaction due to an error',
      };
    }
  }
}
