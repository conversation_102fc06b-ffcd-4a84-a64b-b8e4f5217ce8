import express from 'express';
import { AdvancedReportController } from '../controllers/advanced-report.controller';
import { authMiddleware } from '../middlewares/auth.middleware';

const router = express.Router();
const reportController = new AdvancedReportController();

// Apply authentication middleware to all routes
router.use(authMiddleware);

// Report generation
router.post('/generate', reportController.generateReport);

// Report templates
router.get('/templates', reportController.getReportTemplates);
router.get('/templates/:id', reportController.getReportTemplateById);
router.post('/templates', reportController.createReportTemplate);
router.put('/templates/:id', reportController.updateReportTemplate);
router.delete('/templates/:id', reportController.deleteReportTemplate);

// Scheduled reports
router.get('/scheduled', reportController.getScheduledReports);
router.get('/scheduled/:id', reportController.getScheduledReportById);
router.post('/scheduled', reportController.createScheduledReport);
router.put('/scheduled/:id', reportController.updateScheduledReport);
router.delete('/scheduled/:id', reportController.deleteScheduledReport);
router.post('/scheduled/:id/run', reportController.runScheduledReport);

// Saved reports
router.get('/saved', reportController.getSavedReports);
router.get('/saved/:id', reportController.getSavedReportById);
router.get('/saved/:id/download', reportController.downloadSavedReport);
router.delete('/saved/:id', reportController.deleteSavedReport);

export default router;
