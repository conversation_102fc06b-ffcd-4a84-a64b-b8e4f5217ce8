/**
 * Identity Verification Authorization Service
 *
 * Handles authorization logic for identity verification operations.
 */
import { AuthorizationContext, PermissionResult } from '../types/IdentityVerificationControllerTypes';
/**
 * Authorization service for identity verification
 */
export declare class IdentityVerificationAuthService {
    private readonly adminRoles;
    private readonly merchantRoles;
    private readonly userRoles;
    /**
     * Check if user is authorized for the given action
     */
    checkPermission(context: AuthorizationContext): Promise<PermissionResult>;
    /**
     * Check role-based permissions
     */
    private checkRolePermission;
    /**
     * Check verification permissions
     */
    private checkVerificationPermission;
    /**
     * Check statistics permissions
     */
    private checkStatsPermission;
    /**
     * Check admin permissions
     */
    private checkAdminPermission;
    /**
     * Check resource-specific permissions
     */
    private checkResourcePermission;
    /**
     * Require admin role
     */
    requireAdmin(userRole?: string): void;
    /**
     * Require merchant role or higher
     */
    requireMerchant(userRole?: string): void;
    /**
     * Require authenticated user
     */
    requireAuthenticated(userRole?: string): void;
    /**
     * Check if user has specific role
     */
    hasRole(userRole: string, requiredRole: string): boolean;
    /**
     * Get user permissions for a resource
     */
    getUserPermissions(userRole: string, resource: string): string[];
    /**
     * Validate authorization context
     */
    validateAuthorizationContext(context: AuthorizationContext): void;
    /**
     * Create authorization context from request
     */
    createAuthorizationContext(user: any, resource: string, action: string, resourceId?: string): AuthorizationContext;
    /**
     * Handle authorization error
     */
    handleAuthorizationError(result: PermissionResult): never;
    /**
     * Check if user can access verification
     */
    canAccessVerification(user: any, verification: any): boolean;
    /**
     * Extract user context from request
     */
    extractUserContext(req: any): {
        userId?: string;
        merchantId?: string;
    };
}
//# sourceMappingURL=IdentityVerificationAuthService.d.ts.map