{"file": "F:\\Amazing pay flow\\src\\utils\\app-error.ts", "mappings": ";;;AAAA,oBAAoB;AACpB;;;GAGG;AACH,MAAa,QAAS,SAAQ,KAAK;IAM/B,YACI,OAAe,EACf,aAAqB,GAAG,EACxB,OAAe,uBAAuB,EACtC,gBAAyB,IAAI,EAC7B,OAAQ;QAER,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QAEvB,sBAAsB;QACtB,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QAEhD,+BAA+B;QAC/B,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC;IACpD,CAAC;CACJ;AAzBD,4BAyBC;AAED;;GAEG;AACI,MAAM,qBAAqB,GAAO,CAAC,OAAe,EAAE,OAAe,aAAa,EAAE,OAAQ,EAAE,EAAE;IACjG,OAAO,IAAI,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;AAC3D,CAAC,CAAC;AAFW,QAAA,qBAAqB,yBAEhC;AAEK,MAAM,uBAAuB,GAAO,CAAC,OAAe,EAAE,OAAe,cAAc,EAAE,OAAQ,EAAE,EAAE;IACpG,OAAO,IAAI,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;AAC3D,CAAC,CAAC;AAFW,QAAA,uBAAuB,2BAElC;AAEK,MAAM,oBAAoB,GAAO,CAAC,OAAe,EAAE,OAAe,WAAW,EAAE,OAAQ,EAAE,EAAE;IAC9F,OAAO,IAAI,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;AAC3D,CAAC,CAAC;AAFW,QAAA,oBAAoB,wBAE/B;AAEK,MAAM,mBAAmB,GAAO,CAAC,OAAe,EAAE,OAAe,WAAW,EAAE,OAAQ,EAAE,EAAE;IAC7F,OAAO,IAAI,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;AAC3D,CAAC,CAAC;AAFW,QAAA,mBAAmB,uBAE9B;AAEK,MAAM,mBAAmB,GAAO,CAAC,OAAe,EAAE,OAAe,UAAU,EAAE,OAAQ,EAAE,EAAE;IAC5F,OAAO,IAAI,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;AAC3D,CAAC,CAAC;AAFW,QAAA,mBAAmB,uBAE9B;AAEK,MAAM,yBAAyB,GAAO,CAAC,OAAe,EAAE,OAAe,uBAAuB,EAAE,OAAQ,EAAE,EAAE;IAC/G,OAAO,IAAI,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;AAC3D,CAAC,CAAC;AAFW,QAAA,yBAAyB,6BAEpC;AAEK,MAAM,6BAA6B,GAAO,CAAC,OAAe,EAAE,OAAe,qBAAqB,EAAE,OAAQ,EAAE,EAAE;IACjH,OAAO,IAAI,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;AAC3D,CAAC,CAAC;AAFW,QAAA,6BAA6B,iCAExC", "names": [], "sources": ["F:\\Amazing pay flow\\src\\utils\\app-error.ts"], "sourcesContent": ["// jscpd:ignore-file\n/**\n * Custom application error class\n * Used for standardized error handling throughout the application\n */\nexport class AppError extends Error {\n    public readonly statusCode: number;\n    public readonly isOperational: boolean;\n    public readonly code: string;\n    public readonly details?;\n\n    constructor(\n        message: string,\n        statusCode: number = 500,\n        code: string = \"INTERNAL_SERVER_ERROR\",\n        isOperational: boolean = true,\n        details?\n    ) {\n        super(message);\n        this.statusCode = statusCode;\n        this.isOperational = isOperational;\n        this.code = code;\n        this.details = details;\n    \n        // Capture stack trace\n        Error.captureStackTrace(this, this.constructor);\n    \n        // Set the prototype explicitly\n        Object.setPrototypeOf(this, AppError.prototype);\n    }\n}\n\n/**\n * Factory methods for common error types\n */\nexport const createBadRequestError: any =(message: string, code: string = \"BAD_REQUEST\", details?) => {\n    return new AppError(message, 400, code, true, details);\n};\n\nexport const createUnauthorizedError: any =(message: string, code: string = \"UNAUTHORIZED\", details?) => {\n    return new AppError(message, 401, code, true, details);\n};\n\nexport const createForbiddenError: any =(message: string, code: string = \"FORBIDDEN\", details?) => {\n    return new AppError(message, 403, code, true, details);\n};\n\nexport const createNotFoundError: any =(message: string, code: string = \"NOT_FOUND\", details?) => {\n    return new AppError(message, 404, code, true, details);\n};\n\nexport const createConflictError: any =(message: string, code: string = \"CONFLICT\", details?) => {\n    return new AppError(message, 409, code, true, details);\n};\n\nexport const createInternalServerError: any =(message: string, code: string = \"INTERNAL_SERVER_ERROR\", details?) => {\n    return new AppError(message, 500, code, true, details);\n};\n\nexport const createServiceUnavailableError: any =(message: string, code: string = \"SERVICE_UNAVAILABLE\", details?) => {\n    return new AppError(message, 503, code, true, details);\n};\n"], "version": 3}