import { BaseController } from "./base.controller";
declare global {
    namespace Express {
        interface Request {
            user?: {
                id: string;
                role: string;
                merchantId?: string;
            };
        }
    }
}
/**
 * AlertAnalyticsController
 */
export declare class AlertAnalyticsController extends BaseController {
    constructor();
    /**
     * Get alert count by status
     */
    getAlertCountByStatus: any;
    /**
     * Get alert count by severity
     */
    getAlertCountBySeverity: any;
    /**
     * Get alert count by type
     */
    getAlertCountByType: any;
    /**
     * Get alert count by day
     */
    getAlertCountByDay: any;
    /**
     * Get alert count by hour
     */
    getAlertCountByHour: any;
    /**
     * Get top merchants by alert count
     */
    getTopMerchantsByAlertCount: any;
    /**
     * Get alert resolution time statistics
     */
    getAlertResolutionTimeStats: any;
    /**
     * Get alert trends
     */
    getAlertTrends: any;
    /**
     * Helper method to check admin role
     */
    private checkAdminRole;
    /**
     * Helper method to parse date range
     */
    private parseDateRange;
    /**
     * Helper method to determine target merchant ID
     */
    private determineTargetMerchantId;
    /**
     * Helper method to check authorization
     */
    private checkAuthorization;
}
declare const _default: AlertAnalyticsController;
export default _default;
//# sourceMappingURL=alert-analytics.controller.d.ts.map