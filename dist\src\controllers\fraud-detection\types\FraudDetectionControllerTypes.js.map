{"version": 3, "file": "FraudDetectionControllerTypes.js", "sourceRoot": "", "sources": ["../../../../../src/controllers/fraud-detection/types/FraudDetectionControllerTypes.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;AAuIH;;GAEG;AACH,IAAY,SAKX;AALD,WAAY,SAAS;IACnB,wBAAW,CAAA;IACX,8BAAiB,CAAA;IACjB,0BAAa,CAAA;IACb,kCAAqB,CAAA;AACvB,CAAC,EALW,SAAS,yBAAT,SAAS,QAKpB;AAiLD;;GAEG;AACH,IAAY,oBAOX;AAPD,WAAY,oBAAoB;IAC9B,mDAA2B,CAAA;IAC3B,2DAAmC,CAAA;IACnC,mDAA2B,CAAA;IAC3B,uDAA+B,CAAA;IAC/B,qDAA6B,CAAA;IAC7B,2DAAmC,CAAA;AACrC,CAAC,EAPW,oBAAoB,oCAApB,oBAAoB,QAO/B;AAED;;GAEG;AACH,IAAY,sBAKX;AALD,WAAY,sBAAsB;IAChC,6DAAmC,CAAA;IACnC,uDAA6B,CAAA;IAC7B,uEAA6C,CAAA;IAC7C,+DAAqC,CAAA;AACvC,CAAC,EALW,sBAAsB,sCAAtB,sBAAsB,QAKjC"}