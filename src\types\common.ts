/**
 * Common type definitions for AmazingPay Flow
 * This file contains shared types used across the application
 */

import { Request, Response, NextFunction } from 'express';
import { PrismaClient } from '@prisma/client';

// ============================================================================
// EXPRESS EXTENSIONS
// ============================================================================

/**
 * Extended Express Request with user information
 */
export interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    email: string;
    role: string;
    firstName?: string;
    lastName?: string;
    isActive: boolean;
  };
  apiVersion?: string;
  requestId?: string;
  startTime?: number;
}

/**
 * Extended Express Response with additional methods
 */
export interface ExtendedResponse extends Response {
  success: (data?: unknown, message?: string) => Response;
  error: (message: string, statusCode?: number, details?: any) => Response;
  paginated: (data: any[], pagination: PaginationInfo) => Response;
}

// ============================================================================
// API RESPONSE TYPES
// ============================================================================

/**
 * Standard API response format
 */
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: {
    message: string;
    code: string;
    type: string;
    details?: unknown;
  };
  timestamp: Date;
  requestId?: string;
}

/**
 * Service result wrapper
 */
export interface ServiceResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  code?: string;
  details?: unknown;
}

/**
 * Pagination information
 */
export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

/**
 * Paginated response
 */
export interface PaginatedResponse<T = any> {
  data: T[];
  pagination: PaginationInfo;
}

// ============================================================================
// DATABASE TYPES
// ============================================================================

/**
 * Prisma transaction client type
 */
export type PrismaTransactionClient = Parameters<Parameters<PrismaClient['$transaction']>[0]>[0];

/**
 * Database filter options
 */
export interface DatabaseFilters {
  [key: string]: unknown;
}

/**
 * Sort options
 */
export interface SortOptions {
  field: string;
  direction: 'asc' | 'desc';
}

// ============================================================================
// BUSINESS DOMAIN TYPES
// ============================================================================

/**
 * Payment data structure
 */
export interface PaymentData {
  id: string;
  amount: number;
  currency: string;
  status: string;
  paymentMethod: string;
  merchantId: string;
  customerId?: string;
  reference?: string;
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Transaction data structure
 */
export interface TransactionData {
  id: string;
  reference: string;
  amount: number;
  currency: string;
  status: string;
  type: string;
  paymentMethodId?: string;
  merchantId: string;
  customerId?: string;
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * User data structure
 */
export interface UserData {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  role: string;
  isActive: boolean;
  lastLoginAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Merchant data structure
 */
export interface MerchantData {
  id: string;
  name: string;
  email: string;
  status: string;
  businessType?: string;
  country?: string;
  settings?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

// ============================================================================
// VERIFICATION TYPES
// ============================================================================

/**
 * Verification data structure
 */
export interface VerificationData {
  id: string;
  type: string;
  status: string;
  data: Record<string, any>;
  result?: Record<string, any>;
  merchantId?: string;
  customerId?: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Identity verification data
 */
export interface IdentityData {
  id: string;
  type: string;
  status: string;
  data: Record<string, any>;
  verifiedAt?: Date;
  expiresAt?: Date;
  createdAt: Date;
}

/**
 * Document verification data
 */
export interface DocumentData {
  id: string;
  type: string;
  status: string;
  url: string;
  metadata?: Record<string, any>;
  verifiedAt?: Date;
  createdAt: Date;
}

// ============================================================================
// CONFIGURATION TYPES
// ============================================================================

/**
 * Application configuration
 */
export interface AppConfig {
  port: number;
  nodeEnv: string;
  database: DatabaseConfig;
  redis?: RedisConfig;
  security: SecurityConfig;
  logging: LoggingConfig;
}

/**
 * Database configuration
 */
export interface DatabaseConfig {
  url: string;
  maxConnections?: number;
  timeout?: number;
  ssl?: boolean;
}

/**
 * Redis configuration
 */
export interface RedisConfig {
  url: string;
  maxRetries?: number;
  retryDelay?: number;
}

/**
 * Security configuration
 */
export interface SecurityConfig {
  jwtSecret: string;
  jwtExpiresIn: string;
  bcryptRounds: number;
  rateLimiting: RateLimitConfig;
}

/**
 * Rate limiting configuration
 */
export interface RateLimitConfig {
  windowMs: number;
  maxRequests: number;
  skipSuccessfulRequests?: boolean;
}

/**
 * Logging configuration
 */
export interface LoggingConfig {
  level: string;
  format: string;
  enableConsole: boolean;
  enableFile: boolean;
  filePath?: string;
}

// ============================================================================
// UTILITY TYPES
// ============================================================================

/**
 * Generic key-value object
 */
export type KeyValueObject = Record<string, any>;

/**
 * String key-value object
 */
export type StringKeyValueObject = Record<string, string>;

/**
 * Async function type
 */
export type AsyncFunction<T = any> = (...args: any[]) => Promise<T>;

/**
 * Express middleware function
 */
export type MiddlewareFunction = (req: Request, res: Response, next: NextFunction) => void | Promise<void>;

/**
 * Express error middleware function
 */
export type ErrorMiddlewareFunction = (error: Error, req: Request, res: Response, next: NextFunction) => void | Promise<void>;

/**
 * Event handler function
 */
export type EventHandler<T = any> = (data: T) => void | Promise<void>;

/**
 * Validation result
 */
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings?: string[];
}

/**
 * Cache entry
 */
export interface CacheEntry<T = any> {
  value: T;
  expiresAt: Date;
  createdAt: Date;
}

/**
 * Health check result
 */
export interface HealthCheckResult {
  status: 'healthy' | 'unhealthy' | 'degraded';
  checks: Record<string, {
    status: 'pass' | 'fail' | 'warn';
    message?: string;
    duration?: number;
  }>;
  timestamp: Date;
}
