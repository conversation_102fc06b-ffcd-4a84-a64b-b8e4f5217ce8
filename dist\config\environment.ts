// jscpd:ignore-file
import dotenv from "dotenv";
import fs from "fs";
import path from "path";

// Determine which environment file to load
const getEnvFile: any =(): string => {
    // Always use production environment
    process.env.NODE_ENV = "production";
    const envFile: any =`.env.production`;

    // Check if environment-specific file exists
    if (fs.existsSync(path.resolve(process.cwd(), envFile))) {
        return envFile;
    }

    // Fall back to .env
    return ".env";
};

// Load environment variables
export const loadEnvironment: any =(): void => {
    const envFile = getEnvFile();
    console.log(`Loading environment from ${envFile}`);

    const result: any =dotenv.config({ path: envFile });

    if (result.error) {
        console.warn(`Error loading ${envFile}: ${(result.error as Error).message}`);
        console.warn("Falling back to .env");
        dotenv.config();
    }
};

// Get environment name
export const getEnvironment: any =(): string => {
    // Always return production
    return "production";
};

// Check if current environment is production
export const isProduction: any =(): boolean => {
    // Always return true to force production mode
    return true;
};

// Check if current environment is demo
export const isDemo: any =(): boolean => {
    // Always return false to disable demo mode
    return false;
};

// Check if current environment is development
export const isDevelopment: any =(): boolean => {
    // Always return false to disable development mode
    return false;
};

// Check if current environment is test
export const isTest: any =(): boolean => {
    // Always return false to disable test mode
    return false;
};

// Export environment variables
export default {
    environment: getEnvironment(),
    isProduction: isProduction(),
    isDemo: isDemo(),
    isDevelopment: isDevelopment(),
    isTest: isTest()
};
