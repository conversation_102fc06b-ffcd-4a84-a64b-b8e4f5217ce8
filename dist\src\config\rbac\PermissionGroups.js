"use strict";
// jscpd:ignore-file
/**
 * Permission Groups
 *
 * Defines groups of related permissions for easier management.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ALL_PERMISSIONS = exports.ADMIN_USER_PERMISSIONS = exports.ROLE_PERMISSIONS = exports.NOTIFICATION_PERMISSIONS = exports.SETTINGS_PERMISSIONS = exports.SECURITY_PERMISSIONS = exports.MONITORING_PERMISSIONS = exports.ANALYTICS_PERMISSIONS = exports.SUBSCRIPTION_PERMISSIONS = exports.VERIFICATION_PERMISSIONS = exports.PAYMENT_PERMISSIONS = exports.MERCHANT_PERMISSIONS = exports.ADMIN_PERMISSIONS = void 0;
/**
 * Permission group for admin access
 */
exports.ADMIN_PERMISSIONS = [
    "admin:access"
];
/**
 * Permission group for merchant management
 */
exports.MERCHANT_PERMISSIONS = {
    READ: [
        "merchants:view"
    ],
    WRITE: [
        "merchants:create",
        "merchants:update",
        "merchants:delete"
    ],
    APPROVE: [
        "merchant_approvals:view",
        "merchant_approvals:approve",
        "merchant_approvals:reject"
    ],
    ALL: [
        "merchants:view",
        "merchants:create",
        "merchants:update",
        "merchants:delete",
        "merchant_approvals:view",
        "merchant_approvals:approve",
        "merchant_approvals:reject",
        "merchant_segmentation:view",
        "merchant_support:view",
        "merchant_onboarding:view",
        "merchant_onboarding:update"
    ]
};
/**
 * Permission group for payment management
 */
exports.PAYMENT_PERMISSIONS = {
    READ: [
        "payments:view"
    ],
    WRITE: [
        "payments:process",
        "payments:refund",
        "payments:cancel"
    ],
    METHODS: [
        "payment_methods:view",
        "payment_methods:create",
        "payment_methods:update",
        "payment_methods:delete"
    ],
    GATEWAYS: [
        "payment_gateways:view",
        "payment_gateways:create",
        "payment_gateways:update",
        "payment_gateways:delete"
    ],
    ROUTING: [
        "payment_routing:view",
        "payment_routing:update"
    ],
    FEES: [
        "fees:view",
        "fees:update"
    ],
    ALL: [
        "payments:view",
        "payments:process",
        "payments:refund",
        "payments:cancel",
        "payment_methods:view",
        "payment_methods:create",
        "payment_methods:update",
        "payment_methods:delete",
        "payment_gateways:view",
        "payment_gateways:create",
        "payment_gateways:update",
        "payment_gateways:delete",
        "payment_routing:view",
        "payment_routing:update",
        "fees:view",
        "fees:update"
    ]
};
/**
 * Permission group for verification management
 */
exports.VERIFICATION_PERMISSIONS = {
    READ: [
        "verification_methods:view",
        "verification_monitoring:view"
    ],
    WRITE: [
        "verification_methods:create",
        "verification_methods:update",
        "verification_methods:delete"
    ],
    ALL: [
        "verification_methods:view",
        "verification_methods:create",
        "verification_methods:update",
        "verification_methods:delete",
        "verification_monitoring:view"
    ]
};
/**
 * Permission group for subscription management
 */
exports.SUBSCRIPTION_PERMISSIONS = {
    READ: [
        "subscription_plans:view"
    ],
    WRITE: [
        "subscription_plans:create",
        "subscription_plans:update",
        "subscription_plans:delete"
    ],
    ALL: [
        "subscription_plans:view",
        "subscription_plans:create",
        "subscription_plans:update",
        "subscription_plans:delete"
    ]
};
/**
 * Permission group for analytics
 */
exports.ANALYTICS_PERMISSIONS = {
    READ: [
        "analytics:view"
    ],
    WRITE: [
        "analytics:export",
        "analytics:configure"
    ],
    ALL: [
        "analytics:view",
        "analytics:export",
        "analytics:configure"
    ]
};
/**
 * Permission group for monitoring
 */
exports.MONITORING_PERMISSIONS = {
    READ: [
        "monitoring:view"
    ],
    WRITE: [
        "monitoring:configure"
    ],
    ALERTS: [
        "alerts:view",
        "alerts:create",
        "alerts:update",
        "alerts:delete"
    ],
    ALL: [
        "monitoring:view",
        "monitoring:configure",
        "alerts:view",
        "alerts:create",
        "alerts:update",
        "alerts:delete",
        "alert_notifications:view",
        "alert_notifications:update"
    ]
};
/**
 * Permission group for security management
 */
exports.SECURITY_PERMISSIONS = {
    READ: [
        "security:view"
    ],
    WRITE: [
        "security:update"
    ],
    ALL: [
        "security:view",
        "security:update"
    ]
};
/**
 * Permission group for settings management
 */
exports.SETTINGS_PERMISSIONS = {
    READ: [
        "settings:view"
    ],
    WRITE: [
        "settings:update"
    ],
    ALL: [
        "settings:view",
        "settings:update"
    ]
};
/**
 * Permission group for notification management
 */
exports.NOTIFICATION_PERMISSIONS = {
    READ: [
        "notifications:view"
    ],
    WRITE: [
        "notifications:create",
        "notifications:update",
        "notifications:delete"
    ],
    ALL: [
        "notifications:view",
        "notifications:create",
        "notifications:update",
        "notifications:delete"
    ]
};
/**
 * Permission group for role management
 */
exports.ROLE_PERMISSIONS = {
    READ: [
        "roles:view"
    ],
    WRITE: [
        "roles:create",
        "roles:update",
        "roles:delete"
    ],
    ALL: [
        "roles:view",
        "roles:create",
        "roles:update",
        "roles:delete"
    ]
};
/**
 * Permission group for admin user management
 */
exports.ADMIN_USER_PERMISSIONS = {
    READ: [
        "admin_users:view"
    ],
    WRITE: [
        "admin_users:create",
        "admin_users:update",
        "admin_users:delete"
    ],
    ALL: [
        "admin_users:view",
        "admin_users:create",
        "admin_users:update",
        "admin_users:delete"
    ]
};
/**
 * All permissions
 */
exports.ALL_PERMISSIONS = [
    ...exports.ADMIN_PERMISSIONS,
    ...exports.MERCHANT_PERMISSIONS.ALL,
    ...exports.PAYMENT_PERMISSIONS.ALL,
    ...exports.VERIFICATION_PERMISSIONS.ALL,
    ...exports.SUBSCRIPTION_PERMISSIONS.ALL,
    ...exports.ANALYTICS_PERMISSIONS.ALL,
    ...exports.MONITORING_PERMISSIONS.ALL,
    ...exports.SECURITY_PERMISSIONS.ALL,
    ...exports.SETTINGS_PERMISSIONS.ALL,
    ...exports.NOTIFICATION_PERMISSIONS.ALL,
    ...exports.ROLE_PERMISSIONS.ALL,
    ...exports.ADMIN_USER_PERMISSIONS.ALL
];
//# sourceMappingURL=PermissionGroups.js.map