"use strict";
// jscpd:ignore-file
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const subscription_service_1 = __importDefault(require("../services/subscription.service"));
class SubscriptionPlansController {
    async getAllPlans(req, res) {
        try {
            const plans = await subscription_service_1.default.getAllPlans();
            return res.status(200).json({
                status: "success",
                data: plans
            });
        }
        catch (error) {
            return res.status(500).json({
                status: "error",
                message: error.message || "Failed to retrieve subscription plans"
            });
        }
    }
    async getPlanById(req, res) {
        try {
            const { id } = req.params;
            const plan = await subscription_service_1.default.getPlanById(id);
            if (!plan) {
                return res.status(404).json({
                    status: "error",
                    message: "Subscription plan not found"
                });
            }
            return res.status(200).json({
                status: "success",
                data: plan
            });
        }
        catch (error) {
            return res.status(500).json({
                status: "error",
                message: error.message || "Failed to retrieve subscription plan"
            });
        }
    }
    async createPlan(req, res) {
        try {
            const { name, duration, maxPayments, maxMethods, price, features } = req.body;
            // Basic validation
            if (!name || !duration || !price) {
                return res.status(400).json({
                    status: "error",
                    message: "Name, duration, and price are required"
                });
            }
            const newPlan = await subscription_service_1.default.createPlan({
                name,
                duration,
                maxPayments: maxPayments || 100,
                maxMethods: maxMethods || 2,
                price,
                features: features || []
            });
            return res.status(201).json({
                status: "success",
                data: newPlan
            });
        }
        catch (error) {
            return res.status(500).json({
                status: "error",
                message: error.message || "Failed to create subscription plan"
            });
        }
    }
    async updatePlan(req, res) {
        try {
            const { id } = req.params;
            const updateData = req.body;
            // Prevent updating the ID
            delete updateData.id;
            const updatedPlan = await subscription_service_1.default.updatePlan(id, updateData);
            if (!updatedPlan) {
                return res.status(404).json({
                    status: "error",
                    message: "Subscription plan not found"
                });
            }
            return res.status(200).json({
                status: "success",
                data: updatedPlan
            });
        }
        catch (error) {
            return res.status(500).json({
                status: "error",
                message: error.message || "Failed to update subscription plan"
            });
        }
    }
    async deletePlan(req, res) {
        try {
            const { id } = req.params;
            const success = await subscription_service_1.default.deletePlan(id);
            if (!success) {
                return res.status(404).json({
                    status: "error",
                    message: "Subscription plan not found"
                });
            }
            return res.status(200).json({
                status: "success",
                message: "Subscription plan deleted successfully"
            });
        }
        catch (error) {
            return res.status(500).json({
                status: "error",
                message: error.message || "Failed to delete subscription plan"
            });
        }
    }
}
exports.default = new SubscriptionPlansController();
//# sourceMappingURL=subscription-plans.controller.js.map