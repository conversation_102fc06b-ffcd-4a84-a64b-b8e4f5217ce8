// jscpd:ignore-file
/**
 * Enhanced Risk Engine Controller
 *
 * This controller handles API requests related to the enhanced risk engine.
 */

import { Request, Response, NextFunction } from 'express';
import { BaseController } from "./base.controller";
import { EnhancedRiskEngineService, RiskModelType, BehavioralPatternType } from "../services/enhanced-risk-engine.service";
import { logger } from "../utils/logger";
import { PrismaClient } from "@prisma/client";
import { Transaction, Merchant } from '../types';
import { BaseController } from "./base.controller";
import { EnhancedRiskEngineService, RiskModelType, BehavioralPatternType } from "../services/enhanced-risk-engine.service";
import { logger } from "../utils/logger";
import { PrismaClient } from "@prisma/client";
import { Transaction, Merchant } from '../types';

/**
 * Enhanced risk engine controller
 */
export class EnhancedRiskEngineController extends BaseController {
    private enhancedRiskEngineService: EnhancedRiskEngineService;
    private prisma: PrismaClient;

    constructor() {
        super();
        this.enhancedRiskEngineService = new EnhancedRiskEngineService();
        this.prisma = new PrismaClient();
    }

    /**
     * Assess transaction risk
     */
    assessTransactionRisk = this.asyncHandler(async (req: Request, res: Response) => {
        try {
            const { transactionId } = req.params;
            const { ipAddress, userAgent, deviceId } = req.body;

            // Get transaction
            const transaction: any = await this.prisma.transaction.findUnique({
                where: { id: transactionId }
            });

            if (!transaction) {
                return res.notFound("Transaction", transactionId);
            }

            // Get merchant
            const merchant: any = await this.prisma.merchant.findUnique({
                where: { id: transaction.merchantId }
            });

            if (!merchant) {
                return res.notFound("Merchant", transaction.merchantId);
            }

            // Assess transaction risk
            const riskAssessment: any = await this.enhancedRiskEngineService.assessTransactionRisk(
                transaction,
                ipAddress,
                userAgent || "Unknown",
                deviceId || "Unknown",
                merchant
            );

            return res.success("Transaction risk assessment", riskAssessment);
        } catch (error) {
            logger.error("Error assessing transaction risk:", error);
            return res.serverError("Failed to assess transaction risk");
        }
    });

    /**
     * Get risk assessment for a transaction
     */
    getTransactionRiskAssessment = this.asyncHandler(async (req: Request, res: Response) => {
        try {
            const { transactionId } = req.params;

            // Get risk assessment
            const riskAssessment: any = await this.prisma.enhancedRiskAssessment.findFirst({
                where: { transactionId },
                orderBy: { createdAt: "desc" }
            });

            if (!riskAssessment) {
                return res.notFound("Risk assessment", transactionId);
            }

            // Format response
            const formattedAssessment: any = {
                ...riskAssessment,
                factors: JSON.parse(riskAssessment.factors),
                velocityChecks: JSON.parse(riskAssessment.velocityChecks)
            };

            return res.success("Transaction risk assessment", formattedAssessment);
        } catch (error) {
            logger.error("Error getting transaction risk assessment:", error);
            return res.serverError("Failed to get transaction risk assessment");
        }
    });

    /**
   * Get merchant risk configuration
   */
    getMerchantRiskConfig = this.asyncHandler(async (req: Request, res: Response) => {
        try {
            const { merchantId } = req.params;

            // Get risk configuration
            const riskConfig: any = await this.enhancedRiskEngineService.getMerchantRiskConfig(merchantId);

            if (!riskConfig) {
                return res.success("Merchant risk configuration", {
                    message: "No custom configuration found, using default configuration",
                    config: this.enhancedRiskEngineService["defaultConfig"]
                });
            }

            return res.success("Merchant risk configuration", riskConfig);
        } catch (error) {
            logger.error("Error getting merchant risk configuration:", error);
            return res.serverError("Failed to get merchant risk configuration");
        }
    });

    /**
   * Update merchant risk configuration
   */
    updateMerchantRiskConfig = this.asyncHandler(async (req: Request, res: Response) => {
        try {
            const { merchantId } = req.params;
            const {
                riskModel,
                velocityThresholds,
                behavioralAnalysis,
                machineLearning
            } = req.body;

            // Validate risk model
            if (riskModel && !Object.values(RiskModelType).includes(riskModel)) {
                return res.badRequest(`Invalid risk model. Must be one of: ${Object.values(RiskModelType).join(", ")}`);
            }

            // Convert merchantId to number
            const numericMerchantId: any = parseInt(merchantId);

            // Update or create risk configuration
            const config: Record<string, any> = await this.prisma.enhancedRiskConfig.upsert({
                where: { merchantId: numericMerchantId },
                update: { riskModel: riskModel || "HYBRID",
                    velocityThresholds: velocityThresholds ? JSON.stringify(velocityThresholds) : undefined,
                    behavioralAnalysis: behavioralAnalysis ? JSON.stringify(behavioralAnalysis) : undefined,
                    machineLearning: machineLearning ? JSON.stringify(machineLearning) : undefined,
                    updatedAt: new Date()
                },
                create: { merchantId: numericMerchantId,
                    riskModel: riskModel || "HYBRID",
                    velocityThresholds: JSON.stringify(velocityThresholds || {
                        transactionsPerMinute: 3,
                        transactionsPerHour: 10,
                        transactionsPerDay: 30,
                        amountPerMinute: 5000,
                        amountPerHour: 20000,
                        amountPerDay: 50000,
                        failedTransactionsPerDay: 5,
                        countriesPerDay: 3,
                        ipAddressesPerDay: 5,
                        devicesPerDay: 3
                    }),
                    behavioralAnalysis: JSON.stringify(behavioralAnalysis || {
                        enabled: true,
                        minTransactions: 5,
                        anomalyThreshold: 2.5
                    }),
                    machineLearning: JSON.stringify(machineLearning || {
                        enabled: true,
                        minConfidence: 0.7,
                        modelEndpoint: process.env.ML_MODEL_ENDPOINT
                    }),
                    createdAt: new Date(),
                    updatedAt: new Date()
                }
            });

            // Format response
            const formattedConfig: any = {
                ...config,
                velocityThresholds: JSON.parse(config.velocityThresholds),
                behavioralAnalysis: JSON.parse(config.behavioralAnalysis),
                machineLearning: JSON.parse(config.machineLearning)
            };

            return res.success("Merchant risk configuration updated", formattedConfig);
        } catch (error) {
            logger.error("Error updating merchant risk configuration:", error);
            return res.serverError("Failed to update merchant risk configuration");
        }
    });

    /**
   * Get risk statistics
   */
    getRiskStatistics = this.asyncHandler(async (req: Request, res: Response) => {
        try {
            const { merchantId } = req.params;
            const { startDate, endDate } = req.query;

            // Parse dates
            const parsedStartDate: any = startDate ? new Date(startDate as string) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
            const parsedEndDate: any = endDate ? new Date(endDate as string) : new Date();

            // Get risk assessments
            const riskAssessments: any = await this.prisma.enhancedRiskAssessment.findMany({
                where: { transactionId: {
                        in: await this.prisma.transaction.findMany({
                            where: {
                                merchantId,
                                createdAt: { gte: parsedStartDate,
                                    lte: parsedEndDate
                                }
                            },
                            select: { id: true
                            }
                        }).then(transactions => transactions.map(tx => tx.id))
                    }
                }
            });

            // Calculate statistics
            const totalAssessments: any = riskAssessments.length;
            const flaggedAssessments: any = riskAssessments.filter(assessment => assessment.isFlagged).length;
            const blockedAssessments: any = riskAssessments.filter(assessment => assessment.isBlocked).length;

            // Calculate risk level distribution
            const riskLevelDistribution: any = riskAssessments.reduce((acc, assessment) => {
                acc[assessment.level] = (acc[assessment.level] || 0) + 1;
                return acc;
            }, {} as Record<string, number>);

            // Calculate behavioral pattern distribution
            const behavioralPatternDistribution: any = riskAssessments.reduce((acc, assessment) => {
                acc[assessment.behavioralPattern] = (acc[assessment.behavioralPattern] || 0) + 1;
                return acc;
            }, {} as Record<string, number>);

            // Calculate average risk score
            const averageRiskScore: any = riskAssessments.reduce((sum, assessment) => sum + assessment.score, 0) / totalAssessments;

            return res.success("Risk statistics", {
                totalAssessments,
                flaggedAssessments,
                blockedAssessments,
                flaggedRate: totalAssessments > 0 ? (flaggedAssessments / totalAssessments) * 100 : 0,
                blockedRate: totalAssessments > 0 ? (blockedAssessments / totalAssessments) * 100 : 0,
                riskLevelDistribution,
                behavioralPatternDistribution,
                averageRiskScore,
                period: { startDate: parsedStartDate,
                    endDate: parsedEndDate
                }
            });
        } catch (error) {
            logger.error("Error getting risk statistics:", error);
            return res.serverError("Failed to get risk statistics");
        }
    });
}
