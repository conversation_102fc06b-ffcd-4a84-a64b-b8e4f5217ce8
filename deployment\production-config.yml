# Production Environment Configuration for AmazingPay Flow
# This configuration is optimized for production deployment with high availability

version: '3.8'

services:
  # Main Application Service (Load Balanced)
  amazingpay-api-1:
    build:
      context: ..
      dockerfile: Dockerfile
      target: production
    container_name: amazingpay-prod-api-1
    restart: always
    environment:
      - NODE_ENV=production
      - PORT=3000
      - DATABASE_URL=${DATABASE_URL}
      - JWT_SECRET=${JWT_SECRET}
      - REDIS_URL=${REDIS_URL}
      - LOG_LEVEL=info
      - CORS_ORIGIN=${CORS_ORIGIN}
      - RATE_LIMIT_WINDOW_MS=900000
      - RATE_LIMIT_MAX_REQUESTS=500
      - ENABLE_SWAGGER=false
      - ENABLE_METRICS=true
      - HEALTH_CHECK_INTERVAL=15000
      - INSTANCE_ID=api-1
    volumes:
      - /var/log/amazingpay:/app/logs
      - /var/uploads/amazingpay:/app/uploads
    depends_on:
      - postgres-primary
      - redis-cluster
    networks:
      - amazingpay-prod-network
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '1.0'
          memory: 1G
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 15s
      timeout: 5s
      retries: 3
      start_period: 30s

  amazingpay-api-2:
    build:
      context: ..
      dockerfile: Dockerfile
      target: production
    container_name: amazingpay-prod-api-2
    restart: always
    environment:
      - NODE_ENV=production
      - PORT=3000
      - DATABASE_URL=${DATABASE_URL}
      - JWT_SECRET=${JWT_SECRET}
      - REDIS_URL=${REDIS_URL}
      - LOG_LEVEL=info
      - CORS_ORIGIN=${CORS_ORIGIN}
      - RATE_LIMIT_WINDOW_MS=900000
      - RATE_LIMIT_MAX_REQUESTS=500
      - ENABLE_SWAGGER=false
      - ENABLE_METRICS=true
      - HEALTH_CHECK_INTERVAL=15000
      - INSTANCE_ID=api-2
    volumes:
      - /var/log/amazingpay:/app/logs
      - /var/uploads/amazingpay:/app/uploads
    depends_on:
      - postgres-primary
      - redis-cluster
    networks:
      - amazingpay-prod-network
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '1.0'
          memory: 1G
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 15s
      timeout: 5s
      retries: 3
      start_period: 30s

  # PostgreSQL Primary Database
  postgres-primary:
    image: postgres:15-alpine
    container_name: amazingpay-prod-db-primary
    restart: always
    environment:
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_REPLICATION_USER=${POSTGRES_REPLICATION_USER}
      - POSTGRES_REPLICATION_PASSWORD=${POSTGRES_REPLICATION_PASSWORD}
    volumes:
      - postgres_primary_data:/var/lib/postgresql/data
      - ./database/postgresql.conf:/etc/postgresql/postgresql.conf
      - ./database/pg_hba.conf:/etc/postgresql/pg_hba.conf
    command: postgres -c config_file=/etc/postgresql/postgresql.conf
    networks:
      - amazingpay-prod-network
    deploy:
      resources:
        limits:
          cpus: '4.0'
          memory: 4G
        reservations:
          cpus: '2.0'
          memory: 2G
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 5

  # PostgreSQL Read Replica
  postgres-replica:
    image: postgres:15-alpine
    container_name: amazingpay-prod-db-replica
    restart: always
    environment:
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - PGUSER=${POSTGRES_REPLICATION_USER}
      - POSTGRES_MASTER_SERVICE=postgres-primary
    volumes:
      - postgres_replica_data:/var/lib/postgresql/data
    depends_on:
      - postgres-primary
    networks:
      - amazingpay-prod-network
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '1.0'
          memory: 1G

  # Redis Cluster
  redis-cluster:
    image: redis:7-alpine
    container_name: amazingpay-prod-redis
    restart: always
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD} --maxmemory 1gb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    networks:
      - amazingpay-prod-network
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # Load Balancer - HAProxy
  haproxy:
    image: haproxy:2.8-alpine
    container_name: amazingpay-prod-lb
    restart: always
    ports:
      - "80:80"
      - "443:443"
      - "8404:8404"  # HAProxy stats
    volumes:
      - ./haproxy/haproxy.cfg:/usr/local/etc/haproxy/haproxy.cfg:ro
      - ./ssl:/etc/ssl/certs:ro
    depends_on:
      - amazingpay-api-1
      - amazingpay-api-2
    networks:
      - amazingpay-prod-network
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.5'
          memory: 256M
    healthcheck:
      test: ["CMD", "haproxy", "-c", "-f", "/usr/local/etc/haproxy/haproxy.cfg"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Monitoring - Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: amazingpay-prod-prometheus
    restart: always
    volumes:
      - ./monitoring/prometheus-prod.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    networks:
      - amazingpay-prod-network
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '1.0'
          memory: 1G

  # Monitoring - Grafana
  grafana:
    image: grafana/grafana:latest
    container_name: amazingpay-prod-grafana
    restart: always
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_USER}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_SERVER_DOMAIN=${GRAFANA_DOMAIN}
      - GF_SERVER_ROOT_URL=https://${GRAFANA_DOMAIN}
      - GF_SECURITY_COOKIE_SECURE=true
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
    depends_on:
      - prometheus
    networks:
      - amazingpay-prod-network
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M

  # Log Management - Elasticsearch Cluster
  elasticsearch-master:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: amazingpay-prod-es-master
    restart: always
    environment:
      - node.name=es-master
      - cluster.name=amazingpay-cluster
      - discovery.seed_hosts=elasticsearch-data1,elasticsearch-data2
      - cluster.initial_master_nodes=es-master
      - bootstrap.memory_lock=true
      - ES_JAVA_OPTS=-Xms2g -Xmx2g
      - xpack.security.enabled=true
      - ELASTIC_PASSWORD=${ELASTIC_PASSWORD}
    ulimits:
      memlock:
        soft: -1
        hard: -1
    volumes:
      - elasticsearch_master_data:/usr/share/elasticsearch/data
    networks:
      - amazingpay-prod-network
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G

  # Backup Service
  backup:
    image: postgres:15-alpine
    container_name: amazingpay-prod-backup
    restart: "no"
    environment:
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - BACKUP_SCHEDULE=${BACKUP_SCHEDULE:-0 2 * * *}
    volumes:
      - /var/backups/amazingpay:/backups
      - ./scripts/backup.sh:/backup.sh:ro
    depends_on:
      - postgres-primary
    networks:
      - amazingpay-prod-network
    profiles:
      - backup

# Network Configuration
networks:
  amazingpay-prod-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# Volume Configuration
volumes:
  postgres_primary_data:
    driver: local
  postgres_replica_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  elasticsearch_master_data:
    driver: local

# Secrets Configuration
secrets:
  jwt_secret:
    external: true
  postgres_password:
    external: true
  redis_password:
    external: true
  elastic_password:
    external: true
