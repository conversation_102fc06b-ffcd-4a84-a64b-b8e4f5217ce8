/**
 * Merchant Self-Service Controller
 *
 * This controller handles API requests related to merchant self-service tools.
 */
import { Request, Response } from "express";
import { BaseController } from "./base.controller";
/**
 * Merchant self-service controller
 */
export declare class MerchantSelfServiceController extends BaseController {
    private merchantSelfServiceService;
    constructor();
    /**
   * Create API key
   */
    createApiKey: (req: Request, res: Response, next: import("express").NextFunction) => void;
    /**
   * Get merchant API keys
   */
    getMerchantApiKeys: (req: Request, res: Response, next: import("express").NextFunction) => void;
    /**
   * Delete API key
   */
    deleteApiKey: (req: Request, res: Response, next: import("express").NextFunction) => void;
    /**
   * Create webhook
   */
    createWebhook: (req: Request, res: Response, next: import("express").NextFunction) => void;
    /**
   * Get merchant webhooks
   */
    getMerchantWebhooks: (req: Request, res: Response, next: import("express").NextFunction) => void;
    /**
   * Update webhook
   */
    updateWebhook: (req: Request, res: Response, next: import("express").NextFunction) => void;
    /**
   * Delete webhook
   */
    deleteWebhook: (req: Request, res: Response, next: import("express").NextFunction) => void;
    /**
   * Set notification preference
   */
    setNotificationPreference: (req: Request, res: Response, next: import("express").NextFunction) => void;
    /**
   * Get merchant notification preferences
   */
    getMerchantNotificationPreferences: (req: Request, res: Response, next: import("express").NextFunction) => void;
}
//# sourceMappingURL=merchant-self-service.controller.d.ts.map