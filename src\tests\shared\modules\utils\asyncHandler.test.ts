import { asyncHand<PERSON> } from '../../../../shared/modules/utils/asyncHandler';
import { Request, Response, NextFunction } from 'express';

describe('asyncHandler', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: jest.Mock<NextFunction>;

  beforeEach(() => {
    mockRequest = {};
    mockResponse = {};
    mockNext = jest.fn();
  });

  it('should call the handler function with request, response, and next', async () => {
    // Arrange
    const handlerFn: unknown =jest.fn().mockResolvedValue('result');
    const wrappedFn: unknown =asyncHandler(handlerFn);
    
    // Act
    await wrappedFn(mockRequest as Request, mockResponse as Response, mockNext);
    
    // Assert
    expect(handlerFn).toHaveBeenCalledWith(mockRequest, mockResponse, mockNext);
  });

  it('should not call next when handler resolves successfully', async () => {
    // Arrange
    const handlerFn: unknown =jest.fn().mockResolvedValue('result');
    const wrappedFn: unknown =asyncHandler(handlerFn);
    
    // Act
    await wrappedFn(mockRequest as Request, mockResponse as Response, mockNext);
    
    // Assert
    expect(mockNext).not.toHaveBeenCalled();
  });

  it('should call next with error when handler rejects', async () => {
    // Arrange
    const error: Error =new Error('Test error');
    const handlerFn: unknown =jest.fn().mockRejectedValue(error);
    const wrappedFn: unknown =asyncHandler(handlerFn);
    
    // Act
    await wrappedFn(mockRequest as Request, mockResponse as Response, mockNext);
    
    // Assert
    expect(mockNext).toHaveBeenCalledWith(error);
  });

  it('should handle synchronous errors in the handler', async () => {
    // Arrange
    const error: Error =new Error('Sync error');
    const handlerFn: unknown =jest.fn().mockImplementation(() => {
      throw error;
    });
    const wrappedFn: unknown =asyncHandler(handlerFn);
    
    // Act
    await wrappedFn(mockRequest as Request, mockResponse as Response, mockNext);
    
    // Assert
    expect(mockNext).toHaveBeenCalledWith(error);
  });
});
