/**
 * Alert Aggregation Business Service
 *
 * Handles business logic for alert aggregation operations.
 */

import { PrismaClient } from '@prisma/client';
import { AppError, ErrorType, ErrorCode } from '../../../utils/errors/AppError';
import {
  CreateAggregationRuleRequest,
  UpdateAggregationRuleRequest,
  CreateCorrelationRuleRequest,
  UpdateCorrelationRuleRequest,
  AggregationRuleResponse,
  CorrelationRuleResponse,
  AggregationRuleFilters,
  CorrelationRuleFilters,
  PaginationParams,
} from '../types/AlertAggregationTypes';

/**
 * Business service for alert aggregation
 */
export class AlertAggregationBusinessService {
  constructor(private prisma: PrismaClient) {}

  /**
   * Get all aggregation rules with optional filtering and pagination
   */
  async getAggregationRules(
    filters?: AggregationRuleFilters,
    pagination?: PaginationParams
  ): Promise<{ rules: AggregationRuleResponse[]; total: number }> {
    try {
      const where: unknown = {};

      // Apply filters
      if (filters?.type && filters.type !== 'ANY') {
        where.type = filters.type;
      }

      if (filters?.severity && filters.severity !== 'ANY') {
        where.severity = filters.severity;
      }

      if (filters?.enabled !== undefined) {
        where.enabled = filters.enabled;
      }

      if (filters?.search) {
        where.OR = [
          { name: { contains: filters.search, mode: 'insensitive' } },
          { description: { contains: filters.search, mode: 'insensitive' } },
        ];
      }

      // Build query options
      const queryOptions: unknown = {
        where,
        orderBy: { createdAt: 'desc' },
      };

      // Apply pagination
      if (pagination) {
        const { page = 1, limit = 10, sortBy, sortOrder } = pagination;
        queryOptions.skip = (page - 1) * limit;
        queryOptions.take = limit;

        if (sortBy) {
          queryOptions.orderBy = { [sortBy]: sortOrder ?? 'desc' };
        }
      }

      // Execute queries
      const [rules, total] = await Promise.all([
        this.prisma.alertAggregationRule.findMany(queryOptions),
        this.prisma.alertAggregationRule.count({ where }),
      ]);

      return {
        rules: rules.map(this.mapAggregationRuleToResponse),
        total,
      };
    } catch (error) {
      throw new AppError({
        message: 'Failed to get aggregation rules',
        type: ErrorType.INTERNAL,
        code: ErrorCode.INTERNAL_SERVER_ERROR,
        details: { originalError: error instanceof Error ? error.message : error },
      });
    }
  }

  /**
   * Get a specific aggregation rule by ID
   */
  async getAggregationRule(id: string): Promise<AggregationRuleResponse> {
    try {
      const rule = await this.prisma.alertAggregationRule.findUnique({
        where: { id },
      });

      if (!rule) {
        throw new AppError({
          message: 'Aggregation rule not found',
          type: ErrorType.NOT_FOUND,
          code: ErrorCode.RESOURCE_NOT_FOUND,
        });
      }

      return this.mapAggregationRuleToResponse(rule);
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError({
        message: 'Failed to get aggregation rule',
        type: ErrorType.INTERNAL,
        code: ErrorCode.INTERNAL_SERVER_ERROR,
        details: { originalError: error instanceof Error ? error.message : error },
      });
    }
  }

  /**
   * Create a new aggregation rule
   */
  async createAggregationRule(
    data: CreateAggregationRuleRequest
  ): Promise<AggregationRuleResponse> {
    try {
      // Check for duplicate name
      const existingRule = await this.prisma.alertAggregationRule.findFirst({
        where: { name: data.name },
      });

      if (existingRule) {
        throw new AppError({
          message: 'Aggregation rule with this name already exists',
          type: ErrorType.VALIDATION,
          code: 'DUPLICATE_RESOURCE' as unknown,
        });
      }

      const rule = await this.prisma.alertAggregationRule.create({
        data: {
          name: data.name,
          description: data.description,
          conditions: {
            type: data.type,
            severity: data.severity,
            timeWindow: data.timeWindow,
            threshold: data.threshold,
            groupBy: data.groupBy,
          },
          actions: {},
          isActive: data.enabled ?? true,
        },
      });

      return this.mapAggregationRuleToResponse(rule);
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError({
        message: 'Failed to create aggregation rule',
        type: ErrorType.INTERNAL,
        code: ErrorCode.INTERNAL_SERVER_ERROR,
        details: { originalError: error instanceof Error ? error.message : error },
      });
    }
  }

  /**
   * Update an existing aggregation rule
   */
  async updateAggregationRule(
    id: string,
    data: UpdateAggregationRuleRequest
  ): Promise<AggregationRuleResponse> {
    try {
      // Check if rule exists
      const existingRule = await this.prisma.alertAggregationRule.findUnique({
        where: { id },
      });

      if (!existingRule) {
        throw new AppError({
          message: 'Aggregation rule not found',
          type: ErrorType.NOT_FOUND,
          code: ErrorCode.RESOURCE_NOT_FOUND,
        });
      }

      // Check for duplicate name if name is being updated
      if (data.name && data.name !== existingRule.name) {
        const duplicateRule = await this.prisma.alertAggregationRule.findFirst({
          where: {
            name: data.name,
            id: { not: id },
          },
        });

        if (duplicateRule) {
          throw new AppError({
            message: 'Aggregation rule with this name already exists',
            type: ErrorType.VALIDATION,
            code: 'DUPLICATE_RESOURCE' as unknown,
          });
        }
      }

      const updatedRule = await this.prisma.alertAggregationRule.update({
        where: { id },
        data: {
          name: data.name || existingRule.name,
          description: data.description || existingRule.description,
          conditions: {
            ...(existingRule.conditions as unknown),
            type: data.type || (existingRule.conditions as unknown)?.type,
            severity: data.severity || (existingRule.conditions as unknown)?.severity,
            timeWindow: data.timeWindow || (existingRule.conditions as unknown)?.timeWindow,
            threshold: data.threshold || (existingRule.conditions as unknown)?.threshold,
            groupBy: data.groupBy || (existingRule.conditions as unknown)?.groupBy,
          },
          actions: existingRule.actions,
          isActive: data.enabled !== undefined ? data.enabled : existingRule.isActive,
        },
      });

      return this.mapAggregationRuleToResponse(updatedRule);
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError({
        message: 'Failed to update aggregation rule',
        type: ErrorType.INTERNAL,
        code: ErrorCode.INTERNAL_SERVER_ERROR,
        details: { originalError: error instanceof Error ? error.message : error },
      });
    }
  }

  /**
   * Delete an aggregation rule
   */
  async deleteAggregationRule(id: string): Promise<void> {
    try {
      // Check if rule exists
      const existingRule = await this.prisma.alertAggregationRule.findUnique({
        where: { id },
      });

      if (!existingRule) {
        throw new AppError({
          message: 'Aggregation rule not found',
          type: ErrorType.NOT_FOUND,
          code: ErrorCode.RESOURCE_NOT_FOUND,
        });
      }

      await this.prisma.alertAggregationRule.delete({
        where: { id },
      });
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError({
        message: 'Failed to delete aggregation rule',
        type: ErrorType.INTERNAL,
        code: ErrorCode.INTERNAL_SERVER_ERROR,
        details: { originalError: error instanceof Error ? error.message : error },
      });
    }
  }

  /**
   * Get all correlation rules with optional filtering and pagination
   */
  async getCorrelationRules(
    filters?: CorrelationRuleFilters,
    pagination?: PaginationParams
  ): Promise<{ rules: CorrelationRuleResponse[]; total: number }> {
    try {
      const where: unknown = {};

      // Apply filters
      if (filters?.enabled !== undefined) {
        where.enabled = filters.enabled;
      }

      if (filters?.search) {
        where.OR = [
          { name: { contains: filters.search, mode: 'insensitive' } },
          { description: { contains: filters.search, mode: 'insensitive' } },
        ];
      }

      // Build query options
      const queryOptions: unknown = {
        where,
        orderBy: { createdAt: 'desc' },
      };

      // Apply pagination
      if (pagination) {
        const { page = 1, limit = 10, sortBy, sortOrder } = pagination;
        queryOptions.skip = (page - 1) * limit;
        queryOptions.take = limit;

        if (sortBy) {
          queryOptions.orderBy = { [sortBy]: sortOrder ?? 'desc' };
        }
      }

      // Execute queries
      const [rules, total] = await Promise.all([
        this.prisma.alertCorrelationRule.findMany(queryOptions),
        this.prisma.alertCorrelationRule.count({ where }),
      ]);

      return {
        rules: rules.map(this.mapCorrelationRuleToResponse),
        total,
      };
    } catch (error) {
      throw new AppError({
        message: 'Failed to get correlation rules',
        type: ErrorType.INTERNAL,
        code: ErrorCode.INTERNAL_SERVER_ERROR,
        details: { originalError: error instanceof Error ? error.message : error },
      });
    }
  }

  /**
   * Get a specific correlation rule by ID
   */
  async getCorrelationRule(id: string): Promise<CorrelationRuleResponse> {
    try {
      const rule = await this.prisma.alertCorrelationRule.findUnique({
        where: { id },
      });

      if (!rule) {
        throw new AppError({
          message: 'Correlation rule not found',
          type: ErrorType.NOT_FOUND,
          code: ErrorCode.RESOURCE_NOT_FOUND,
        });
      }

      return this.mapCorrelationRuleToResponse(rule);
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError({
        message: 'Failed to get correlation rule',
        type: ErrorType.INTERNAL,
        code: ErrorCode.INTERNAL_SERVER_ERROR,
        details: { originalError: error instanceof Error ? error.message : error },
      });
    }
  }

  /**
   * Map aggregation rule to response format
   */
  private mapAggregationRuleToResponse(rule: unknown): AggregationRuleResponse {
    return {
      id: rule.id,
      name: rule.name,
      description: rule.description,
      type: rule.type,
      severity: rule.severity,
      timeWindow: rule.timeWindow,
      threshold: rule.threshold,
      groupBy: rule.groupBy,
      enabled: rule.enabled,
      createdAt: rule.createdAt,
      updatedAt: rule.updatedAt,
    };
  }

  /**
   * Map correlation rule to response format
   */
  private mapCorrelationRuleToResponse(rule: unknown): CorrelationRuleResponse {
    return {
      id: rule.id,
      name: rule.name,
      description: rule.description,
      conditions: rule.conditions,
      timeWindow: rule.timeWindow,
      enabled: rule.enabled,
      createdAt: rule.createdAt,
      updatedAt: rule.updatedAt,
    };
  }
}
