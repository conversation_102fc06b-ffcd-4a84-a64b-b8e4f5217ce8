"use strict";
/**
 * Identity Verification Response Mapper
 *
 * Handles response formatting for identity verification operations.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.IdentityVerificationResponseMapper = void 0;
const AppError_1 = require("../../../utils/errors/AppError");
/**
 * Response mapper for identity verification
 */
class IdentityVerificationResponseMapper {
    /**
     * Send success response
     */
    static sendSuccess(res, data, message, statusCode = 200, pagination) {
        const response = {
            success: true,
            data,
            message,
            pagination,
            timestamp: new Date(),
            requestId: res.locals.requestId ?? 'unknown',
        };
        res.status(statusCode).json(response);
    }
    /**
     * Send error response
     */
    static sendError(res, error, statusCode) {
        let errorResponse;
        if (error instanceof AppError_1.AppError) {
            errorResponse = {
                success: false,
                error: {
                    message: error.message,
                    code: error.code,
                    type: error.type,
                    details: error.details,
                },
                timestamp: new Date(),
                requestId: res.locals.requestId ?? 'unknown',
            };
            statusCode = statusCode ?? error.statusCode ?? 400;
        }
        else {
            errorResponse = {
                success: false,
                error: {
                    message: error.message ?? 'Internal server error',
                    code: 'INTERNAL_SERVER_ERROR',
                    type: 'INTERNAL',
                },
                timestamp: new Date(),
                requestId: res.locals.requestId ?? 'unknown',
            };
            statusCode = statusCode ?? 500;
        }
        res.status(statusCode).json(errorResponse);
    }
    /**
     * Send verification result response
     */
    static sendVerificationResult(res, result, message) {
        const statusCode = result.success ? 200 : 400;
        this.sendSuccess(res, result, message ?? (result.success ? 'Verification completed successfully' : 'Verification failed'), statusCode);
    }
    /**
     * Send verification response
     */
    static sendVerification(res, verification, message) {
        this.sendSuccess(res, verification, message ?? 'Verification retrieved successfully');
    }
    /**
     * Send verifications list response
     */
    static sendVerificationsList(res, verifications, total, page = 1, limit = 10) {
        const totalPages = Math.ceil(total / limit);
        this.sendSuccess(res, verifications, `Retrieved ${verifications.length} verifications`, 200, {
            page,
            limit,
            total,
            totalPages,
        });
    }
    /**
     * Send claim response
     */
    static sendClaim(res, claim, message, statusCode = 200) {
        this.sendSuccess(res, claim, message ?? 'Claim processed successfully', statusCode);
    }
    /**
     * Send claim created response
     */
    static sendClaimCreated(res, claim) {
        this.sendClaim(res, claim, 'Claim added successfully', 201);
    }
    /**
     * Send verification statistics response
     */
    static sendVerificationStats(res, stats) {
        this.sendSuccess(res, stats, 'Verification statistics retrieved successfully');
    }
    /**
     * Send supported networks response
     */
    static sendSupportedNetworks(res, networks) {
        this.sendSuccess(res, networks, 'Supported networks retrieved successfully');
    }
    /**
     * Send blockchain verification request created response
     */
    static sendBlockchainVerificationRequestCreated(res, request) {
        this.sendSuccess(res, request, 'Blockchain verification request created successfully', 201);
    }
    /**
     * Send blockchain verification success response
     */
    static sendBlockchainVerificationSuccess(res) {
        this.sendSuccess(res, { verified: true }, 'Blockchain identity verified successfully');
    }
    /**
     * Send blockchain verification failure response
     */
    static sendBlockchainVerificationFailure(res) {
        this.sendSuccess(res, { verified: false }, 'Blockchain identity verification failed', 400);
    }
    /**
     * Send verification expiration check response
     */
    static sendVerificationExpirationCheck(res, count) {
        this.sendSuccess(res, { count }, `Found ${count} expired verifications`);
    }
    /**
     * Send verification expiration set response
     */
    static sendVerificationExpirationSet(res, verification) {
        this.sendSuccess(res, verification, 'Verification expiration set successfully');
    }
    /**
     * Send ENS domain verification response
     */
    static sendENSDomainVerification(res, result) {
        this.sendVerificationResult(res, result, 'ENS domain verification completed');
    }
    /**
     * Send validation error response
     */
    static sendValidationError(res, errors, message = 'Validation failed') {
        const error = new AppError_1.AppError({
            message,
            type: 'VALIDATION',
            code: 'INVALID_INPUT',
            details: { errors },
        });
        this.sendError(res, error, 400);
    }
    /**
     * Send authorization error response
     */
    static sendAuthorizationError(res, message = 'Access denied', requiredRole) {
        const error = new AppError_1.AppError({
            message,
            type: 'AUTHENTICATION',
            code: 'INVALID_CREDENTIALS',
            details: { requiredRole },
        });
        this.sendError(res, error, 403);
    }
    /**
     * Send not found error response
     */
    static sendNotFoundError(res, resource = 'Resource') {
        const error = new AppError_1.AppError({
            message: `${resource} not found`,
            type: 'NOT_FOUND',
            code: 'RESOURCE_NOT_FOUND',
        });
        this.sendError(res, error, 404);
    }
    /**
     * Send internal server error response
     */
    static sendInternalServerError(res, message = 'Internal server error') {
        const error = new AppError_1.AppError({
            message,
            type: 'INTERNAL',
            code: 'INTERNAL_SERVER_ERROR',
        });
        this.sendError(res, error, 500);
    }
    /**
     * Format pagination metadata
     */
    static formatPagination(page, limit, total) {
        const totalPages = Math.ceil(total / limit);
        return {
            page,
            limit,
            total,
            totalPages,
            hasNext: page < totalPages,
            hasPrev: page > 1,
        };
    }
    /**
     * Handle async controller method
     */
    static asyncHandler(fn) {
        return (req, res, next) => {
            Promise.resolve(fn(req, res, next)).catch((error) => next(error));
        };
    }
    /**
     * Set response headers for API
     */
    static setApiHeaders(res) {
        res.setHeader('Content-Type', 'application/json');
        res.setHeader('X-API-Version', '1.0');
        res.setHeader('X-Response-Time', Date.now());
    }
    /**
     * Log response for debugging
     */
    static logResponse(method, url, statusCode, responseTime) {
        console.log(`${method} ${url} - ${statusCode} - ${responseTime}ms`);
    }
    /**
     * Transform verification data for response
     */
    static transformVerification(verification) {
        return {
            id: verification.id,
            type: verification.type,
            status: verification.status,
            address: verification.address,
            userId: verification.userId,
            merchantId: verification.merchantId,
            metadata: verification.metadata,
            createdAt: verification.createdAt,
            updatedAt: verification.updatedAt,
            expiresAt: verification.expiresAt,
        };
    }
    /**
     * Transform claim data for response
     */
    static transformClaim(claim) {
        return {
            id: claim.id,
            verificationId: claim.verificationId,
            type: claim.type,
            value: claim.value,
            issuer: claim.issuer,
            isRevoked: claim.isRevoked,
            createdAt: claim.createdAt,
            updatedAt: claim.updatedAt,
        };
    }
    /**
     * Transform verification statistics for response
     */
    static transformVerificationStats(stats) {
        return {
            totalVerifications: stats.totalVerifications ?? 0,
            verifiedCount: stats.verifiedCount ?? 0,
            pendingCount: stats.pendingCount ?? 0,
            failedCount: stats.failedCount ?? 0,
            expiredCount: stats.expiredCount ?? 0,
            verificationsByType: stats.verificationsByType ?? {},
            verificationsByNetwork: stats.verificationsByNetwork ?? {},
        };
    }
}
exports.IdentityVerificationResponseMapper = IdentityVerificationResponseMapper;
//# sourceMappingURL=IdentityVerificationResponseMapper.js.map