/**
 * Fraud Detection Business Service
 *
 * Handles business logic for fraud detection operations.
 */
import { PrismaClient } from '@prisma/client';
import { AssessTransactionRiskRequest, UpdateFraudConfigRequest, RiskAssessmentResponse, FraudConfigResponse, FlaggedTransactionResponse, FraudStatisticsResponse, FraudDetectionFilters, PaginationParams } from '../types/FraudDetectionControllerTypes';
/**
 * Business service for fraud detection operations
 */
export declare class FraudDetectionBusinessService {
    private readonly fraudDetectionService;
    private readonly prisma;
    constructor(prisma: PrismaClient);
    /**
     * Assess transaction risk
     */
    assessTransactionRisk(data: AssessTransactionRiskRequest): Promise<any>;
    /**
     * Get transaction risk assessment
     */
    getTransactionRiskAssessment(transactionId: string): Promise<RiskAssessmentResponse>;
    /**
     * Get merchant fraud configuration
     */
    getMerchantFraudConfig(merchantId: number): Promise<FraudConfigResponse>;
    /**
     * Update merchant fraud configuration
     */
    updateMerchantFraudConfig(merchantId: number, data: UpdateFraudConfigRequest): Promise<FraudConfigResponse>;
    /**
     * Get flagged transactions
     */
    getFlaggedTransactions(filters?: FraudDetectionFilters, pagination?: PaginationParams): Promise<{
        transactions: FlaggedTransactionResponse[];
        total: number;
    }>;
    /**
     * Get fraud statistics
     */
    getFraudStatistics(merchantId?: number, startDate?: Date, endDate?: Date): Promise<FraudStatisticsResponse>;
    /**
     * Map risk assessment to response format
     */
    private mapRiskAssessmentToResponse;
    /**
     * Map fraud config to response format
     */
    private mapFraudConfigToResponse;
    /**
     * Map flagged transaction to response format
     */
    private mapFlaggedTransactionToResponse;
    /**
     * Group risk assessments by day
     */
    private groupByDay;
}
//# sourceMappingURL=FraudDetectionBusinessService.d.ts.map