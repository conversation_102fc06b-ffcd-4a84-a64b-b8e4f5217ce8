/**
 * Response Utilities
 * 
 * This module provides utility functions for handling API responses.
 */

import { Response } from 'express';

/**
 * Send a success response
 */
export const sendSuccess: any =(res: Response, data: any = {}, message: string = 'Success', statusCode: number = 200) => {
  return res.status(statusCode).json({
    success: true,
    message,
    data
  });
};

/**
 * Send an error response
 */
export const sendError: any =(res: Response, message: string = 'Error', statusCode: number = 500, error: any = null) => {
  return res.status(statusCode).json({
    success: false,
    message,
    error: error ? ((error as Error).message || error) : null
  });
};

/**
 * Create a standard API response
 */
export const createApiResponse: any =(success: boolean, message: string, data: any = null, error: any = null) => {
  return {
    success,
    message,
    data,
    error
  };
};