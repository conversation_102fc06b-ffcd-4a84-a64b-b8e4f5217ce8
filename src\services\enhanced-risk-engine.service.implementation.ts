// jscpd:ignore-file
/**
 * Enhanced Risk Engine Service Implementation
 *
 * This file contains the implementation of the methods for the EnhancedRiskEngineService.
 */

import { EnhancedRiskEngineService, RiskVelocityCheckResult, BehavioralPatternType, RiskModelType } from "./enhanced-risk-engine.service";
import { Transaction, Merchant } from "@prisma/client";
import { RiskFactor, RiskLevel, RiskScore } from "./fraud-detection";
import { logger } from "../utils/logger";
import axios from "axios";



/**
 * Perform velocity checks for a transaction
 * @param transaction Transaction to check
 * @param merchantId Merchant ID
 * @param thresholds Velocity thresholds
 * @returns Velocity check results
 */
EnhancedRiskEngineService.prototype.performVelocityChecks = async function(
    transaction: Transaction,
    merchantId: number | string,
    thresholds
): Promise<RiskVelocityCheckResult[]> {
    try {
        const results: RiskVelocityCheckResult[] = [];
        const numericMerchantId: unknown = typeof merchantId === "string" ? parseInt(merchantId) : merchantId;

        // Check transactions per minute
        const minuteAgo: Date =new Date(Date.now() - 60 * 1000);
        const transactionsLastMinute: unknown = await this.prisma.transaction.count({
            where: { merchantId: numericMerchantId,
                customerEmail: transaction.customerEmail,
                createdAt: { gte: minuteAgo }
            }
        });

        results.push({
            type: "TRANSACTIONS_PER_MINUTE",
            currentValue: transactionsLastMinute,
            thresholdValue: thresholds.transactionsPerMinute,
            triggered: transactionsLastMinute >= thresholds.transactionsPerMinute,
            riskScore: transactionsLastMinute >= thresholds.transactionsPerMinute ? 90 :
                transactionsLastMinute >= thresholds.transactionsPerMinute * 0.7 ? 70 :
                    transactionsLastMinute >= thresholds.transactionsPerMinute * 0.5 ? 50 : 10
        });

        // Check transactions per hour
        const hourAgo: Date =new Date(Date.now() - 60 * 60 * 1000);
        const transactionsLastHour: unknown = await this.prisma.transaction.count({
            where: { merchantId: numericMerchantId,
                customerEmail: transaction.customerEmail,
                createdAt: { gte: hourAgo }
            }
        });

        results.push({
            type: "TRANSACTIONS_PER_HOUR",
            currentValue: transactionsLastHour,
            thresholdValue: thresholds.transactionsPerHour,
            triggered: transactionsLastHour >= thresholds.transactionsPerHour,
            riskScore: transactionsLastHour >= thresholds.transactionsPerHour ? 85 :
                transactionsLastHour >= thresholds.transactionsPerHour * 0.7 ? 65 :
                    transactionsLastHour >= thresholds.transactionsPerHour * 0.5 ? 45 : 10
        });

        // Check transactions per day
        const dayAgo: Date =new Date(Date.now() - 24 * 60 * 60 * 1000);
        const transactionsLastDay: unknown = await this.prisma.transaction.count({
            where: { merchantId: numericMerchantId,
                customerEmail: transaction.customerEmail,
                createdAt: { gte: dayAgo }
            }
        });

        results.push({
            type: "TRANSACTIONS_PER_DAY",
            currentValue: transactionsLastDay,
            thresholdValue: thresholds.transactionsPerDay,
            triggered: transactionsLastDay >= thresholds.transactionsPerDay,
            riskScore: transactionsLastDay >= thresholds.transactionsPerDay ? 80 :
                transactionsLastDay >= thresholds.transactionsPerDay * 0.7 ? 60 :
                    transactionsLastDay >= thresholds.transactionsPerDay * 0.5 ? 40 : 10
        });

        // Check amount per minute
        const transactionsAmountLastMinute: unknown = await this.prisma.transaction.findMany({
            where: { merchantId: numericMerchantId,
                customerEmail: transaction.customerEmail,
                createdAt: { gte: minuteAgo }
            },
            select: { amount: true
            }
        });

        const amountLastMinute: unknown = transactionsAmountLastMinute.reduce(
            (sum, tx) => sum + parseFloat(tx.amount.toString()),
            parseFloat(transaction.amount.toString())
        );

        results.push({
            type: "AMOUNT_PER_MINUTE",
            currentValue: amountLastMinute,
            thresholdValue: thresholds.amountPerMinute,
            triggered: amountLastMinute >= thresholds.amountPerMinute,
            riskScore: amountLastMinute >= thresholds.amountPerMinute ? 90 :
                amountLastMinute >= thresholds.amountPerMinute * 0.7 ? 70 :
                    amountLastMinute >= thresholds.amountPerMinute * 0.5 ? 50 : 10
        });

        // Check failed transactions per day
        const failedTransactionsLastDay: unknown = await this.prisma.transaction.count({
            where: { merchantId: numericMerchantId,
                customerEmail: transaction.customerEmail,
                status: "FAILED",
                createdAt: { gte: dayAgo }
            }
        });

        results.push({
            type: "FAILED_TRANSACTIONS_PER_DAY",
            currentValue: failedTransactionsLastDay,
            thresholdValue: thresholds.failedTransactionsPerDay,
            triggered: failedTransactionsLastDay >= thresholds.failedTransactionsPerDay,
            riskScore: failedTransactionsLastDay >= thresholds.failedTransactionsPerDay ? 85 :
                failedTransactionsLastDay >= thresholds.failedTransactionsPerDay * 0.7 ? 65 :
                    failedTransactionsLastDay >= thresholds.failedTransactionsPerDay * 0.5 ? 45 : 10
        });

        // Check countries per day
        const transactionsLastDayWithCountry: unknown = await this.prisma.transaction.findMany({
            where: { merchantId: numericMerchantId,
                customerEmail: transaction.customerEmail,
                createdAt: { gte: dayAgo }
            },
            select: { country: true
            }
        });

        const uniqueCountries: unknown = new Set(transactionsLastDayWithCountry.map(tx => tx.country));
        if (transaction.country && !uniqueCountries.has(transaction.country)) {
            uniqueCountries.add(transaction.country);
        }

        results.push({
            type: "COUNTRIES_PER_DAY",
            currentValue: uniqueCountries.size,
            thresholdValue: thresholds.countriesPerDay,
            triggered: uniqueCountries.size >= thresholds.countriesPerDay,
            riskScore: uniqueCountries.size >= thresholds.countriesPerDay ? 90 :
                uniqueCountries.size >= thresholds.countriesPerDay * 0.7 ? 70 :
                    uniqueCountries.size >= thresholds.countriesPerDay * 0.5 ? 50 : 10
        });

        return results;
    } catch (error) {
        logger.error("Error performing velocity checks:", error);
        return [];
    }
};

/**
 * Analyze behavioral pattern for a transaction
 * @param transaction Transaction to analyze
 * @param merchantId Merchant ID
 * @param config Behavioral analysis configuration
 * @returns Behavioral pattern type
 */
EnhancedRiskEngineService.prototype.analyzeBehavioralPattern = async function(
    transaction: Transaction,
    merchantId: number | string,
    config
): Promise<BehavioralPatternType> {
    try {
        const numericMerchantId: unknown = typeof merchantId === "string" ? parseInt(merchantId) : merchantId;

        // Get previous transactions for this customer
        const previousTransactions: unknown = await this.prisma.transaction.findMany({
            where: { merchantId: numericMerchantId,
                customerEmail: transaction.customerEmail,
                id: { not: transaction.id }
            },
            orderBy: { createdAt: "desc"
            },
            take: 20
        });

        // If not enough transactions for analysis, return UNKNOWN
        if (previousTransactions.length < config.minTransactions) {
            return BehavioralPatternType.UNKNOWN;
        }

        // Calculate average and standard deviation of transaction amounts
        const amounts: unknown = previousTransactions.map(tx => parseFloat(tx.amount.toString()));
        const avgAmount: unknown = amounts.reduce((sum, amount) => sum + amount, 0) / amounts.length;
        const stdDevAmount: unknown = Math.sqrt(
            amounts.reduce((sum, amount) => sum + Math.pow(amount - avgAmount, 2), 0) / amounts.length
        );

        // Calculate z-score for current transaction amount
        const currentAmount: unknown = parseFloat(transaction.amount.toString());
        const zScore: unknown = Math.abs((currentAmount - avgAmount) / stdDevAmount);

        // Check if transaction amount is an anomaly
        if (zScore > config.anomalyThreshold) {
            return BehavioralPatternType.SUSPICIOUS;
        }

        // Check time patterns
        const timePatternAnomaly: unknown = this.checkTimePatternAnomaly(transaction, previousTransactions);
        if (timePatternAnomaly) {
            return BehavioralPatternType.SUSPICIOUS;
        }

        // Check payment method patterns
        const paymentMethodAnomaly: unknown = this.checkPaymentMethodAnomaly(transaction, previousTransactions);
        if (paymentMethodAnomaly) {
            return BehavioralPatternType.SUSPICIOUS;
        }

        // If no anomalies detected, return NORMAL
        return BehavioralPatternType.NORMAL;
    } catch (error) {
        logger.error("Error analyzing behavioral pattern:", error);
        return BehavioralPatternType.UNKNOWN;
    }
};

/**
 * Check if transaction time is anomalous compared to previous transactions
 * @param transaction Current transaction
 * @param previousTransactions Previous transactions
 * @returns Whether time pattern is anomalous
 */
EnhancedRiskEngineService.prototype.checkTimePatternAnomaly = function(
    transaction: Transaction,
    previousTransactions: Transaction[]
): boolean {
    try {
    // Extract hours from previous transactions
        const hours: unknown = previousTransactions.map(tx => new Date(tx.createdAt).getHours());

        // Calculate frequency of each hour
        const hourFrequency: Record<number, number> = {};
        hours.forEach((hour)) => {
            hourFrequency[hour] = (hourFrequency[hour] ?? 0) + 1;
        });

        // Calculate total transactions
        const totalTransactions: unknown = previousTransactions.length;

        // Get current transaction hour
        const currentHour: Date =new Date(transaction.createdAt).getHours();

        // Check if current hour is unusual (less than 10% of previous transactions)
        const currentHourFrequency: unknown = hourFrequency[currentHour] ?? 0;
        const currentHourPercentage: unknown = (currentHourFrequency / totalTransactions) * 100;

        return currentHourPercentage < 10;
    } catch (error) {
        logger.error("Error checking time pattern anomaly:", error);
        return false;
    }
};

/**
 * Check if payment method is anomalous compared to previous transactions
 * @param transaction Current transaction
 * @param previousTransactions Previous transactions
 * @returns Whether payment method is anomalous
 */
EnhancedRiskEngineService.prototype.checkPaymentMethodAnomaly = function(
    transaction: Transaction,
    previousTransactions: Transaction[]
): boolean {
    try {
    // Extract payment methods from previous transactions
        const paymentMethods: unknown = previousTransactions.map(tx => tx.paymentMethodId);

        // Calculate frequency of each payment method
        const methodFrequency: Record<string, number> = {};
        paymentMethods.forEach((method)) => {
            if (method) {
                methodFrequency[method] = (methodFrequency[method] ?? 0) + 1;
            }
        });

        // Calculate total transactions
        const totalTransactions: unknown = previousTransactions.length;

        // Get current transaction payment method
        const currentMethod: unknown = transaction.paymentMethodId;

        // If no payment method, return false
        if (!currentMethod) {
            return false;
        }

        // Check if current method is unusual (never used before or less than 5% of previous transactions)
        const currentMethodFrequency: unknown = methodFrequency[currentMethod] ?? 0;
        const currentMethodPercentage: unknown = (currentMethodFrequency / totalTransactions) * 100;

        return currentMethodFrequency === 0 || currentMethodPercentage < 5;
    } catch (error) {
        logger.error("Error checking payment method anomaly:", error);
        return false;
    }
};

/**
 * Apply machine learning model to transaction
 * @param transaction Transaction to assess
 * @param ipAddress IP address of the user
 * @param userAgent User agent of the user
 * @param deviceId Device ID of the user
 * @param merchant Merchant associated with the transaction
 * @param config Machine learning configuration
 * @returns Machine learning result
 */
EnhancedRiskEngineService.prototype.applyMachineLearningModel = async function(
    transaction: Transaction,
    ipAddress: string,
    userAgent: string,
    deviceId: string,
    merchant: Merchant,
    config
): Promise<{ score: number; confidence: number }> {
    try {
    // If no model endpoint, return default values
        if (!config.modelEndpoint) {
            return { score: 0, confidence: 0 };
        }

        // Prepare data for ML model
        const modelData: unknown = {
            transaction_id: transaction.id,
            merchant_id: merchant.id,
            amount: parseFloat(transaction.amount.toString()),
            currency: transaction.currency,
            payment_method_id: transaction.paymentMethodId,
            customer_email: transaction.customerEmail,
            customer_ip: ipAddress,
            user_agent: userAgent,
            device_id: deviceId,
            country: transaction.country,
            created_at: transaction.createdAt
        };

        // Call ML model API
        const response: unknown = await axios.post(config.modelEndpoint, modelData, {
            headers: {
                "Content-Type": "application/json",
                "Authorization": `Bearer ${process.env.ML_API_KEY}`
            },
            timeout: 5000 // 5 second timeout
        });

        // Extract score and confidence from response
        const { score, confidence } = response.data;

        // If confidence is below threshold, return default values
        if (confidence < config.minConfidence) {
            return { score: 0, confidence: 0 };
        }

        return { score, confidence };
    } catch (error) {
        logger.error("Error applying machine learning model:", error);
        return { score: 0, confidence: 0 };
    }
};

/**
 * Calculate enhanced risk score
 * @param baseScore Base risk score from fraud detection service
 * @param velocityChecks Velocity check results
 * @param behavioralPattern Behavioral pattern
 * @param mlScore Machine learning score
 * @param confidence Confidence in ML score
 * @returns Enhanced risk score
 */
EnhancedRiskEngineService.prototype.calculateEnhancedRiskScore = function(
    baseScore: RiskScore,
    velocityChecks: RiskVelocityCheckResult[],
    behavioralPattern: BehavioralPatternType,
    mlScore: number,
    confidence: number
): RiskScore {
    try {
    // Start with base score
        let score: unknown = baseScore.score;

        // Add velocity check contributions
        const triggeredChecks: unknown = velocityChecks.filter(check => check.triggered);
        if (triggeredChecks.length > 0) {
            const avgVelocityScore: unknown = triggeredChecks.reduce((sum, check) => sum + check.riskScore, 0) / triggeredChecks.length;
            score = Math.max(score, avgVelocityScore);
        }

        // Add behavioral pattern contribution
        if (behavioralPattern === BehavioralPatternType.SUSPICIOUS) {
            score = Math.max(score, 75);
        } else if (behavioralPattern === BehavioralPatternType.FRAUDULENT) {
            score = Math.max(score, 90);
        }

        // Add machine learning contribution if confidence is high enough
        if (confidence >= 0.7) {
            // Weighted average with ML score
            score = (score * 0.7) + (mlScore * 0.3 * confidence);
        }

        // Ensure score is between 0 and 100
        score = Math.max(0, Math.min(100, score));

        // Determine risk level
        let level: RiskLevel;
        if (score >= 85) {
            level = RiskLevel.CRITICAL;
        } else if (score >= 70) {
            level = RiskLevel.HIGH;
        } else if (score >= 50) {
            level = RiskLevel.MEDIUM;
        } else {
            level = RiskLevel.LOW;
        }

        // Create enhanced factors array
        const factors: unknown = [...baseScore.factors];

        // Add velocity factors
        triggeredChecks.forEach((check)) => {
            factors.push({
                factor: RiskFactor.FREQUENCY,
                score: check.riskScore,
                reason: `${check.type} exceeded threshold (${check.currentValue} > ${check.thresholdValue})`,
                weight: 0.15,
                contribution: check.riskScore * 0.15
            });
        });

        // Add behavioral factor if suspicious or fraudulent
        if (behavioralPattern === BehavioralPatternType.SUSPICIOUS || behavioralPattern === BehavioralPatternType.FRAUDULENT) {
            factors.push({
                factor: RiskFactor.BEHAVIOR,
                score: behavioralPattern === BehavioralPatternType.FRAUDULENT ? 90 : 75,
                reason: `${behavioralPattern} behavioral pattern detected`,
                weight: 0.2,
                contribution: (behavioralPattern === BehavioralPatternType.FRAUDULENT ? 90 : 75) * 0.2
            });
        }

        // Add machine learning factor if confidence is high enough
        if (confidence >= 0.7) {
            factors.push({
                factor: RiskFactor.BEHAVIOR,
                score: mlScore,
                reason: `Machine learning model prediction (${confidence.toFixed(2)} confidence)`,
                weight: 0.3 * confidence,
                contribution: mlScore * 0.3 * confidence
            });
        }

        return {
            score,
            level,
            factors
        };
    } catch (error) {
        logger.error("Error calculating enhanced risk score:", error);
        return baseScore;
    }
};

/**
 * Generate reason for risk assessment
 * @param factors Risk factors
 * @param level Risk level
 * @returns Reason string
 */
EnhancedRiskEngineService.prototype.generateReason = function(
    factors: Array<{ factor: RiskFactor;
    score: number;
    reason: string;
    weight: number;
    contribution: number;
  }>,
    level: RiskLevel
): string {
    // Sort factors by contribution (highest first)
    const sortedFactors: unknown = [...factors].sort((a, b) => b.contribution - a.contribution);

    // Take top 3 factors
    const topFactors: unknown = sortedFactors.slice(0, 3);

    // Generate reason based on risk level and top factors
    let reason: unknown = `Transaction risk level: ${level}. `;

    if (topFactors.length > 0) {
        reason += "Triggered by: ";
        reason += topFactors.map(f => f.reason).join(", ");
    }

    return reason;
};

/**
 * Generate recommended action for risk assessment
 * @param level Risk level
 * @param isBlocked Whether the transaction is blocked
 * @returns Recommended action string
 */
EnhancedRiskEngineService.prototype.generateRecommendedAction = function(
    level: RiskLevel,
    isBlocked: boolean
): string {
    if (isBlocked) {
        return "Block transaction and notify fraud team";
    }

    switch (level) {
    case RiskLevel.CRITICAL:
        return "Manual review required before processing";
    case RiskLevel.HIGH:
        return "Additional verification recommended";
    case RiskLevel.MEDIUM:
        return "Monitor for suspicious activity";
    case RiskLevel.LOW:
        return "Process normally";
    default:
        return "Process normally";
    }
};
