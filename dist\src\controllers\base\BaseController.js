"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseController = void 0;
const appError_1 = require("../../utils/appError");
const asyncHandler_1 = require("../../utils/asyncHandler");
const controller_utils_1 = require("../../utils/controller.utils");
const logger_1 = require("../../utils/logger");
const ServiceError_1 = require("../../utils/errors/ServiceError");
const AppError_1 = require("../../utils/errors/AppError");
class BaseController {
    constructor() {
        // Base controller constructor
    }
    /**
     * Async handler for controller methods
     * @param fn Function to handle
     * @returns Express handler
     */
    asyncHandler(fn) {
        return (0, asyncHandler_1.asyncHandler)(fn);
    }
    /**
     * Check if user is authorized
     * @param req Express request
     * @throws AppError if user is not authorized
     */
    checkAuthorization(req) {
        return controller_utils_1.ControllerUtils.checkAuth(req);
    }
    /**
     * Check if user is admin
     * @param req Express request
     * @throws AppError if user is not admin
     */
    checkAdminRole(req) {
        return controller_utils_1.ControllerUtils.checkAdmin(req);
    }
    /**
     * Check if user is merchant
     * @param req Express request
     * @throws AppError if user is not merchant
     */
    checkMerchantRole(req) {
        return controller_utils_1.ControllerUtils.checkMerchant(req);
    }
    /**
     * Parse date range from request
     * @param req Express request
     * @param startDateField Start date field name
     * @param endDateField End date field name
     * @returns Date range
     */
    parseDateRange(req, startDateField = "startDate", endDateField = "endDate") {
        return controller_utils_1.ControllerUtils.parseDateRange(req, startDateField, endDateField);
    }
    /**
     * Parse pagination parameters from request
     * @param req Express request
     * @returns Pagination parameters
     */
    parsePagination(req) {
        return controller_utils_1.ControllerUtils.parsePagination(req);
    }
    /**
     * Validate required fields
     * @param req Express request
     * @param fields Required fields
     * @throws AppError if any required field is missing
     */
    validateRequiredFields(req, fields) {
        controller_utils_1.ControllerUtils.validateRequiredFields(req, fields);
    }
    /**
     * Validate enum value
     * @param value Value to validate
     * @param enumType Enum type
     * @param fieldName Field name for error message
     * @throws AppError if value is not in enum
     */
    validateEnum(value, enumType, fieldName) {
        controller_utils_1.ControllerUtils.validateEnum(value, enumType, fieldName);
    }
    /**
     * Send success response
     * @param res Express response
     * @param data Response data
     * @param statusCode HTTP status code
     */
    sendSuccess(res, data, statusCode = 200) {
        return res.status(statusCode).json(controller_utils_1.ControllerUtils.formatSuccessResponse(data));
    }
    /**
     * Send message response
     * @param res Express response
     * @param message Response message
     * @param statusCode HTTP status code
     */
    sendMessage(res, message, statusCode = 200) {
        return res.status(statusCode).json(controller_utils_1.ControllerUtils.formatMessageResponse(message));
    }
    /**
     * Send paginated success response
     * @param res Express response
     * @param data Response data
     * @param total Total number of items
     * @param page Current page
     * @param limit Items per page
     * @param statusCode HTTP status code
     */
    sendPaginatedSuccess(res, data, total, page, limit, statusCode = 200) {
        return res.status(statusCode).json(controller_utils_1.ControllerUtils.formatPaginatedResponse(data, total, page, limit));
    }
    /**
     * Handle error
     * @param error Error
     * @param res Response
     */
    handleError(error, res) {
        logger_1.logger.error(`Controller error:`, error);
        if (error instanceof ServiceError_1.ServiceError) {
            return res.status(error.statusCode).json({
                success: false,
                error: { message: error.message,
                    code: error.code,
                    details: error.details }
            });
        }
        if (error instanceof appError_1.AppError) {
            return res.status(error.statusCode).json(controller_utils_1.ControllerUtils.formatErrorResponse(error.message));
        }
        return res.status(500).json(controller_utils_1.ControllerUtils.formatErrorResponse("Internal server error"));
    }
    /**
     * Send error response
     * @param res Express response
     * @param error Error
     * @returns Response
     */
    sendError(res, error, statusCode = 500) {
        if (typeof error === 'string') {
            return res.status(statusCode).json(controller_utils_1.ControllerUtils.formatErrorResponse(error));
        }
        return this.handleError(error, res);
    }
    /**
     * Send not found response
     * @param res Express response
     * @param resource Resource name
     * @param id Resource ID
     * @returns Response
     */
    sendNotFound(res, resource, id) {
        const error = new appError_1.AppError(`${resource} with ID ${id} not found`, 404);
        return this.handleError(error, res);
    }
    /**
     * Send validation error response
     * @param res Express response
     * @param errors Validation errors
     * @returns Response
     */
    sendValidationError(res, errors) {
        const error = new ServiceError_1.ServiceError({
            message: 'Validation failed',
            statusCode: 400,
            code: AppError_1.ErrorCode.INVALID_INPUT,
            details: errors
        });
        return this.handleError(error, res);
    }
}
exports.BaseController = BaseController;
//# sourceMappingURL=BaseController.js.map