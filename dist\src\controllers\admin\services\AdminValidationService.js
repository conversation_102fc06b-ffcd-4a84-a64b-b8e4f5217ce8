"use strict";
/**
 * Admin Validation Service
 *
 * Handles input validation for admin operations.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminValidationService = void 0;
const AppError_1 = require("../../../utils/errors/AppError");
const AdminControllerTypes_1 = require("../types/AdminControllerTypes");
/**
 * Validation service for admin operations
 */
class AdminValidationService {
    /**
     * Validate admin user creation request
     */
    validateCreateAdminUser(data) {
        const errors = [];
        if (!data.email) {
            errors.push({ field: 'email', message: 'Email is required' });
        }
        else if (typeof data.email !== 'string' || !this.isValidEmail(data.email)) {
            errors.push({ field: 'email', message: 'Invalid email format', value: data.email });
        }
        if (!data.name) {
            errors.push({ field: 'name', message: 'Name is required' });
        }
        else if (typeof data.name !== 'string' || data.name.trim().length === 0) {
            errors.push({ field: 'name', message: 'Name must be a non-empty string' });
        }
        else if (data.name.length > 100) {
            errors.push({ field: 'name', message: 'Name must be less than 100 characters' });
        }
        if (!data.password) {
            errors.push({ field: 'password', message: 'Password is required' });
        }
        else if (typeof data.password !== 'string' || data.password.length < 8) {
            errors.push({ field: 'password', message: 'Password must be at least 8 characters long' });
        }
        else if (!this.isValidPassword(data.password)) {
            errors.push({
                field: 'password',
                message: 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
            });
        }
        if (!data.roleId) {
            errors.push({ field: 'roleId', message: 'Role ID is required' });
        }
        else if (!this.isValidUUID(data.roleId)) {
            errors.push({ field: 'roleId', message: 'Invalid role ID format', value: data.roleId });
        }
        if (errors.length > 0) {
            throw new AppError_1.AppError({
                message: 'Validation failed',
                type: AppError_1.ErrorType.VALIDATION,
                code: AppError_1.ErrorCode.INVALID_INPUT,
                details: { errors },
            });
        }
        return {
            email: data.email.toLowerCase().trim(),
            name: data.name.trim(),
            password: data.password,
            roleId: data.roleId,
        };
    }
    /**
     * Validate admin user update request
     */
    validateUpdateAdminUser(data) {
        const errors = [];
        if (data.email !== undefined) {
            if (typeof data.email !== 'string' || !this.isValidEmail(data.email)) {
                errors.push({ field: 'email', message: 'Invalid email format', value: data.email });
            }
        }
        if (data.name !== undefined) {
            if (typeof data.name !== 'string' || data.name.trim().length === 0) {
                errors.push({ field: 'name', message: 'Name must be a non-empty string' });
            }
            else if (data.name.length > 100) {
                errors.push({ field: 'name', message: 'Name must be less than 100 characters' });
            }
        }
        if (data.roleId !== undefined) {
            if (!this.isValidUUID(data.roleId)) {
                errors.push({ field: 'roleId', message: 'Invalid role ID format', value: data.roleId });
            }
        }
        if (data.isActive !== undefined && typeof data.isActive !== 'boolean') {
            errors.push({ field: 'isActive', message: 'isActive must be a boolean' });
        }
        if (errors.length > 0) {
            throw new AppError_1.AppError({
                message: 'Validation failed',
                type: AppError_1.ErrorType.VALIDATION,
                code: AppError_1.ErrorCode.INVALID_INPUT,
                details: { errors },
            });
        }
        const result = {};
        if (data.email !== undefined)
            result.email = data.email.toLowerCase().trim();
        if (data.name !== undefined)
            result.name = data.name.trim();
        if (data.roleId !== undefined)
            result.roleId = data.roleId;
        if (data.isActive !== undefined)
            result.isActive = data.isActive;
        return result;
    }
    /**
     * Validate role creation request
     */
    validateCreateRole(data) {
        const errors = [];
        if (!data.name) {
            errors.push({ field: 'name', message: 'Name is required' });
        }
        else if (typeof data.name !== 'string' || data.name.trim().length === 0) {
            errors.push({ field: 'name', message: 'Name must be a non-empty string' });
        }
        else if (data.name.length > 50) {
            errors.push({ field: 'name', message: 'Name must be less than 50 characters' });
        }
        if (!data.type) {
            errors.push({ field: 'type', message: 'Type is required' });
        }
        else if (!Object.values(AdminControllerTypes_1.RoleType).includes(data.type)) {
            errors.push({
                field: 'type',
                message: `Invalid role type. Must be one of: ${Object.values(AdminControllerTypes_1.RoleType).join(', ')}`,
                value: data.type,
            });
        }
        if (!data.description) {
            errors.push({ field: 'description', message: 'Description is required' });
        }
        else if (typeof data.description !== 'string' || data.description.trim().length === 0) {
            errors.push({ field: 'description', message: 'Description must be a non-empty string' });
        }
        else if (data.description.length > 500) {
            errors.push({
                field: 'description',
                message: 'Description must be less than 500 characters',
            });
        }
        if (!data.permissions) {
            errors.push({ field: 'permissions', message: 'Permissions are required' });
        }
        else if (!Array.isArray(data.permissions)) {
            errors.push({ field: 'permissions', message: 'Permissions must be an array' });
        }
        else if (data.permissions.length === 0) {
            errors.push({ field: 'permissions', message: 'At least one permission is required' });
        }
        else {
            data.permissions.forEach((permissionId, index) => {
                if (!this.isValidUUID(permissionId)) {
                    errors.push({
                        field: `permissions[${index}]`,
                        message: 'Invalid permission ID format',
                        value: permissionId,
                    });
                }
            });
        }
        if (errors.length > 0) {
            throw new AppError_1.AppError({
                message: 'Validation failed',
                type: AppError_1.ErrorType.VALIDATION,
                code: AppError_1.ErrorCode.INVALID_INPUT,
                details: { errors },
            });
        }
        return {
            name: data.name.trim(),
            type: data.type,
            description: data.description.trim(),
            permissions: data.permissions,
        };
    }
    /**
     * Validate role update request
     */
    validateUpdateRole(data) {
        const errors = [];
        if (data.name !== undefined) {
            if (typeof data.name !== 'string' || data.name.trim().length === 0) {
                errors.push({ field: 'name', message: 'Name must be a non-empty string' });
            }
            else if (data.name.length > 50) {
                errors.push({ field: 'name', message: 'Name must be less than 50 characters' });
            }
        }
        if (data.description !== undefined) {
            if (typeof data.description !== 'string' || data.description.trim().length === 0) {
                errors.push({ field: 'description', message: 'Description must be a non-empty string' });
            }
            else if (data.description.length > 500) {
                errors.push({
                    field: 'description',
                    message: 'Description must be less than 500 characters',
                });
            }
        }
        if (data.permissions !== undefined) {
            if (!Array.isArray(data.permissions)) {
                errors.push({ field: 'permissions', message: 'Permissions must be an array' });
            }
            else {
                data.permissions.forEach((permissionId, index) => {
                    if (!this.isValidUUID(permissionId)) {
                        errors.push({
                            field: `permissions[${index}]`,
                            message: 'Invalid permission ID format',
                            value: permissionId,
                        });
                    }
                });
            }
        }
        if (data.isActive !== undefined && typeof data.isActive !== 'boolean') {
            errors.push({ field: 'isActive', message: 'isActive must be a boolean' });
        }
        if (errors.length > 0) {
            throw new AppError_1.AppError({
                message: 'Validation failed',
                type: AppError_1.ErrorType.VALIDATION,
                code: AppError_1.ErrorCode.INVALID_INPUT,
                details: { errors },
            });
        }
        const result = {};
        if (data.name !== undefined)
            result.name = data.name.trim();
        if (data.description !== undefined)
            result.description = data.description.trim();
        if (data.permissions !== undefined)
            result.permissions = data.permissions;
        if (data.isActive !== undefined)
            result.isActive = data.isActive;
        return result;
    }
    /**
     * Validate permission creation request
     */
    validateCreatePermission(data) {
        const errors = [];
        if (!data.name) {
            errors.push({ field: 'name', message: 'Name is required' });
        }
        else if (typeof data.name !== 'string' || data.name.trim().length === 0) {
            errors.push({ field: 'name', message: 'Name must be a non-empty string' });
        }
        else if (data.name.length > 50) {
            errors.push({ field: 'name', message: 'Name must be less than 50 characters' });
        }
        if (!data.description) {
            errors.push({ field: 'description', message: 'Description is required' });
        }
        else if (typeof data.description !== 'string' || data.description.trim().length === 0) {
            errors.push({ field: 'description', message: 'Description must be a non-empty string' });
        }
        else if (data.description.length > 200) {
            errors.push({
                field: 'description',
                message: 'Description must be less than 200 characters',
            });
        }
        if (!data.resource) {
            errors.push({ field: 'resource', message: 'Resource is required' });
        }
        else if (!Object.values(AdminControllerTypes_1.PermissionResource).includes(data.resource)) {
            errors.push({
                field: 'resource',
                message: `Invalid resource. Must be one of: ${Object.values(AdminControllerTypes_1.PermissionResource).join(', ')}`,
                value: data.resource,
            });
        }
        if (!data.action) {
            errors.push({ field: 'action', message: 'Action is required' });
        }
        else if (!Object.values(AdminControllerTypes_1.PermissionAction).includes(data.action)) {
            errors.push({
                field: 'action',
                message: `Invalid action. Must be one of: ${Object.values(AdminControllerTypes_1.PermissionAction).join(', ')}`,
                value: data.action,
            });
        }
        if (errors.length > 0) {
            throw new AppError_1.AppError({
                message: 'Validation failed',
                type: AppError_1.ErrorType.VALIDATION,
                code: AppError_1.ErrorCode.INVALID_INPUT,
                details: { errors },
            });
        }
        return {
            name: data.name.trim(),
            description: data.description.trim(),
            resource: data.resource,
            action: data.action,
        };
    }
    /**
     * Validate ID parameter
     */
    validateId(id, fieldName = 'id') {
        if (!id) {
            throw new AppError_1.AppError({
                message: `${fieldName} is required`,
                type: AppError_1.ErrorType.VALIDATION,
                code: AppError_1.ErrorCode.MISSING_REQUIRED_FIELD,
            });
        }
        if (!this.isValidUUID(id)) {
            throw new AppError_1.AppError({
                message: `${fieldName} must be a valid UUID`,
                type: AppError_1.ErrorType.VALIDATION,
                code: AppError_1.ErrorCode.INVALID_INPUT,
            });
        }
        return id;
    }
    /**
     * Validate pagination parameters
     */
    validatePaginationParams(query) {
        const page = query.page ? parseInt(query.page, 10) : 1;
        const limit = query.limit ? parseInt(query.limit, 10) : 10;
        if (isNaN(page) || page < 1) {
            throw new AppError_1.AppError({
                message: 'Page must be a positive integer',
                type: AppError_1.ErrorType.VALIDATION,
                code: AppError_1.ErrorCode.INVALID_INPUT,
            });
        }
        if (isNaN(limit) || limit < 1 || limit > 100) {
            throw new AppError_1.AppError({
                message: 'Limit must be between 1 and 100',
                type: AppError_1.ErrorType.VALIDATION,
                code: AppError_1.ErrorCode.INVALID_INPUT,
            });
        }
        const result = { page, limit };
        if (query.sortBy) {
            const validSortFields = ['name', 'email', 'createdAt', 'updatedAt', 'status'];
            if (!validSortFields.includes(query.sortBy)) {
                throw new AppError_1.AppError({
                    message: `Invalid sort field. Must be one of: ${validSortFields.join(', ')}`,
                    type: AppError_1.ErrorType.VALIDATION,
                    code: AppError_1.ErrorCode.INVALID_INPUT,
                });
            }
            result.sortBy = query.sortBy;
        }
        if (query.sortOrder) {
            if (!['asc', 'desc'].includes(query.sortOrder)) {
                throw new AppError_1.AppError({
                    message: 'Sort order must be either "asc" or "desc"',
                    type: AppError_1.ErrorType.VALIDATION,
                    code: AppError_1.ErrorCode.INVALID_INPUT,
                });
            }
            result.sortOrder = query.sortOrder;
        }
        return result;
    }
    /**
     * Check if string is a valid email
     */
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    /**
     * Check if string is a valid password
     */
    isValidPassword(password) {
        // At least 8 characters, one uppercase, one lowercase, one number, one special character
        const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/;
        return passwordRegex.test(password);
    }
    /**
     * Check if string is a valid UUID
     */
    isValidUUID(uuid) {
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
        return uuidRegex.test(uuid);
    }
}
exports.AdminValidationService = AdminValidationService;
//# sourceMappingURL=AdminValidationService.js.map