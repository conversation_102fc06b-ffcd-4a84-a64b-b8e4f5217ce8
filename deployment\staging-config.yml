# Staging Environment Configuration for AmazingPay Flow
# This configuration is optimized for staging deployment and testing

version: '3.8'

services:
  # Main Application Service
  amazingpay-api:
    build:
      context: ..
      dockerfile: Dockerfile
      target: production
    container_name: amazingpay-staging-api
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=staging
      - PORT=3000
      - DATABASE_URL=${DATABASE_URL}
      - JWT_SECRET=${JWT_SECRET}
      - REDIS_URL=${REDIS_URL}
      - LOG_LEVEL=debug
      - CORS_ORIGIN=${CORS_ORIGIN}
      - RATE_LIMIT_WINDOW_MS=900000
      - RATE_LIMIT_MAX_REQUESTS=1000
      - ENABLE_SWAGGER=true
      - ENABLE_METRICS=true
      - HEALTH_CHECK_INTERVAL=30000
    volumes:
      - ../logs:/app/logs
      - ../uploads:/app/uploads
    depends_on:
      - postgres
      - redis
    networks:
      - amazingpay-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: amazingpay-staging-db
    restart: unless-stopped
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ../database/init:/docker-entrypoint-initdb.d
    networks:
      - amazingpay-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: amazingpay-staging-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    networks:
      - amazingpay-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: amazingpay-staging-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/staging.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
      - ../logs/nginx:/var/log/nginx
    depends_on:
      - amazingpay-api
    networks:
      - amazingpay-network
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Monitoring - Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: amazingpay-staging-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - amazingpay-network

  # Monitoring - Grafana
  grafana:
    image: grafana/grafana:latest
    container_name: amazingpay-staging-grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_USER}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
    depends_on:
      - prometheus
    networks:
      - amazingpay-network

  # Log Management - ELK Stack (Elasticsearch)
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: amazingpay-staging-elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - ES_JAVA_OPTS=-Xms512m -Xmx512m
      - xpack.security.enabled=false
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - amazingpay-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Log Management - Logstash
  logstash:
    image: docker.elastic.co/logstash/logstash:8.11.0
    container_name: amazingpay-staging-logstash
    restart: unless-stopped
    volumes:
      - ./logging/logstash.conf:/usr/share/logstash/pipeline/logstash.conf:ro
      - ../logs:/var/log/app:ro
    depends_on:
      - elasticsearch
    networks:
      - amazingpay-network

  # Log Management - Kibana
  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: amazingpay-staging-kibana
    restart: unless-stopped
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    depends_on:
      - elasticsearch
    networks:
      - amazingpay-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:5601/api/status || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Testing Service
  test-runner:
    build:
      context: ..
      dockerfile: Dockerfile.test
    container_name: amazingpay-staging-tests
    environment:
      - NODE_ENV=test
      - DATABASE_URL=${TEST_DATABASE_URL}
      - REDIS_URL=${TEST_REDIS_URL}
    volumes:
      - ../src:/app/src:ro
      - ../tests:/app/tests:ro
      - ../coverage:/app/coverage
    depends_on:
      - postgres
      - redis
    networks:
      - amazingpay-network
    profiles:
      - testing

# Network Configuration
networks:
  amazingpay-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# Volume Configuration
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  elasticsearch_data:
    driver: local

# Health Check Configuration
x-healthcheck-defaults: &healthcheck-defaults
  interval: 30s
  timeout: 10s
  retries: 3
  start_period: 30s
