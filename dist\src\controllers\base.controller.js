"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseController = void 0;
/**
 * Base controller class
 */
class BaseController {
    /**
     * Handle async route handler
     * @param fn Async route handler function
     * @returns Express route handler
     */
    asyncHandler(fn) {
        return (req, res, next) => {
            Promise.resolve(fn(req, res, next)).catch(next);
        };
    }
    /**
     * Send a success response
     * @param res Express response object
     * @param data Response data
     * @param message Success message
     * @param meta Response metadata
     */
    sendSuccess(res, data, message, meta) {
        res.status(200).json({
            success: true,
            data,
            message,
            meta,
            timestamp: new Date().toISOString(),
        });
    }
    /**
     * Send a paginated response
     * @param res Express response object
     * @param data Response data
     * @param pagination Pagination information
     * @param message Success message
     * @param meta Response metadata
     */
    sendPaginated(res, data, pagination, message, meta) {
        res.status(200).json({
            success: true,
            data,
            pagination,
            message,
            meta,
            timestamp: new Date().toISOString(),
        });
    }
    /**
     * Send an error response
     * @param res Express response object
     * @param error Service error
     */
    sendError(res, error) {
        if (error.validationErrors) {
            res.status(400).json({
                success: false,
                error: error.message || 'Validation failed',
                errorCode: error.errorCode,
                validationErrors: error.validationErrors,
                timestamp: new Date().toISOString(),
            });
        }
        else if (error.errorCode === 'NOT_FOUND' && error.resource) {
            const message = error.id
                ? `${error.resource} with ID ${error.id} not found`
                : `${error.resource} not found`;
            res.status(404).json({
                success: false,
                error: message,
                errorCode: error.errorCode,
                timestamp: new Date().toISOString(),
            });
        }
        else {
            res.status(error.statusCode || 500).json({
                success: false,
                error: error.message,
                errorCode: error.errorCode,
                timestamp: new Date().toISOString(),
            });
        }
    }
    /**
     * Create pagination info from request query parameters
     * @param req Express request object
     * @param total Total number of items
     * @returns Pagination information
     */
    createPaginationInfo(req, total) {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const totalPages = Math.ceil(total / limit);
        return {
            page,
            limit,
            total,
            totalPages,
            hasNext: page < totalPages,
            hasPrev: page > 1,
        };
    }
    /**
     * Get pagination parameters from request query parameters
     * @param req Express request object
     * @returns Pagination parameters
     */
    getPaginationParams(req) {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const skip = (page - 1) * limit;
        return {
            skip,
            take: limit,
            page,
            limit,
        };
    }
    /**
     * Get sort parameters from request query parameters
     * @param req Express request object
     * @param defaultField Default sort field
     * @param defaultOrder Default sort order
     * @returns Sort parameters
     */
    getSortParams(req, defaultField = 'createdAt', defaultOrder = 'desc') {
        const field = req.query.sortBy || defaultField;
        const orderParam = req.query.sortOrder?.toLowerCase();
        const order = orderParam === 'asc' ? 'asc' : 'desc';
        return {
            field,
            order,
        };
    }
    /**
     * Get filter parameters from request query parameters
     * @param req Express request object
     * @param allowedFields Allowed filter fields
     * @returns Filter parameters
     */
    getFilterParams(req, allowedFields) {
        const filters = {};
        allowedFields.forEach((field) => {
            if (req.query[field] !== undefined) {
                filters[field] = req.query[field];
            }
        });
        return filters;
    }
    /**
     * Get search parameters from request query parameters
     * @param req Express request object
     * @param searchFields Search fields
     * @returns Search parameters
     */
    getSearchParams(req, searchFields) {
        const searchTerm = req.query.search;
        if (!searchTerm) {
            return null;
        }
        const searchParams = {
            OR: searchFields.map((field) => ({
                [field]: {
                    contains: searchTerm,
                    mode: 'insensitive',
                },
            })),
        };
        return searchParams;
    }
}
exports.BaseController = BaseController;
//# sourceMappingURL=base.controller.js.map