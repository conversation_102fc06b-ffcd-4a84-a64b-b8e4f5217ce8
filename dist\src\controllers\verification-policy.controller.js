"use strict";
// jscpd:ignore-file
/**
 * Verification Policy Controller
 *
 * Handles verification policy operations.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.verifyWithPolicyChain = exports.getApplicablePolicies = exports.createPolicy = exports.getAllPolicies = void 0;
const client_1 = require("@prisma/client");
const VerificationService_1 = require("../services/verification/VerificationService");
const VerificationPolicy_1 = require("../services/verification/policy/VerificationPolicy");
const logger_1 = require("../lib/logger");
const error_middleware_1 = require("../middlewares/error.middleware");
const prisma = new client_1.PrismaClient();
const verificationService = new VerificationService_1.VerificationService(prisma);
/**
 * Get all verification policies
 */
const getAllPolicies = async (req, res, next) => {
    try {
        const policies = await verificationService.getAllPolicies();
        res.status(200).json({
            success: true,
            policies: policies.map(policy => ({
                name: policy.getName(),
                description: policy.getDescription(),
                requiredMethods: policy.getRequiredMethods()
            }))
        });
    }
    catch (error) {
        logger_1.logger.error("Error getting verification policies:", error);
        next(new error_middleware_1.AppError({
            message: "Failed to get verification policies",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};
exports.getAllPolicies = getAllPolicies;
/**
 * Create a verification policy
 */
const createPolicy = async (req, res, next) => {
    try {
        const { name, description, requiredMethods, conditions } = req.body;
        if (!name || !requiredMethods || !Array.isArray(requiredMethods)) {
            return next(new error_middleware_1.AppError({
                message: "Invalid policy data",
                type: ErrorType.VALIDATION,
                code: ErrorCode.INVALID_INPUT
            }));
        }
        // Create policy
        const policy = new VerificationPolicy_1.VerificationPolicy(name)
            .setDescription(description || "")
            .requireMethods(requiredMethods);
        // Add conditions
        if (conditions) {
            if (conditions.amountExceeds) {
                policy.whenAmountExceeds(conditions.amountExceeds);
            }
            if (conditions.amountBelow) {
                policy.whenAmountBelow(conditions.amountBelow);
            }
            if (conditions.currency) {
                policy.forCurrency(conditions.currency);
            }
            if (conditions.paymentMethod) {
                policy.forPaymentMethod(conditions.paymentMethod);
            }
            if (conditions.merchantId) {
                policy.forMerchant(conditions.merchantId);
            }
        }
        // Register policy
        verificationService.registerPolicy(policy);
        res.status(201).json({
            success: true,
            policy: { name: policy.getName(),
                description: policy.getDescription(),
                requiredMethods: policy.getRequiredMethods()
            }
        });
    }
    catch (error) {
        logger_1.logger.error("Error creating verification policy:", error);
        next(new error_middleware_1.AppError({
            message: "Failed to create verification policy",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};
exports.createPolicy = createPolicy;
/**
 * Get applicable policies for a verification request
 */
const getApplicablePolicies = async (req, res, next) => {
    try {
        const { merchantId, amount, currency, paymentMethodType } = req.body;
        if (!merchantId || !amount || !currency || !paymentMethodType) {
            return next(new error_middleware_1.AppError({
                message: "Missing required fields",
                type: ErrorType.VALIDATION,
                code: ErrorCode.MISSING_REQUIRED_FIELD
            }));
        }
        // Create request
        const request = {
            transactionId: "test-transaction",
            merchantId,
            amount: parseFloat(amount),
            currency,
            paymentMethodType,
            paymentMethodId: "test-payment-method",
            verificationMethod: "test-verification-method",
            metadata: req.body.metadata || {}
        };
        // Find applicable policies
        const applicablePolicies = await verificationService.findApplicablePolicies(request);
        res.status(200).json({
            success: true,
            policies: applicablePolicies.map(policy => ({
                name: policy.getName(),
                description: policy.getDescription(),
                requiredMethods: policy.getRequiredMethods()
            }))
        });
    }
    catch (error) {
        logger_1.logger.error("Error getting applicable verification policies:", error);
        next(new error_middleware_1.AppError({
            message: "Failed to get applicable verification policies",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};
exports.getApplicablePolicies = getApplicablePolicies;
/**
 * Verify using policy chain
 */
const verifyWithPolicyChain = async (req, res, next) => {
    try {
        const { transactionId, merchantId, amount, currency, paymentMethodId, paymentMethodType, verificationData, options } = req.body;
        if (!transactionId || !merchantId || !amount || !currency || !paymentMethodType) {
            return next(new error_middleware_1.AppError({
                message: "Missing required fields",
                type: ErrorType.VALIDATION,
                code: ErrorCode.MISSING_REQUIRED_FIELD
            }));
        }
        // Create request
        const request = {
            transactionId,
            merchantId,
            amount: parseFloat(amount),
            currency,
            paymentMethodType,
            paymentMethodId: paymentMethodId || "default-payment-method",
            verificationMethod: "policy-chain",
            verificationData: verificationData || {},
            metadata: req.body.metadata || {}
        };
        // Verify with chain
        const result = await verificationService.verifyWithChain(request, options);
        res.json({
            success: result.success,
            transactionId: result.transactionId,
            message: result.message,
            completedSteps: result.completedSteps,
            totalSteps: result.totalSteps,
            stepResults: result.stepResults.map(step => ({
                success: step.success,
                stepName: step.stepName,
                stepIndex: step.stepIndex,
                message: step.message
            })),
            timestamp: result.timestamp
        });
    }
    catch (error) {
        logger_1.logger.error("Error verifying with policy chain:", error);
        next(new error_middleware_1.AppError({
            message: "Failed to verify with policy chain",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};
exports.verifyWithPolicyChain = verifyWithPolicyChain;
exports.default = {
    getAllPolicies: exports.getAllPolicies,
    createPolicy: exports.createPolicy,
    getApplicablePolicies: exports.getApplicablePolicies,
    verifyWithPolicyChain: exports.verifyWithPolicyChain
};
//# sourceMappingURL=verification-policy.controller.js.map