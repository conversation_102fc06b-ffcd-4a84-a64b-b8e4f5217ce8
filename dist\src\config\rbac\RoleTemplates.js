"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ROLE_TEMPLATES = exports.AUDITOR_TEMPLATE = exports.SYSTEM_ADMINISTRATOR_TEMPLATE = exports.ANALYTICS_MANAGER_TEMPLATE = exports.COMPLIANCE_OFFICER_TEMPLATE = exports.SUPPORT_ADMIN_TEMPLATE = exports.SECURITY_ADMIN_TEMPLATE = exports.MERCHANT_ADMIN_TEMPLATE = exports.FINANCIAL_ADMIN_TEMPLATE = exports.ADMIN_TEMPLATE = exports.SUPER_ADMIN_TEMPLATE = void 0;
// jscpd:ignore-file
/**
 * Role Templates
 *
 * Defines predefined role templates for quick assignment.
 */
const PermissionGroups_1 = require("./PermissionGroups");
/**
 * Super Admin role template
 */
exports.SUPER_ADMIN_TEMPLATE = {
    name: "Super Admin",
    type: "super_admin",
    description: "Full access to all system features",
    permissions: PermissionGroups_1.ALL_PERMISSIONS,
    isSystem: true
};
/**
 * Admin role template
 */
exports.ADMIN_TEMPLATE = {
    name: "Admin",
    type: "admin",
    description: "Administrative access to most system features",
    permissions: [
        ...PermissionGroups_1.ADMIN_PERMISSIONS,
        ...PermissionGroups_1.MERCHANT_PERMISSIONS.ALL,
        ...PermissionGroups_1.PAYMENT_PERMISSIONS.READ,
        ...PermissionGroups_1.PAYMENT_PERMISSIONS.WRITE,
        ...PermissionGroups_1.VERIFICATION_PERMISSIONS.READ,
        ...PermissionGroups_1.SUBSCRIPTION_PERMISSIONS.READ,
        ...PermissionGroups_1.ANALYTICS_PERMISSIONS.READ,
        ...PermissionGroups_1.MONITORING_PERMISSIONS.READ,
        ...PermissionGroups_1.SECURITY_PERMISSIONS.READ,
        ...PermissionGroups_1.SETTINGS_PERMISSIONS.READ,
        ...PermissionGroups_1.NOTIFICATION_PERMISSIONS.ALL
    ],
    isSystem: true
};
/**
 * Financial Admin role template
 */
exports.FINANCIAL_ADMIN_TEMPLATE = {
    name: "Financial Admin",
    type: "financial_admin",
    description: "Manages payments, transactions, and fees",
    permissions: [
        ...PermissionGroups_1.ADMIN_PERMISSIONS,
        ...PermissionGroups_1.MERCHANT_PERMISSIONS.READ,
        ...PermissionGroups_1.PAYMENT_PERMISSIONS.ALL,
        ...PermissionGroups_1.SUBSCRIPTION_PERMISSIONS.ALL,
        ...PermissionGroups_1.ANALYTICS_PERMISSIONS.READ,
        ...PermissionGroups_1.ANALYTICS_PERMISSIONS.WRITE
    ],
    isSystem: true
};
/**
 * Merchant Admin role template
 */
exports.MERCHANT_ADMIN_TEMPLATE = {
    name: "Merchant Admin",
    type: "merchant_admin",
    description: "Manages merchant accounts and approvals",
    permissions: [
        ...PermissionGroups_1.ADMIN_PERMISSIONS,
        ...PermissionGroups_1.MERCHANT_PERMISSIONS.ALL,
        ...PermissionGroups_1.SUBSCRIPTION_PERMISSIONS.READ,
        ...PermissionGroups_1.ANALYTICS_PERMISSIONS.READ
    ],
    isSystem: true
};
/**
 * Security Admin role template
 */
exports.SECURITY_ADMIN_TEMPLATE = {
    name: "Security Admin",
    type: "security_admin",
    description: "Manages security settings and verification methods",
    permissions: [
        ...PermissionGroups_1.ADMIN_PERMISSIONS,
        ...PermissionGroups_1.VERIFICATION_PERMISSIONS.ALL,
        ...PermissionGroups_1.SECURITY_PERMISSIONS.ALL,
        ...PermissionGroups_1.MONITORING_PERMISSIONS.ALL
    ],
    isSystem: true
};
/**
 * Support Admin role template
 */
exports.SUPPORT_ADMIN_TEMPLATE = {
    name: "Support Admin",
    type: "support_admin",
    description: "Provides merchant support and troubleshooting",
    permissions: [
        ...PermissionGroups_1.ADMIN_PERMISSIONS,
        ...PermissionGroups_1.MERCHANT_PERMISSIONS.READ,
        ...PermissionGroups_1.PAYMENT_PERMISSIONS.READ,
        ...PermissionGroups_1.VERIFICATION_PERMISSIONS.READ,
        ...PermissionGroups_1.MONITORING_PERMISSIONS.READ,
        ...PermissionGroups_1.NOTIFICATION_PERMISSIONS.READ
    ],
    isSystem: true
};
/**
 * Compliance Officer role template
 */
exports.COMPLIANCE_OFFICER_TEMPLATE = {
    name: "Compliance Officer",
    type: "compliance_officer",
    description: "Monitors compliance with regulations and policies",
    permissions: [
        ...PermissionGroups_1.ADMIN_PERMISSIONS,
        ...PermissionGroups_1.MERCHANT_PERMISSIONS.READ,
        ...PermissionGroups_1.MERCHANT_PERMISSIONS.APPROVE,
        ...PermissionGroups_1.VERIFICATION_PERMISSIONS.READ,
        ...PermissionGroups_1.MONITORING_PERMISSIONS.READ,
        ...PermissionGroups_1.SECURITY_PERMISSIONS.READ
    ],
    isSystem: true
};
/**
 * Analytics Manager role template
 */
exports.ANALYTICS_MANAGER_TEMPLATE = {
    name: "Analytics Manager",
    type: "analytics_manager",
    description: "Manages analytics and reporting",
    permissions: [
        ...PermissionGroups_1.ADMIN_PERMISSIONS,
        ...PermissionGroups_1.ANALYTICS_PERMISSIONS.ALL,
        ...PermissionGroups_1.MERCHANT_PERMISSIONS.READ,
        ...PermissionGroups_1.PAYMENT_PERMISSIONS.READ
    ],
    isSystem: true
};
/**
 * System Administrator role template
 */
exports.SYSTEM_ADMINISTRATOR_TEMPLATE = {
    name: "System Administrator",
    type: "system_administrator",
    description: "Manages system settings and configurations",
    permissions: [
        ...PermissionGroups_1.ADMIN_PERMISSIONS,
        ...PermissionGroups_1.SETTINGS_PERMISSIONS.ALL,
        ...PermissionGroups_1.SECURITY_PERMISSIONS.ALL,
        ...PermissionGroups_1.ROLE_PERMISSIONS.ALL,
        ...PermissionGroups_1.ADMIN_USER_PERMISSIONS.ALL
    ],
    isSystem: true
};
/**
 * Auditor role template
 */
exports.AUDITOR_TEMPLATE = {
    name: "Auditor",
    type: "auditor",
    description: "Read-only access to all system data for auditing purposes",
    permissions: [
        ...PermissionGroups_1.ADMIN_PERMISSIONS,
        ...PermissionGroups_1.MERCHANT_PERMISSIONS.READ,
        ...PermissionGroups_1.PAYMENT_PERMISSIONS.READ,
        ...PermissionGroups_1.VERIFICATION_PERMISSIONS.READ,
        ...PermissionGroups_1.SUBSCRIPTION_PERMISSIONS.READ,
        ...PermissionGroups_1.ANALYTICS_PERMISSIONS.READ,
        ...PermissionGroups_1.MONITORING_PERMISSIONS.READ,
        ...PermissionGroups_1.SECURITY_PERMISSIONS.READ,
        ...PermissionGroups_1.SETTINGS_PERMISSIONS.READ,
        ...PermissionGroups_1.NOTIFICATION_PERMISSIONS.READ,
        ...PermissionGroups_1.ROLE_PERMISSIONS.READ,
        ...PermissionGroups_1.ADMIN_USER_PERMISSIONS.READ
    ],
    isSystem: true
};
/**
 * All role templates
 */
exports.ROLE_TEMPLATES = {
    SUPER_ADMIN: exports.SUPER_ADMIN_TEMPLATE,
    ADMIN: exports.ADMIN_TEMPLATE,
    FINANCIAL_ADMIN: exports.FINANCIAL_ADMIN_TEMPLATE,
    MERCHANT_ADMIN: exports.MERCHANT_ADMIN_TEMPLATE,
    SECURITY_ADMIN: exports.SECURITY_ADMIN_TEMPLATE,
    SUPPORT_ADMIN: exports.SUPPORT_ADMIN_TEMPLATE,
    COMPLIANCE_OFFICER: exports.COMPLIANCE_OFFICER_TEMPLATE,
    ANALYTICS_MANAGER: exports.ANALYTICS_MANAGER_TEMPLATE,
    SYSTEM_ADMINISTRATOR: exports.SYSTEM_ADMINISTRATOR_TEMPLATE,
    AUDITOR: exports.AUDITOR_TEMPLATE
};
//# sourceMappingURL=RoleTemplates.js.map