"use strict";
/**
 * Fraud Detection Controller Types
 *
 * Type definitions for fraud detection controller components.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.FraudDetectionResource = exports.FraudDetectionAction = exports.RiskLevel = void 0;
/**
 * Risk level enum
 */
var RiskLevel;
(function (RiskLevel) {
    RiskLevel["LOW"] = "LOW";
    RiskLevel["MEDIUM"] = "MEDIUM";
    RiskLevel["HIGH"] = "HIGH";
    RiskLevel["CRITICAL"] = "CRITICAL";
})(RiskLevel || (exports.RiskLevel = RiskLevel = {}));
/**
 * Fraud detection action types
 */
var FraudDetectionAction;
(function (FraudDetectionAction) {
    FraudDetectionAction["ASSESS_RISK"] = "assess_risk";
    FraudDetectionAction["VIEW_ASSESSMENT"] = "view_assessment";
    FraudDetectionAction["VIEW_CONFIG"] = "view_config";
    FraudDetectionAction["UPDATE_CONFIG"] = "update_config";
    FraudDetectionAction["VIEW_FLAGGED"] = "view_flagged";
    FraudDetectionAction["VIEW_STATISTICS"] = "view_statistics";
})(FraudDetectionAction || (exports.FraudDetectionAction = FraudDetectionAction = {}));
/**
 * Fraud detection resource types
 */
var FraudDetectionResource;
(function (FraudDetectionResource) {
    FraudDetectionResource["RISK_ASSESSMENT"] = "risk_assessment";
    FraudDetectionResource["FRAUD_CONFIG"] = "fraud_config";
    FraudDetectionResource["FLAGGED_TRANSACTIONS"] = "flagged_transactions";
    FraudDetectionResource["FRAUD_STATISTICS"] = "fraud_statistics";
})(FraudDetectionResource || (exports.FraudDetectionResource = FraudDetectionResource = {}));
//# sourceMappingURL=FraudDetectionControllerTypes.js.map