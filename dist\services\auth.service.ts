// jscpd:ignore-file

import jwt from 'jsonwebtoken';
import { User } from '@prisma/client';
import { logger } from '../lib/logger';
import prisma from '../lib/prisma';
import { CryptoUtils } from '../utils';
import twoFactorAuthService from './two-factor-auth.service';

export interface LoginResponse {
  token: string;
  user: {
    id: string;
    email: string;
    role: string;
    twoFactorEnabled?: boolean;
  };
  merchantId?: string;
}

export interface MerchantRegistrationData {
  name: string;
  email: string;
  password: string;
  businessName?: string;
  contactPhone: string;
  merchantLocation: string;
  country: string;
  governorate: string;
}

export interface MerchantRegistrationResponse {
  merchant: {
    id: string;
    name: string;
    email: string;
    businessName?: string;
    contactPhone: string;
    merchantLocation: string;
    country: string;
    governorate: string;
    isActive: boolean;
    isVerified: boolean;
    createdAt: Date;
  };
  token: string;
}

class AuthService {
  async login(email: string, password: string): Promise<LoginResponse> {
    try {
      // Find user by email
      const user: any = await prisma.user.findUnique({
        where: { email },
      });

      if (!user) {
        throw new Error('Invalid credentials');
      }

      // Check if user is active
      if (!user.isActive) {
        throw new Error('User account is inactive');
      }

      // Verify password
      const isPasswordValid: any = await CryptoUtils.verifyPassword(
        password,
        user.hashedPassword,
        user.salt || ''
      );

      if (!isPasswordValid) {
        throw new Error('Invalid credentials');
      }

      // Update last login timestamp
      await prisma.user.update({
        where: { id: user.id },
        data: { lastLoginAt: new Date() },
      });

      // Generate JWT token
      const token: any = jwt.sign(
        { userId: user.id, role: user.role },
        process.env.JWT_SECRET || 'amazingpay_jwt_secret',
        { expiresIn: process.env.JWT_EXPIRES_IN || '1d' }
      );

      // Get merchant ID if user is a merchant
      let merchantId: string | undefined;

      if (user.role === 'MERCHANT') {
        const merchant = await prisma.merchant.findFirst({
          where: { userId: user.id },
        });
        merchantId = merchant?.id;
      }

      return {
        token,
        user: {
          id: user.id,
          email: user.email,
          role: user.role,
          twoFactorEnabled: user.twoFactorEnabled || false,
        },
        merchantId,
      };
    } catch (error) {
      logger.error(`Login error: ${error}`);
      throw error;
    }
  }

  // Logout a user
  async logout(userId: string): Promise<{ success: boolean }> {
    try {
      // In a more sophisticated implementation, we would add the token to a blacklist
      // For now, we'll just return success
      logger.info(`User ${userId} logged out`);
      return { success: true };
    } catch (error) {
      logger.error(`Logout error: ${error}`);
      throw error;
    }
  }

  // Get user by ID
  async getUserById(userId: string): Promise<any> {
    try {
      const user: any = await prisma.user.findUnique({
        where: { id: userId },
      });

      if (!user) {
        throw new Error('User not found');
      }

      // Return user without sensitive information
      return {
        id: user.id,
        email: user.email,
        role: user.role,
        isActive: user.isActive,
        lastLoginAt: user.lastLoginAt,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      };
    } catch (error) {
      logger.error(`Get user by ID error: ${error}`);
      throw error;
    }
  }

  // Two-factor authentication methods
  async getTwoFactorStatus(userId: string): Promise<{ enabled: boolean }> {
    try {
      return await twoFactorAuthService.getTwoFactorStatus(userId);
    } catch (error) {
      logger.error(`Get 2FA status error: ${error}`);
      throw error;
    }
  }

  async setupTwoFactor(
    userId: string
  ): Promise<{ qrCodeUrl: string; secret: string; backupCodes: string[] }> {
    try {
      return await twoFactorAuthService.setupTwoFactor(userId);
    } catch (error) {
      logger.error(`Setup 2FA error: ${error}`);
      throw error;
    }
  }

  async verifyAndEnableTwoFactor(
    userId: string,
    code: string,
    secret: string
  ): Promise<{ success: boolean }> {
    try {
      return await twoFactorAuthService.verifyAndEnableTwoFactor(userId, code, secret);
    } catch (error) {
      logger.error(`Enable 2FA error: ${error}`);
      throw error;
    }
  }

  async disableTwoFactor(userId: string, code: string): Promise<{ success: boolean }> {
    try {
      return await twoFactorAuthService.disableTwoFactor(userId, code);
    } catch (error) {
      logger.error(`Disable 2FA error: ${error}`);
      throw error;
    }
  }

  /**
   * Verify a two-factor authentication token
   * @param userId User ID
   * @param token Two-factor authentication token
   * @returns Success status
   */
  async verifyTwoFactorToken(userId: string, token: string): Promise<{ success: boolean }> {
    try {
      return await twoFactorAuthService.verifyToken(userId, token);
    } catch (error) {
      logger.error(`Verify 2FA token error: ${error}`);
      throw error;
    }
  }

  async registerMerchant(data: MerchantRegistrationData): Promise<MerchantRegistrationResponse> {
    try {
      // Check if merchant already exists
      const existingMerchant: any = await prisma.merchant.findFirst({
        where: { email: data.email },
      });

      if (existingMerchant) {
        throw new Error('Merchant with this email already exists');
      }

      // Hash password
      const { hash: hashedPassword, salt } = await CryptoUtils.hashPassword(data.password);

      // Generate API key and secret
      const apiKey: any = CryptoUtils.generateUuid();
      const apiSecret: any = CryptoUtils.generateUuid();

      // Create a user first, then associate merchant
      logger.info('Creating user and merchant with proper association');

      // Create a user first
      const user: any = await prisma.user.create({
        data: {
          email: data.email,
          hashedPassword: hashedPassword,
          salt: salt,
          role: 'MERCHANT',
          isActive: true,
        },
      });

      logger.info(`Created user with ID: ${user.id}`);

      // Then create merchant with user association
      const merchant: any = await prisma.merchant.create({
        data: {
          name: data.name,
          email: data.email,
          businessName: data.businessName || null,
          contactPhone: data.contactPhone,
          merchantLocation: data.merchantLocation,
          country: data.country,
          governorate: data.governorate,
          apiKey,
          apiSecret,
          isActive: false, // Requires admin approval
          isVerified: false, // Requires verification
          user: { connect: { id: user.id } },
        },
      });

      logger.info(`Successfully created merchant with ID: ${merchant.id}`);

      // Generate JWT token
      const token: any = jwt.sign(
        { merchantId: merchant.id, email: merchant.email },
        process.env.JWT_SECRET || 'fallback-secret',
        { expiresIn: process.env.JWT_EXPIRES_IN || '1d' }
      );

      // Return merchant data (excluding sensitive fields)
      return {
        merchant: {
          id: merchant.id,
          name: merchant.name,
          email: merchant.email,
          businessName: merchant.businessName,
          contactPhone: merchant.contactPhone,
          merchantLocation: merchant.merchantLocation,
          country: merchant.country,
          governorate: merchant.governorate,
          isActive: merchant.isActive,
          isVerified: merchant.isVerified,
          createdAt: merchant.createdAt,
        },
        token,
      };
    } catch (error) {
      logger.error(`Merchant registration error: ${error}`);
      throw error;
    }
  }
}

export default new AuthService();
