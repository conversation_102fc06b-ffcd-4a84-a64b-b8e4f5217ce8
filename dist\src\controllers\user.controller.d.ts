import { BaseController } from "../../core/BaseController";
/**
 * User controller
 * This controller handles user-related operations
 */
export declare class UserController extends BaseController {
    private userService;
    /**
     * Create a new user controller
     */
    constructor();
    /**
     * Get all users
     */
    getUsers: any;
    /**
     * Get a user by ID
     */
    getUser: any;
    /**
     * Create a new user
     */
    createUser: any;
    /**
     * Update a user
     */
    updateUser: any;
    /**
     * Delete a user
     */
    deleteUser: any;
    /**
     * Get current user
     */
    getCurrentUser: any;
    /**
     * Update current user
     */
    updateCurrentUser: any;
}
//# sourceMappingURL=user.controller.d.ts.map