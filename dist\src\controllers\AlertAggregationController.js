"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AlertAggregationController = void 0;
const BaseController_1 = require("../base/BaseController");
const asyncHandler_1 = require("../../utils/asyncHandler");
const appError_1 = require("../../utils/appError");
const prisma_1 = __importDefault(require("../../lib/prisma"));
const alert_service_1 = require("../../services/alert.service");
/**
 * Alert aggregation controller
 */
class AlertAggregationController extends BaseController_1.BaseController {
    constructor() {
        super();
        /**
         * Get aggregation rules
         * @route GET /api/alerts/aggregation/rules
         */
        this.getAggregationRules = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            // Check authorization and admin role
            const { userRole } = this.checkAuthorization(req);
            this.checkAdminRole(userRole);
            // Get aggregation rules
            const rules = await prisma_1.default.alertAggregationRule.findMany({
                orderBy: { createdAt: "desc" }
            });
            // Return rules
            return this.sendSuccess(res, rules);
        });
        /**
         * Get aggregation rule by ID
         * @route GET /api/alerts/aggregation/rules/:id
         */
        this.getAggregationRule = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            // Check authorization and admin role
            const { userRole } = this.checkAuthorization(req);
            this.checkAdminRole(userRole);
            // Get rule ID from params
            const ruleId = req.params.id;
            if (!ruleId) {
                throw new appError_1.AppError({
                    message: "Rule ID is required",
                    type: ErrorType.VALIDATION,
                    code: ErrorCode.MISSING_REQUIRED_FIELD
                });
            }
            // Get aggregation rule
            const rule = await prisma_1.default.alertAggregationRule.findUnique({
                where: { id: ruleId }
            });
            // Check if rule exists
            if (!rule) {
                throw new appError_1.AppError({
                    message: "Rule not found",
                    type: ErrorType.NOT_FOUND,
                    code: ErrorCode.RESOURCE_NOT_FOUND
                });
            }
            // Return rule
            return this.sendSuccess(res, rule);
        });
        /**
         * Create aggregation rule
         * @route POST /api/alerts/aggregation/rules
         */
        this.createAggregationRule = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            // Check authorization and admin role
            const { userRole } = this.checkAuthorization(req);
            this.checkAdminRole(userRole);
            // Get rule data from body
            const { name, description, type, severity, timeWindow, threshold, groupBy, enabled } = req.body;
            // Validate required fields
            if (!name || !description || !type || !severity || !timeWindow || !threshold || !groupBy) {
                throw new appError_1.AppError({
                    message: "Missing required fields",
                    type: ErrorType.VALIDATION,
                    code: ErrorCode.MISSING_REQUIRED_FIELD
                });
            }
            // Validate type
            if (type !== "ANY" && !Object.values(alert_service_1.AlertType).includes(type)) {
                throw new appError_1.AppError({
                    message: "Invalid alert type",
                    type: ErrorType.VALIDATION,
                    code: ErrorCode.INVALID_INPUT
                });
            }
            // Validate severity
            if (severity !== "ANY" && !Object.values(alert_service_1.AlertSeverity).includes(severity)) {
                throw new appError_1.AppError({
                    message: "Invalid alert severity",
                    type: ErrorType.VALIDATION,
                    code: ErrorCode.INVALID_INPUT
                });
            }
            // Validate timeWindow
            if (timeWindow <= 0) {
                throw new appError_1.AppError({
                    message: "Time window must be greater than 0",
                    type: ErrorType.VALIDATION,
                    code: ErrorCode.INVALID_INPUT
                });
            }
            // Validate threshold
            if (threshold <= 0) {
                throw new appError_1.AppError({
                    message: "Threshold must be greater than 0",
                    type: ErrorType.VALIDATION,
                    code: ErrorCode.INVALID_INPUT
                });
            }
            // Validate groupBy
            if (!Array.isArray(groupBy) || groupBy.length === 0) {
                throw new appError_1.AppError({
                    message: "Group by must be a non-empty array",
                    type: ErrorType.VALIDATION,
                    code: ErrorCode.INVALID_INPUT
                });
            }
            // Create aggregation rule
            const rule = await prisma_1.default.alertAggregationRule.create({
                data: {
                    name,
                    description,
                    type,
                    severity,
                    timeWindow,
                    threshold,
                    groupBy,
                    enabled: enabled !== undefined ? enabled : true
                }
            });
            // Return created rule
            return this.sendSuccess(res, rule, 201);
        });
        /**
         * Update aggregation rule
         * @route PUT /api/alerts/aggregation/rules/:id
         */
        this.updateAggregationRule = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            // Check authorization and admin role
            const { userRole } = this.checkAuthorization(req);
            this.checkAdminRole(userRole);
            // Get rule ID from params
            const ruleId = req.params.id;
            if (!ruleId) {
                throw new appError_1.AppError({
                    message: "Rule ID is required",
                    type: ErrorType.VALIDATION,
                    code: ErrorCode.MISSING_REQUIRED_FIELD
                });
            }
            // Get rule data from body
            const { name, description, type, severity, timeWindow, threshold, groupBy, enabled } = req.body;
            // Check if rule exists
            const existingRule = await prisma_1.default.alertAggregationRule.findUnique({
                where: { id: ruleId }
            });
            if (!existingRule) {
                throw new appError_1.AppError({
                    message: "Rule not found",
                    type: ErrorType.NOT_FOUND,
                    code: ErrorCode.RESOURCE_NOT_FOUND
                });
            }
            // Validate type if provided
            if (type !== undefined && type !== "ANY" && !Object.values(alert_service_1.AlertType).includes(type)) {
                throw new appError_1.AppError({
                    message: "Invalid alert type",
                    type: ErrorType.VALIDATION,
                    code: ErrorCode.INVALID_INPUT
                });
            }
            // Validate severity if provided
            if (severity !== undefined && severity !== "ANY" && !Object.values(alert_service_1.AlertSeverity).includes(severity)) {
                throw new appError_1.AppError({
                    message: "Invalid alert severity",
                    type: ErrorType.VALIDATION,
                    code: ErrorCode.INVALID_INPUT
                });
            }
            // Validate timeWindow if provided
            if (timeWindow !== undefined && timeWindow <= 0) {
                throw new appError_1.AppError({
                    message: "Time window must be greater than 0",
                    type: ErrorType.VALIDATION,
                    code: ErrorCode.INVALID_INPUT
                });
            }
            // Validate threshold if provided
            if (threshold !== undefined && threshold <= 0) {
                throw new appError_1.AppError({
                    message: "Threshold must be greater than 0",
                    type: ErrorType.VALIDATION,
                    code: ErrorCode.INVALID_INPUT
                });
            }
            // Validate groupBy if provided
            if (groupBy !== undefined && (!Array.isArray(groupBy) || groupBy.length === 0)) {
                throw new appError_1.AppError({
                    message: "Group by must be a non-empty array",
                    type: ErrorType.VALIDATION,
                    code: ErrorCode.INVALID_INPUT
                });
            }
            // Update aggregation rule
            const rule = await prisma_1.default.alertAggregationRule.update({
                where: { id: ruleId },
                data: { name: name !== undefined ? name : undefined,
                    description: description !== undefined ? description : undefined,
                    type: type !== undefined ? type : undefined,
                    severity: severity !== undefined ? severity : undefined,
                    timeWindow: timeWindow !== undefined ? timeWindow : undefined,
                    threshold: threshold !== undefined ? threshold : undefined,
                    groupBy: groupBy !== undefined ? groupBy : undefined,
                    enabled: enabled !== undefined ? enabled : undefined
                }
            });
            // Return updated rule
            return this.sendSuccess(res, rule);
        });
        /**
         * Delete aggregation rule
         * @route DELETE /api/alerts/aggregation/rules/:id
         */
        this.deleteAggregationRule = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            // Check authorization and admin role
            const { userRole } = this.checkAuthorization(req);
            this.checkAdminRole(userRole);
            // Get rule ID from params
            const ruleId = req.params.id;
            if (!ruleId) {
                throw new appError_1.AppError({
                    message: "Rule ID is required",
                    type: ErrorType.VALIDATION,
                    code: ErrorCode.MISSING_REQUIRED_FIELD
                });
            }
            // Check if rule exists
            const existingRule = await prisma_1.default.alertAggregationRule.findUnique({
                where: { id: ruleId }
            });
            if (!existingRule) {
                throw new appError_1.AppError({
                    message: "Rule not found",
                    type: ErrorType.NOT_FOUND,
                    code: ErrorCode.RESOURCE_NOT_FOUND
                });
            }
            // Delete aggregation rule
            await prisma_1.default.alertAggregationRule.delete({
                where: { id: ruleId }
            });
            // Return success
            return this.sendSuccess(res, { success: true, message: "Rule deleted successfully" });
        });
        /**
         * Get correlation rules
         * @route GET /api/alerts/correlation/rules
         */
        this.getCorrelationRules = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            // Check authorization and admin role
            const { userRole } = this.checkAuthorization(req);
            this.checkAdminRole(userRole);
            // Get correlation rules
            const rules = await prisma_1.default.alertCorrelationRule.findMany({
                orderBy: { createdAt: "desc" }
            });
            // Return rules
            return this.sendSuccess(res, rules);
        });
        /**
         * Get correlation rule by ID
         * @route GET /api/alerts/correlation/rules/:id
         */
        this.getCorrelationRule = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            // Check authorization and admin role
            const { userRole } = this.checkAuthorization(req);
            this.checkAdminRole(userRole);
            // Get rule ID from params
            const ruleId = req.params.id;
            if (!ruleId) {
                throw new appError_1.AppError({
                    message: "Rule ID is required",
                    type: ErrorType.VALIDATION,
                    code: ErrorCode.MISSING_REQUIRED_FIELD
                });
            }
            // Get correlation rule
            const rule = await prisma_1.default.alertCorrelationRule.findUnique({
                where: { id: ruleId }
            });
            // Check if rule exists
            if (!rule) {
                throw new appError_1.AppError({
                    message: "Rule not found",
                    type: ErrorType.NOT_FOUND,
                    code: ErrorCode.RESOURCE_NOT_FOUND
                });
            }
            // Return rule
            return this.sendSuccess(res, rule);
        });
    }
}
exports.AlertAggregationController = AlertAggregationController;
//# sourceMappingURL=AlertAggregationController.js.map