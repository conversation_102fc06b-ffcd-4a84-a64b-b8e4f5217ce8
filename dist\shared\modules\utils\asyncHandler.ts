/**
 * Async Handler
 * 
 * This module provides a utility function for handling async controller methods.
 */

import { Request, Response, NextFunction } from 'express';

/**
 * Wrap an async function to catch errors and pass them to the next middleware
 */
export const asyncHandler: any =(fn: (req: Request, res: Response, next: NextFunction) => Promise<any>) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      await fn(req, res, next);
    } catch (error) {
      next(error);
    }
  };
};