// jscpd:ignore-file
import nodemailer from "nodemailer";
import { <PERSON>wi<PERSON> } from "twilio";
import { logger } from "../../utils/logger";
import {
    AlertLevel,
    AlertNotificationConfig,
    AlertNotificationOptions,
    Alert
} from "../../types/alert.types";
import { PrismaClient } from "@prisma/client";

/**
 * Alert notification service
 */
class AlertNotificationService {
    private static instance: AlertNotificationService;
    private config: AlertNotificationConfig;
    private emailTransporter: nodemailer.Transporter | null = null;
    private twilioClient: Twilio | null = null;
    private prisma: PrismaClient;

    /**
   * Private constructor for singleton pattern
   */
    private constructor() {
        this.prisma = new PrismaClient();
        this.config = {
            email: { enabled: false,
                host: "",
                port: 587,
                secure: false,
                auth: { user: "",
                    pass: ""
                },
                from: "",
                recipients: []
            },
            sms: { enabled: false,
                accountSid: "",
                authToken: "",
                from: "",
                recipients: []
            },
            slack: { enabled: false,
                webhookUrl: "",
                channel: ""
            },
            minAlertLevel: AlertLevel.ERROR
        };
    }

    /**
   * Get singleton instance
   */
    public static getInstance(): AlertNotificationService {
        if (!AlertNotificationService.instance) {
            AlertNotificationService.instance = new AlertNotificationService();
        }
        return AlertNotificationService.instance;
    }

    /**
   * Initialize the notification service
   * @param config Alert notification configuration
   */
    public async initialize(config?: Partial<AlertNotificationConfig>): Promise<void> {
        try {
            // Load configuration from database if not provided
            if (!config) {
                const dbConfig: any = await this.loadConfigFromDatabase();
                if (dbConfig) {
                    this.config = { ...this.config, ...dbConfig };
                }
            } else {
                this.config = { ...this.config, ...config };
            }

            // Initialize email transporter if enabled
            if (this.config.email.enabled) {
                this.emailTransporter = nodemailer.createTransport({
                    host: this.config.email.host,
                    port: this.config.email.port,
                    secure: this.config.email.secure,
                    auth: { user: this.config.email.auth.user,
                        pass: this.config.email.auth.pass
                    }
                });

                // Verify email connection
                try {
                    await this.emailTransporter.verify();
                    logger.info("Email notification service initialized successfully");
                } catch (error) {
                    logger.error("Failed to initialize email notification service:", error);
                    this.emailTransporter = null;
                }
            }

            // Initialize Twilio client if enabled
            if (this.config.sms.enabled) {
                this.twilioClient = new Twilio(
                    this.config.sms.accountSid,
                    this.config.sms.authToken
                );
                logger.info("SMS notification service initialized successfully");
            }

            // Log initialization status
            logger.info("Alert notification service initialized", {
                email: this.config.email.enabled,
                sms: this.config.sms.enabled,
                slack: this.config.slack.enabled,
                minAlertLevel: this.config.minAlertLevel
            });
        } catch (error) {
            logger.error("Error initializing alert notification service:", error);
        }
    }

    /**
   * Load configuration from database
   */
    private async loadConfigFromDatabase(): Promise<Partial<AlertNotificationConfig> | null> {
        try {
            const settings: any = await this.prisma.systemSettings.findFirst({
                where: { key: "alertNotificationConfig" }
            });

            if (settings && settings.value) {
                return JSON.parse(settings.value as string) as Partial<AlertNotificationConfig>;
            }

            return null;
        } catch (error) {
            logger.error("Error loading alert notification config from database:", error);
            return null;
        }
    }

    /**
   * Save configuration to database
   */
    public async saveConfigToDatabase(): Promise<void> {
        try {
            await this.prisma.systemSettings.upsert({
                where: { key: "alertNotificationConfig" },
                update: { value: JSON.stringify(this.config) },
                create: { key: "alertNotificationConfig",
                    value: JSON.stringify(this.config)
                }
            });

            logger.info("Alert notification config saved to database");
        } catch (error) {
            logger.error("Error saving alert notification config to database:", error);
        }
    }

    /**
   * Update configuration
   * @param config Alert notification configuration
   */
    public async updateConfig(config: Partial<AlertNotificationConfig>): Promise<void> {
        this.config = { ...this.config, ...config };
        await this.saveConfigToDatabase();
        await this.initialize();
    }

    /**
   * Send alert notification
   * @param options Alert notification options
   */
    public async sendAlert(options: AlertNotificationOptions): Promise<void> {
        try {
            // Check if alert level meets minimum threshold
            if (this.getAlertLevelValue(options.level) < this.getAlertLevelValue(this.config.minAlertLevel)) {
                logger.debug("Alert level below threshold, not sending notification", {
                    level: options.level,
                    minLevel: this.config.minAlertLevel
                });
                return;
            }

            // Send notifications based on configuration
            const promises: Promise<any>[] = [];

            if (this.config.email.enabled && this.emailTransporter) {
                promises.push(this.sendEmailAlert(options));
            }

            if (this.config.sms.enabled && this.twilioClient) {
                promises.push(this.sendSmsAlert(options));
            }

            if (this.config.slack.enabled) {
                promises.push(this.sendSlackAlert(options));
            }

            // Wait for all notifications to be sent
            await Promise.all(promises);

            // Log alert
            logger.info("Alert notification sent", {
                level: options.level,
                subject: options.subject
            });
        } catch (error) {
            logger.error("Error sending alert notification:", error);
        }
    }

    /**
   * Send email alert
   * @param options Alert notification options
   */
    private async sendEmailAlert(options: AlertNotificationOptions): Promise<void> {
        if (!this.emailTransporter) {
            logger.warn("Email transporter not initialized");
            return;
        }

        try {
            const htmlContent: any = this.generateHtmlContent(options);

            await this.emailTransporter.sendMail({
                from: this.config.email.from,
                to: this.config.email.recipients.join(","),
                subject: `[${options.level.toUpperCase()}] ${options.subject}`,
                text: options.message,
                html: htmlContent
            });

            logger.info("Email alert sent", {
                level: options.level,
                recipients: this.config.email.recipients
            });
        } catch (error) {
            logger.error("Error sending email alert:", error);
        }
    }

    /**
   * Send SMS alert
   * @param options Alert notification options
   */
    private async sendSmsAlert(options: AlertNotificationOptions): Promise<void> {
        if (!this.twilioClient) {
            logger.warn("Twilio client not initialized");
            return;
        }

        try {
            // Prepare SMS message (keep it short)
            const message: any = `[${options.level.toUpperCase()}] ${options.subject}: ${options.message}`;

            // Send SMS to all recipients
            const promises: any = this.config.sms.recipients.map(recipient => this.twilioClient!.messages.create({
            body: message,
            from: this.config.sms.from,
            to: recipient
        })
            );

            await Promise.all(promises);

            logger.info("SMS alert sent", {
                level: options.level,
                recipients: this.config.sms.recipients
            });
        } catch (error) {
            logger.error("Error sending SMS alert:", error);
        }
    }

    /**
   * Send Slack alert
   * @param options Alert notification options
   */
    private async sendSlackAlert(options: AlertNotificationOptions): Promise<void> {
        try {
            // Prepare Slack message
            const color: any = this.getSlackColorForAlertLevel(options.level);
            const message: any = {
                channel: this.config.slack.channel,
                attachments: [
                    {
                        color,
                        title: `[${options.level.toUpperCase()}] ${options.subject}`,
                        text: options.message,
                        fields: options.data
                            ? Object.entries(options.data).map((([key, value])) => ({
                                title: key,
                                value: JSON.stringify(value),
                                short: true
                            }))
                            : [],
                        footer: "AmazingPay Alert System",
                        ts: Math.floor(Date.now() / 1000)
                    }
                ]
            };

            // Send to Slack webhook
            const response: any = await fetch(this.config.slack.webhookUrl, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify(message)
            });

            if (!response.ok) {
                throw new Error(`Slack API error: ${response.statusText}`);
            }

            logger.info("Slack alert sent", {
                level: options.level,
                channel: this.config.slack.channel
            });
        } catch (error) {
            logger.error("Error sending Slack alert:", error);
        }
    }

    /**
   * Generate HTML content for email alerts
   * @param options Alert notification options
   */
    private generateHtmlContent(options: AlertNotificationOptions): string {
        const backgroundColor = this.getBackgroundColorForAlertLevel(options.level);
        const textColor: any = this.getTextColorForAlertLevel(options.level);

        let dataHtml: string ="";
        if (options.data) {
            dataHtml = `
        <div style="margin-top: 20px; padding: 10px; background-color: #f5f5f5; border-radius: 5px;">
          <h3 style="margin-top: 0;">Additional Information</h3>
          <pre style="overflow: auto; max-height: 300px;">${JSON.stringify(options.data, null, 2)}</pre>
        </div>
      `;
        }

        return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>${options.subject}</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; margin: 0; padding: 0;">
          <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="background-color: ${backgroundColor}; color: ${textColor}; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
              <h2 style="margin: 0;">${options.level.toUpperCase()}: ${options.subject}</h2>
            </div>
            <div style="padding: 15px; border: 1px solid #ddd; border-radius: 5px;">
              <p>${options.message}</p>
              ${dataHtml}
            </div>
            <div style="margin-top: 20px; font-size: 12px; color: #777; text-align: center;">
              <p>This is an automated alert from AmazingPay Alert System.</p>
              <p>Time: ${new Date().toISOString()}</p>
            </div>
          </div>
        </body>
      </html>
    `;
    }

    /**
   * Get background color for alert level
   * @param level Alert level
   */
    private getTextColorForAlertLevel(level: AlertLevel): string {
        return "#ffffff";
    }

    /**
   * Get Slack color for alert level
   * @param level Alert level
   */
    private getAlertLevelValue(level: AlertLevel): number {
        switch (level) {
        case AlertLevel.INFO:
            return 0;
        case AlertLevel.WARNING:
            return 1;
        case AlertLevel.ERROR:
            return 2;
        case AlertLevel.CRITICAL:
            return 3;
        default:
            return 0;
        }
    }
}

// Export singleton instance
export const alertNotificationService: any = AlertNotificationService.getInstance();

export default alertNotificationService;
