"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AlertController = void 0;
const BaseController_1 = require("../base/BaseController");
const alert_service_1 = require("../../services/alert.service");
const asyncHandler_1 = require("../../utils/asyncHandler");
const appError_1 = require("../../utils/appError");
const prisma_1 = __importDefault(require("../../lib/prisma"));
/**
 * Alert controller
 */
class AlertController extends BaseController_1.BaseController {
    constructor() {
        super();
        /**
         * Get alerts
         * @route GET /api/alerts
         */
        this.getAlerts = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            // Check authorization
            const { userRole, userId, merchantId } = this.checkAuthorization(req);
            // Get query parameters
            const status = req.query.status;
            const severity = req.query.severity;
            const type = req.query.type;
            const startDate = req.query.startDate ? new Date(req.query.startDate) : undefined;
            const endDate = req.query.endDate ? new Date(req.query.endDate) : undefined;
            const search = req.query.search;
            const limit = this.parseInteger(req.query.limit, "limit", 10);
            const offset = this.parseInteger(req.query.offset, "offset", 0, 0);
            const sortBy = req.query.sortBy || "createdAt";
            const sortOrder = req.query.sortOrder || "desc";
            // Determine target merchant ID
            const targetMerchantId = this.determineTargetMerchantId(userRole, merchantId, req.query.merchantId);
            // Get alerts
            const alerts = await this.alertService.getAlerts({
                merchantId: targetMerchantId,
                status,
                severity,
                type,
                startDate,
                endDate,
                search,
                limit,
                offset,
                sortBy,
                sortOrder
            });
            // Return paginated response
            return this.sendPaginatedSuccess(res, alerts.alerts, alerts.total, limit, offset);
        });
        /**
         * Get alert by ID
         * @route GET /api/alerts/:id
         */
        this.getAlert = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            // Check authorization
            const { userRole, userId, merchantId } = this.checkAuthorization(req);
            // Get alert ID from params
            const alertId = req.params.id;
            if (!alertId) {
                throw new appError_1.AppError({
                    message: "Alert ID is required",
                    type: ErrorType.VALIDATION,
                    code: ErrorCode.MISSING_REQUIRED_FIELD
                });
            }
            // Get alert
            const alert = await this.alertService.getAlert(alertId);
            // Check if user is authorized to view this alert
            if (userRole !== "ADMIN" && alert.merchantId !== merchantId) {
                throw new appError_1.AppError({
                    message: "Unauthorized",
                    type: ErrorType.AUTHENTICATION,
                    code: ErrorCode.INVALID_CREDENTIALS
                });
            }
            // Return alert
            return this.sendSuccess(res, alert);
        });
        /**
         * Update alert status
         * @route PUT /api/alerts/:id/status
         */
        this.updateAlertStatus = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            // Check authorization
            const { userRole, userId, merchantId } = this.checkAuthorization(req);
            // Get alert ID from params
            const alertId = req.params.id;
            if (!alertId) {
                throw new appError_1.AppError({
                    message: "Alert ID is required",
                    type: ErrorType.VALIDATION,
                    code: ErrorCode.MISSING_REQUIRED_FIELD
                });
            }
            // Get status from body
            const { status } = req.body;
            if (!status || !Object.values(alert_service_1.AlertStatus).includes(status)) {
                throw new appError_1.AppError({
                    message: "Valid status is required",
                    type: ErrorType.VALIDATION,
                    code: ErrorCode.MISSING_REQUIRED_FIELD
                });
            }
            // Get alert to check authorization
            const alert = await this.alertService.getAlert(alertId);
            // Check if user is authorized to update this alert
            if (userRole !== "ADMIN" && alert.merchantId !== merchantId) {
                throw new appError_1.AppError({
                    message: "Unauthorized",
                    type: ErrorType.AUTHENTICATION,
                    code: ErrorCode.INVALID_CREDENTIALS
                });
            }
            // Update alert status
            const updatedAlert = await this.alertService.updateAlertStatus(alertId, status, userId);
            // Return updated alert
            return this.sendSuccess(res, updatedAlert);
        });
        /**
         * Create a test alert
         * @route POST /api/alerts/test
         */
        this.createTestAlert = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            // Check authorization
            const { userRole, userId } = this.checkAuthorization(req);
            // Only admins can create test alerts
            this.checkAdminRole(userRole);
            // Get alert data from body
            const { type, severity, title, message, details, targetMerchantId } = req.body;
            // Validate required fields
            if (!type || !severity || !title || !message) {
                throw new appError_1.AppError({
                    message: "Type, severity, title, and message are required",
                    type: ErrorType.VALIDATION,
                    code: ErrorCode.MISSING_REQUIRED_FIELD
                });
            }
            // Validate type and severity
            if (!Object.values(alert_service_1.AlertType).includes(type)) {
                throw new appError_1.AppError({
                    message: "Invalid alert type",
                    type: ErrorType.VALIDATION,
                    code: ErrorCode.INVALID_INPUT
                });
            }
            if (!Object.values(alert_service_1.AlertSeverity).includes(severity)) {
                throw new appError_1.AppError({
                    message: "Invalid alert severity",
                    type: ErrorType.VALIDATION,
                    code: ErrorCode.INVALID_INPUT
                });
            }
            // If targetMerchantId is provided, check if it exists
            if (targetMerchantId) {
                const merchant = await prisma_1.default.merchant.findUnique({
                    where: { id: targetMerchantId }
                });
                if (!merchant) {
                    throw new appError_1.AppError({
                        message: "Target merchant not found",
                        type: ErrorType.NOT_FOUND,
                        code: ErrorCode.RESOURCE_NOT_FOUND
                    });
                }
            }
            // Create test alert
            const alertId = await this.alertService.createAlert({
                type: type,
                severity: severity,
                title,
                message,
                source: "test",
                details: details || {},
                merchantId: targetMerchantId,
                notificationMethods: req.body.notificationMethods || []
            });
            // Return success
            return res.status(201).json({
                success: true,
                message: "Test alert created successfully",
                alertId
            });
        });
        /**
         * Get alert count
         * @route GET /api/alerts/count
         */
        this.getAlertCount = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            // Check authorization
            const { userRole, merchantId } = this.checkAuthorization(req);
            // Get query parameters
            const status = req.query.status;
            const severity = req.query.severity;
            const type = req.query.type;
            const startDate = req.query.startDate ? new Date(req.query.startDate) : undefined;
            const endDate = req.query.endDate ? new Date(req.query.endDate) : undefined;
            const search = req.query.search;
            // Determine target merchant ID
            const targetMerchantId = this.determineTargetMerchantId(userRole, merchantId, req.query.merchantId);
            // Create where clause
            const where = {};
            // Add filters
            if (status)
                where.status = status;
            if (severity)
                where.severity = severity;
            if (type)
                where.type = type;
            if (targetMerchantId)
                where.merchantId = targetMerchantId;
            // Add date range filter
            if (startDate || endDate) {
                where.createdAt = {};
                if (startDate)
                    where.createdAt.gte = startDate;
                if (endDate) {
                    const endOfDay = new Date(endDate);
                    endOfDay.setHours(23, 59, 59, 999);
                    where.createdAt.lte = endOfDay;
                }
            }
            // Add search filter
            if (search) {
                where.OR = [
                    { title: { contains: search, mode: "insensitive" } },
                    { message: { contains: search, mode: "insensitive" } },
                    { source: { contains: search, mode: "insensitive" } }
                ];
            }
            // Get alert count
            const count = await prisma_1.default.alert.count({ where });
            // Get counts by status, severity, and type
            const statusCounts = await prisma_1.default.$queryRaw `
      SELECT status, COUNT(*) as count
      FROM "Alert"
      WHERE ${where.merchantId ? prisma_1.default.sql `"merchantId" = ${where.merchantId}` : prisma_1.default.sql `1=1`}
      GROUP BY status
    `;
            const severityCounts = await prisma_1.default.$queryRaw `
      SELECT severity, COUNT(*) as count
      FROM "Alert"
      WHERE ${where.merchantId ? prisma_1.default.sql `"merchantId" = ${where.merchantId}` : prisma_1.default.sql `1=1`}
      GROUP BY severity
    `;
            const typeCounts = await prisma_1.default.$queryRaw `
      SELECT type, COUNT(*) as count
      FROM "Alert"
      WHERE ${where.merchantId ? prisma_1.default.sql `"merchantId" = ${where.merchantId}` : prisma_1.default.sql `1=1`}
      GROUP BY type
    `;
            // Return counts
            return this.sendSuccess(res, {
                count,
                statusCounts,
                severityCounts,
                typeCounts
            });
        });
        this.alertService = new alert_service_1.AlertService();
    }
}
exports.AlertController = AlertController;
//# sourceMappingURL=AlertController.js.map