// jscpd:ignore-file
/**
 * Operational Mode Controller
 *
 * Handles operational mode operations.
 */

import { Request, Response, NextFunction } from "express";
import { PrismaClient } from "@prisma/client";
import { OperationalModeService, OperationalMode } from "../services/system/OperationalModeService";
import { logger } from "../lib/logger";
import { AppError } from "../middlewares/error.middleware";
import { container } from "../lib/DIContainer";
import { User } from '../types';
import { PrismaClient } from "@prisma/client";
import { OperationalModeService, OperationalMode } from "../services/system/OperationalModeService";
import { logger } from "../lib/logger";
import { AppError } from "../middlewares/error.middleware";
import { container } from "../lib/DIContainer";
import { User } from '../types';

// Extend the Express Request type to include user property
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role: string;
        merchantId?: string;
      };
    }
  }
}


/**
 * Get current operational mode
 */
export const getCurrentMode: unknown =async (req: Request, res: Response, next: NextFunction) => {
    try {
        const operationalModeService: unknown =container.resolve<OperationalModeService>("operationalModeService");
        const status: unknown =await operationalModeService.getSystemStatus();

        res.json({
            success: true,
            status
        });
    } catch (error) {
        logger.error("Error getting operational mode:", error);
        next(new AppError({
            message: "Failed to get operational mode",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};

/**
 * Set operational mode
 */
export const setOperationalMode: unknown =async (req: Request, res: Response, next: NextFunction) => {
    try {
        const { mode } = req.body;

        if (!mode || !Object.values(OperationalMode).includes(mode as OperationalMode)) {
            return next(new AppError({
            message: "Invalid operational mode",
            type: ErrorType.VALIDATION,
            code: ErrorCode.INVALID_INPUT
        }));
        }

        const operationalModeService: unknown =container.resolve<OperationalModeService>("operationalModeService");
        const currentMode: unknown =operationalModeService.getCurrentMode();

        // Prevent switching to the same mode
        if (currentMode === mode) {
            return res.json({
                success: true,
                message: `System is already in ${mode} mode`,
                status: await operationalModeService.getSystemStatus()
            });
        }

        // Only production mode is supported
        if (mode !== OperationalMode.PRODUCTION) {
            logger.error(`Attempted to switch to unsupported mode: ${mode}`);
            return next(new AppError({
            message: "Only production mode is supported",
            type: ErrorType.VALIDATION,
            code: ErrorCode.INVALID_INPUT
        }));
        }

        // Comprehensive validation for production mode
        logger.info("Performing comprehensive validation for production mode");

        // Check database connectivity
        const prisma: any =container.resolve<PrismaClient>("prisma");
        try {
            await prisma.$queryRaw`SELECT 1`;
        } catch (dbError) {
            logger.error("Database connectivity check failed:", dbError);
            return next(new AppError({
            message: "Database connectivity check failed",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
        }

        // Check system health
        const healthStatus: unknown =await checkSystemHealth();
        if (!healthStatus.healthy) {
            logger.error("System health check failed:", healthStatus.issues);
            return next(new AppError(`System health check failed: ${healthStatus.issues.join(", ")}`, 500));
        }

        logger.info("All systems are ready in production mode");

        // Set operational mode
        await operationalModeService.setOperationalMode(mode as OperationalMode, req.user?.id ?? "system");

        // Get updated status
        const status: unknown =await operationalModeService.getSystemStatus();

        // Record admin action
        await recordAdminAction(req.user?.id ?? "system", "change_operational_mode", {
            previousMode: currentMode,
            newMode: mode,
            timestamp: new Date()
        });

        res.json({
            success: true,
            message: `Operational mode set to ${mode}`,
            status,
            previousMode: currentMode
        });
    } catch (error) {
        logger.error("Error setting operational mode:", error);
        next(new AppError({
            message: "Failed to set operational mode",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};

/**
 * Check system health
 * @returns System health status
 */
const checkSystemHealth: unknown =async (): Promise<{ healthy: boolean; issues: string[] }> => {
    const issues: string[] = [];

    try {
    // Check database connectivity
        const prisma: any =container.resolve<PrismaClient>("prisma");
        try {
            await prisma.$queryRaw`SELECT 1`;
        } catch (error) {
            issues.push("Database connectivity check failed");
        }

        // Check available disk space
        // This would be implemented with a system-specific check

        // Check memory usage
        // This would be implemented with a system-specific check

        // Check external service connectivity
        // This would check connectivity to payment gateways, etc.

        return {
            healthy: issues.length === 0,
            issues
        };
    } catch (error) {
        logger.error("Error checking system health:", error);
        return {
            healthy: false,
            issues: ["System health check failed with an unexpected error"]
        };
    }
};

/**
 * Record admin action
 * @param userId User ID
 * @param action Action
 * @param details Action details
 */
const recordAdminAction: unknown =async (userId: string, action: string, details): Promise<void> => {
    try {
        const prisma: any =container.resolve<PrismaClient>("prisma");

        await prisma.adminAction.create({
            data: {
                userId,
                action,
                details,
                timestamp: new Date()
            }
        });
    } catch (error) {
        logger.error("Error recording admin action:", error);
    // Don't throw here to prevent disrupting the main flow
    }
};

/**
 * Enable or disable the system
 */
export const setSystemEnabled: unknown =async (req: Request, res: Response, next: NextFunction) => {
    try {
        const { enabled } = req.body;

        if (typeof enabled !== "boolean") {
            return next(new AppError({
            message: "Invalid system status",
            type: ErrorType.VALIDATION,
            code: ErrorCode.INVALID_INPUT
        }));
        }

        const operationalModeService: unknown =container.resolve<OperationalModeService>("operationalModeService");

        // Set system status
        await operationalModeService.setSystemEnabled(enabled, req.user?.id ?? "system");

        // Get updated status
        const status: unknown =await operationalModeService.getSystemStatus();

        res.json({
            success: true,
            message: `System ${enabled ? "enabled" : "disabled"}`,
            status
        });
    } catch (error) {
        logger.error("Error setting system status:", error);
        next(new AppError({
            message: "Failed to set system status",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};

export default {
    getCurrentMode,
    setOperationalMode,
    setSystemEnabled
};
