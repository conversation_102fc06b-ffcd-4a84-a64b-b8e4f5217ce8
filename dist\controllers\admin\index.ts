/**
 * Admin Controller Module
 * 
 * Centralized exports for the admin controller system.
 */

// Main controller export
export { AdminController } from './AdminController';

// Service exports
export { AdminAuthorizationService } from './services/AdminAuthorizationService';
export { AdminValidationService } from './services/AdminValidationService';
export { AdminBusinessService } from './services/AdminBusinessService';

// Mapper exports
export { AdminResponseMapper } from './mappers/AdminResponseMapper';

// Type exports
export * from './types/AdminControllerTypes';

// Default export - main controller class
export { AdminController as default } from './AdminController';
