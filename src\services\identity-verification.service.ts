import { Request, Response, NextFunction } from 'express';
import { PrismaClient } from '@prisma/client';
// jscpd:ignore-file
// DEPRECATED: This file is being restructured into smaller modules
// New location: src/services/identity-verification/
// TODO: Remove this file after migration is complete

import { PrismaClient, IdentityVerificationMethodEnum, IdentityVerificationStatusEnum } from "@prisma/client";
import { ethers } from "ethers";
import axios from "axios";
import * as crypto from "crypto";
import { AppError } from "../utils/app-error";
import { User, Merchant } from '../types';


/**
 * Error codes for identity verification
 */
export enum IdentityVerificationErrorCode {
  INVALID_SIGNATURE = "INVALID_SIGNATURE",
  INVALID_ADDRESS = "INVALID_ADDRESS",
  INVALID_PROOF = "INVALID_PROOF",
  VERIFICATION_FAILED = "VERIFICATION_FAILED",
  VERIFICATION_NOT_FOUND = "VERIFICATION_NOT_FOUND",
  CLAIM_NOT_FOUND = "CLAIM_NOT_FOUND",
  INTERNAL_ERROR = "INTERNAL_ERROR",
  PROVIDER_ERROR = "PROVIDER_ERROR",
  INVALID_PARAMETERS = "INVALID_PARAMETERS",
  UNAUTHORIZED = "UNAUTHORIZED",
}

/**
 * Custom error class for identity verification errors
 */
export class IdentityVerificationError extends AppError {
    code: IdentityVerificationErrorCode;

    constructor(message: string, code: IdentityVerificationErrorCode, statusCode: number = 400) {
        super(message, statusCode);
        this.code = code;
        this.name = "IdentityVerificationError";
    }
}

const prisma: unknown = new PrismaClient();

// ERC-1484 Identity Registry ABI (simplified)
const ERC1484_ABI: unknown = [
    "function getIdentity(uint ein) view returns (address[] memory, address[] memory, address[] memory)",
    "function getEIN(address _address: unknown) view returns (uint)",
    "function hasIdentity(address _address: unknown) view returns (bool)",
    "function isAssociatedAddressFor(uint ein: unknown, address _address: unknown) view returns (bool)"
];

// ERC-725 Identity ABI (simplified)
const ERC725_ABI: unknown = [
    "function getKey(bytes32 _key) view returns (bytes32)",
    "function getKeys(bytes32[] memory _keys: unknown) view returns (bytes32[] memory)",
    "function execute(uint256 _operation: unknown, address _to: unknown, uint256 _value: unknown, bytes memory _data: unknown) returns (bytes32)"
];

// Polygon ID Verifier ABI (simplified)
const POLYGON_ID_VERIFIER_ABI: unknown = [
    "function verifyProof(uint256 _circuitId, uint256[] memory _inputs: unknown, uint256[2] memory _a: unknown, uint256[2][2] memory _b: unknown, uint256[2] memory _c: unknown, uint256[] memory _signals: unknown) view returns (bool)"
];

// Identity verification result interface
export interface IdentityVerificationResult {
  success: boolean;
  method: IdentityVerificationMethodEnum;
  status: IdentityVerificationStatusEnum;
  message: string;
  data?;
  error?: string;
  verificationId?: string;
}

// Identity verification service
export class IdentityVerificationService {
    /**
   * Verify identity using Ethereum signature
   */
    async verifyEthereumSignature(
        address: string,
        message: string,
        signature: string,
        userId?: string,
        merchantId?: string
    ): Promise<IdentityVerificationResult> {
        try {
            // Verify signature
            const recoveredAddress: unknown = ethers.verifyMessage(message, signature);

            if (recoveredAddress.toLowerCase() !== address.toLowerCase()) {
                return {
                    success: false,
                    method: IdentityVerificationMethodEnum.ETHEREUM_SIGNATURE,
                    status: IdentityVerificationStatusEnum.REJECTED,
                    message: "Signature verification failed",
                    error: "Invalid signature"
                };
            }

            // Create verification record
            const verification: unknown = await prisma.identityVerification.create({
                data: {
                    userId,
                    merchantId,
                    method: IdentityVerificationMethodEnum.ETHEREUM_SIGNATURE,
                    status: IdentityVerificationStatusEnum.VERIFIED,
                    address,
                    verificationData: {
                        message,
                        signature,
                        recoveredAddress
                    },
                    verifiedAt: new Date()
                }
            });

            return {
                success: true,
                method: IdentityVerificationMethodEnum.ETHEREUM_SIGNATURE,
                status: IdentityVerificationStatusEnum.VERIFIED,
                message: "Ethereum signature verified successfully",
                data: {
                    address,
                    message,
                    recoveredAddress
                },
                verificationId: verification.id
            };
        } catch (error) {
            console.error("Error verifying Ethereum signature:", error);

            return {
                success: false,
                method: IdentityVerificationMethodEnum.ETHEREUM_SIGNATURE,
                status: IdentityVerificationStatusEnum.REJECTED,
                message: "Error verifying Ethereum signature",
                error: error instanceof Error ? (error as Error).message : "Unknown error"
            };
        }
    }

    /**
   * Verify identity using ERC-1484 (Ethereum Identity Registry)
   */
    async verifyERC1484Identity(
        address: string,
        ein: string,
        registryAddress: string,
        userId?: string,
        merchantId?: string
    ): Promise<IdentityVerificationResult> {
        try {
            // Connect to Ethereum provider
            const provider: unknown = new ethers.JsonRpcProvider(
                process.env.ETHEREUM_RPC_URL || "https://mainnet.infura.io/v3/your-infura-key"
            );

            // Create contract instance
            const registry: unknown = new ethers.Contract(
                registryAddress,
                ERC1484_ABI,
                provider
            );

            // Check if address has an identity
            const hasIdentity: unknown = await registry.hasIdentity(address);

            if (!hasIdentity) {
                return {
                    success: false,
                    method: IdentityVerificationMethodEnum.ETHEREUM_ERC1484,
                    status: IdentityVerificationStatusEnum.REJECTED,
                    message: "Address does not have an ERC-1484 identity"
                };
            }

            // Get EIN for address
            const addressEin: unknown = await registry.getEIN(address);

            // Check if EIN matches
            if (addressEin.toString() !== ein) {
                return {
                    success: false,
                    method: IdentityVerificationMethodEnum.ETHEREUM_ERC1484,
                    status: IdentityVerificationStatusEnum.REJECTED,
                    message: "EIN does not match address"
                };
            }

            // Check if address is associated with EIN
            const isAssociated: unknown = await registry.isAssociatedAddressFor(addressEin, address);

            if (!isAssociated) {
                return {
                    success: false,
                    method: IdentityVerificationMethodEnum.ETHEREUM_ERC1484,
                    status: IdentityVerificationStatusEnum.REJECTED,
                    message: "Address is not associated with EIN"
                };
            }

            // Create verification record
            const verification: unknown = await prisma.identityVerification.create({
                data: {
                    userId,
                    merchantId,
                    method: IdentityVerificationMethodEnum.ETHEREUM_ERC1484,
                    status: IdentityVerificationStatusEnum.VERIFIED,
                    address,
                    verificationData: {
                        ein,
                        registryAddress,
                        hasIdentity,
                        addressEin: addressEin.toString(),
                        isAssociated
                    },
                    verifiedAt: new Date()
                }
            });

            return {
                success: true,
                method: IdentityVerificationMethodEnum.ETHEREUM_ERC1484,
                status: IdentityVerificationStatusEnum.VERIFIED,
                message: "ERC-1484 identity verified successfully",
                data: {
                    address,
                    ein,
                    registryAddress
                },
                verificationId: verification.id
            };
        } catch (error) {
            console.error("Error verifying ERC-1484 identity:", error);

            return {
                success: false,
                method: IdentityVerificationMethodEnum.ETHEREUM_ERC1484,
                status: IdentityVerificationStatusEnum.REJECTED,
                message: "Error verifying ERC-1484 identity",
                error: error instanceof Error ? (error as Error).message : "Unknown error"
            };
        }
    }

    /**
   * Get identity verification by ID
   */
    async getVerificationById(id: string) {
        try {
            const verification: unknown = await prisma.identityVerification.findUnique({
                where: { id },
                include: { claims: true }
            });

            if (!verification) {
                throw new IdentityVerificationError(
                    "Identity verification not found",
                    IdentityVerificationErrorCode.VERIFICATION_NOT_FOUND,
                    404
                );
            }

            return verification;
        } catch (error) {
            // Re-throw IdentityVerificationError
            if (error instanceof IdentityVerificationError) {
                throw error;
            }

            // Log and wrap other errors
            console.error("Error getting verification by ID:", error);
            throw new IdentityVerificationError(
                "Failed to retrieve identity verification",
                IdentityVerificationErrorCode.INTERNAL_ERROR,
                500
            );
        }
    }

    /**
   * Get identity verifications for user
   */
    async getVerificationsForUser(userId: string) {
        try {
            if (!userId) {
                throw new IdentityVerificationError(
                    "User ID is required",
                    IdentityVerificationErrorCode.INVALID_PARAMETERS,
                    400
                );
            }

            return await prisma.identityVerification.findMany({
                where: { userId },
                include: { claims: true },
                orderBy: { createdAt: "desc" }
            });
        } catch (error) {
            // Re-throw IdentityVerificationError
            if (error instanceof IdentityVerificationError) {
                throw error;
            }

            // Log and wrap other errors
            console.error("Error getting verifications for user:", error);
            throw new IdentityVerificationError(
                "Failed to retrieve identity verifications",
                IdentityVerificationErrorCode.INTERNAL_ERROR,
                500
            );
        }
    }

    /**
   * Get identity verifications for merchant
   */
    async getVerificationsForMerchant(merchantId: string) {
        try {
            if (!merchantId) {
                throw new IdentityVerificationError(
                    "Merchant ID is required",
                    IdentityVerificationErrorCode.INVALID_PARAMETERS,
                    400
                );
            }

            return await prisma.identityVerification.findMany({
                where: { merchantId },
                include: { claims: true },
                orderBy: { createdAt: "desc" }
            });
        } catch (error) {
            // Re-throw IdentityVerificationError
            if (error instanceof IdentityVerificationError) {
                throw error;
            }

            // Log and wrap other errors
            console.error("Error getting verifications for merchant:", error);
            throw new IdentityVerificationError(
                "Failed to retrieve identity verifications",
                IdentityVerificationErrorCode.INTERNAL_ERROR,
                500
            );
        }
    }

    /**
   * Verify identity using ERC-725 (Identity)
   */
    async verifyERC725Identity(
        address: string,
        key: string,
        value: string,
        userId?: string,
        merchantId?: string
    ): Promise<IdentityVerificationResult> {
        try {
            // Connect to Ethereum provider
            const provider: unknown = new ethers.JsonRpcProvider(
                process.env.ETHEREUM_RPC_URL || "https://mainnet.infura.io/v3/your-infura-key"
            );

            // Create contract instance
            const identity: unknown = new ethers.Contract(
                address,
                ERC725_ABI,
                provider
            );

            // Get key value
            const keyBytes: unknown = ethers.keccak256(ethers.toUtf8Bytes(key));
            const storedValue: unknown = await identity.getKey(keyBytes);

            // Check if value matches
            const valueBytes: unknown = ethers.keccak256(ethers.toUtf8Bytes(value));

            if (storedValue !== valueBytes) {
                return {
                    success: false,
                    method: IdentityVerificationMethodEnum.ETHEREUM_ERC725,
                    status: IdentityVerificationStatusEnum.REJECTED,
                    message: "Key value does not match"
                };
            }

            // Create verification record
            const verification: unknown = await prisma.identityVerification.create({
                data: {
                    userId,
                    merchantId,
                    method: IdentityVerificationMethodEnum.ETHEREUM_ERC725,
                    status: IdentityVerificationStatusEnum.VERIFIED,
                    address,
                    verificationData: {
                        key,
                        value,
                        keyBytes,
                        valueBytes,
                        storedValue
                    },
                    verifiedAt: new Date()
                }
            });

            return {
                success: true,
                method: IdentityVerificationMethodEnum.ETHEREUM_ERC725,
                status: IdentityVerificationStatusEnum.VERIFIED,
                message: "ERC-725 identity verified successfully",
                data: {
                    address,
                    key,
                    value
                },
                verificationId: verification.id
            };
        } catch (error) {
            console.error("Error verifying ERC-725 identity:", error);

            return {
                success: false,
                method: IdentityVerificationMethodEnum.ETHEREUM_ERC725,
                status: IdentityVerificationStatusEnum.REJECTED,
                message: "Error verifying ERC-725 identity",
                error: error instanceof Error ? (error as Error).message : "Unknown error"
            };
        }
    }

    /**
   * Verify identity using ENS
   */
    async verifyENS(
        ensName: string,
        address: string,
        userId?: string,
        merchantId?: string
    ): Promise<IdentityVerificationResult> {
        try {
            // Connect to Ethereum provider
            const provider: unknown = new ethers.JsonRpcProvider(
                process.env.ETHEREUM_RPC_URL || "https://mainnet.infura.io/v3/your-infura-key"
            );

            // Resolve ENS name to address
            const resolvedAddress: unknown = await provider.resolveName(ensName);

            if (!resolvedAddress || resolvedAddress.toLowerCase() !== address.toLowerCase()) {
                return {
                    success: false,
                    method: IdentityVerificationMethodEnum.ENS,
                    status: IdentityVerificationStatusEnum.REJECTED,
                    message: "ENS name does not resolve to the provided address"
                };
            }

            // Create verification record
            const verification: unknown = await prisma.identityVerification.create({
                data: {
                    userId,
                    merchantId,
                    method: IdentityVerificationMethodEnum.ENS,
                    status: IdentityVerificationStatusEnum.VERIFIED,
                    address,
                    verificationData: {
                        ensName,
                        resolvedAddress
                    },
                    verifiedAt: new Date()
                }
            });

            return {
                success: true,
                method: IdentityVerificationMethodEnum.ENS,
                status: IdentityVerificationStatusEnum.VERIFIED,
                message: "ENS identity verified successfully",
                data: {
                    ensName,
                    address,
                    resolvedAddress
                },
                verificationId: verification.id
            };
        } catch (error) {
            console.error("Error verifying ENS identity:", error);

            return {
                success: false,
                method: IdentityVerificationMethodEnum.ENS,
                status: IdentityVerificationStatusEnum.REJECTED,
                message: "Error verifying ENS identity",
                error: error instanceof Error ? (error as Error).message : "Unknown error"
            };
        }
    }

    /**
   * Verify identity using Polygon ID
   */
    async verifyPolygonID(
        address: string,
        proof: string,
        userId?: string,
        merchantId?: string
    ): Promise<IdentityVerificationResult> {
        try {
            // In a real implementation, this would verify the zero-knowledge proof
            // against a Polygon ID verifier contract
            // For this example, we'll simulate the verification

            // Parse the proof (in a real implementation, this would be a ZKP)
            const proofData: unknown = JSON.parse(proof);

            // Verify the proof (simulated)
            const isValid: unknown = proofData && proofData.address && proofData.address.toLowerCase() === address.toLowerCase();

            if (!isValid) {
                return {
                    success: false,
                    method: IdentityVerificationMethodEnum.POLYGON_ID,
                    status: IdentityVerificationStatusEnum.REJECTED,
                    message: "Polygon ID proof verification failed"
                };
            }

            // Create verification record
            const verification: unknown = await prisma.identityVerification.create({
                data: {
                    userId,
                    merchantId,
                    method: IdentityVerificationMethodEnum.POLYGON_ID,
                    status: IdentityVerificationStatusEnum.VERIFIED,
                    address,
                    verificationData: { proof: proofData
                    },
                    verifiedAt: new Date()
                }
            });

            return {
                success: true,
                method: IdentityVerificationMethodEnum.POLYGON_ID,
                status: IdentityVerificationStatusEnum.VERIFIED,
                message: "Polygon ID verified successfully",
                data: {
                    address,
                    proofType: proofData.type
                },
                verificationId: verification.id
            };
        } catch (error) {
            console.error("Error verifying Polygon ID:", error);

            return {
                success: false,
                method: IdentityVerificationMethodEnum.POLYGON_ID,
                status: IdentityVerificationStatusEnum.REJECTED,
                message: "Error verifying Polygon ID",
                error: error instanceof Error ? (error as Error).message : "Unknown error"
            };
        }
    }

    /**
   * Verify identity using Worldcoin
   */
    async verifyWorldcoin(
        address: string,
        nullifier: string,
        proof: string,
        userId?: string,
        merchantId?: string
    ): Promise<IdentityVerificationResult> {
        try {
            // In a real implementation, this would verify the Worldcoin proof
            // against the Worldcoin API or smart contract
            // For this example, we'll simulate the verification

            // Verify the proof (simulated)
            const isValid: unknown = address && nullifier && proof;

            if (!isValid) {
                return {
                    success: false,
                    method: IdentityVerificationMethodEnum.WORLDCOIN,
                    status: IdentityVerificationStatusEnum.REJECTED,
                    message: "Worldcoin verification failed"
                };
            }

            // Create verification record
            const verification: unknown = await prisma.identityVerification.create({
                data: {
                    userId,
                    merchantId,
                    method: IdentityVerificationMethodEnum.WORLDCOIN,
                    status: IdentityVerificationStatusEnum.VERIFIED,
                    address,
                    verificationData: {
                        nullifier,
                        proof
                    },
                    verifiedAt: new Date()
                }
            });

            return {
                success: true,
                method: IdentityVerificationMethodEnum.WORLDCOIN,
                status: IdentityVerificationStatusEnum.VERIFIED,
                message: "Worldcoin identity verified successfully",
                data: {
                    address,
                    nullifier
                },
                verificationId: verification.id
            };
        } catch (error) {
            console.error("Error verifying Worldcoin identity:", error);

            return {
                success: false,
                method: IdentityVerificationMethodEnum.WORLDCOIN,
                status: IdentityVerificationStatusEnum.REJECTED,
                message: "Error verifying Worldcoin identity",
                error: error instanceof Error ? (error as Error).message : "Unknown error"
            };
        }
    }

    /**
   * Verify identity using Unstoppable Domains
   */
    async verifyUnstoppableDomains(
        domain: string,
        address: string,
        userId?: string,
        merchantId?: string
    ): Promise<IdentityVerificationResult> {
        try {
            // In a real implementation, this would verify the domain ownership
            // against the Unstoppable Domains API
            // For this example, we'll simulate the verification

            // Simulate API call to Unstoppable Domains
            const resolvedAddress: unknown = await this.simulateUnstoppableDomainsResolve(domain);

            if (!resolvedAddress || resolvedAddress.toLowerCase() !== address.toLowerCase()) {
                return {
                    success: false,
                    method: IdentityVerificationMethodEnum.UNSTOPPABLE_DOMAINS,
                    status: IdentityVerificationStatusEnum.REJECTED,
                    message: "Unstoppable domain does not resolve to the provided address"
                };
            }

            // Create verification record
            const verification: unknown = await prisma.identityVerification.create({
                data: {
                    userId,
                    merchantId,
                    method: IdentityVerificationMethodEnum.UNSTOPPABLE_DOMAINS,
                    status: IdentityVerificationStatusEnum.VERIFIED,
                    address,
                    verificationData: {
                        domain,
                        resolvedAddress
                    },
                    verifiedAt: new Date()
                }
            });

            return {
                success: true,
                method: IdentityVerificationMethodEnum.UNSTOPPABLE_DOMAINS,
                status: IdentityVerificationStatusEnum.VERIFIED,
                message: "Unstoppable Domains identity verified successfully",
                data: {
                    domain,
                    address,
                    resolvedAddress
                },
                verificationId: verification.id
            };
        } catch (error) {
            console.error("Error verifying Unstoppable Domains identity:", error);

            return {
                success: false,
                method: IdentityVerificationMethodEnum.UNSTOPPABLE_DOMAINS,
                status: IdentityVerificationStatusEnum.REJECTED,
                message: "Error verifying Unstoppable Domains identity",
                error: error instanceof Error ? (error as Error).message : "Unknown error"
            };
        }
    }

    /**
   * Simulate Unstoppable Domains resolution
   * In a real implementation, this would call the Unstoppable Domains API
   */
    private async simulateUnstoppableDomainsResolve(domain: string): Promise<string | null> {
    // This is a simulation - in a real implementation, this would call the Unstoppable Domains API
        const mockDomains: Record<string, string> = {
            "example.crypto": "******************************************",
            "test.crypto": "******************************************",
            "merchant.crypto": "******************************************"
        };

        return mockDomains[domain] || null;
    }

    /**
   * Add a claim to an identity verification
   */
    async addClaim(
        verificationId: string,
        type: string,
        value: string,
        issuer: string
    ) {
        try {
            // Validate parameters
            if (!verificationId || !type || !value || !issuer) {
                throw new IdentityVerificationError(
                    "Verification ID, type, value, and issuer are required",
                    IdentityVerificationErrorCode.INVALID_PARAMETERS,
                    400
                );
            }

            // Check if verification exists
            const verification: unknown = await prisma.identityVerification.findUnique({
                where: { id: verificationId }
            });

            if (!verification) {
                throw new IdentityVerificationError(
                    "Identity verification not found",
                    IdentityVerificationErrorCode.VERIFICATION_NOT_FOUND,
                    404
                );
            }

            // Create claim
            return await prisma.identityClaim.create({
                data: { identityVerificationId: verificationId,
                    type,
                    value,
                    issuer,
                    issuanceDate: new Date()
                }
            });
        } catch (error) {
            // Re-throw IdentityVerificationError
            if (error instanceof IdentityVerificationError) {
                throw error;
            }

            // Log and wrap other errors
            console.error("Error adding claim:", error);
            throw new IdentityVerificationError(
                "Failed to add claim to identity verification",
                IdentityVerificationErrorCode.INTERNAL_ERROR,
                500
            );
        }
    }

    /**
   * Revoke a claim
   */
    async revokeClaim(claimId: string) {
        try {
            // Validate parameters
            if (!claimId) {
                throw new IdentityVerificationError(
                    "Claim ID is required",
                    IdentityVerificationErrorCode.INVALID_PARAMETERS,
                    400
                );
            }

            // Check if claim exists
            const claim: unknown = await prisma.identityClaim.findUnique({
                where: { id: claimId }
            });

            if (!claim) {
                throw new IdentityVerificationError(
                    "Identity claim not found",
                    IdentityVerificationErrorCode.CLAIM_NOT_FOUND,
                    404
                );
            }

            // Check if claim is already revoked
            if (claim.revoked) {
                throw new IdentityVerificationError(
                    "Claim is already revoked",
                    IdentityVerificationErrorCode.INVALID_PARAMETERS,
                    400
                );
            }

            // Revoke claim
            return await prisma.identityClaim.update({
                where: { id: claimId },
                data: { revoked: true }
            });
        } catch (error) {
            // Re-throw IdentityVerificationError
            if (error instanceof IdentityVerificationError) {
                throw error;
            }

            // Log and wrap other errors
            console.error("Error revoking claim:", error);
            throw new IdentityVerificationError(
                "Failed to revoke identity claim",
                IdentityVerificationErrorCode.INTERNAL_ERROR,
                500
            );
        }
    }

    /**
   * Set expiration for a verification
   */
    async setVerificationExpiration(verificationId: string, expiresAt: Date) {
        try {
            // Validate parameters
            if (!verificationId) {
                throw new IdentityVerificationError(
                    "Verification ID is required",
                    IdentityVerificationErrorCode.INVALID_PARAMETERS,
                    400
                );
            }

            if (!expiresAt) {
                throw new IdentityVerificationError(
                    "Expiration date is required",
                    IdentityVerificationErrorCode.INVALID_PARAMETERS,
                    400
                );
            }

            // Check if expiration date is in the past
            if (expiresAt < new Date()) {
                throw new IdentityVerificationError(
                    "Expiration date cannot be in the past",
                    IdentityVerificationErrorCode.INVALID_PARAMETERS,
                    400
                );
            }

            // Check if verification exists
            const verification: unknown = await prisma.identityVerification.findUnique({
                where: { id: verificationId }
            });

            if (!verification) {
                throw new IdentityVerificationError(
                    "Identity verification not found",
                    IdentityVerificationErrorCode.VERIFICATION_NOT_FOUND,
                    404
                );
            }

            // Set expiration
            return await prisma.identityVerification.update({
                where: { id: verificationId },
                data: { expiresAt }
            });
        } catch (error) {
            // Re-throw IdentityVerificationError
            if (error instanceof IdentityVerificationError) {
                throw error;
            }

            // Log and wrap other errors
            console.error("Error setting verification expiration:", error);
            throw new IdentityVerificationError(
                "Failed to set verification expiration",
                IdentityVerificationErrorCode.INTERNAL_ERROR,
                500
            );
        }
    }

    /**
   * Check if a verification is expired
   */
    async checkVerificationExpiration() {
        try {
            const now: Date = new Date();

            // Find all verifications that have expired
            const expiredVerifications: unknown = await prisma.identityVerification.findMany({
                where: { expiresAt: {
                        lt: now
                    },
                    status: IdentityVerificationStatusEnum.VERIFIED
                }
            });

            // Update expired verifications
            for (const verification of expiredVerifications) {
                await prisma.identityVerification.update({
                    where: { id: verification.id },
                    data: { status: IdentityVerificationStatusEnum.EXPIRED }
                });
            }


            return expiredVerifications.length;
        } catch (error) {
            // Log and wrap errors
            console.error("Error checking verification expiration:", error);
            throw new IdentityVerificationError(
                "Failed to check verification expiration",
                IdentityVerificationErrorCode.INTERNAL_ERROR,
                500
            );
        }
    }

    /**
   * Get verification statistics
   */
    async getVerificationStats() {
        try {
            // Get counts by method
            const methodCounts: unknown = await prisma.$queryRaw`
        SELECT "method", COUNT(*) as "count"
        FROM "IdentityVerification"
        GROUP BY "method"
        ORDER BY "count" DESC
      `;

            // Get counts by status
            const statusCounts: unknown = await prisma.$queryRaw`
        SELECT "status", COUNT(*) as "count"
        FROM "IdentityVerification"
        GROUP BY "status"
        ORDER BY "count" DESC
      `;

            // Get total count
            const totalCount: unknown = await prisma.identityVerification.count();

            // Get recent verifications
            const recentVerifications: unknown = await prisma.identityVerification.findMany({
                take: 5,
                orderBy: { createdAt: "desc" },
                include: { claims: true }
            });

            return {
                totalCount,
                methodCounts,
                statusCounts,
                recentVerifications
            };
        } catch (error) {
            // Log and wrap errors
            console.error("Error getting verification stats:", error);
            throw new IdentityVerificationError(
                "Failed to get verification statistics",
                IdentityVerificationErrorCode.INTERNAL_ERROR,
                500
            );
        }
    }

    /**
   * Create a blockchain-based verification request
   * @param userId User ID
   * @param walletAddress Wallet address
   * @param network Blockchain network
   */
    async createBlockchainVerificationRequest(
        userId: string,
        walletAddress: string,
        network: string
    ): Promise<unknown> {
        try {
            const timestamp: unknown = Date.now();
            const message: unknown = `Verify your identity for AmazingPay: ${userId}-${timestamp}`;
            const nonce: unknown = crypto.randomBytes(16).toString("hex");

            const verificationRequest: unknown = await prisma.identityVerification.create({
                data: {
                    userId,
                    address: walletAddress,
                    method: IdentityVerificationMethodEnum.SELF_SOVEREIGN,
                    status: IdentityVerificationStatusEnum.PENDING,
                    verificationData: {
                        network,
                        challengeMessage: message,
                        nonce,
                        timestamp
                    },
                    expiresAt: new Date(timestamp + 3600000) // 1 hour expiration
                }
            });

            return {
                requestId: verificationRequest.id,
                message,
                nonce,
                expiresAt: verificationRequest.expiresAt
            };
        } catch (error) {
            console.error("Error creating blockchain verification request:", error);
            throw new IdentityVerificationError(
                "Failed to create verification request",
                IdentityVerificationErrorCode.INTERNAL_ERROR,
                500
            );
        }
    }

    /**
   * Complete a blockchain-based verification request
   * @param requestId Verification request ID
   * @param signature Signature provided by the user
   */
    async completeBlockchainVerification(
        requestId: string,
        signature: string
    ): Promise<boolean> {
        try {
            const verificationRequest: unknown = await prisma.identityVerification.findUnique({
                where: { id: requestId }
            });

            if (!verificationRequest) {
                throw new IdentityVerificationError(
                    "Verification request not found",
                    IdentityVerificationErrorCode.VERIFICATION_NOT_FOUND,
                    404
                );
            }

            if (verificationRequest.status !== IdentityVerificationStatusEnum.PENDING) {
                throw new IdentityVerificationError(
                    "Verification request is not pending",
                    IdentityVerificationErrorCode.INVALID_PARAMETERS,
                    400
                );
            }

            if (verificationRequest.expiresAt && verificationRequest.expiresAt < new Date()) {
                await prisma.identityVerification.update({
                    where: { id: requestId },
                    data: { status: IdentityVerificationStatusEnum.EXPIRED }
                });
                throw new IdentityVerificationError(
                    "Verification request has expired",
                    IdentityVerificationErrorCode.VERIFICATION_FAILED,
                    400
                );
            }

            const verificationData: unknown = verificationRequest.verificationData as unknown;
            const network: unknown = verificationData.network;
            const message: unknown = verificationData.challengeMessage;

            // Verify the signature based on the network
            let isValid: boolean = false;

            if (network === "tron") {
                isValid = await this.verifyTronSignature(
                    verificationRequest.address,
                    signature,
                    message
                );
            } else {
                // For Ethereum-compatible chains (Ethereum, BSC, Polygon)
                try {
                    const recoveredAddress: unknown = ethers.verifyMessage(message, signature);
                    isValid = recoveredAddress.toLowerCase() === verificationRequest.address.toLowerCase();
                } catch (error) {
                    console.error("Error verifying signature:", error);
                    isValid = false;
                }
            }

            if (isValid) {
                await prisma.identityVerification.update({
                    where: { id: requestId },
                    data: { status: IdentityVerificationStatusEnum.VERIFIED,
                        verifiedAt: new Date(),
                        verificationData: {
                            ...verificationData,
                            signature,
                            verifiedAt: new Date()
                        }
                    }
                });

                // Update user's verification status if this is a user verification
                if (verificationRequest.userId) {
                    await prisma.user.update({
                        where: { id: verificationRequest.userId },
                        data: { isActive: true }
                    });
                }

                // Update merchant's verification status if this is a merchant verification
                if (verificationRequest.merchantId) {
                    await prisma.merchant.update({
                        where: { id: verificationRequest.merchantId },
                        data: { isVerified: true }
                    });
                }

                return true;
            } else {
                await prisma.identityVerification.update({
                    where: { id: requestId },
                    data: { status: IdentityVerificationStatusEnum.REJECTED,
                        verificationData: {
                            ...verificationData,
                            signature,
                            rejectedAt: new Date(),
                            reason: "Invalid signature"
                        }
                    }
                });
                return false;
            }
        } catch (error) {
            if (error instanceof IdentityVerificationError) {
                throw error;
            }

            console.error("Error completing blockchain verification:", error);
            throw new IdentityVerificationError(
                "Failed to complete verification",
                IdentityVerificationErrorCode.INTERNAL_ERROR,
                500
            );
        }
    }

    /**
   * Verify Tron signature
   * @param address Tron wallet address
   * @param signature Signature provided by the user
   * @param message Message that was signed
   */
    private async verifyTronSignature(
        address: string,
        signature: string,
        message: string
    ): Promise<boolean> {
        try {
            // For Tron, we need to use their API
            const response: unknown = await axios.post(
                "https://api.trongrid.io/wallet/validateaddress",
                {
                    address,
                    signature,
                    message
                }
            );

            return response.data.result;
        } catch (error) {
            console.error("Error verifying Tron signature:", error);
            return false;
        }
    }

    /**
   * Get supported blockchain networks
   */
    async getSupportedNetworks(): Promise<unknown[]> {
        return [
            {
                id: "ethereum",
                name: "Ethereum",
                icon: "ethereum.svg",
                description: "Ethereum Mainnet",
                features: ["signature", "ens", "unstoppable"]
            },
            {
                id: "bsc",
                name: "Binance Smart Chain",
                icon: "bsc.svg",
                description: "Binance Smart Chain Mainnet",
                features: ["signature"]
            },
            {
                id: "polygon",
                name: "Polygon",
                icon: "polygon.svg",
                description: "Polygon (Matic) Mainnet",
                features: ["signature", "polygon-id"]
            },
            {
                id: "tron",
                name: "Tron",
                icon: "tron.svg",
                description: "Tron Mainnet",
                features: ["signature"]
            }
        ];
    }

    /**
   * Verify ENS domain ownership
   * @param userId User ID
   * @param ensName ENS domain name
   */
    async verifyENSDomain(
        userId: string,
        ensName: string
    ): Promise<unknown> {
        try {
            if (!ensName || !ensName.endsWith(".eth")) {
                throw new IdentityVerificationError(
                    "Invalid ENS name. Must end with .eth",
                    IdentityVerificationErrorCode.INVALID_PARAMETERS,
                    400
                );
            }

            // Connect to Ethereum provider
            const provider: unknown = new ethers.JsonRpcProvider(
                process.env.ETHEREUM_RPC_URL || "https://mainnet.infura.io/v3/your-infura-key"
            );

            // Resolve ENS name to address
            const resolvedAddress: unknown = await provider.resolveName(ensName);

            if (!resolvedAddress) {
                throw new IdentityVerificationError(
                    "ENS name could not be resolved to an address",
                    IdentityVerificationErrorCode.VERIFICATION_FAILED,
                    400
                );
            }

            // Create verification record
            const verification: unknown = await prisma.identityVerification.create({
                data: {
                    userId,
                    address: resolvedAddress,
                    method: IdentityVerificationMethodEnum.ENS,
                    status: IdentityVerificationStatusEnum.PENDING,
                    verificationData: {
                        ensName,
                        resolvedAddress,
                        timestamp: Date.now()
                    }
                }
            });

            return {
                verificationId: verification.id,
                ensName,
                resolvedAddress,
                message: "To complete verification, you must sign a message with this wallet"
            };
        } catch (error) {
            if (error instanceof IdentityVerificationError) {
                throw error;
            }

            console.error("Error verifying ENS domain:", error);
            throw new IdentityVerificationError(
                "Failed to verify ENS domain",
                IdentityVerificationErrorCode.INTERNAL_ERROR,
                500
            );
        }
    }

    /**
   * Complete ENS verification with signature
   * @param verificationId Verification ID
   * @param signature Signature
   */
    async completeENSVerification(
        verificationId: string,
        signature: string
    ): Promise<boolean> {
        try {
            const verification: unknown = await prisma.identityVerification.findUnique({
                where: { id: verificationId }
            });

            if (!verification) {
                throw new IdentityVerificationError(
                    "Verification request not found",
                    IdentityVerificationErrorCode.VERIFICATION_NOT_FOUND,
                    404
                );
            }

            if (verification.method !== IdentityVerificationMethodEnum.ENS) {
                throw new IdentityVerificationError(
                    "Invalid verification method",
                    IdentityVerificationErrorCode.INVALID_PARAMETERS,
                    400
                );
            }

            if (verification.status !== IdentityVerificationStatusEnum.PENDING) {
                throw new IdentityVerificationError(
                    "Verification is not pending",
                    IdentityVerificationErrorCode.INVALID_PARAMETERS,
                    400
                );
            }

            const verificationData: unknown = verification.verificationData as unknown;
            const message: unknown = `Verify ENS name ${verificationData.ensName} for AmazingPay`;

            // Verify signature
            try {
                const recoveredAddress: unknown = ethers.verifyMessage(message, signature);
                const isValid: unknown = recoveredAddress.toLowerCase() === verification.address.toLowerCase();

                if (isValid) {
                    await prisma.identityVerification.update({
                        where: { id: verificationId },
                        data: { status: IdentityVerificationStatusEnum.VERIFIED,
                            verifiedAt: new Date(),
                            verificationData: {
                                ...verificationData,
                                signature,
                                verifiedAt: new Date()
                            }
                        }
                    });

                    // Update user's verification status
                    if (verification.id // Fixed: using id instead of userId) {
                        await prisma.user.update({
                            where: { id: verification.id //, Fixed: using id instead of userId },
                            data: { isActive: true }
                        });
                    }

                    return true;
                } else {
                    await prisma.identityVerification.update({
                        where: { id: verificationId },
                        data: { status: IdentityVerificationStatusEnum.REJECTED,
                            verificationData: {
                                ...verificationData,
                                signature,
                                rejectedAt: new Date(),
                                reason: "Invalid signature"
                            }
                        }
                    });
                    return false;
                }
            } catch (error) {
                console.error("Error verifying signature:", error);
                await prisma.identityVerification.update({
                    where: { id: verificationId },
                    data: { status: IdentityVerificationStatusEnum.REJECTED,
                        verificationData: {
                            ...verificationData,
                            signature,
                            rejectedAt: new Date(),
                            reason: "Error verifying signature"
                        }
                    }
                });
                return false;
            }
        } catch (error) {
            if (error instanceof IdentityVerificationError) {
                throw error;
            }

            console.error("Error completing ENS verification:", error);
            throw new IdentityVerificationError(
                "Failed to complete ENS verification",
                IdentityVerificationErrorCode.INTERNAL_ERROR,
                500
            );
        }
    }

    /**
   * Verify Unstoppable Domain ownership
   * @param userId User ID
   * @param domain Unstoppable domain name
   */
    async verifyUnstoppableDomain(
        userId: string,
        domain: string
    ): Promise<unknown> {
        try {
            if (!domain || (!domain.endsWith(".crypto") && !domain.endsWith(".nft") && !domain.endsWith(".wallet") && !domain.endsWith(".x") && !domain.endsWith(".zil"))) {
                throw new IdentityVerificationError(
                    "Invalid Unstoppable domain. Must end with .crypto, .nft, .wallet, .x, or .zil",
                    IdentityVerificationErrorCode.INVALID_PARAMETERS,
                    400
                );
            }

            // Resolve domain to address using Unstoppable Domains API
            try {
                const response: unknown = await axios.get(`https://unstoppabledomains.g.alchemy.com/domains/${domain}`, {
                    headers: {
                        "Authorization": `Bearer ${process.env.UNSTOPPABLE_API_KEY || "demo-api-key"}`
                    }
                });

                if (!response.data || !response.data.records || !response.data.records["crypto.ETH.address"]) {
                    throw new IdentityVerificationError(
                        "Could not resolve Unstoppable domain to an Ethereum address",
                        IdentityVerificationErrorCode.VERIFICATION_FAILED,
                        400
                    );
                }

                const resolvedAddress: unknown = response.data.records["crypto.ETH.address"];

                // Create verification record
                const verification: unknown = await prisma.identityVerification.create({
                    data: {
                        userId,
                        address: resolvedAddress,
                        method: IdentityVerificationMethodEnum.UNSTOPPABLE_DOMAINS,
                        status: IdentityVerificationStatusEnum.PENDING,
                        verificationData: {
                            domain,
                            resolvedAddress,
                            timestamp: Date.now()
                        }
                    }
                });

                return {
                    verificationId: verification.id,
                    domain,
                    resolvedAddress,
                    message: `Verify Unstoppable domain ${domain} for AmazingPay`
                };
            } catch (error) {
                console.error("Error resolving Unstoppable domain:", error);
                throw new IdentityVerificationError(
                    "Failed to resolve Unstoppable domain",
                    IdentityVerificationErrorCode.VERIFICATION_FAILED,
                    400
                );
            }
        } catch (error) {
            if (error instanceof IdentityVerificationError) {
                throw error;
            }

            console.error("Error verifying Unstoppable domain:", error);
            throw new IdentityVerificationError(
                "Failed to verify Unstoppable domain",
                IdentityVerificationErrorCode.INTERNAL_ERROR,
                500
            );
        }
    }

    /**
   * Complete Unstoppable domain verification with signature
   * @param verificationId Verification ID
   * @param signature Signature
   */
    async completeUnstoppableDomainVerification(
        verificationId: string,
        signature: string
    ): Promise<boolean> {
        try {
            const verification: unknown = await prisma.identityVerification.findUnique({
                where: { id: verificationId }
            });

            if (!verification) {
                throw new IdentityVerificationError(
                    "Verification request not found",
                    IdentityVerificationErrorCode.VERIFICATION_NOT_FOUND,
                    404
                );
            }

            if (verification.method !== IdentityVerificationMethodEnum.UNSTOPPABLE_DOMAINS) {
                throw new IdentityVerificationError(
                    "Invalid verification method",
                    IdentityVerificationErrorCode.INVALID_PARAMETERS,
                    400
                );
            }

            if (verification.status !== IdentityVerificationStatusEnum.PENDING) {
                throw new IdentityVerificationError(
                    "Verification is not pending",
                    IdentityVerificationErrorCode.INVALID_PARAMETERS,
                    400
                );
            }

            const verificationData: unknown = verification.verificationData as unknown;
            const message: unknown = `Verify Unstoppable domain ${verificationData.domain} for AmazingPay`;

            // Verify signature
            try {
                const recoveredAddress: unknown = ethers.verifyMessage(message, signature);
                const isValid: unknown = recoveredAddress.toLowerCase() === verification.address.toLowerCase();

                if (isValid) {
                    await prisma.identityVerification.update({
                        where: { id: verificationId },
                        data: { status: IdentityVerificationStatusEnum.VERIFIED,
                            verifiedAt: new Date(),
                            verificationData: {
                                ...verificationData,
                                signature,
                                verifiedAt: new Date()
                            }
                        }
                    });

                    // Update user's verification status
                    if (verification.id // Fixed: using id instead of userId) {
                        await prisma.user.update({
                            where: { id: verification.id //, Fixed: using id instead of userId },
                            data: { isActive: true }
                        });
                    }

                    return true;
                } else {
                    await prisma.identityVerification.update({
                        where: { id: verificationId },
                        data: { status: IdentityVerificationStatusEnum.REJECTED,
                            verificationData: {
                                ...verificationData,
                                signature,
                                rejectedAt: new Date(),
                                reason: "Invalid signature"
                            }
                        }
                    });
                    return false;
                }
            } catch (error) {
                console.error("Error verifying signature:", error);
                await prisma.identityVerification.update({
                    where: { id: verificationId },
                    data: { status: IdentityVerificationStatusEnum.REJECTED,
                        verificationData: {
                            ...verificationData,
                            signature,
                            rejectedAt: new Date(),
                            reason: "Error verifying signature"
                        }
                    }
                });
                return false;
            }
        } catch (error) {
            if (error instanceof IdentityVerificationError) {
                throw error;
            }

            console.error("Error completing Unstoppable domain verification:", error);
            throw new IdentityVerificationError(
                "Failed to complete Unstoppable domain verification",
                IdentityVerificationErrorCode.INTERNAL_ERROR,
                500
            );
        }
    }

    /**
   * Create a Polygon ID verification request
   * @param userId User ID
   */
    async createPolygonIDVerificationRequest(
        userId: string
    ): Promise<unknown> {
        try {
            // Generate a unique session ID for this verification
            const sessionId: unknown = crypto.randomBytes(16).toString("hex");
            const timestamp: unknown = Date.now();

            // Create a QR code payload for Polygon ID
            const qrPayload: unknown = {
                type: "https://iden3-communication.io/authorization/1.0/request",
                messageId: sessionId,
                thid: sessionId,
                from: "did:polygonid:amazingpay",
                body: { callbackUrl: `${process.env.API_URL || "https://api.amazingpayme.com"}/identity-verification/polygon-id/callback`,
                    reason: "AmazingPay identity verification",
                    scope: [
                        {
                            id: 1,
                            circuitId: "credentialAtomicQuerySigV2",
                            query: { allowedIssuers: ["*"],
                                type: "KYCAgeCredential",
                                context: "https://raw.githubusercontent.com/iden3/claim-schema-vocab/main/schemas/json-ld/kyc-v3.json-ld",
                                credentialSubject: { birthday: {
                                        $lt: Math.floor(Date.now() / 1000) - (18 * 365 * 24 * 60 * 60) // 18 years ago
                                    }
                                }
                            }
                        }
                    ]
                }
            };

            // Create verification record
            const verification: unknown = await prisma.identityVerification.create({
                data: {
                    userId,
                    address: sessionId, // Using sessionId as address for Polygon ID
                    method: IdentityVerificationMethodEnum.POLYGON_ID,
                    status: IdentityVerificationStatusEnum.PENDING,
                    verificationData: {
                        sessionId,
                        qrPayload,
                        timestamp
                    },
                    expiresAt: new Date(timestamp + 3600000) // 1 hour expiration
                }
            });

            return {
                verificationId: verification.id,
                sessionId,
                qrPayload,
                expiresAt: verification.expiresAt
            };
        } catch (error) {
            console.error("Error creating Polygon ID verification request:", error);
            throw new IdentityVerificationError(
                "Failed to create Polygon ID verification request",
                IdentityVerificationErrorCode.INTERNAL_ERROR,
                500
            );
        }
    }

    /**
   * Handle Polygon ID verification callback
   * @param sessionId Session ID
   * @param proofData Proof data from Polygon ID
   */
    async handlePolygonIDCallback(
        sessionId: string,
        proofData
    ): Promise<boolean> {
        try {
            // Find verification by sessionId (stored in address field)
            const verification: unknown = await prisma.identityVerification.findFirst({
                where: { address: sessionId,
                    method: IdentityVerificationMethodEnum.POLYGON_ID,
                    status: IdentityVerificationStatusEnum.PENDING
                }
            });

            if (!verification) {
                throw new IdentityVerificationError(
                    "Verification request not found",
                    IdentityVerificationErrorCode.VERIFICATION_NOT_FOUND,
                    404
                );
            }

            if (verification.expiresAt && verification.expiresAt < new Date()) {
                await prisma.identityVerification.update({
                    where: { id: verification.id },
                    data: { status: IdentityVerificationStatusEnum.EXPIRED }
                });
                throw new IdentityVerificationError(
                    "Verification request has expired",
                    IdentityVerificationErrorCode.VERIFICATION_FAILED,
                    400
                );
            }

            // Verify the proof data
            // In a real implementation, this would involve cryptographic verification
            // of the zero-knowledge proof from Polygon ID
            const isValid: unknown = this.verifyPolygonIDProof(proofData);

            if (isValid) {
                await prisma.identityVerification.update({
                    where: { id: verification.id },
                    data: { status: IdentityVerificationStatusEnum.VERIFIED,
                        verifiedAt: new Date(),
                        verificationData: {
                            ...verification.verificationData as unknown,
                            proofData,
                            verifiedAt: new Date()
                        }
                    }
                });

                // Update user's verification status
                if (verification.id // Fixed: using id instead of userId) {
                    await prisma.user.update({
                        where: { id: verification.id //, Fixed: using id instead of userId },
                        data: { isActive: true }
                    });
                }

                return true;
            } else {
                await prisma.identityVerification.update({
                    where: { id: verification.id },
                    data: { status: IdentityVerificationStatusEnum.REJECTED,
                        verificationData: {
                            ...verification.verificationData as unknown,
                            proofData,
                            rejectedAt: new Date(),
                            reason: "Invalid proof"
                        }
                    }
                });
                return false;
            }
        } catch (error) {
            if (error instanceof IdentityVerificationError) {
                throw error;
            }

            console.error("Error handling Polygon ID callback:", error);
            throw new IdentityVerificationError(
                "Failed to handle Polygon ID callback",
                IdentityVerificationErrorCode.INTERNAL_ERROR,
                500
            );
        }
    }

    /**
   * Check Polygon ID verification status
   * @param verificationId Verification ID
   */
    async checkPolygonIDVerificationStatus(
        verificationId: string
    ): Promise<unknown> {
        try {
            const verification: unknown = await prisma.identityVerification.findUnique({
                where: { id: verificationId }
            });

            if (!verification) {
                throw new IdentityVerificationError(
                    "Verification request not found",
                    IdentityVerificationErrorCode.VERIFICATION_NOT_FOUND,
                    404
                );
            }

            if (verification.method !== IdentityVerificationMethodEnum.POLYGON_ID) {
                throw new IdentityVerificationError(
                    "Invalid verification method",
                    IdentityVerificationErrorCode.INVALID_PARAMETERS,
                    400
                );
            }

            return {
                status: verification.status,
                verifiedAt: verification.verifiedAt,
                expiresAt: verification.expiresAt
            };
        } catch (error) {
            if (error instanceof IdentityVerificationError) {
                throw error;
            }

            console.error("Error checking Polygon ID verification status:", error);
            throw new IdentityVerificationError(
                "Failed to check Polygon ID verification status",
                IdentityVerificationErrorCode.INTERNAL_ERROR,
                500
            );
        }
    }

    /**
   * Verify Polygon ID proof
   * @param proofData Proof data from Polygon ID
   */
    private verifyPolygonIDProof(proofData): boolean {
    // In a real implementation, this would involve cryptographic verification
    // of the zero-knowledge proof from Polygon ID
    // For this example, we'll just check if the proof data exists
        return !!proofData && typeof proofData === "object";
    }

    /**
   * Verify Worldcoin identity
   * @param userId User ID
   * @param worldcoinProof Proof from Worldcoin
   */
    async verifyWorldcoinIdentity(
        userId: string,
        worldcoinProof
    ): Promise<unknown> {
        try {
            // Validate the proof
            if (!worldcoinProof || !worldcoinProof.merkle_root || !worldcoinProof.nullifier_hash || !worldcoinProof.proof) {
                throw new IdentityVerificationError(
                    "Invalid Worldcoin proof",
                    IdentityVerificationErrorCode.INVALID_PARAMETERS,
                    400
                );
            }

            // In a real implementation, we would verify the proof with the Worldcoin API
            // For this example, we'll assume the proof is valid if it has the required fields
            const isValid: unknown = this.verifyWorldcoinProof(worldcoinProof);

            if (!isValid) {
                throw new IdentityVerificationError(
                    "Invalid Worldcoin proof",
                    IdentityVerificationErrorCode.VERIFICATION_FAILED,
                    400
                );
            }

            // Create verification record
            const verification: unknown = await prisma.identityVerification.create({
                data: {
                    userId,
                    address: worldcoinProof.nullifier_hash, // Using nullifier_hash as address
                    method: IdentityVerificationMethodEnum.WORLDCOIN,
                    status: IdentityVerificationStatusEnum.VERIFIED, // Immediately verified since we've already checked the proof
                    verifiedAt: new Date(),
                    verificationData: { proof: worldcoinProof,
                        verifiedAt: new Date()
                    }
                }
            });

            // Update user's verification status
            if (userId) {
                await prisma.user.update({
                    where: { id: userId },
                    data: { isActive: true }
                });
            }

            return {
                verificationId: verification.id,
                status: verification.status,
                verifiedAt: verification.verifiedAt
            };
        } catch (error) {
            if (error instanceof IdentityVerificationError) {
                throw error;
            }

            console.error("Error verifying Worldcoin identity:", error);
            throw new IdentityVerificationError(
                "Failed to verify Worldcoin identity",
                IdentityVerificationErrorCode.INTERNAL_ERROR,
                500
            );
        }
    }

    /**
   * Verify Worldcoin proof
   * @param proof Proof from Worldcoin
   */
    private verifyWorldcoinProof(proof): boolean {
    // In a real implementation, this would involve cryptographic verification
    // of the zero-knowledge proof from Worldcoin
    // For this example, we'll just check if the proof has the required fields
        return (
            !!proof &&
      typeof proof === "object" &&
      !!proof.merkle_root &&
      !!proof.nullifier_hash &&
      !!proof.proof
        );
    }

    /**
   * Verify Unstoppable Domain ownership
   * @param userId User ID
   * @param domain Unstoppable domain name
   */
    async verifyUnstoppableDomain(
        userId: string,
        domain: string
    ): Promise<unknown> {
        try {
            if (!domain || (!domain.endsWith(".crypto") && !domain.endsWith(".nft") && !domain.endsWith(".wallet") && !domain.endsWith(".x") && !domain.endsWith(".zil"))) {
                throw new IdentityVerificationError(
                    "Invalid Unstoppable domain. Must end with .crypto, .nft, .wallet, .x, or .zil",
                    IdentityVerificationErrorCode.INVALID_PARAMETERS,
                    400
                );
            }

            // Resolve domain to address using Unstoppable Domains API
            try {
                const response: unknown = await axios.get(`https://unstoppabledomains.g.alchemy.com/domains/${domain}`, {
                    headers: {
                        "Authorization": `Bearer ${process.env.UNSTOPPABLE_API_KEY || "demo-api-key"}`
                    }
                });

                if (!response.data || !response.data.records || !response.data.records["crypto.ETH.address"]) {
                    throw new IdentityVerificationError(
                        "Could not resolve Unstoppable domain to an Ethereum address",
                        IdentityVerificationErrorCode.VERIFICATION_FAILED,
                        400
                    );
                }

                const resolvedAddress: unknown = response.data.records["crypto.ETH.address"];

                // Create verification record
                const verification: unknown = await prisma.identityVerification.create({
                    data: {
                        userId,
                        address: resolvedAddress,
                        method: IdentityVerificationMethodEnum.UNSTOPPABLE_DOMAINS,
                        status: IdentityVerificationStatusEnum.PENDING,
                        verificationData: {
                            domain,
                            resolvedAddress,
                            timestamp: Date.now()
                        }
                    }
                });

                return {
                    verificationId: verification.id,
                    domain,
                    resolvedAddress,
                    message: `Verify Unstoppable domain ${domain} for AmazingPay`
                };
            } catch (error) {
                console.error("Error resolving Unstoppable domain:", error);
                throw new IdentityVerificationError(
                    "Failed to resolve Unstoppable domain",
                    IdentityVerificationErrorCode.VERIFICATION_FAILED,
                    400
                );
            }
        } catch (error) {
            if (error instanceof IdentityVerificationError) {
                throw error;
            }

            console.error("Error verifying Unstoppable domain:", error);
            throw new IdentityVerificationError(
                "Failed to verify Unstoppable domain",
                IdentityVerificationErrorCode.INTERNAL_ERROR,
                500
            );
        }
    }

    /**
   * Complete Unstoppable domain verification with signature
   * @param verificationId Verification ID
   * @param signature Signature
   */
    async completeUnstoppableDomainVerification(
        verificationId: string,
        signature: string
    ): Promise<boolean> {
        try {
            const verification: unknown = await prisma.identityVerification.findUnique({
                where: { id: verificationId }
            });

            if (!verification) {
                throw new IdentityVerificationError(
                    "Verification request not found",
                    IdentityVerificationErrorCode.VERIFICATION_NOT_FOUND,
                    404
                );
            }

            if (verification.method !== IdentityVerificationMethodEnum.UNSTOPPABLE_DOMAINS) {
                throw new IdentityVerificationError(
                    "Invalid verification method",
                    IdentityVerificationErrorCode.INVALID_PARAMETERS,
                    400
                );
            }

            if (verification.status !== IdentityVerificationStatusEnum.PENDING) {
                throw new IdentityVerificationError(
                    "Verification is not pending",
                    IdentityVerificationErrorCode.INVALID_PARAMETERS,
                    400
                );
            }

            const verificationData: unknown = verification.verificationData as unknown;
            const message: unknown = `Verify Unstoppable domain ${verificationData.domain} for AmazingPay`;

            // Verify signature
            try {
                const recoveredAddress: unknown = ethers.verifyMessage(message, signature);
                const isValid: unknown = recoveredAddress.toLowerCase() === verification.address.toLowerCase();

                if (isValid) {
                    await prisma.identityVerification.update({
                        where: { id: verificationId },
                        data: { status: IdentityVerificationStatusEnum.VERIFIED,
                            verifiedAt: new Date(),
                            verificationData: {
                                ...verificationData,
                                signature,
                                verifiedAt: new Date()
                            }
                        }
                    });

                    // Update user's verification status
                    if (verification.id // Fixed: using id instead of userId) {
                        await prisma.user.update({
                            where: { id: verification.id //, Fixed: using id instead of userId },
                            data: { isActive: true }
                        });
                    }

                    return true;
                } else {
                    await prisma.identityVerification.update({
                        where: { id: verificationId },
                        data: { status: IdentityVerificationStatusEnum.REJECTED,
                            verificationData: {
                                ...verificationData,
                                signature,
                                rejectedAt: new Date(),
                                reason: "Invalid signature"
                            }
                        }
                    });
                    return false;
                }
            } catch (error) {
                console.error("Error verifying signature:", error);
                await prisma.identityVerification.update({
                    where: { id: verificationId },
                    data: { status: IdentityVerificationStatusEnum.REJECTED,
                        verificationData: {
                            ...verificationData,
                            signature,
                            rejectedAt: new Date(),
                            reason: "Error verifying signature"
                        }
                    }
                });
                return false;
            }
        } catch (error) {
            if (error instanceof IdentityVerificationError) {
                throw error;
            }

            console.error("Error completing Unstoppable domain verification:", error);
            throw new IdentityVerificationError(
                "Failed to complete Unstoppable domain verification",
                IdentityVerificationErrorCode.INTERNAL_ERROR,
                500
            );
        }
    }
}
