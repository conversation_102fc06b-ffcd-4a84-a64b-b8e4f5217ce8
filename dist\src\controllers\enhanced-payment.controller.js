"use strict";
// jscpd:ignore-file
/**
 * Enhanced Payment Controller
 *
 * Handles payment operations using the new payment service.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getAllPaymentMethods = exports.getPaymentMethodDetails = exports.getAvailablePaymentMethods = exports.processPayment = void 0;
const client_1 = require("@prisma/client");
const EnhancedPaymentService_1 = require("../services/payment/EnhancedPaymentService");
const PaymentMethodFactory_1 = require("../factories/payment/PaymentMethodFactory");
const PaymentPluginManager_1 = require("../plugins/payment/PaymentPluginManager");
const BinancePaymentPlugin_1 = __importDefault(require("../plugins/payment/BinancePaymentPlugin"));
const enhanced_subscription_service_1 = require("../services/enhanced-subscription.service");
const logger_1 = require("../lib/logger");
const AppError_1 = require("../utils/errors/AppError");
const prisma = new client_1.PrismaClient();
const subscriptionService = new enhanced_subscription_service_1.EnhancedSubscriptionService(prisma);
const paymentService = new EnhancedPaymentService_1.EnhancedPaymentService(prisma, subscriptionService);
const paymentPluginManager = PaymentPluginManager_1.PaymentPluginManager.getInstance(paymentService);
// Register plugins
paymentPluginManager.registerPlugin(BinancePaymentPlugin_1.default);
/**
 * Process a payment
 */
const processPayment = async (req, res, next) => {
    try {
        const { merchantId, amount, currency, paymentMethodId, paymentMethodType, paymentData, metadata } = req.body;
        // Validate required fields
        if (!merchantId || !amount || !currency || !paymentMethodType) {
            return next(new AppError_1.AppError({
                message: "Missing required fields",
                type: ErrorType.VALIDATION,
                code: ErrorCode.MISSING_REQUIRED_FIELD
            }));
        }
        // Check if the payment method exists
        const paymentMethodFactory = PaymentMethodFactory_1.PaymentMethodFactory.getInstance();
        if (!paymentMethodFactory.hasPaymentMethod(paymentMethodType)) {
            return next(new AppError_1.AppError(`Unsupported payment method: ${paymentMethodType}`, 400));
        }
        // Process the payment
        const result = await paymentService.processPayment({
            merchantId,
            amount,
            currency,
            paymentMethodId,
            paymentMethodType,
            paymentData: paymentData || {},
            metadata: metadata || {}
        });
        // Return the result
        res.json({
            success: result.success,
            transactionId: result.transactionId,
            message: result.message,
            details: result.details,
            timestamp: result.timestamp,
            redirectUrl: result.redirectUrl
        });
    }
    catch (error) {
        logger_1.logger.error("Payment processing error:", error);
        next(new AppError_1.AppError({
            message: "Payment processing failed",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};
exports.processPayment = processPayment;
/**
 * Get available payment methods for a merchant
 */
const getAvailablePaymentMethods = async (req, res, next) => {
    try {
        const { merchantId } = req.params;
        const { currency } = req.query;
        if (!merchantId) {
            return next(new AppError_1.AppError({
                message: "Merchant ID is required",
                type: ErrorType.VALIDATION,
                code: ErrorCode.MISSING_REQUIRED_FIELD
            }));
        }
        // Get available payment methods
        const paymentMethods = await paymentService.getAvailablePaymentMethods(merchantId, currency);
        // Return the payment methods
        res.json({
            success: true,
            paymentMethods
        });
    }
    catch (error) {
        logger_1.logger.error("Error getting available payment methods:", error);
        next(new AppError_1.AppError({
            message: "Failed to get available payment methods",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};
exports.getAvailablePaymentMethods = getAvailablePaymentMethods;
/**
 * Get payment method details
 */
const getPaymentMethodDetails = async (req, res, next) => {
    try {
        const { type } = req.params;
        if (!type) {
            return next(new AppError_1.AppError({
                message: "Payment method type is required",
                type: ErrorType.VALIDATION,
                code: ErrorCode.MISSING_REQUIRED_FIELD
            }));
        }
        // Get the payment method
        const paymentMethodFactory = PaymentMethodFactory_1.PaymentMethodFactory.getInstance();
        if (!paymentMethodFactory.hasPaymentMethod(type)) {
            return next(new AppError_1.AppError(`Payment method not found: ${type}`, 404));
        }
        const method = paymentMethodFactory.getPaymentMethod(type);
        // Map to a simpler format
        const methodData = {
            type: method.getType(),
            name: method.getName(),
            description: method.getDescription(),
            icon: method.getIcon(),
            enabled: method.isEnabled(),
            supportedCurrencies: method.getSupportedCurrencies(),
            requiredFields: method.getRequiredFields()
        };
        res.json({
            paymentMethod: methodData
        });
    }
    catch (error) {
        logger_1.logger.error("Error getting payment method details:", error);
        next(new AppError_1.AppError({
            message: "Failed to get payment method details",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};
exports.getPaymentMethodDetails = getPaymentMethodDetails;
/**
 * Get all payment methods
 */
const getAllPaymentMethods = async (req, res, next) => {
    try {
        // Get all payment methods
        const paymentMethodFactory = PaymentMethodFactory_1.PaymentMethodFactory.getInstance();
        const methods = paymentMethodFactory.getAllPaymentMethods().map(method => ({
            type: method.getType(),
            name: method.getName(),
            description: method.getDescription(),
            icon: method.getIcon(),
            enabled: method.isEnabled(),
            supportedCurrencies: method.getSupportedCurrencies()
        }));
        res.json({
            success: true,
            paymentMethods: methods
        });
    }
    catch (error) {
        logger_1.logger.error("Error getting all payment methods:", error);
        next(new AppError_1.AppError({
            message: "Failed to get payment methods",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};
exports.getAllPaymentMethods = getAllPaymentMethods;
exports.default = {
    processPayment: exports.processPayment,
    getAvailablePaymentMethods: exports.getAvailablePaymentMethods,
    getPaymentMethodDetails: exports.getPaymentMethodDetails,
    getAllPaymentMethods: exports.getAllPaymentMethods
};
//# sourceMappingURL=enhanced-payment.controller.js.map