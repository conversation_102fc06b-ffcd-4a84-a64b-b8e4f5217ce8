// jscpd:ignore-file
/**
 * Module Registry
 *
 * A central registry for module configurations.
 */

import { logger } from './logger';
import { eventBus } from './EventBus';

/**
 * Module configuration
 */
export interface ModuleConfig {
  /**
   * Whether the module is enabled
   */
  enabled: boolean;

  /**
   * Module-specific configuration
   */
  config: Record<string, any>;

  /**
   * Module dependencies
   */
  dependencies?: string[];

  /**
   * Module version
   */
  version?: string;

  /**
   * Module description
   */
  description?: string;
}

/**
 * Module registry
 */
export class ModuleRegistry {
  private static instance: ModuleRegistry;
  private modules: Map<string, ModuleConfig> = new Map();

  /**
   * Get the singleton instance
   */
  public static getInstance(): ModuleRegistry {
    if (!ModuleRegistry.instance) {
      ModuleRegistry.instance = new ModuleRegistry();
    }

    return ModuleRegistry.instance;
  }

  /**
   * Register a module
   *
   * @param name Module name
   * @param config Module configuration
   */
  public registerModule(name: string, config: ModuleConfig): void {
    // Check dependencies
    if (config.dependencies) {
      for (const dependency of config.dependencies) {
        if (!this.modules.has(dependency)) {
          logger.warn(`Module ${name} depends on ${dependency}, but it is not registered`);
        }
      }
    }

    // Register module
    this.modules.set(name, config);

    // Emit event
    eventBus.emit('module.registered', {
      name,
      config,
    });

    logger.info(`Registered module: ${name}`, {
      enabled: config.enabled,
      version: config.version,
      dependencies: config.dependencies,
    });
  }

  /**
   * Get a module configuration
   *
   * @param name Module name
   * @returns Module configuration or undefined if not found
   */
  public getModule(name: string): ModuleConfig | undefined {
    return this.modules.get(name);
  }

  /**
   * Check if a module is registered
   *
   * @param name Module name
   * @returns True if module is registered
   */
  public hasModule(name: string): boolean {
    return this.modules.has(name);
  }

  /**
   * Check if a module is enabled
   *
   * @param name Module name
   * @returns True if module is enabled, false if disabled or not found
   */
  public isModuleEnabled(name: string): boolean {
    const module = this.modules.get(name);
    return module ? module?.enabled : false;
  }

  /**
   * Enable a module
   *
   * @param name Module name
   * @returns True if module was enabled, false if not found
   */
  public enableModule(name: string): boolean {
    const module = this.modules.get(name);

    if (!module) {
      return false;
    }

    module.enabled = true;
    this.modules.set(name, module);

    // Emit event
    eventBus.emit('module.enabled', {
      name,
      config: module,
    });

    logger.info(`Enabled module: ${name}`);

    return true;
  }

  /**
   * Disable a module
   *
   * @param name Module name
   * @returns True if module was disabled, false if not found
   */
  public disableModule(name: string): boolean {
    const module = this.modules.get(name);

    if (!module) {
      return false;
    }

    module.enabled = false;
    this.modules.set(name, module);

    // Emit event
    eventBus.emit('module.disabled', {
      name,
      config: module,
    });

    logger.info(`Disabled module: ${name}`);

    return true;
  }

  /**
   * Update module configuration
   *
   * @param name Module name
   * @param config New configuration (partial)
   * @returns True if module was updated, false if not found
   */
  public updateModuleConfig(name: string, config: Partial<ModuleConfig>): boolean {
    const module = this.modules.get(name);

    if (!module) {
      return false;
    }

    // Update configuration
    const updatedConfig: ModuleConfig = {
      ...module,
      ...config,
      config: {
        ...module.config,
        ...(config.config ?? {}),
      },
    };

    this.modules.set(name, updatedConfig);

    // Emit event
    eventBus.emit('module.updated', {
      name,
      config: updatedConfig,
    });

    logger.info(`Updated module configuration: ${name}`);

    return true;
  }

  /**
   * Get all registered modules
   *
   * @returns Object with module names as keys and configurations as values
   */
  public getAllModules(): Record<string, ModuleConfig> {
    const modules: Record<string, ModuleConfig> = {};

    // Use Array.from to convert the Map entries to an array
    Array.from(this.modules.entries()).forEach(([name, config]) => {
      modules[name] = config;
    });

    return modules;
  }

  /**
   * Get enabled modules
   *
   * @returns Object with module names as keys and configurations as values
   */
  public getEnabledModules(): Record<string, ModuleConfig> {
    const modules: Record<string, ModuleConfig> = {};

    // Use Array.from to convert the Map entries to an array
    Array.from(this.modules.entries()).forEach(([name, config]) => {
      if (config.enabled) {
        modules[name] = config;
      }
    });

    return modules;
  }

  /**
   * Check if all dependencies of a module are enabled
   *
   * @param name Module name
   * @returns True if all dependencies are enabled, false otherwise
   */
  public areDependenciesSatisfied(name: string): boolean {
    const module = this.modules.get(name);

    if (!module || !module?.dependencies) {
      return true;
    }

    return module?.dependencies.every((dependency) => this.isModuleEnabled(dependency));
  }
}

// Export singleton instance
export const moduleRegistry = ModuleRegistry.getInstance();
