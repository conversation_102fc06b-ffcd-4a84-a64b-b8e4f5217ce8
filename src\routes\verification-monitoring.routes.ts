// jscpd:ignore-file
/**
 * Verification Monitoring Routes
 * 
 * This file defines routes for the verification monitoring dashboard.
 */

import { Router } from "express";
import { VerificationMonitoringController } from "../controllers/monitoring/verification-monitoring.controller";
import { authenticate, authorize } from "../middlewares/auth.middleware";
import { VerificationMonitoringController } from "../controllers/monitoring/verification-monitoring.controller";
import { authenticate, authorize } from "../middlewares/auth.middleware";

const router: any =Router();
const verificationMonitoringController = new VerificationMonitoringController();

// All routes require authentication and admin authorization
router.use(authenticate);
router.use(authorize(["admin"]));

/**
 * @route GET /api/monitoring/verification/metrics
 * @desc Get verification metrics
 * @access Private (Admin)
 */
router.get(
    "/metrics",
    verificationMonitoringController.getVerificationMetrics.bind(verificationMonitoringController)
);

/**
 * @route GET /api/monitoring/verification/errors
 * @desc Get verification errors
 * @access Private (Admin)
 */
router.get(
    "/errors",
    verificationMonitoringController.getVerificationErrors.bind(verificationMonitoringController)
);

/**
 * @route GET /api/monitoring/verification/methods
 * @desc Get verification method metrics
 * @access Private (Admin)
 */
router.get(
    "/methods",
    verificationMonitoringController.getVerificationMethodMetrics.bind(verificationMonitoringController)
);

export default router;
