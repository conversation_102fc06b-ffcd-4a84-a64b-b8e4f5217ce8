// jscpd:ignore-file
import * as winston from 'winston';
import * as path from 'path';
import * as fs from 'fs';
import { format as formatDate } from 'date-fns';

// Get environment-specific logs directory
const getLogsDir = (): string => {
  const env = process.env.NODE_ENV || 'development';
  const logsDir = path.join(process.cwd(), 'logs', env);

  try {
    // Ensure logs directory exists
    if (!fs.existsSync(logsDir)) {
      fs.mkdirSync(logsDir, { recursive: true });
    }
  } catch (error) {
    // Fallback to a temporary directory if we can't create the logs directory
    console.error('Error creating logs directory:', error);
    return path.join(process.cwd(), 'temp');
  }

  return logsDir;
};

// Get the logs directory
const logsDir = getLogsDir();

// Define log levels
const levels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
};

// Define log level based on environment
const getLogLevel = (): string => {
  const env = process.env.NODE_ENV || 'development';

  if (process.env.LOG_LEVEL) {
    return process.env.LOG_LEVEL.toLowerCase();
  }

  switch (env) {
    case 'production':
      return 'info';
    case 'test':
      return 'warn';
    case 'development':
    default:
      return 'debug';
  }
};

// Define colors for each level
const colors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'blue',
};

// Add colors to winston
winston.addColors(colors);

// Create a custom format for console output
const consoleFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD, HH:mm:ss:ms' }),
  winston.format.colorize({ all: true }),
  winston.format.printf((info) => {
    const { timestamp, level, message, ...rest } = info;
    const metaData = Object.keys(rest).length ? JSON.stringify(rest, null, 2) : '';
    return `${timestamp} ${level}: ${message} ${metaData}`;
  })
);

// Create a custom format for file output (JSON)
const fileFormat = winston.format.combine(winston.format.timestamp(), winston.format.json());

// Generate date-based log filenames
const getLogFileName = (level: string): string => {
  const date = formatDate(new Date(), 'yyyy-MM-dd');
  return path.join(logsDir, `${level}-${date}.log`);
};

// Define where to store logs
const transports = [
  // Console transport
  new winston.transports.Console({
    format: consoleFormat,
  }),

  // Error log file transport
  new winston.transports.File({
    filename: getLogFileName('error'),
    level: 'error',
    format: fileFormat,
    maxsize: 10 * 1024 * 1024, // 10MB
    maxFiles: 30,
  }),

  // All logs file transport
  new winston.transports.File({
    filename: getLogFileName('combined'),
    format: fileFormat,
    maxsize: 10 * 1024 * 1024, // 10MB
    maxFiles: 30,
  }),
];

// Create the logger
export const logger = winston.createLogger({
  level: getLogLevel(),
  levels,
  transports,
  exitOnError: false,
  handleExceptions: true,
  handleRejections: true,
});

// Add exception handlers
logger.exceptions.handle(
  new winston.transports.File({
    filename: getLogFileName('exceptions'),
    format: fileFormat,
    maxsize: 10 * 1024 * 1024, // 10MB
    maxFiles: 30,
  })
);

// Add rejection handlers
logger.rejections.handle(
  new winston.transports.File({
    filename: getLogFileName('rejections'),
    format: fileFormat,
    maxsize: 10 * 1024 * 1024, // 10MB
    maxFiles: 30,
  })
);

// Export a stream object for Morgan middleware
export const stream = {
  write: (message: string) => {
    logger.http(message.trim());
  },
};

// Add request ID to log context
export const addRequestId = (requestId: string) => {
  return logger.child({ requestId });
};

// Create a logger with context
export const createLogger = (context: Record<string, any>) => {
  return logger.child(context);
};

// Log startup information
logger.info(`Logger initialized with level: ${getLogLevel()}`);
logger.info(`Environment: ${process.env.NODE_ENV || 'development'}`);
logger.info(`Operational mode: ${process.env.OPERATIONAL_MODE || 'development'}`);
logger.info(`Log files directory: ${logsDir}`);
