// jscpd:ignore-file
/**
 * Two-Factor Authentication Service
 *
 * Provides functionality for managing two-factor authentication
 */

import { PrismaClient } from '@prisma/client';
import { authenticator } from 'otplib';
import QRCode from 'qrcode';
import crypto from 'crypto';
import { logger } from '../utils/logger';
import prisma from '../lib/prisma';
import {
  AppError,
  createBadRequestError,
  createNotFoundError,
} from '../middlewares/error.middleware';
import { User } from '../types';
import { authenticator } from 'otplib';
import { logger } from '../utils/logger';
import {
  AppError,
  createBadRequestError,
  createNotFoundError,
} from '../middlewares/error.middleware';
import { User } from '../types';

// Number of backup codes to generate
const BACKUP_CODES_COUNT: number = 10;

// Length of each backup code segment
const BACKUP_CODE_SEGMENT_LENGTH: number = 4;

// Number of segments in a backup code
const BACKUP_CODE_SEGMENTS: number = 2;

// Separator for backup code segments
const BACKUP_CODE_SEPARATOR: string = '-';

/**
 * Two-Factor Authentication Service
 */
class TwoFactorAuthService {
  private prisma: PrismaClient;

  constructor(prismaClient: PrismaClient = prisma) {
    this.prisma = prismaClient;
  }

  /**
   * Get 2FA status for a user
   * @param userId User ID
   * @returns 2FA status
   */
  async getTwoFactorStatus(userId: string): Promise<{ enabled: boolean }> {
    try {
      const user: unknown = await this.prisma.user.findUnique({
        where: { id: userId },
        select: { twoFactorEnabled: true },
      });

      if (!user) {
        throw createNotFoundError('User not found');
      }

      return { enabled: user.twoFactorEnabled };
    } catch (error) {
      logger.error('Error getting 2FA status:', error);
      throw error;
    }
  }

  /**
   * Setup 2FA for a user
   * @param userId User ID
   * @returns Setup data including secret, QR code URL, and backup codes
   */
  async setupTwoFactor(
    userId: string
  ): Promise<{ secret: string; qrCodeUrl: string; backupCodes: string[] }> {
    try {
      // Get user
      const user: unknown = await this.prisma.user.findUnique({
        where: { id: userId },
        select: { id: true, email: true, twoFactorEnabled: true },
      });

      if (!user) {
        throw createNotFoundError('User not found');
      }

      // Check if 2FA is already enabled
      if (user.twoFactorEnabled) {
        throw createBadRequestError('Two-factor authentication is already enabled');
      }

      // Generate secret
      const secret: unknown = authenticator.generateSecret();

      // Generate QR code
      const serviceName: string = 'AmazingPay';
      const otpauth: unknown = authenticator.keyuri(user.email, serviceName, secret);
      const qrCodeUrl: unknown = await QRCode.toDataURL(otpauth);

      // Generate backup codes
      const backupCodes: unknown = this.generateBackupCodes();

      // Hash backup codes
      const hashedBackupCodes: unknown = backupCodes.map((code) => this.hashBackupCode(code));

      // Store secret and hashed backup codes in database
      await this.prisma.user.update({
        where: { id: userId },
        data: {
          twoFactorSecret: secret,
          // Store hashed backup codes in metadata or a separate table in a real implementation
        },
      });

      return {
        secret,
        qrCodeUrl,
        backupCodes,
      };
    } catch (error) {
      logger.error('Error setting up 2FA:', error);
      throw error;
    }
  }

  /**
   * Verify and enable 2FA for a user
   * @param userId User ID
   * @param token Token from authenticator app
   * @param secret Secret key
   * @returns Success status
   */
  async verifyAndEnableTwoFactor(
    userId: string,
    token: string,
    secret: string
  ): Promise<{ success: boolean }> {
    try {
      // Get user
      const user: unknown = await this.prisma.user.findUnique({
        where: { id: userId },
        select: { id: true, twoFactorEnabled: true, twoFactorSecret: true },
      });

      if (!user) {
        throw createNotFoundError('User not found');
      }

      if (user.twoFactorEnabled) {
        throw createBadRequestError('Two-factor authentication is already enabled');
      }

      // Verify that the provided secret matches the stored secret
      if (user.twoFactorSecret !== secret) {
        throw createBadRequestError('Invalid secret');
      }

      // Verify token
      const isValid: unknown = authenticator.verify({ token, secret });

      if (!isValid) {
        throw createBadRequestError('Invalid verification code');
      }

      // Enable 2FA
      await this.prisma.user.update({
        where: { id: userId },
        data: { twoFactorEnabled: true },
      });

      return { success: true };
    } catch (error) {
      logger.error('Error verifying and enabling 2FA:', error);
      throw error;
    }
  }

  /**
   * Verify a 2FA token
   * @param userId User ID
   * @param token Token from authenticator app
   * @returns Success status
   */
  async verifyTwoFactorToken(userId: string, token: string): Promise<{ success: boolean }> {
    try {
      // Get user
      const user: unknown = await this.prisma.user.findUnique({
        where: { id: userId },
        select: { id: true, twoFactorEnabled: true, twoFactorSecret: true },
      });

      if (!user) {
        throw createNotFoundError('User not found');
      }

      if (!user.twoFactorEnabled || !user.twoFactorSecret) {
        throw createBadRequestError('Two-factor authentication is not enabled');
      }

      // Verify token
      const isValid: unknown = authenticator.verify({ token, secret: user.twoFactorSecret });

      if (!isValid) {
        throw createBadRequestError('Invalid verification code');
      }

      return { success: true };
    } catch (error) {
      logger.error('Error verifying 2FA token:', error);
      throw error;
    }
  }

  /**
   * Disable 2FA for a user
   * @param userId User ID
   * @returns Success status
   */
  async disableTwoFactor(userId: string): Promise<{ success: boolean }> {
    try {
      // Get user
      const user: unknown = await this.prisma.user.findUnique({
        where: { id: userId },
        select: { id: true, twoFactorEnabled: true },
      });

      if (!user) {
        throw createNotFoundError('User not found');
      }

      if (!user.twoFactorEnabled) {
        throw createBadRequestError('Two-factor authentication is not enabled');
      }

      // Disable 2FA
      await this.prisma.user.update({
        where: { id: userId },
        data: {
          twoFactorEnabled: false,
          twoFactorSecret: null,
        },
      });

      return { success: true };
    } catch (error) {
      logger.error('Error disabling 2FA:', error);
      throw error;
    }
  }

  /**
   * Generate backup codes
   * @returns Array of backup codes
   */
  private generateBackupCodes(): string[] {
    const backupCodes: string[] = [];

    for (let i: number = 0; i < BACKUP_CODES_COUNT; i++) {
      const segments: string[] = [];

      for (let j: number = 0; j < BACKUP_CODE_SEGMENTS; j++) {
        const segment: unknown = crypto.randomBytes(BACKUP_CODE_SEGMENT_LENGTH / 2).toString('hex');
        segments.push(segment);
      }

      backupCodes.push(segments.join(BACKUP_CODE_SEPARATOR));
    }

    return backupCodes;
  }

  /**
   * Hash a backup code
   * @param code Backup code
   * @returns Hashed backup code
   */
  private hashBackupCode(code: string): string {
    return crypto.createHash('sha256').update(code).digest('hex');
  }
}

export default new TwoFactorAuthService();
