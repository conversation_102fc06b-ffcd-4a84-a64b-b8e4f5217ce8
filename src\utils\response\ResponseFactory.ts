// jscpd:ignore-file
import { Response } from "express";
import { AppError } from "../errors/AppError";
import { logger } from "../../lib/logger";
import { isDevelopment } from "../environment-validator";
import { AppError } from "../errors/AppError";
import { isDevelopment } from "../environment-validator";

/**
 * Response status
 */
export enum ResponseStatus {
  SUCCESS = "success",
  ERROR = "error",
  FAIL = "fail"
}

/**
 * Pagination metadata
 */
export interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

/**
 * Response factory
 * This class provides a centralized way to create API responses
 */
export class ResponseFactory {
  /**
   * Send a success response
   * @param res Express response
   * @param data Response data
   * @param message Success message
   * @param statusCode HTTP status code
   */
  static success<T>(
    res: Response,
    data: T,
    message = "Operation successful",
    statusCode = 200
  ): Response {
    return res.status(statusCode).json({
      status: ResponseStatus.SUCCESS,
      message,
      data
    });
  }

  /**
   * Send a paginated success response
   * @param res Express response
   * @param data Response data
   * @param pagination Pagination metadata
   * @param message Success message
   * @param statusCode HTTP status code
   */
  static paginated<T>(
    res: Response,
    data: T[],
    pagination: PaginationMeta,
    message = "Operation successful",
    statusCode = 200
  ): Response {
    return res.status(statusCode).json({
      status: ResponseStatus.SUCCESS,
      message,
      data,
      meta: {
        pagination
      }
    });
  }

  /**
   * Send a created response
   * @param res Express response
   * @param data Response data
   * @param message Success message
   */
  static created<T>(
    res: Response,
    data: T,
    message = "Resource created successfully"
  ): Response {
    return this.success(res, data, message, 201);
  }

  /**
   * Send a no content response
   * @param res Express response
   */
  static noContent(res: Response): Response {
    return res.status(204).end();
  }

  /**
   * Send an error response
   * @param res Express response
   * @param error Error object
   */
  static error(res: Response, error: AppError | Error): Response {
    if (error instanceof AppError) {
      // Log all AppErrors as warnings
      logger.warn(`${error.code}: ${(error as Error).message}`, {
        statusCode: error.statusCode,
        details: error.details ?? undefined
      });

      return res.status(error.statusCode).json(
        error.toJSON()
      );
    }

    // Handle generic errors
    logger.error(`Unhandled error: ${(error as Error).message}`, {
      stack: error.stack
    });

    return res.status(500).json({
      status: ResponseStatus.ERROR,
      message: "Internal server error",
      ...(isDevelopment() && { stack: error.stack })
    });
  }

  /**
   * Send a validation error response
   * @param res Express response
   * @param errors Validation errors
   * @param message Error message
   */
  static validationError(
    res: Response,
    errors: Record<string, string[]>,
    message = "Validation failed"
  ): Response {
    return res.status(400).json({
      status: ResponseStatus.FAIL,
      message,
      errors
    });
  }

  /**
   * Send an unauthorized response
   * @param res Express response
   * @param message Error message
   */
  static unauthorized(
    res: Response,
    message = "Authentication required"
  ): Response {
    return res.status(401).json({
      status: ResponseStatus.ERROR,
      message
    });
  }

  /**
   * Send a forbidden response
   * @param res Express response
   * @param message Error message
   */
  static forbidden(
    res: Response,
    message = "You do not have permission to perform this action"
  ): Response {
    return res.status(403).json({
      status: ResponseStatus.ERROR,
      message
    });
  }

  /**
   * Send a not found response
   * @param res Express response
   * @param message Error message
   */
  static notFound(
    res: Response,
    message = "Resource not found"
  ): Response {
    return res.status(404).json({
      status: ResponseStatus.ERROR,
      message
    });
  }
}

export default ResponseFactory;

