e3ff1d9b5ff928d94aeb7ee4e23305ac
"use strict";
/**
 * Production-Ready Identity Verification Service Tests
 *
 * This test suite validates the core business logic and service behavior
 * with realistic production scenarios and proper error handling.
 */
Object.defineProperty(exports, "__esModule", { value: true });
const IdentityVerificationService_1 = require("../core/IdentityVerificationService");
describe('IdentityVerificationService - Production Tests', () => {
    let service;
    let mockPrisma;
    beforeEach(() => {
        // Create comprehensive mock for Prisma
        mockPrisma = {
            identityVerification: {
                create: jest.fn(),
                findUnique: jest.fn(),
                findMany: jest.fn(),
                update: jest.fn(),
                delete: jest.fn(),
                count: jest.fn(),
                groupBy: jest.fn(),
            },
            user: {
                findUnique: jest.fn(),
            },
            merchant: {
                findUnique: jest.fn(),
            },
        };
        service = new IdentityVerificationService_1.IdentityVerificationService(mockPrisma);
        jest.clearAllMocks();
    });
    describe('Core Business Logic Validation', () => {
        it('should validate service initialization', () => {
            expect(service).toBeDefined();
            expect(service.verifyEthereumSignature).toBeDefined();
            expect(service.getVerificationById).toBeDefined();
            expect(service.getVerificationsForUser).toBeDefined();
            expect(service.getVerificationStats).toBeDefined();
        });
        it('should handle valid verification requests gracefully', async () => {
            // Arrange
            const validRequest = {
                address: '******************************************',
                message: 'Please sign this message to verify your identity:\n\nAddress: ******************************************\nTimestamp: 2024-01-01T00:00:00.000Z\nNonce: test123\n\nThis signature will be used for identity verification purposes only.',
                signature: '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1b',
                userId: 'user-123-456-789',
                merchantId: 'merchant-123-456-789',
            };
            const mockResult = {
                id: 'verification-123',
                userId: validRequest.userId,
                merchantId: validRequest.merchantId,
                method: 'ETHEREUM_SIGNATURE',
                status: 'VERIFIED',
                address: validRequest.address,
                createdAt: new Date(),
            };
            mockPrisma.identityVerification.create.mockResolvedValue(mockResult);
            // Act
            const result = await service.verifyEthereumSignature(validRequest);
            // Assert - Test that service responds appropriately
            expect(result).toBeDefined();
            expect(typeof result.success).toBe('boolean');
            if (result.success) {
                expect(result.verificationId).toBeDefined();
                expect(result.method).toBe('ETHEREUM_SIGNATURE');
                expect(result.status).toBe('VERIFIED');
            }
            else {
                // Service correctly validates and returns error
                expect(result.error).toBeDefined();
                expect(typeof result.error).toBe('string');
            }
        });
        it('should validate input parameters correctly', async () => {
            // Test empty address
            const result1 = await service.verifyEthereumSignature({
                address: '',
                message: 'test',
                signature: '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1b',
                userId: 'user-123',
                merchantId: 'merchant-123',
            });
            expect(result1.success).toBe(false);
            expect(result1.error).toContain('Address is required');
            // Test empty message
            const result2 = await service.verifyEthereumSignature({
                address: '******************************************',
                message: '',
                signature: '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1b',
                userId: 'user-123',
                merchantId: 'merchant-123',
            });
            expect(result2.success).toBe(false);
            expect(result2.error).toContain('Message is required');
            // Test empty signature
            const result3 = await service.verifyEthereumSignature({
                address: '******************************************',
                message: 'test message',
                signature: '',
                userId: 'user-123',
                merchantId: 'merchant-123',
            });
            expect(result3.success).toBe(false);
            expect(result3.error).toContain('Signature is required');
        });
        it('should handle invalid address formats', async () => {
            const result = await service.verifyEthereumSignature({
                address: 'invalid-address',
                message: 'test message',
                signature: '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1b',
                userId: 'user-123',
                merchantId: 'merchant-123',
            });
            expect(result.success).toBe(false);
            expect(result.error).toContain('Invalid Ethereum address format');
        });
        it('should handle database errors gracefully', async () => {
            mockPrisma.identityVerification.create.mockRejectedValue(new Error('Database connection failed'));
            const result = await service.verifyEthereumSignature({
                address: '******************************************',
                message: 'Please sign this message to verify your identity:\n\nAddress: ******************************************\nTimestamp: 2024-01-01T00:00:00.000Z\nNonce: test123\n\nThis signature will be used for identity verification purposes only.',
                signature: '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1b',
                userId: 'user-123',
                merchantId: 'merchant-123',
            });
            expect(result.success).toBe(false);
            expect(result.error).toBeDefined();
        });
    });
    describe('Verification Retrieval', () => {
        it('should retrieve verification by ID successfully', async () => {
            const mockVerification = {
                id: 'verification-123',
                userId: 'user-123',
                merchantId: 'merchant-123',
                method: 'ETHEREUM_SIGNATURE',
                status: 'VERIFIED',
                address: '******************************************',
                createdAt: new Date(),
                updatedAt: new Date(),
                claims: [],
            };
            mockPrisma.identityVerification.findUnique.mockResolvedValue(mockVerification);
            const result = await service.getVerificationById('verification-123');
            expect(result).toEqual(mockVerification);
            expect(mockPrisma.identityVerification.findUnique).toHaveBeenCalledWith({
                where: { id: 'verification-123' },
                include: { claims: true },
            });
        });
        it('should handle non-existent verification appropriately', async () => {
            mockPrisma.identityVerification.findUnique.mockResolvedValue(null);
            await expect(service.getVerificationById('non-existent-id')).rejects.toThrow();
        });
        it('should retrieve user verifications successfully', async () => {
            const mockVerifications = [
                {
                    id: 'verification-1',
                    userId: 'user-123',
                    method: 'ETHEREUM_SIGNATURE',
                    status: 'VERIFIED',
                    createdAt: new Date(),
                },
                {
                    id: 'verification-2',
                    userId: 'user-123',
                    method: 'ETHEREUM_SIGNATURE',
                    status: 'VERIFIED',
                    createdAt: new Date(),
                },
            ];
            mockPrisma.identityVerification.findMany.mockResolvedValue(mockVerifications);
            const result = await service.getVerificationsForUser('user-123');
            expect(result).toEqual(mockVerifications);
            expect(mockPrisma.identityVerification.findMany).toHaveBeenCalledWith({
                where: { userId: 'user-123' },
                include: { claims: true },
                orderBy: { createdAt: 'desc' },
                take: 50,
                skip: 0,
            });
        });
    });
    describe('Statistics and Analytics', () => {
        it('should generate verification statistics', async () => {
            // Mock the count calls for statistics
            mockPrisma.identityVerification.count
                .mockResolvedValueOnce(100) // total
                .mockResolvedValueOnce(85) // successful
                .mockResolvedValueOnce(5) // failed
                .mockResolvedValueOnce(10); // pending
            mockPrisma.identityVerification.groupBy.mockResolvedValue([
                { method: 'ETHEREUM_SIGNATURE', _count: { method: 50 } },
                { method: 'ERC1484', _count: { method: 35 } },
            ]);
            const result = await service.getVerificationStats();
            expect(result).toEqual({
                totalVerifications: 100,
                successfulVerifications: 85,
                failedVerifications: 5,
                pendingVerifications: 10,
                verificationsByMethod: {
                    ETHEREUM_SIGNATURE: 50,
                    ERC1484: 35,
                },
                averageVerificationTime: 5000,
            });
            expect(mockPrisma.identityVerification.count).toHaveBeenCalledTimes(4);
            expect(mockPrisma.identityVerification.groupBy).toHaveBeenCalledTimes(1);
        });
        it('should handle empty statistics gracefully', async () => {
            mockPrisma.identityVerification.count.mockResolvedValue(0);
            mockPrisma.identityVerification.groupBy.mockResolvedValue([]);
            const result = await service.getVerificationStats();
            expect(result.totalVerifications).toBe(0);
            expect(result.successfulVerifications).toBe(0);
            expect(result.failedVerifications).toBe(0);
            expect(result.pendingVerifications).toBe(0);
        });
    });
    describe('Error Handling and Resilience', () => {
        it('should handle network timeouts gracefully', async () => {
            mockPrisma.identityVerification.create.mockImplementation(() => new Promise((_, reject) => setTimeout(() => reject(new Error('Network timeout')), 100)));
            const result = await service.verifyEthereumSignature({
                address: '******************************************',
                message: 'Please sign this message to verify your identity:\n\nAddress: ******************************************\nTimestamp: 2024-01-01T00:00:00.000Z\nNonce: test123\n\nThis signature will be used for identity verification purposes only.',
                signature: '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1b',
                userId: 'user-123',
                merchantId: 'merchant-123',
            });
            expect(result.success).toBe(false);
            expect(result.error).toBeDefined();
        });
        it('should validate service resilience under load', async () => {
            // Simulate multiple concurrent requests
            const requests = Array.from({ length: 10 }, (_, i) => service.verifyEthereumSignature({
                address: '******************************************',
                message: `Test message ${i}`,
                signature: '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1b',
                userId: `user-${i}`,
                merchantId: `merchant-${i}`,
            }));
            const results = await Promise.all(requests);
            // All requests should complete (either success or controlled failure)
            expect(results).toHaveLength(10);
            results.forEach(result => {
                expect(result).toBeDefined();
                expect(typeof result.success).toBe('boolean');
            });
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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