"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebhookController = void 0;
const base_controller_1 = require("./base.controller");
const asyncHandler_1 = require("../utils/asyncHandler");
const AppError_1 = require("../utils/errors/AppError");
const webhook_service_1 = require("../services/webhook.service");
const client_1 = require("@prisma/client");
const prisma = new client_1.PrismaClient();
/**
 * WebhookController
 * Controller for handling webhook operations
 */
class WebhookController extends base_controller_1.BaseController {
    constructor() {
        super();
        /**
         * Get webhooks for the authenticated merchant
         */
        this.getWebhooks = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Get merchant ID from authenticated user
                const merchantId = req.user?.id;
                if (!merchantId) {
                    throw new AppError_1.AppError({
                        message: "Unauthorized",
                        type: ErrorType.AUTHENTICATION,
                        code: ErrorCode.INVALID_CREDENTIALS
                    });
                }
                // Get pagination parameters
                const limit = parseInt(req.query.limit) || 10;
                const offset = parseInt(req.query.offset) || 0;
                // Create webhook service
                const webhookService = new webhook_service_1.WebhookService();
                // Get webhooks
                const webhooks = await webhookService.getWebhooks(merchantId, limit, offset);
                // Return webhooks
                return res.status(200).json({
                    success: true,
                    data: webhooks
                });
            }
            catch (error) {
                if (error instanceof AppError_1.AppError) {
                    throw error;
                }
                throw new AppError_1.AppError({
                    message: "Failed to get webhooks",
                    type: ErrorType.INTERNAL,
                    code: ErrorCode.INTERNAL_SERVER_ERROR
                });
            }
        });
        /**
         * Get a single webhook by ID
         */
        this.getWebhook = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Get merchant ID from authenticated user
                const merchantId = req.user?.id;
                if (!merchantId) {
                    throw new AppError_1.AppError({
                        message: "Unauthorized",
                        type: ErrorType.AUTHENTICATION,
                        code: ErrorCode.INVALID_CREDENTIALS
                    });
                }
                // Get webhook ID from params
                const webhookId = req.params.id;
                if (!webhookId) {
                    throw new AppError_1.AppError({
                        message: "Webhook ID is required",
                        type: ErrorType.VALIDATION,
                        code: ErrorCode.MISSING_REQUIRED_FIELD
                    });
                }
                // Create webhook service
                const webhookService = new webhook_service_1.WebhookService();
                // Get webhook
                const webhook = await webhookService.getWebhook(webhookId);
                // Check if webhook belongs to merchant
                if (webhook.merchantId !== merchantId) {
                    throw new AppError_1.AppError({
                        message: "Unauthorized",
                        type: ErrorType.AUTHENTICATION,
                        code: ErrorCode.INVALID_CREDENTIALS
                    });
                }
                // Return webhook
                return res.status(200).json({
                    success: true,
                    data: webhook
                });
            }
            catch (error) {
                if (error instanceof AppError_1.AppError) {
                    throw error;
                }
                throw new AppError_1.AppError({
                    message: "Failed to get webhook",
                    type: ErrorType.INTERNAL,
                    code: ErrorCode.INTERNAL_SERVER_ERROR
                });
            }
        });
        /**
         * Retry a failed webhook
         */
        this.retryWebhook = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Get merchant ID from authenticated user
                const merchantId = req.user?.id;
                if (!merchantId) {
                    throw new AppError_1.AppError({
                        message: "Unauthorized",
                        type: ErrorType.AUTHENTICATION,
                        code: ErrorCode.INVALID_CREDENTIALS
                    });
                }
                // Get webhook ID from params
                const webhookId = req.params.id;
                if (!webhookId) {
                    throw new AppError_1.AppError({
                        message: "Webhook ID is required",
                        type: ErrorType.VALIDATION,
                        code: ErrorCode.MISSING_REQUIRED_FIELD
                    });
                }
                // Check if webhook belongs to merchant
                const webhook = await prisma.webhook.findUnique({
                    where: { id: webhookId }
                });
                if (!webhook) {
                    throw new AppError_1.AppError({
                        message: "Webhook not found",
                        type: ErrorType.NOT_FOUND,
                        code: ErrorCode.RESOURCE_NOT_FOUND
                    });
                }
                if (webhook.merchantId !== merchantId) {
                    throw new AppError_1.AppError({
                        message: "Unauthorized",
                        type: ErrorType.AUTHENTICATION,
                        code: ErrorCode.INVALID_CREDENTIALS
                    });
                }
                // Create webhook service
                const webhookService = new webhook_service_1.WebhookService();
                // Retry webhook
                const success = await webhookService.retryWebhook(webhookId);
                if (!success) {
                    throw new AppError_1.AppError({
                        message: "Failed to retry webhook",
                        type: ErrorType.INTERNAL,
                        code: ErrorCode.INTERNAL_SERVER_ERROR
                    });
                }
                // Return success
                return res.status(200).json({
                    success: true,
                    message: "Webhook retry initiated"
                });
            }
            catch (error) {
                if (error instanceof AppError_1.AppError) {
                    throw error;
                }
                throw new AppError_1.AppError({
                    message: "Failed to retry webhook",
                    type: ErrorType.INTERNAL,
                    code: ErrorCode.INTERNAL_SERVER_ERROR
                });
            }
        });
        /**
         * Update the webhook URL for the authenticated merchant
         */
        this.updateWebhookUrl = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Get merchant ID from authenticated user
                const merchantId = req.user?.id;
                if (!merchantId) {
                    throw new AppError_1.AppError({
                        message: "Unauthorized",
                        type: ErrorType.AUTHENTICATION,
                        code: ErrorCode.INVALID_CREDENTIALS
                    });
                }
                // Get webhook URL from body
                const { webhookUrl } = req.body;
                if (!webhookUrl) {
                    throw new AppError_1.AppError({
                        message: "Webhook URL is required",
                        type: ErrorType.VALIDATION,
                        code: ErrorCode.MISSING_REQUIRED_FIELD
                    });
                }
                // Validate URL
                try {
                    new URL(webhookUrl);
                }
                catch (error) {
                    throw new AppError_1.AppError({
                        message: "Invalid webhook URL",
                        type: ErrorType.VALIDATION,
                        code: ErrorCode.INVALID_INPUT
                    });
                }
                // Update merchant webhook URL
                await prisma.merchant.update({
                    where: { id: merchantId },
                    data: { webhookUrl }
                });
                // Return success
                return res.status(200).json({
                    success: true,
                    message: "Webhook URL updated successfully"
                });
            }
            catch (error) {
                if (error instanceof AppError_1.AppError) {
                    throw error;
                }
                throw new AppError_1.AppError({
                    message: "Failed to update webhook URL",
                    type: ErrorType.INTERNAL,
                    code: ErrorCode.INTERNAL_SERVER_ERROR
                });
            }
        });
        /**
         * Test the webhook configuration for the authenticated merchant
         */
        this.testWebhook = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Get merchant ID from authenticated user
                const merchantId = req.user?.id;
                if (!merchantId) {
                    throw new AppError_1.AppError({
                        message: "Unauthorized",
                        type: ErrorType.AUTHENTICATION,
                        code: ErrorCode.INVALID_CREDENTIALS
                    });
                }
                // Get merchant
                const merchant = await prisma.merchant.findUnique({
                    where: { id: merchantId },
                    select: { id: true,
                        webhookUrl: true
                    }
                });
                if (!merchant) {
                    throw new AppError_1.AppError({
                        message: "Merchant not found",
                        type: ErrorType.NOT_FOUND,
                        code: ErrorCode.RESOURCE_NOT_FOUND
                    });
                }
                if (!merchant.webhookUrl) {
                    throw new AppError_1.AppError({
                        message: "Webhook URL not configured",
                        type: ErrorType.VALIDATION,
                        code: ErrorCode.INVALID_INPUT
                    });
                }
                // Create webhook service
                const webhookService = new webhook_service_1.WebhookService();
                // Test webhook
                const success = await webhookService.testWebhook(merchantId, merchant.webhookUrl);
                if (!success) {
                    throw new AppError_1.AppError({
                        message: "Failed to test webhook",
                        type: ErrorType.INTERNAL,
                        code: ErrorCode.INTERNAL_SERVER_ERROR
                    });
                }
                // Return success
                return res.status(200).json({
                    success: true,
                    message: "Test webhook sent successfully"
                });
            }
            catch (error) {
                if (error instanceof AppError_1.AppError) {
                    throw error;
                }
                throw new AppError_1.AppError({
                    message: "Failed to test webhook",
                    type: ErrorType.INTERNAL,
                    code: ErrorCode.INTERNAL_SERVER_ERROR
                });
            }
        });
    }
}
exports.WebhookController = WebhookController;
exports.default = new WebhookController();
//# sourceMappingURL=webhook.controller.js.map