/**
 * Fraud Detection Response Mapper
 *
 * Handles response formatting for fraud detection operations.
 */
import { Response } from 'express';
import { RiskAssessmentResponse, FraudConfigResponse, FlaggedTransactionResponse, FraudStatisticsResponse } from '../types/FraudDetectionControllerTypes';
import { AppError } from '../../../utils/errors/AppError';
/**
 * Response mapper for fraud detection operations
 */
export declare class FraudDetectionResponseMapper {
    /**
     * Send success response
     */
    static sendSuccess<T>(res: Response, data: T, message?: string, statusCode?: number, pagination?: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
    }): void;
    /**
     * Send error response
     */
    static sendError(res: Response, error: AppError | Error, statusCode?: number): void;
    /**
     * Send risk assessment response
     */
    static sendRiskAssessment(res: Response, assessment: any, message?: string): void;
    /**
     * Send transaction risk assessment response
     */
    static sendTransactionRiskAssessment(res: Response, assessment: RiskAssessmentResponse, message?: string): void;
    /**
     * Send fraud configuration response
     */
    static sendFraudConfig(res: Response, config: FraudConfigResponse, message?: string): void;
    /**
     * Send fraud configuration updated response
     */
    static sendFraudConfigUpdated(res: Response, config: FraudConfigResponse): void;
    /**
     * Send flagged transactions list response
     */
    static sendFlaggedTransactionsList(res: Response, transactions: FlaggedTransactionResponse[], total: number, page?: number, limit?: number): void;
    /**
     * Send fraud statistics response
     */
    static sendFraudStatistics(res: Response, statistics: FraudStatisticsResponse): void;
    /**
     * Send validation error response
     */
    static sendValidationError(res: Response, errors: any[], message?: string): void;
    /**
     * Send authorization error response
     */
    static sendAuthorizationError(res: Response, message?: string, requiredRole?: string): void;
    /**
     * Send not found error response
     */
    static sendNotFoundError(res: Response, resource?: string): void;
    /**
     * Send internal server error response
     */
    static sendInternalServerError(res: Response, message?: string): void;
    /**
     * Handle async controller method
     */
    static asyncHandler(fn: Function): (req: any, res: Response, next: Function) => void;
    /**
     * Set response headers for API
     */
    static setApiHeaders(res: Response): void;
    /**
     * Format risk level for display
     */
    static formatRiskLevel(level: string): string;
    /**
     * Format risk score for display
     */
    static formatRiskScore(score: number): string;
    /**
     * Format percentage for display
     */
    static formatPercentage(value: number): string;
    /**
     * Format currency amount
     */
    static formatCurrency(amount: number, currency?: string): string;
    /**
     * Format date for display
     */
    static formatDate(date: Date): string;
    /**
     * Create summary statistics
     */
    static createSummaryStats(statistics: FraudStatisticsResponse): any;
    /**
     * Transform risk assessment for display
     */
    static transformRiskAssessment(assessment: RiskAssessmentResponse): any;
    /**
     * Transform flagged transaction for display
     */
    static transformFlaggedTransaction(transaction: FlaggedTransactionResponse): any;
}
//# sourceMappingURL=FraudDetectionResponseMapper.d.ts.map