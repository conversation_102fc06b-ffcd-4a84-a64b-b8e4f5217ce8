"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TransactionController = void 0;
const BaseController_1 = require("../../core/BaseController");
const transaction_service_1 = require("../../services/transaction.service");
const ErrorFactory_1 = require("../../utils/errors/ErrorFactory");
/**
 * Transaction controller
 * This controller handles transaction-related operations
 */
class TransactionController extends BaseController_1.BaseController {
    /**
     * Create a new transaction controller
     */
    constructor() {
        super();
        /**
         * Get all transactions
         */
        this.getTransactions = this.createHandler(async (req, res) => {
            // Check authorization
            const { userRole, userId, merchantId } = this.checkAuthorization(req);
            // Parse query parameters
            const { startDate, endDate } = req.query;
            const status = req.query.status;
            const requestedMerchantId = req.query.merchantId;
            // Determine target merchant ID
            const targetMerchantId = this.determineTargetMerchantId(userRole, merchantId, requestedMerchantId);
            // Parse pagination parameters
            const { limit, offset } = this.parsePagination(req);
            // Parse date range if provided
            let dateRange;
            if (startDate && endDate) {
                dateRange = this.parseDateRange(startDate, endDate);
            }
            // Get transactions
            const result = await this.transactionService.getTransactions({
                merchantId: targetMerchantId,
                status,
                startDate: dateRange?.startDate,
                endDate: dateRange?.endDate,
                limit,
                offset
            });
            // Send paginated response
            return this.sendPaginatedSuccess(res, result.data, result.total, limit, offset);
        });
        /**
         * Get a transaction by ID
         */
        this.getTransaction = this.createHandler(async (req, res) => {
            // Check authorization
            const { userRole, userId, merchantId } = this.checkAuthorization(req);
            // Get transaction ID
            const { id } = req.params;
            // Get transaction
            const transaction = await this.transactionService.getTransactionById(id);
            // Check if transaction exists
            if (!transaction) {
                throw ErrorFactory_1.ErrorFactory.notFound('Transaction', id);
            }
            // Check if user has access to this transaction
            if (userRole !== 'ADMIN' && transaction.merchantId !== merchantId) {
                throw ErrorFactory_1.ErrorFactory.authorization('You do not have permission to view this transaction');
            }
            // Send success response
            return this.sendSuccess(res, transaction);
        });
        /**
         * Create a new transaction
         */
        this.createTransaction = this.createHandler(async (req, res) => {
            // Check authorization
            const { userRole, userId, merchantId } = this.checkAuthorization(req);
            // Get request body
            const { amount, currency, method, description } = req.body;
            // Validate required fields
            if (!amount || !currency || !method) {
                throw ErrorFactory_1.ErrorFactory.validation('Amount, currency, and method are required');
            }
            // Create transaction
            const transaction = await this.transactionService.createTransaction({
                amount: parseFloat(amount),
                currency,
                method,
                description,
                merchantId: merchantId || req.body.merchantId,
                userId
            });
            // Send success response
            return this.sendSuccess(res, transaction, 201);
        });
        /**
         * Update a transaction
         */
        this.updateTransaction = this.createHandler(async (req, res) => {
            // Check authorization
            const { userRole, userId, merchantId } = this.checkAuthorization(req);
            // Get transaction ID
            const { id } = req.params;
            // Get request body
            const { status, notes } = req.body;
            // Get transaction
            const transaction = await this.transactionService.getTransactionById(id);
            // Check if transaction exists
            if (!transaction) {
                throw ErrorFactory_1.ErrorFactory.notFound('Transaction', id);
            }
            // Check if user has access to this transaction
            if (userRole !== 'ADMIN' && transaction.merchantId !== merchantId) {
                throw ErrorFactory_1.ErrorFactory.authorization('You do not have permission to update this transaction');
            }
            // Update transaction
            const updatedTransaction = await this.transactionService.updateTransaction(id, {
                status,
                notes,
                updatedBy: userId
            });
            // Send success response
            return this.sendSuccess(res, updatedTransaction);
        });
        /**
         * Delete a transaction
         */
        this.deleteTransaction = this.createHandler(async (req, res) => {
            // Check authorization
            const { userRole } = this.checkAuthorization(req);
            // Only admins can delete transactions
            this.checkAdminRole(userRole);
            // Get transaction ID
            const { id } = req.params;
            // Delete transaction
            await this.transactionService.deleteTransaction(id);
            // Send success response
            return this.sendSuccess(res, { message: 'Transaction deleted successfully' });
        });
        /**
         * Get transaction statistics
         */
        this.getTransactionStats = this.createHandler(async (req, res) => {
            // Check authorization
            const { userRole, userId, merchantId } = this.checkAuthorization(req);
            // Parse query parameters
            const { startDate, endDate } = req.query;
            const requestedMerchantId = req.query.merchantId;
            // Determine target merchant ID
            const targetMerchantId = this.determineTargetMerchantId(userRole, merchantId, requestedMerchantId);
            // Parse date range
            const dateRange = this.parseDateRange(startDate, endDate);
            // Get transaction statistics
            const stats = await this.transactionService.getTransactionStats({
                merchantId: targetMerchantId,
                startDate: dateRange.startDate,
                endDate: dateRange.endDate
            });
            // Send success response
            return this.sendSuccess(res, stats);
        });
        this.transactionService = new transaction_service_1.TransactionService();
    }
}
exports.TransactionController = TransactionController;
//# sourceMappingURL=transaction.controller.js.map