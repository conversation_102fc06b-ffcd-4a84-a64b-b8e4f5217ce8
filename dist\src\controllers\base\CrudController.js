"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CrudController = void 0;
const asyncHandler_1 = require("../../middleware/asyncHandler");
const BaseController_1 = require("./BaseController");
/**
 * Generic CRUD controller with common CRUD operations
 */
class CrudController extends BaseController_1.BaseController {
    constructor(service, entityName) {
        super();
        this.requiredCreateFields = [];
        this.requiredUpdateFields = [];
        /**
         * Get all entities
         * @route GET /api/{entity}
         */
        this.getAll = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Check authentication
                this.checkAuthorization(req);
                // Parse pagination
                const { page, limit, offset } = this.parsePagination(req);
                // Get filters
                const filters = this.parseFilters(req);
                // Get entities
                const result = await this.getAllEntities(page, limit, offset, filters);
                // Return response
                return this.sendPaginatedSuccess(res, result.data, result.total, page, limit);
            }
            catch (error) {
                this.handleError(error, res);
            }
        });
        /**
         * Get entity by ID
         * @route GET /api/{entity}/:id
         */
        this.getById = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Check authentication
                this.checkAuthorization(req);
                // Get ID
                const id = req.params.id;
                // Get entity
                const entity = await this.getEntityById(id);
                // Return response
                return this.sendSuccess(res, entity);
            }
            catch (error) {
                this.handleError(error, res);
            }
        });
        /**
         * Create entity
         * @route POST /api/{entity}
         */
        this.create = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Check authentication
                this.checkAuthorization(req);
                // Validate required fields
                this.validateRequiredFields(req, this.requiredCreateFields);
                // Validate input
                this.validateCreateInput(req);
                // Create entity
                const entity = await this.createEntity(req.body);
                // Return response
                return this.sendSuccess(res, entity, 201);
            }
            catch (error) {
                this.handleError(error, res);
            }
        });
        /**
         * Update entity
         * @route PUT /api/{entity}/:id
         */
        this.update = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Check authentication
                this.checkAuthorization(req);
                // Get ID
                const id = req.params.id;
                // Validate required fields
                if (this.requiredUpdateFields.length > 0) {
                    this.validateRequiredFields(req, this.requiredUpdateFields);
                }
                // Validate input
                this.validateUpdateInput(req);
                // Update entity
                const entity = await this.updateEntity(id, req.body);
                // Return response
                return this.sendSuccess(res, entity);
            }
            catch (error) {
                this.handleError(error, res);
            }
        });
        /**
         * Delete entity
         * @route DELETE /api/{entity}/:id
         */
        this.delete = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Check authentication
                this.checkAuthorization(req);
                // Get ID
                const id = req.params.id;
                // Delete entity
                await this.deleteEntity(id);
                // Return response
                return this.sendMessage(res, `${this.entityName} deleted successfully`);
            }
            catch (error) {
                this.handleError(error, res);
            }
        });
        this.service = service;
        this.entityName = entityName;
    }
    /**
     * Parse filters from request
     * @param req Request
     * @returns Filters
     */
    parseFilters(req) {
        // Default implementation - override in child classes
        const { page, limit, sortBy, sortOrder, ...filters } = req.query;
        return filters;
    }
    /**
     * Validate create input
     * @param req Request
     */
    validateCreateInput(req) {
        // Default implementation - override in child classes
    }
    /**
     * Validate update input
     * @param req Request
     */
    validateUpdateInput(req) {
        // Default implementation - override in child classes
    }
}
exports.CrudController = CrudController;
exports.default = CrudController;
//# sourceMappingURL=CrudController.js.map