4017c1797ff894a10ca51742d1355219
"use strict";
/**
 * Blockchain Utilities for Identity Verification
 *
 * Common blockchain operations and utilities.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.BlockchainUtils = exports.POLYGON_ID_VERIFIER_ABI = exports.ERC725_ABI = exports.ERC1484_ABI = void 0;
const ethers_1 = require("ethers");
/**
 * ERC-1484 Identity Registry ABI (simplified)
 */
exports.ERC1484_ABI = [
    "function getIdentity(uint ein) view returns (address[] memory, address[] memory, address[] memory)",
    "function getEIN(address _address) view returns (uint)",
    "function hasIdentity(address _address) view returns (bool)",
    "function isAssociatedAddressFor(uint ein, address _address) view returns (bool)"
];
/**
 * ERC-725 Identity ABI (simplified)
 */
exports.ERC725_ABI = [
    "function getKey(bytes32 _key) view returns (bytes32)",
    "function getKeys(bytes32[] memory _keys) view returns (bytes32[] memory)",
    "function execute(uint256 _operation, address _to, uint256 _value, bytes memory _data) returns (bytes32)"
];
/**
 * Polygon ID Verifier ABI (simplified)
 */
exports.POLYGON_ID_VERIFIER_ABI = [
    "function verifyProof(uint256 _circuitId, uint256[] memory _inputs, uint256[2] memory _a, uint256[2][2] memory _b, uint256[2] memory _c, uint256[] memory _signals) view returns (bool)"
];
/**
 * Blockchain utility class
 */
class BlockchainUtils {
    /**
     * Get Ethereum provider
     */
    static getProvider() {
        if (!this.provider) {
            const rpcUrl = process.env.ETHEREUM_RPC_URL || "https://mainnet.infura.io/v3/your-infura-key";
            this.provider = new ethers_1.ethers.JsonRpcProvider(rpcUrl);
        }
        return this.provider;
    }
    /**
     * Validate Ethereum address
     */
    static isValidAddress(address) {
        try {
            return ethers_1.ethers.isAddress(address);
        }
        catch {
            return false;
        }
    }
    /**
     * Normalize Ethereum address
     */
    static normalizeAddress(address) {
        if (!this.isValidAddress(address)) {
            throw new Error("Invalid Ethereum address");
        }
        return ethers_1.ethers.getAddress(address);
    }
    /**
     * Verify message signature
     */
    static verifySignature(message, signature, expectedAddress) {
        try {
            const recoveredAddress = ethers_1.ethers.verifyMessage(message, signature);
            return recoveredAddress.toLowerCase() === expectedAddress.toLowerCase();
        }
        catch {
            return false;
        }
    }
    /**
     * Recover address from signature
     */
    static recoverAddress(message, signature) {
        return ethers_1.ethers.verifyMessage(message, signature);
    }
    /**
     * Create contract instance
     */
    static createContract(address, abi) {
        const provider = this.getProvider();
        return new ethers_1.ethers.Contract(address, abi, provider);
    }
    /**
     * Get ERC-1484 registry contract
     */
    static getERC1484Contract(registryAddress) {
        return this.createContract(registryAddress, exports.ERC1484_ABI);
    }
    /**
     * Get ERC-725 identity contract
     */
    static getERC725Contract(identityAddress) {
        return this.createContract(identityAddress, exports.ERC725_ABI);
    }
    /**
     * Get Polygon ID verifier contract
     */
    static getPolygonIDContract(verifierAddress) {
        return this.createContract(verifierAddress, exports.POLYGON_ID_VERIFIER_ABI);
    }
    /**
     * Convert string to bytes32
     */
    static stringToBytes32(str) {
        return ethers_1.ethers.keccak256(ethers_1.ethers.toUtf8Bytes(str));
    }
    /**
     * Convert bytes32 to string
     */
    static bytes32ToString(bytes32) {
        return ethers_1.ethers.toUtf8String(bytes32);
    }
    /**
     * Check if transaction is confirmed
     */
    static async isTransactionConfirmed(txHash, requiredConfirmations = 6) {
        try {
            const provider = this.getProvider();
            const receipt = await provider.getTransactionReceipt(txHash);
            if (!receipt) {
                return false;
            }
            const currentBlock = await provider.getBlockNumber();
            const confirmations = currentBlock - receipt.blockNumber;
            return confirmations >= requiredConfirmations;
        }
        catch {
            return false;
        }
    }
    /**
     * Get transaction details
     */
    static async getTransactionDetails(txHash) {
        const provider = this.getProvider();
        const [transaction, receipt] = await Promise.all([
            provider.getTransaction(txHash),
            provider.getTransactionReceipt(txHash)
        ]);
        return {
            transaction,
            receipt,
            confirmations: receipt ? await provider.getBlockNumber() - receipt.blockNumber : 0
        };
    }
    /**
     * Estimate gas for contract call
     */
    static async estimateGas(contract, method, params) {
        return await contract[method].estimateGas(...params);
    }
    /**
     * Get current gas price
     */
    static async getGasPrice() {
        const provider = this.getProvider();
        const feeData = await provider.getFeeData();
        return feeData.gasPrice || BigInt(0);
    }
}
exports.BlockchainUtils = BlockchainUtils;
BlockchainUtils.provider = null;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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