// jscpd:ignore-file
import { Router } from "express";
import BlockchainVerificationController from "../controllers/blockchain-verification.controller";
import { BinanceVerificationController } from "../controllers/refactored/binance-verification.controller.ts";
import { authenticate } from '../middlewares/auth';
import { BinanceVerificationController } from "../controllers/refactored/binance-verification.controller.ts";
import { authenticate } from '../middlewares/auth';

const router: unknown =Router();

// Blockchain verification routes
router.post("/blockchain", authenticate, BlockchainVerificationController.verifyBlockchainTransaction);
router.post("/blockchain/trc20", authenticate, BlockchainVerificationController.verifyBinanceTransaction);
// These methods don't exist in the controller, so we'll comment them out for now
// router.post('/blockchain/erc20', authenticate, BlockchainVerificationController.verifyBinanceTransaction);
// router.post('/blockchain/bep20', authenticate, BlockchainVerificationController.verifyBinanceTransaction);
// router.post('/blockchain/polygon', authenticate, BlockchainVerificationController.verifyBinanceTransaction);

// Binance verification routes
router.post("/binance/trc20", authenticate, BinanceVerificationController.verifyTRC20Transaction);
router.post("/binance/c2c", authenticate, BinanceVerificationController.verifyC2CTransaction);
router.post("/binance/pay", authenticate, BinanceVerificationController.verifyPayTransaction);
router.post("/binance/test", authenticate, BinanceVerificationController.testConnection);

export default router;
