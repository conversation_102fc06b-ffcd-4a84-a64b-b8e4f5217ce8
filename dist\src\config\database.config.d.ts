export type Environment = 'production';
export interface DatabaseConfig {
    url: string;
    host: string;
    port: number;
    username: string;
    password: string;
    database: string;
    ssl: boolean;
    connectionPoolMin: number;
    connectionPoolMax: number;
    statementTimeout: number;
    logQueries: boolean;
}
export declare const getEnvironment: any;
export declare const getDatabaseConfig: any;
export declare const getPrismaClient: any;
export declare const closeDatabaseConnection: any;
export declare const getDatabaseUrl: any;
declare const databaseConfig: any;
export default databaseConfig;
//# sourceMappingURL=database.config.d.ts.map