/**
 * Enhanced Payment Controller
 *
 * Handles payment operations using the new payment service.
 */
/**
 * Process a payment
 */
export declare const processPayment: any;
/**
 * Get available payment methods for a merchant
 */
export declare const getAvailablePaymentMethods: any;
/**
 * Get payment method details
 */
export declare const getPaymentMethodDetails: any;
/**
 * Get all payment methods
 */
export declare const getAllPaymentMethods: any;
declare const _default: {
    processPayment: any;
    getAvailablePaymentMethods: any;
    getPaymentMethodDetails: any;
    getAllPaymentMethods: any;
};
export default _default;
//# sourceMappingURL=enhanced-payment.controller.d.ts.map