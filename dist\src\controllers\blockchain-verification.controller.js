"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BlockchainVerificationController = void 0;
const BaseController_1 = require("./base/BaseController");
const logger_1 = require("../utils/logger");
const blockchain_1 = require("../types/blockchain");
const verification_1 = require("../types/verification");
const blockchain_api_service_1 = require("../services/blockchain/blockchain-api.service");
const binance_api_service_1 = require("../services/blockchain/binance-api.service");
const verification_websocket_service_1 = require("../services/websocket/verification-websocket.service");
const prisma_1 = __importDefault(require("../lib/prisma"));
class BlockchainVerificationController extends BaseController_1.BaseController {
    constructor() {
        super();
        /**
         * Verify a blockchain transaction
         */
        this.verifyBlockchainTransaction = async (req, res) => {
            try {
                const { paymentId, merchantId, paymentMethodId, amount, currency, txHash, fromAddress, toAddress, network, metadata } = req.body;
                // Validate required fields
                if (!paymentId || !merchantId || !amount || !currency || !txHash || !network) {
                    return res.status(400).json({
                        success: false,
                        message: "Missing required fields"
                    });
                }
                // Validate network
                if (!Object.values(blockchain_1.BlockchainNetwork).includes(network)) {
                    return res.status(400).json({
                        success: false,
                        message: "Invalid network"
                    });
                }
                // Get merchant
                const merchant = await prisma_1.default.merchant.findUnique({
                    where: { id: merchantId }
                });
                if (!merchant) {
                    return res.status(404).json({
                        success: false,
                        message: "Merchant not found"
                    });
                }
                // Get transaction
                const transaction = await prisma_1.default.transaction.findUnique({
                    where: { id: paymentId }
                });
                if (!transaction) {
                    return res.status(404).json({
                        success: false,
                        message: "Transaction not found"
                    });
                }
                // Get payment method
                const paymentMethod = paymentMethodId ? await prisma_1.default.paymentMethod.findUnique({
                    where: { id: paymentMethodId }
                }) : null;
                let verificationResult;
                try {
                    verificationResult = await this.blockchainApiService.verifyTransaction(txHash, toAddress || paymentMethod?.address, parseFloat(amount.toString()), currency, network);
                }
                catch (error) {
                    return res.status(400).json({
                        success: false,
                        message: "Error verifying transaction",
                        error: error instanceof Error ? error.message : "Unknown error"
                    });
                }
                // Update transaction record
                const updatedTransaction = await prisma_1.default.transaction.update({
                    where: { id: transaction.id },
                    data: { status: verificationResult.success ? "COMPLETED" : "FAILED",
                        verificationMethod: `BLOCKCHAIN_${network}`,
                        verificationData: JSON.stringify({
                            ...JSON.parse(transaction.verificationData || '{}'),
                            txHash,
                            fromAddress: verificationResult.fromAddress || fromAddress,
                            toAddress: verificationResult.toAddress || toAddress || paymentMethod?.address,
                            amount: verificationResult.amount || parseFloat(amount.toString()),
                            currency,
                            timestamp: verificationResult.timestamp || Date.now(),
                            confirmations: verificationResult.confirmations || 0,
                            status: verificationResult.success ? "success" : "failed",
                            blockNumber: verificationResult.blockNumber,
                            fee: verificationResult.fee
                        })
                    }
                });
                // Create verification record
                const verification = await prisma_1.default.verification.create({
                    data: { transactionId: transaction.id,
                        merchantId: merchant.id,
                        method: `BLOCKCHAIN_${network}`,
                        status: verificationResult.success ? verification_1.VerificationStatus.SUCCESS : verification_1.VerificationStatus.FAILED,
                        data: JSON.stringify({
                            txHash,
                            fromAddress: verificationResult.fromAddress || fromAddress,
                            toAddress: verificationResult.toAddress || toAddress || paymentMethod?.address,
                            amount: verificationResult.amount || parseFloat(amount.toString()),
                            currency,
                            network,
                            timestamp: verificationResult.timestamp || Date.now(),
                            confirmations: verificationResult.confirmations || 0,
                            blockNumber: verificationResult.blockNumber,
                            fee: verificationResult.fee
                        })
                    }
                });
                // Emit verification event
                verification_websocket_service_1.verificationEvents.emit('verification', {
                    transactionId: transaction.id,
                    merchantId: merchant.id,
                    status: verificationResult.success ? verification_1.VerificationStatus.SUCCESS : verification_1.VerificationStatus.FAILED,
                    method: `BLOCKCHAIN_${network}`,
                    timestamp: new Date()
                });
                return res.status(200).json({
                    success: true,
                    message: verificationResult.success ? "Transaction verified successfully" : "Transaction verification failed",
                    data: { transaction: updatedTransaction,
                        verification,
                        verificationResult
                    }
                });
            }
            catch (error) {
                logger_1.logger.error(`Error in verifyBlockchainTransaction: ${error instanceof Error ? error.message : 'Unknown error'}`);
                return res.status(500).json({
                    success: false,
                    message: "Internal server error",
                    error: error instanceof Error ? error.message : "Unknown error"
                });
            }
        };
        /**
         * Verify a Binance transaction
         */
        this.verifyBinanceTransaction = async (req, res) => {
            try {
                const { paymentId, merchantId, paymentMethodId, amount, currency, txHash, toAddress, apiKey, secretKey, metadata } = req.body;
                // Validate required fields
                if (!paymentId || !merchantId || !amount || !currency || !txHash) {
                    return res.status(400).json({
                        success: false,
                        message: "Missing required fields"
                    });
                }
                // Get merchant
                const merchant = await prisma_1.default.merchant.findUnique({
                    where: { id: merchantId }
                });
                if (!merchant) {
                    return res.status(404).json({
                        success: false,
                        message: "Merchant not found"
                    });
                }
                // Get transaction
                const transaction = await prisma_1.default.transaction.findUnique({
                    where: { id: paymentId }
                });
                if (!transaction) {
                    return res.status(404).json({
                        success: false,
                        message: "Transaction not found"
                    });
                }
                // Get payment method
                const paymentMethod = paymentMethodId ? await prisma_1.default.paymentMethod.findUnique({
                    where: { id: paymentMethodId }
                }) : null;
                const binanceApiKey = apiKey || paymentMethod?.apiKey;
                const binanceSecretKey = secretKey || paymentMethod?.secretKey;
                if (!binanceApiKey || !binanceSecretKey) {
                    return res.status(400).json({
                        success: false,
                        message: "Binance API credentials are required"
                    });
                }
                const verificationResult = await this.binanceApiService.verifyTRC20Transaction(txHash, toAddress || paymentMethod?.address, parseFloat(amount.toString()), currency, binanceApiKey, binanceSecretKey);
                // Update transaction record
                const updatedTransaction = await prisma_1.default.transaction.update({
                    where: { id: transaction.id },
                    data: { status: verificationResult.success ? "COMPLETED" : "FAILED",
                        verificationMethod: "BINANCE_TRC20",
                        verificationData: JSON.stringify({
                            ...JSON.parse(transaction.verificationData || '{}'),
                            txHash,
                            fromAccount: verificationResult.fromAddress,
                            toAccount: verificationResult.toAddress || toAddress || paymentMethod?.address,
                            amount: verificationResult.amount || parseFloat(amount.toString()),
                            currency,
                            timestamp: verificationResult.timestamp || Date.now(),
                            status: verificationResult.success ? "success" : "failed"
                        })
                    }
                });
                // Create verification record
                const verification = await prisma_1.default.verification.create({
                    data: { transactionId: transaction.id,
                        merchantId: merchant.id,
                        method: "BINANCE_TRC20",
                        status: verificationResult.success ? verification_1.VerificationStatus.SUCCESS : verification_1.VerificationStatus.FAILED,
                        data: JSON.stringify({
                            txHash,
                            fromAddress: verificationResult.fromAddress,
                            toAddress: verificationResult.toAddress || toAddress || paymentMethod?.address,
                            amount: verificationResult.amount || parseFloat(amount.toString()),
                            currency,
                            timestamp: verificationResult.timestamp || Date.now(),
                            status: verificationResult.success ? "success" : "failed"
                        })
                    }
                });
                // Emit verification event
                verification_websocket_service_1.verificationEvents.emit('verification', {
                    transactionId: transaction.id,
                    merchantId: merchant.id,
                    status: verificationResult.success ? verification_1.VerificationStatus.SUCCESS : verification_1.VerificationStatus.FAILED,
                    method: "BINANCE_TRC20",
                    timestamp: new Date()
                });
                return res.status(200).json({
                    success: true,
                    message: verificationResult.success ? "Transaction verified successfully" : "Transaction verification failed",
                    data: { transaction: updatedTransaction,
                        verification,
                        verificationResult
                    }
                });
            }
            catch (error) {
                logger_1.logger.error(`Error in verifyBinanceTransaction: ${error instanceof Error ? error.message : 'Unknown error'}`);
                return res.status(500).json({
                    success: false,
                    message: "Internal server error",
                    error: error instanceof Error ? error.message : "Unknown error"
                });
            }
        };
        this.blockchainApiService = new blockchain_api_service_1.BlockchainApiService();
        this.binanceApiService = new binance_api_service_1.BinanceApiService();
    }
}
exports.BlockchainVerificationController = BlockchainVerificationController;
exports.default = new BlockchainVerificationController();
//# sourceMappingURL=blockchain-verification.controller.js.map