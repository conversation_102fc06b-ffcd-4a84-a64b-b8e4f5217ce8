"use strict";
// jscpd:ignore-file
/**
 * Redis client with fallback to in-memory store
 * This provides a unified interface for Redis operations with a fallback to an in-memory store
 *
 * @deprecated Use redisManager from './redis-manager.ts' instead
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.redisClient = void 0;
const logger_1 = require("./logger");
const redis_manager_1 = __importDefault(require("./redis-manager"));
class RedisClient {
    /**
     * Get a value from Redis or memory store
     * @param key Key
     * @returns Value or null if not found
     */
    async get(key) {
        return await redis_manager_1.default.get(key);
    }
    /**
     * Set a value in Redis or memory store
     * @param key Key
     * @param value Value
     * @param expiry Expiry in seconds
     */
    async set(key, value, expiry) {
        await redis_manager_1.default.set(key, value, expiry);
    }
    /**
     * Delete a key from Redis or memory store
     * @param key Key
     */
    async del(key) {
        await redis_manager_1.default.del(key);
    }
    /**
     * Get connection status
     * @returns True if connected to Redis or using memory store
     */
    getIsConnected() {
        return redis_manager_1.default.getIsConnected();
    }
    /**
     * Close Redis client
     */
    async close() {
        await redis_manager_1.default.close();
    }
}
// Export singleton instance
exports.redisClient = new RedisClient();
// Log deprecation warning
logger_1.logger.warn("RedisClient is deprecated. Use redisManager from './redis-manager.ts' instead.");
exports.default = exports.redisClient;
//# sourceMappingURL=redis-client.js.map