// jscpd:ignore-file
/**
 * Audit Middleware
 * 
 * Middleware for audit logging of admin actions.
 */

import { Request, Response, NextFunction } from "express";
import { AuditService } from "../services/audit.service";
import { PrismaClient } from "@prisma/client";
import { logger } from "../lib/logger";
import { Middleware } from '../types/express';
import { AuditService } from "../services/audit.service";
import { PrismaClient } from "@prisma/client";
import { logger } from "../lib/logger";
import { Middleware } from '../types/express';

// Extend the Express Request type to include user property
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role: string;
        merchantId?: string;
      };
    }
  }
}


const prisma: any = new PrismaClient();
const auditService: any = new AuditService(prisma);

/**
 * Middleware to log admin actions
 */
export const auditLog: any = (action: string, resource: string) => {
    return async (req: Request, res: Response, next: NextFunction) => {
    // Store the original end method
        const originalEnd: any = res.end;
        const originalJson: any = res.json;
    
        // Get resource ID from request parameters
        const resourceId: any = req.params.id;
    
        // Get old values for update/delete operations
        let oldValues: Record<string, any> | undefined;
    
        if (["update", "delete"].includes(action) && resourceId) {
            try {
                // Attempt to get the current state of the resource
                const resourceData: any = await prisma[resource].findUnique({
                    where: { id: resourceId }
                });
        
                if (resourceData) {
                    oldValues = resourceData;
                }
            } catch (error) {
                logger.error(`Error getting old values for ${resource}:${resourceId}`, error);
            }
        }
    
        // Override the end method to capture the response
        res.end = function (chunk?, encoding?, callback?) {
            // Restore the original end method
            res.end = originalEnd;
      
            // Log the action
            if (req.user) {
                auditService.logAction({
                    userId: req.user.id,
                    action,
                    resource,
                    resourceId,
                    ipAddress: req.ip,
                    userAgent: req.headers["user-agent"],
                    oldValues,
                    statusCode: res.statusCode
                }).catch((error)) => {
                    logger.error("Error logging audit action:", error);
                });
            }
      
            // Call the original end method
            return originalEnd.call(this, chunk, encoding, callback);
        };
    
        // Override the json method to capture the response data
        res.json = function (body?): Response {
            // Restore the original json method
            res.json = originalJson;
      
            // Log the action with the response data
            if (req.user) {
                auditService.logAction({
                    userId: req.user.id,
                    action,
                    resource,
                    resourceId,
                    ipAddress: req.ip,
                    userAgent: req.headers["user-agent"],
                    oldValues,
                    newValues: body,
                    statusCode: res.statusCode
                }).catch((error)) => {
                    logger.error("Error logging audit action:", error);
                });
            }
      
            // Call the original json method
            return originalJson.call(this, body);
        };
    
        next();
    };
};

/**
 * Middleware to log errors
 */
export const auditError: any = (action: string, resource: string) => {
    return (err, req: Request, res: Response, next: NextFunction) => {
        if (req.user) {
            auditService.logAction({
                userId: req.user.id,
                action,
                resource,
                resourceId: req.params.id,
                ipAddress: req.ip,
                userAgent: req.headers["user-agent"],
                statusCode: err.statusCode || 500,
                errorMessage: err.message
            }).catch((error)) => {
                logger.error("Error logging audit error:", error);
            });
        }
    
        next(err);
    };
};
