{"version": 3, "file": "FraudConfigValidator.js", "sourceRoot": "", "sources": ["../../../../../src/controllers/fraud-detection/validators/FraudConfigValidator.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;AAEH,6DAAgF;AAEhF,mDAAgD;AAEhD;;GAEG;AACH,MAAa,oBAAqB,SAAQ,6BAAa;IAErD;;OAEG;IACH,yBAAyB,CAAC,IAAS;QACjC,MAAM,MAAM,GAAsB,EAAE,CAAC;QAErC,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;YACrC,IAAI,OAAO,IAAI,CAAC,aAAa,KAAK,QAAQ,IAAI,IAAI,CAAC,aAAa,GAAG,CAAC,IAAI,IAAI,CAAC,aAAa,GAAG,GAAG,EAAE,CAAC;gBACjG,MAAM,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,eAAe,EAAE,OAAO,EAAE,mDAAmD,EAAE,KAAK,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;YACnI,CAAC;QACH,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,KAAK,SAAS,EAAE,CAAC;YACtC,IAAI,OAAO,IAAI,CAAC,cAAc,KAAK,QAAQ,IAAI,IAAI,CAAC,cAAc,GAAG,CAAC,IAAI,IAAI,CAAC,cAAc,GAAG,GAAG,EAAE,CAAC;gBACpG,MAAM,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,gBAAgB,EAAE,OAAO,EAAE,oDAAoD,EAAE,KAAK,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;YACtI,CAAC;QACH,CAAC;QAED,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,IAAI,IAAI,CAAC,cAAc,KAAK,SAAS,EAAE,CAAC;YAC1E,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;gBAC9C,MAAM,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,gBAAgB,EAAE,OAAO,EAAE,qDAAqD,EAAE,CAAC,CAAC;YAC3G,CAAC;QACH,CAAC;QAED,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,IAAI,OAAO,IAAI,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;YACxE,MAAM,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE,8BAA8B,EAAE,KAAK,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;QACtG,CAAC;QAED,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;QACvD,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC;QAC/D,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC;QAC7D,IAAI,CAAC,yBAAyB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAE7C,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtB,MAAM,IAAI,mBAAQ,CAAC;gBACjB,OAAO,EAAE,mBAAmB;gBAC5B,IAAI,EAAE,oBAAS,CAAC,UAAU;gBAC1B,IAAI,EAAE,oBAAS,CAAC,aAAa;gBAC7B,OAAO,EAAE,EAAE,MAAM,EAAE;aACpB,CAAC,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACK,qBAAqB,CAAC,aAAkB,EAAE,MAAyB;QACzE,IAAI,aAAa,KAAK,SAAS,EAAE,CAAC;YAChC,IAAI,OAAO,aAAa,KAAK,QAAQ,IAAI,aAAa,KAAK,IAAI,EAAE,CAAC;gBAChE,MAAM,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,eAAe,EAAE,OAAO,EAAE,kCAAkC,EAAE,KAAK,EAAE,aAAa,EAAE,CAAC,CAAC;YAC7G,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;oBACrD,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;wBACxD,MAAM,CAAC,IAAI,CAAC;4BACV,KAAK,EAAE,iBAAiB,GAAG,EAAE;4BAC7B,OAAO,EAAE,gDAAgD;4BACzD,KAAK;yBACN,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,yBAAyB,CAAC,iBAAsB,EAAE,MAAyB;QACjF,IAAI,iBAAiB,KAAK,SAAS,EAAE,CAAC;YACpC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,iBAAiB,CAAC,EAAE,CAAC;gBACtC,MAAM,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,mBAAmB,EAAE,OAAO,EAAE,sCAAsC,EAAE,KAAK,EAAE,iBAAiB,EAAE,CAAC,CAAC;YACzH,CAAC;iBAAM,CAAC;gBACN,iBAAiB,CAAC,OAAO,CAAC,CAAC,OAAY,EAAE,KAAa,EAAE,EAAE;oBACxD,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,EAAE,CAAC;wBACrE,MAAM,CAAC,IAAI,CAAC;4BACV,KAAK,EAAE,qBAAqB,KAAK,GAAG;4BACpC,OAAO,EAAE,6BAA6B;4BACtC,KAAK,EAAE,OAAO;yBACf,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,wBAAwB,CAAC,gBAAqB,EAAE,MAAyB;QAC/E,IAAI,gBAAgB,KAAK,SAAS,EAAE,CAAC;YACnC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE,CAAC;gBACrC,MAAM,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,kBAAkB,EAAE,OAAO,EAAE,sCAAsC,EAAE,KAAK,EAAE,gBAAgB,EAAE,CAAC,CAAC;YACvH,CAAC;iBAAM,CAAC;gBACN,gBAAgB,CAAC,OAAO,CAAC,CAAC,OAAY,EAAE,KAAa,EAAE,EAAE;oBACvD,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC;wBACjE,MAAM,CAAC,IAAI,CAAC;4BACV,KAAK,EAAE,oBAAoB,KAAK,GAAG;4BACnC,OAAO,EAAE,yBAAyB;4BAClC,KAAK,EAAE,OAAO;yBACf,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,yBAAyB,CAAC,IAAS,EAAE,MAAyB;QACpE,IAAI,IAAI,CAAC,oBAAoB,KAAK,SAAS,EAAE,CAAC;YAC5C,IAAI,OAAO,IAAI,CAAC,oBAAoB,KAAK,QAAQ,IAAI,IAAI,CAAC,oBAAoB,IAAI,CAAC,EAAE,CAAC;gBACpF,MAAM,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,sBAAsB,EAAE,OAAO,EAAE,kDAAkD,EAAE,KAAK,EAAE,IAAI,CAAC,oBAAoB,EAAE,CAAC,CAAC;YAChJ,CAAC;QACH,CAAC;QAED,IAAI,IAAI,CAAC,sBAAsB,KAAK,SAAS,EAAE,CAAC;YAC9C,IAAI,OAAO,IAAI,CAAC,sBAAsB,KAAK,QAAQ,IAAI,IAAI,CAAC,sBAAsB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,sBAAsB,CAAC,EAAE,CAAC;gBAC1I,MAAM,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,wBAAwB,EAAE,OAAO,EAAE,sDAAsD,EAAE,KAAK,EAAE,IAAI,CAAC,sBAAsB,EAAE,CAAC,CAAC;YACxJ,CAAC;QACH,CAAC;QAED,IAAI,IAAI,CAAC,qBAAqB,KAAK,SAAS,EAAE,CAAC;YAC7C,IAAI,OAAO,IAAI,CAAC,qBAAqB,KAAK,QAAQ,IAAI,IAAI,CAAC,qBAAqB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,qBAAqB,CAAC,EAAE,CAAC;gBACvI,MAAM,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,OAAO,EAAE,qDAAqD,EAAE,KAAK,EAAE,IAAI,CAAC,qBAAqB,EAAE,CAAC,CAAC;YACrJ,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,IAAS;QAClC,MAAM,MAAM,GAA6B,EAAE,CAAC;QAE5C,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS;YAAE,MAAM,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;QAChF,IAAI,IAAI,CAAC,cAAc,KAAK,SAAS;YAAE,MAAM,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;QACnF,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS;YAAE,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QACpE,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS;YAAE,MAAM,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;QAChF,IAAI,IAAI,CAAC,iBAAiB,KAAK,SAAS;YAAE,MAAM,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC;QAC5F,IAAI,IAAI,CAAC,gBAAgB,KAAK,SAAS;YAAE,MAAM,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC;QACzF,IAAI,IAAI,CAAC,oBAAoB,KAAK,SAAS;YAAE,MAAM,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC;QACrG,IAAI,IAAI,CAAC,sBAAsB,KAAK,SAAS;YAAE,MAAM,CAAC,sBAAsB,GAAG,IAAI,CAAC,sBAAsB,CAAC;QAC3G,IAAI,IAAI,CAAC,qBAAqB,KAAK,SAAS;YAAE,MAAM,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC;QAExG,OAAO,MAAM,CAAC;IAChB,CAAC;CACF;AAvJD,oDAuJC"}