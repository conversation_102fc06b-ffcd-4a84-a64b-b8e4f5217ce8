// jscpd:ignore-file

import { Router } from "express";
import { body } from "express-validator";
import authController from "../controllers/auth.controller";
import { authenticate, authorize } from "../middlewares/auth.middleware";
import { validate } from "../middlewares/validation.middleware";
import { authLimiter, passwordResetLimiter } from "../middlewares/rate-limit.middleware";
import { body } from "express-validator";
import { authenticate, authorize } from "../middlewares/auth.middleware";
import { validate } from "../middlewares/validation.middleware";
import { authLimiter, passwordResetLimiter } from "../middlewares/rate-limit.middleware";

const router: any =Router();

// Admin login route
router.post(
    "/login",
    authLimiter,
    validate([
        body("email").isEmail().withMessage("Valid email is required"),
        body("password").isString().withMessage("Password is required")
    ]),
    authController.login
);

// Logout route
router.post(
    "/logout",
    authenticate,
    authController.logout
);

// Get current user info
router.get(
    "/me",
    authenticate,
    authController.getCurrentUser
);

// Get user permissions
router.get(
    "/permissions",
    authenticate,
    authController.getUserPermissions
);

// Two-factor authentication routes
router.get(
    "/2fa/status",
    authenticate,
    authController.getTwoFactorStatus
);

router.post(
    "/2fa/setup",
    authenticate,
    authController.setupTwoFactor
);

router.post(
    "/2fa/verify",
    authenticate,
    validate([
        body("code").isString().isLength({ min: 6, max: 6 }),
        body("secret").isString()
    ]),
    authController.verifyAndEnableTwoFactor
);

router.post(
    "/2fa/disable",
    authenticate,
    validate([
        body("code").isString().isLength({ min: 6, max: 6 })
    ]),
    authController.disableTwoFactor
);

router.post(
    "/2fa/backup-codes",
    authenticate,
    validate([
        body("code").isString().isLength({ min: 6, max: 6 })
    ]),
    authController.generateBackupCodes
);

router.post(
    "/2fa/recover",
    authenticate,
    validate([
        body("backupCode").isString()
    ]),
    authController.recoverWithBackupCode
);

// Merchant registration route
router.post(
    "/merchant/register",
    validate([
        body("email").isEmail().withMessage("Valid email is required"),
        body("password").isString().isLength({ min: 8 }).withMessage("Password must be at least 8 characters"),
        body("name").isString().withMessage("Name is required"),
        body("contactPhone").isString().withMessage("Contact phone is required"),
        body("merchantLocation").isString().withMessage("Merchant location is required"),
        body("country").isString().withMessage("Country is required"),
        body("governorate").isString().withMessage("Governorate/Province is required")
    ]),
    authController.registerMerchant
);

export default router;