// jscpd:ignore-file
/**
 * Payments Module
 * 
 * This module handles payment methods and payment processing.
 */

import { Router } from 'express';
import { BaseModule } from '../../factories/ModuleFactory';
import { logger } from '../../utils/logger';
import { BaseModule } from '../../factories/ModuleFactory';
import { logger } from '../../utils/logger';

/**
 * Payments Module
 */
class PaymentsModule extends BaseModule {
  /**
   * Constructor
   */
  constructor() {
    super('PaymentsModule');
  }
  
  /**
   * Initialize the module
   */
  initialize(): any {
    logger.info('Initializing PaymentsModule');
    
    // Get controllers
    const paymentMethodController: unknown =this.controllerFactory.getController('paymentMethod');
    const paymentController: unknown =this.controllerFactory.getController('payment');
    
    // Set up payment method routes
    this.router.get('/methods', paymentMethodController.getAll);
    this.router.get('/methods/:id', paymentMethodController.getById);
    this.router.post('/methods', paymentMethodController.create);
    this.router.put('/methods/:id', paymentMethodController.update);
    this.router.delete('/methods/:id', paymentMethodController.delete);
    this.router.put('/methods/:id/default', paymentMethodController.setAsDefault);
    
    // Set up payment routes
    this.router.post('/process', paymentController.processPayment);
    this.router.post('/verify', paymentController.verifyPayment);
    this.router.post('/refund', paymentController.refundPayment);
    this.router.get('/status/:id', paymentController.getPaymentStatus);
    
    logger.info('PaymentsModule initialized');
  }
}

// Export the module
export default new PaymentsModule();
