{"version": 3, "file": "api-analytics.controller.js", "sourceRoot": "", "sources": ["../../../src/controllers/api-analytics.controller.ts"], "names": [], "mappings": ";;;AAEA,8DAA2D;AAC3D,sFAAmF;AAMnF;;;GAGG;AACH,MAAa,sBAAuB,SAAQ,+BAAc;IAGxD;;OAEG;IACH;QACE,KAAK,EAAE,CAAC;QAIV;;WAEG;QACH,iBAAY,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;YACtE,sBAAsB;YACtB,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;YAElD,mCAAmC;YACnC,IAAI,QAAQ,KAAK,OAAO,EAAE,CAAC;gBACzB,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,GAAG,EAAE,sCAAsC,CAAC,CAAC;YAC1E,CAAC;YAED,yBAAyB;YACzB,MAAM,SAAS,GAAO,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,SAAmB,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;YAChG,MAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,OAAiB,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;YACtF,MAAM,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC,IAAc,CAAC;YACtC,MAAM,MAAM,GAAO,GAAG,CAAC,KAAK,CAAC,MAAgB,CAAC;YAC9C,MAAM,UAAU,GAAO,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,UAAoB,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;YACnG,MAAM,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,EAAE,CAAA,CAAC,+CAA+C;YAC3E,MAAM,QAAQ,GAAO,GAAG,CAAC,KAAK,CAAC,QAAkB,CAAC;YAClD,MAAM,UAAU,GAAO,GAAG,CAAC,KAAK,CAAC,UAAoB,CAAC;YACtD,MAAM,KAAK,GAAO,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,KAAe,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;YAC9E,MAAM,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,MAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAE3E,gBAAgB;YAChB,MAAM,SAAS,GAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC;gBAChE,SAAS;gBACT,OAAO;gBACP,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,MAAM;gBACN,QAAQ;gBACR,UAAU;gBACV,KAAK;gBACL,MAAM;aACP,CAAC,CAAC;YAEH,0BAA0B;YAC1B,OAAO,IAAI,CAAC,oBAAoB,CAAC,GAAG,EAAE,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QACxF,CAAC,CAAC,CAAC;QAEH;;WAEG;QACH,wBAAmB,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;YAC7E,sBAAsB;YACtB,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;YAElD,mCAAmC;YACnC,IAAI,QAAQ,KAAK,OAAO,EAAE,CAAC;gBACzB,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,GAAG,EAAE,sCAAsC,CAAC,CAAC;YAC1E,CAAC;YAED,yBAAyB;YACzB,MAAM,SAAS,GAAO,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,SAAmB,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;YAChG,MAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,OAAiB,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;YACtF,MAAM,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC,IAAc,CAAC;YACtC,MAAM,MAAM,GAAO,GAAG,CAAC,KAAK,CAAC,MAAgB,CAAC;YAC9C,MAAM,UAAU,GAAO,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,UAAoB,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;YACnG,MAAM,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,EAAE,CAAA,CAAC,+CAA+C;YAC3E,MAAM,QAAQ,GAAO,GAAG,CAAC,KAAK,CAAC,QAAkB,CAAC;YAClD,MAAM,UAAU,GAAO,GAAG,CAAC,KAAK,CAAC,UAAoB,CAAC;YAEtD,wBAAwB;YACxB,MAAM,OAAO,GAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,mBAAmB,CAAC;gBACrE,SAAS;gBACT,OAAO;gBACP,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,MAAM;gBACN,QAAQ;gBACR,UAAU;aACX,CAAC,CAAC;YAEH,wBAAwB;YACxB,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH;;WAEG;QACH,0BAAqB,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;YAC/E,sBAAsB;YACtB,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;YAElD,mCAAmC;YACnC,IAAI,QAAQ,KAAK,OAAO,EAAE,CAAC;gBACzB,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,GAAG,EAAE,sCAAsC,CAAC,CAAC;YAC1E,CAAC;YAED,0BAA0B;YAC1B,MAAM,EAAE,OAAO,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;YAE/B,yBAAyB;YACzB,MAAM,SAAS,GAAO,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,SAAmB,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;YAChG,MAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,OAAiB,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;YACtF,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,KAAe,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;YAC1E,MAAM,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,MAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAE3E,gBAAgB;YAChB,MAAM,SAAS,GAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC;gBAChE,SAAS;gBACT,OAAO;gBACP,UAAU,EAAE,OAAO;gBACnB,KAAK;gBACL,MAAM;aACP,CAAC,CAAC;YAEH,0BAA0B;YAC1B,OAAO,IAAI,CAAC,oBAAoB,CAAC,GAAG,EAAE,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QACxF,CAAC,CAAC,CAAC;QAEH;;WAEG;QACH,iCAA4B,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;YACtF,sBAAsB;YACtB,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;YAElD,mCAAmC;YACnC,IAAI,QAAQ,KAAK,OAAO,EAAE,CAAC;gBACzB,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,GAAG,EAAE,sCAAsC,CAAC,CAAC;YAC1E,CAAC;YAED,0BAA0B;YAC1B,MAAM,EAAE,OAAO,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;YAE/B,yBAAyB;YACzB,MAAM,SAAS,GAAO,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,SAAmB,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;YAChG,MAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,OAAiB,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;YAEtF,wBAAwB;YACxB,MAAM,OAAO,GAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,mBAAmB,CAAC;gBACrE,SAAS;gBACT,OAAO;gBACP,UAAU,EAAE,OAAO;aACpB,CAAC,CAAC;YAEH,wBAAwB;YACxB,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAjJD,IAAI,CAAC,mBAAmB,GAAG,yCAAmB,CAAC,WAAW,EAAE,CAAC;IAC/D,CAAC;CAiJF;AA1JD,wDA0JC;AAED,kBAAe,sBAAsB,CAAC"}