{"version": 3, "file": "AuthorizationService.js", "sourceRoot": "", "sources": ["../../../../../src/controllers/alert-aggregation/services/AuthorizationService.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;AAEH,6DAAgF;AAGhF;;GAEG;AACH,MAAa,oBAAoB;IAAjC;QACmB,eAAU,GAAG,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;QACtC,iBAAY,GAAG,CAAC,SAAS,EAAE,OAAO,EAAE,aAAa,CAAC,CAAC;QACnD,cAAS,GAAG,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,aAAa,CAAC,CAAC;IA+R3E,CAAC;IA7RC;;OAEG;IACH,KAAK,CAAC,eAAe,CAAC,OAA6B;QACjD,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;QAE3C,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,wBAAwB;aACjC,CAAC;QACJ,CAAC;QAED,+BAA+B;QAC/B,MAAM,cAAc,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;QAC7E,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;YAC5B,OAAO,cAAc,CAAC;QACxB,CAAC;QAED,sCAAsC;QACtC,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC;QACvE,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;YAChC,OAAO,kBAAkB,CAAC;QAC5B,CAAC;QAED,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;IAC3B,CAAC;IAED;;OAEG;IACK,mBAAmB,CACzB,QAAgB,EAChB,QAAgB,EAChB,MAAc;QAEd,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,mBAAmB;gBACtB,OAAO,IAAI,CAAC,8BAA8B,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAC/D,KAAK,mBAAmB;gBACtB,OAAO,IAAI,CAAC,8BAA8B,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAC/D;gBACE,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,MAAM,EAAE,qBAAqB,QAAQ,EAAE;iBACxC,CAAC;QACN,CAAC;IACH,CAAC;IAED;;OAEG;IACK,8BAA8B,CAAC,QAAgB,EAAE,MAAc;QACrE,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,MAAM;gBACT,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACtC,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;gBAC3B,CAAC;gBACD,MAAM;YACR,KAAK,QAAQ,CAAC;YACd,KAAK,QAAQ,CAAC;YACd,KAAK,QAAQ;gBACX,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACvC,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;gBAC3B,CAAC;gBACD,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,MAAM,EAAE,0CAA0C;oBAClD,YAAY,EAAE,OAAO;iBACtB,CAAC;YACJ;gBACE,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,MAAM,EAAE,mBAAmB,MAAM,EAAE;iBACpC,CAAC;QACN,CAAC;QAED,OAAO;YACL,OAAO,EAAE,KAAK;YACd,MAAM,EAAE,0BAA0B;YAClC,YAAY,EAAE,MAAM;SACrB,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,8BAA8B,CAAC,QAAgB,EAAE,MAAc;QACrE,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,MAAM;gBACT,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACzC,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;gBAC3B,CAAC;gBACD,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,MAAM,EAAE,6CAA6C;oBACrD,YAAY,EAAE,SAAS;iBACxB,CAAC;YACJ,KAAK,QAAQ,CAAC;YACd,KAAK,QAAQ,CAAC;YACd,KAAK,QAAQ;gBACX,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACvC,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;gBAC3B,CAAC;gBACD,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,MAAM,EAAE,0CAA0C;oBAClD,YAAY,EAAE,OAAO;iBACtB,CAAC;YACJ;gBACE,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,MAAM,EAAE,mBAAmB,MAAM,EAAE;iBACpC,CAAC;QACN,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,uBAAuB,CAAC,OAA6B;QACjE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC;QAEnC,uDAAuD;QACvD,4EAA4E;QAE5E,gEAAgE;QAChE,IAAI,IAAI,CAAC,UAAU,IAAI,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YACrD,uDAAuD;YACvD,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;QAC3B,CAAC;QAED,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,QAAiB;QAC5B,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YACrD,MAAM,IAAI,mBAAQ,CAAC;gBACjB,OAAO,EAAE,qBAAqB;gBAC9B,IAAI,EAAE,oBAAS,CAAC,cAAc;gBAC9B,IAAI,EAAE,oBAAS,CAAC,mBAAmB;aACpC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,QAAiB;QAC9B,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YACvD,MAAM,IAAI,mBAAQ,CAAC;gBACjB,OAAO,EAAE,uBAAuB;gBAChC,IAAI,EAAE,oBAAS,CAAC,cAAc;gBAC9B,IAAI,EAAE,oBAAS,CAAC,mBAAmB;aACpC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,QAAiB;QACpC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YACpD,MAAM,IAAI,mBAAQ,CAAC;gBACjB,OAAO,EAAE,yBAAyB;gBAClC,IAAI,EAAE,oBAAS,CAAC,cAAc;gBAC9B,IAAI,EAAE,oBAAS,CAAC,mBAAmB;aACpC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,QAAgB,EAAE,YAAoB;QAC5C,MAAM,aAAa,GAA2B;YAC5C,IAAI,EAAE,CAAC;YACP,OAAO,EAAE,CAAC;YACV,KAAK,EAAE,CAAC;YACR,WAAW,EAAE,CAAC;SACf,CAAC;QAEF,MAAM,SAAS,GAAG,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC/C,MAAM,aAAa,GAAG,aAAa,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QAEvD,OAAO,SAAS,IAAI,aAAa,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,QAAgB,EAAE,QAAgB;QACnD,MAAM,WAAW,GAAa,EAAE,CAAC;QAEjC,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,mBAAmB;gBACtB,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACtC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC3B,CAAC;gBACD,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACvC,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;gBACjD,CAAC;gBACD,MAAM;YACR,KAAK,mBAAmB;gBACtB,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACzC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC3B,CAAC;gBACD,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACvC,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;gBACjD,CAAC;gBACD,MAAM;QACV,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;OAEG;IACH,4BAA4B,CAAC,OAA6B;QACxD,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;YAClB,MAAM,IAAI,mBAAQ,CAAC;gBACjB,OAAO,EAAE,0BAA0B;gBACnC,IAAI,EAAE,oBAAS,CAAC,cAAc;gBAC9B,IAAI,EAAE,oBAAS,CAAC,mBAAmB;aACpC,CAAC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;YACtB,MAAM,IAAI,mBAAQ,CAAC;gBACjB,OAAO,EAAE,sBAAsB;gBAC/B,IAAI,EAAE,oBAAS,CAAC,UAAU;gBAC1B,IAAI,EAAE,oBAAS,CAAC,sBAAsB;aACvC,CAAC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YACpB,MAAM,IAAI,mBAAQ,CAAC;gBACjB,OAAO,EAAE,oBAAoB;gBAC7B,IAAI,EAAE,oBAAS,CAAC,UAAU;gBAC1B,IAAI,EAAE,oBAAS,CAAC,sBAAsB;aACvC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACH,0BAA0B,CACxB,IAAS,EACT,QAAgB,EAChB,MAAc,EACd,UAAmB;QAEnB,OAAO;YACL,IAAI,EAAE;gBACJ,EAAE,EAAE,IAAI,EAAE,EAAE;gBACZ,IAAI,EAAE,IAAI,EAAE,IAAI;gBAChB,UAAU,EAAE,IAAI,EAAE,UAAU;aAC7B;YACD,QAAQ;YACR,MAAM;YACN,UAAU;SACX,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,wBAAwB,CAAC,MAAwB;QAC/C,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,IAAI,eAAe,CAAC;QAEjD,MAAM,IAAI,mBAAQ,CAAC;YACjB,OAAO;YACP,IAAI,EAAE,oBAAS,CAAC,cAAc;YAC9B,IAAI,EAAE,oBAAS,CAAC,mBAAmB;YACnC,OAAO,EAAE;gBACP,YAAY,EAAE,MAAM,CAAC,YAAY;gBACjC,mBAAmB,EAAE,MAAM,CAAC,mBAAmB;aAChD;SACF,CAAC,CAAC;IACL,CAAC;CACF;AAlSD,oDAkSC"}