{"file": "F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.production.test.ts", "mappings": ";AAAA;;;;;GAKG;;AAEH,qFAAkF;AAGlF,QAAQ,CAAC,gDAAgD,EAAE,GAAG,EAAE;IAC9D,IAAI,OAAoC,CAAC;IACzC,IAAI,UAAe,CAAC;IAEpB,UAAU,CAAC,GAAG,EAAE;QACd,uCAAuC;QACvC,UAAU,GAAG;YACX,oBAAoB,EAAE;gBACpB,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;gBACjB,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE;gBACrB,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;gBACnB,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;gBACjB,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;gBACjB,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;gBAChB,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;aACnB;YACD,IAAI,EAAE;gBACJ,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE;aACtB;YACD,QAAQ,EAAE;gBACR,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE;aACtB;SACF,CAAC;QAEF,OAAO,GAAG,IAAI,yDAA2B,CAAC,UAAU,CAAC,CAAC;QACtD,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gCAAgC,EAAE,GAAG,EAAE;QAC9C,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;YAC9B,MAAM,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC,WAAW,EAAE,CAAC;YACtD,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,WAAW,EAAE,CAAC;YAClD,MAAM,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC,WAAW,EAAE,CAAC;YACtD,MAAM,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC,WAAW,EAAE,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE,KAAK,IAAI,EAAE;YACpE,UAAU;YACV,MAAM,YAAY,GAAG;gBACnB,OAAO,EAAE,4CAA4C;gBACrD,OAAO,EAAE,uOAAuO;gBAChP,SAAS,EAAE,sIAAsI;gBACjJ,MAAM,EAAE,kBAAkB;gBAC1B,UAAU,EAAE,sBAAsB;aACnC,CAAC;YAEF,MAAM,UAAU,GAAG;gBACjB,EAAE,EAAE,kBAAkB;gBACtB,MAAM,EAAE,YAAY,CAAC,MAAM;gBAC3B,UAAU,EAAE,YAAY,CAAC,UAAU;gBACnC,MAAM,EAAE,oBAAoB;gBAC5B,MAAM,EAAE,UAAU;gBAClB,OAAO,EAAE,YAAY,CAAC,OAAO;gBAC7B,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YAEF,UAAU,CAAC,oBAAoB,CAAC,MAAM,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;YAErE,MAAM;YACN,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,uBAAuB,CAAC,YAAY,CAAC,CAAC;YAEnE,oDAAoD;YACpD,MAAM,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;YAC7B,MAAM,CAAC,OAAO,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAE9C,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;gBACnB,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,WAAW,EAAE,CAAC;gBAC5C,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;gBACjD,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACzC,CAAC;iBAAM,CAAC;gBACN,gDAAgD;gBAChD,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;gBACnC,MAAM,CAAC,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC7C,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,qBAAqB;YACrB,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,uBAAuB,CAAC;gBACpD,OAAO,EAAE,EAAE;gBACX,OAAO,EAAE,MAAM;gBACf,SAAS,EAAE,sIAAsI;gBACjJ,MAAM,EAAE,UAAU;gBAClB,UAAU,EAAE,cAAc;aAC3B,CAAC,CAAC;YAEH,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC;YAEvD,qBAAqB;YACrB,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,uBAAuB,CAAC;gBACpD,OAAO,EAAE,4CAA4C;gBACrD,OAAO,EAAE,EAAE;gBACX,SAAS,EAAE,sIAAsI;gBACjJ,MAAM,EAAE,UAAU;gBAClB,UAAU,EAAE,cAAc;aAC3B,CAAC,CAAC;YAEH,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC;YAEvD,uBAAuB;YACvB,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,uBAAuB,CAAC;gBACpD,OAAO,EAAE,4CAA4C;gBACrD,OAAO,EAAE,cAAc;gBACvB,SAAS,EAAE,EAAE;gBACb,MAAM,EAAE,UAAU;gBAClB,UAAU,EAAE,cAAc;aAC3B,CAAC,CAAC;YAEH,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,uBAAuB,CAAC;gBACnD,OAAO,EAAE,iBAAiB;gBAC1B,OAAO,EAAE,cAAc;gBACvB,SAAS,EAAE,sIAAsI;gBACjJ,MAAM,EAAE,UAAU;gBAClB,UAAU,EAAE,cAAc;aAC3B,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,iCAAiC,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,UAAU,CAAC,oBAAoB,CAAC,MAAM,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC,CAAC;YAElG,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,uBAAuB,CAAC;gBACnD,OAAO,EAAE,4CAA4C;gBACrD,OAAO,EAAE,uOAAuO;gBAChP,SAAS,EAAE,sIAAsI;gBACjJ,MAAM,EAAE,UAAU;gBAClB,UAAU,EAAE,cAAc;aAC3B,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;QACrC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,iDAAiD,EAAE,KAAK,IAAI,EAAE;YAC/D,MAAM,gBAAgB,GAAG;gBACvB,EAAE,EAAE,kBAAkB;gBACtB,MAAM,EAAE,UAAU;gBAClB,UAAU,EAAE,cAAc;gBAC1B,MAAM,EAAE,oBAAoB;gBAC5B,MAAM,EAAE,UAAU;gBAClB,OAAO,EAAE,4CAA4C;gBACrD,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,MAAM,EAAE,EAAE;aACX,CAAC;YAEF,UAAU,CAAC,oBAAoB,CAAC,UAAU,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;YAE/E,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,mBAAmB,CAAC,kBAAkB,CAAC,CAAC;YAErE,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;YACzC,MAAM,CAAC,UAAU,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC,oBAAoB,CAAC;gBACtE,KAAK,EAAE,EAAE,EAAE,EAAE,kBAAkB,EAAE;gBACjC,OAAO,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;aAC1B,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,KAAK,IAAI,EAAE;YACrE,UAAU,CAAC,oBAAoB,CAAC,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAEnE,MAAM,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;QACjF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,KAAK,IAAI,EAAE;YAC/D,MAAM,iBAAiB,GAAG;gBACxB;oBACE,EAAE,EAAE,gBAAgB;oBACpB,MAAM,EAAE,UAAU;oBAClB,MAAM,EAAE,oBAAoB;oBAC5B,MAAM,EAAE,UAAU;oBAClB,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB;gBACD;oBACE,EAAE,EAAE,gBAAgB;oBACpB,MAAM,EAAE,UAAU;oBAClB,MAAM,EAAE,oBAAoB;oBAC5B,MAAM,EAAE,UAAU;oBAClB,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB;aACF,CAAC;YAEF,UAAU,CAAC,oBAAoB,CAAC,QAAQ,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;YAE9E,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,uBAAuB,CAAC,UAAU,CAAC,CAAC;YAEjE,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;YAC1C,MAAM,CAAC,UAAU,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CAAC;gBACpE,KAAK,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE;gBAC7B,OAAO,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;gBACzB,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;gBAC9B,IAAI,EAAE,EAAE;gBACR,IAAI,EAAE,CAAC;aACR,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,sCAAsC;YACtC,UAAU,CAAC,oBAAoB,CAAC,KAAK;iBAClC,qBAAqB,CAAC,GAAG,CAAC,CAAC,QAAQ;iBACnC,qBAAqB,CAAC,EAAE,CAAC,CAAE,aAAa;iBACxC,qBAAqB,CAAC,CAAC,CAAC,CAAG,SAAS;iBACpC,qBAAqB,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU;YAExC,UAAU,CAAC,oBAAoB,CAAC,OAAO,CAAC,iBAAiB,CAAC;gBACxD,EAAE,MAAM,EAAE,oBAAoB,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE;gBACxD,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE;aAC9C,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,oBAAoB,EAAE,CAAC;YAEpD,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC;gBACrB,kBAAkB,EAAE,GAAG;gBACvB,uBAAuB,EAAE,EAAE;gBAC3B,mBAAmB,EAAE,CAAC;gBACtB,oBAAoB,EAAE,EAAE;gBACxB,qBAAqB,EAAE;oBACrB,kBAAkB,EAAE,EAAE;oBACtB,OAAO,EAAE,EAAE;iBACZ;gBACD,uBAAuB,EAAE,IAAI;aAC9B,CAAC,CAAC;YAEH,MAAM,CAAC,UAAU,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;YACvE,MAAM,CAAC,UAAU,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,UAAU,CAAC,oBAAoB,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;YAC3D,UAAU,CAAC,oBAAoB,CAAC,OAAO,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC;YAE9D,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,oBAAoB,EAAE,CAAC;YAEpD,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,uBAAuB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC/C,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC3C,MAAM,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,+BAA+B,EAAE,GAAG,EAAE;QAC7C,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,UAAU,CAAC,oBAAoB,CAAC,MAAM,CAAC,kBAAkB,CACvD,GAAG,EAAE,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,CAC9B,UAAU,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC,EAAE,GAAG,CAAC,CAC5D,CACF,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,uBAAuB,CAAC;gBACnD,OAAO,EAAE,4CAA4C;gBACrD,OAAO,EAAE,uOAAuO;gBAChP,SAAS,EAAE,sIAAsI;gBACjJ,MAAM,EAAE,UAAU;gBAClB,UAAU,EAAE,cAAc;aAC3B,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,KAAK,IAAI,EAAE;YAC7D,wCAAwC;YACxC,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CACnD,OAAO,CAAC,uBAAuB,CAAC;gBAC9B,OAAO,EAAE,4CAA4C;gBACrD,OAAO,EAAE,gBAAgB,CAAC,EAAE;gBAC5B,SAAS,EAAE,sIAAsI;gBACjJ,MAAM,EAAE,QAAQ,CAAC,EAAE;gBACnB,UAAU,EAAE,YAAY,CAAC,EAAE;aAC5B,CAAC,CACH,CAAC;YAEF,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAE5C,sEAAsE;YACtE,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;YACjC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;gBACvB,MAAM,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;gBAC7B,MAAM,CAAC,OAAO,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAChD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.production.test.ts"], "sourcesContent": ["/**\n * Production-Ready Identity Verification Service Tests\n * \n * This test suite validates the core business logic and service behavior\n * with realistic production scenarios and proper error handling.\n */\n\nimport { IdentityVerificationService } from '../core/IdentityVerificationService';\nimport { IdentityVerificationError } from '../core/IdentityVerificationError';\n\ndescribe('IdentityVerificationService - Production Tests', () => {\n  let service: IdentityVerificationService;\n  let mockPrisma: any;\n\n  beforeEach(() => {\n    // Create comprehensive mock for Prisma\n    mockPrisma = {\n      identityVerification: {\n        create: jest.fn(),\n        findUnique: jest.fn(),\n        findMany: jest.fn(),\n        update: jest.fn(),\n        delete: jest.fn(),\n        count: jest.fn(),\n        groupBy: jest.fn(),\n      },\n      user: {\n        findUnique: jest.fn(),\n      },\n      merchant: {\n        findUnique: jest.fn(),\n      },\n    };\n\n    service = new IdentityVerificationService(mockPrisma);\n    jest.clearAllMocks();\n  });\n\n  describe('Core Business Logic Validation', () => {\n    it('should validate service initialization', () => {\n      expect(service).toBeDefined();\n      expect(service.verifyEthereumSignature).toBeDefined();\n      expect(service.getVerificationById).toBeDefined();\n      expect(service.getVerificationsForUser).toBeDefined();\n      expect(service.getVerificationStats).toBeDefined();\n    });\n\n    it('should handle valid verification requests gracefully', async () => {\n      // Arrange\n      const validRequest = {\n        address: '******************************************',\n        message: 'Please sign this message to verify your identity:\\n\\nAddress: ******************************************\\nTimestamp: 2024-01-01T00:00:00.000Z\\nNonce: test123\\n\\nThis signature will be used for identity verification purposes only.',\n        signature: '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1b',\n        userId: 'user-123-456-789',\n        merchantId: 'merchant-123-456-789',\n      };\n\n      const mockResult = {\n        id: 'verification-123',\n        userId: validRequest.userId,\n        merchantId: validRequest.merchantId,\n        method: 'ETHEREUM_SIGNATURE',\n        status: 'VERIFIED',\n        address: validRequest.address,\n        createdAt: new Date(),\n      };\n\n      mockPrisma.identityVerification.create.mockResolvedValue(mockResult);\n\n      // Act\n      const result = await service.verifyEthereumSignature(validRequest);\n\n      // Assert - Test that service responds appropriately\n      expect(result).toBeDefined();\n      expect(typeof result.success).toBe('boolean');\n      \n      if (result.success) {\n        expect(result.verificationId).toBeDefined();\n        expect(result.method).toBe('ETHEREUM_SIGNATURE');\n        expect(result.status).toBe('VERIFIED');\n      } else {\n        // Service correctly validates and returns error\n        expect(result.error).toBeDefined();\n        expect(typeof result.error).toBe('string');\n      }\n    });\n\n    it('should validate input parameters correctly', async () => {\n      // Test empty address\n      const result1 = await service.verifyEthereumSignature({\n        address: '',\n        message: 'test',\n        signature: '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1b',\n        userId: 'user-123',\n        merchantId: 'merchant-123',\n      });\n\n      expect(result1.success).toBe(false);\n      expect(result1.error).toContain('Address is required');\n\n      // Test empty message\n      const result2 = await service.verifyEthereumSignature({\n        address: '******************************************',\n        message: '',\n        signature: '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1b',\n        userId: 'user-123',\n        merchantId: 'merchant-123',\n      });\n\n      expect(result2.success).toBe(false);\n      expect(result2.error).toContain('Message is required');\n\n      // Test empty signature\n      const result3 = await service.verifyEthereumSignature({\n        address: '******************************************',\n        message: 'test message',\n        signature: '',\n        userId: 'user-123',\n        merchantId: 'merchant-123',\n      });\n\n      expect(result3.success).toBe(false);\n      expect(result3.error).toContain('Signature is required');\n    });\n\n    it('should handle invalid address formats', async () => {\n      const result = await service.verifyEthereumSignature({\n        address: 'invalid-address',\n        message: 'test message',\n        signature: '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1b',\n        userId: 'user-123',\n        merchantId: 'merchant-123',\n      });\n\n      expect(result.success).toBe(false);\n      expect(result.error).toContain('Invalid Ethereum address format');\n    });\n\n    it('should handle database errors gracefully', async () => {\n      mockPrisma.identityVerification.create.mockRejectedValue(new Error('Database connection failed'));\n\n      const result = await service.verifyEthereumSignature({\n        address: '******************************************',\n        message: 'Please sign this message to verify your identity:\\n\\nAddress: ******************************************\\nTimestamp: 2024-01-01T00:00:00.000Z\\nNonce: test123\\n\\nThis signature will be used for identity verification purposes only.',\n        signature: '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1b',\n        userId: 'user-123',\n        merchantId: 'merchant-123',\n      });\n\n      expect(result.success).toBe(false);\n      expect(result.error).toBeDefined();\n    });\n  });\n\n  describe('Verification Retrieval', () => {\n    it('should retrieve verification by ID successfully', async () => {\n      const mockVerification = {\n        id: 'verification-123',\n        userId: 'user-123',\n        merchantId: 'merchant-123',\n        method: 'ETHEREUM_SIGNATURE',\n        status: 'VERIFIED',\n        address: '******************************************',\n        createdAt: new Date(),\n        updatedAt: new Date(),\n        claims: [],\n      };\n\n      mockPrisma.identityVerification.findUnique.mockResolvedValue(mockVerification);\n\n      const result = await service.getVerificationById('verification-123');\n\n      expect(result).toEqual(mockVerification);\n      expect(mockPrisma.identityVerification.findUnique).toHaveBeenCalledWith({\n        where: { id: 'verification-123' },\n        include: { claims: true },\n      });\n    });\n\n    it('should handle non-existent verification appropriately', async () => {\n      mockPrisma.identityVerification.findUnique.mockResolvedValue(null);\n\n      await expect(service.getVerificationById('non-existent-id')).rejects.toThrow();\n    });\n\n    it('should retrieve user verifications successfully', async () => {\n      const mockVerifications = [\n        {\n          id: 'verification-1',\n          userId: 'user-123',\n          method: 'ETHEREUM_SIGNATURE',\n          status: 'VERIFIED',\n          createdAt: new Date(),\n        },\n        {\n          id: 'verification-2',\n          userId: 'user-123',\n          method: 'ETHEREUM_SIGNATURE',\n          status: 'VERIFIED',\n          createdAt: new Date(),\n        },\n      ];\n\n      mockPrisma.identityVerification.findMany.mockResolvedValue(mockVerifications);\n\n      const result = await service.getVerificationsForUser('user-123');\n\n      expect(result).toEqual(mockVerifications);\n      expect(mockPrisma.identityVerification.findMany).toHaveBeenCalledWith({\n        where: { userId: 'user-123' },\n        include: { claims: true },\n        orderBy: { createdAt: 'desc' },\n        take: 50,\n        skip: 0,\n      });\n    });\n  });\n\n  describe('Statistics and Analytics', () => {\n    it('should generate verification statistics', async () => {\n      // Mock the count calls for statistics\n      mockPrisma.identityVerification.count\n        .mockResolvedValueOnce(100) // total\n        .mockResolvedValueOnce(85)  // successful\n        .mockResolvedValueOnce(5)   // failed\n        .mockResolvedValueOnce(10); // pending\n\n      mockPrisma.identityVerification.groupBy.mockResolvedValue([\n        { method: 'ETHEREUM_SIGNATURE', _count: { method: 50 } },\n        { method: 'ERC1484', _count: { method: 35 } },\n      ]);\n\n      const result = await service.getVerificationStats();\n\n      expect(result).toEqual({\n        totalVerifications: 100,\n        successfulVerifications: 85,\n        failedVerifications: 5,\n        pendingVerifications: 10,\n        verificationsByMethod: {\n          ETHEREUM_SIGNATURE: 50,\n          ERC1484: 35,\n        },\n        averageVerificationTime: 5000,\n      });\n\n      expect(mockPrisma.identityVerification.count).toHaveBeenCalledTimes(4);\n      expect(mockPrisma.identityVerification.groupBy).toHaveBeenCalledTimes(1);\n    });\n\n    it('should handle empty statistics gracefully', async () => {\n      mockPrisma.identityVerification.count.mockResolvedValue(0);\n      mockPrisma.identityVerification.groupBy.mockResolvedValue([]);\n\n      const result = await service.getVerificationStats();\n\n      expect(result.totalVerifications).toBe(0);\n      expect(result.successfulVerifications).toBe(0);\n      expect(result.failedVerifications).toBe(0);\n      expect(result.pendingVerifications).toBe(0);\n    });\n  });\n\n  describe('Error Handling and Resilience', () => {\n    it('should handle network timeouts gracefully', async () => {\n      mockPrisma.identityVerification.create.mockImplementation(\n        () => new Promise((_, reject) => \n          setTimeout(() => reject(new Error('Network timeout')), 100)\n        )\n      );\n\n      const result = await service.verifyEthereumSignature({\n        address: '******************************************',\n        message: 'Please sign this message to verify your identity:\\n\\nAddress: ******************************************\\nTimestamp: 2024-01-01T00:00:00.000Z\\nNonce: test123\\n\\nThis signature will be used for identity verification purposes only.',\n        signature: '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1b',\n        userId: 'user-123',\n        merchantId: 'merchant-123',\n      });\n\n      expect(result.success).toBe(false);\n      expect(result.error).toBeDefined();\n    });\n\n    it('should validate service resilience under load', async () => {\n      // Simulate multiple concurrent requests\n      const requests = Array.from({ length: 10 }, (_, i) => \n        service.verifyEthereumSignature({\n          address: '******************************************',\n          message: `Test message ${i}`,\n          signature: '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1b',\n          userId: `user-${i}`,\n          merchantId: `merchant-${i}`,\n        })\n      );\n\n      const results = await Promise.all(requests);\n\n      // All requests should complete (either success or controlled failure)\n      expect(results).toHaveLength(10);\n      results.forEach(result => {\n        expect(result).toBeDefined();\n        expect(typeof result.success).toBe('boolean');\n      });\n    });\n  });\n});\n"], "version": 3}