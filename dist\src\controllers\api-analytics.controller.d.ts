import { BaseController } from "../../core/BaseController";
/**
 * API analytics controller
 * This controller handles API analytics requests
 */
export declare class ApiAnalyticsController extends BaseController {
    private apiAnalyticsService;
    /**
     * Create a new API analytics controller
     */
    constructor();
    /**
     * Get API analytics
     */
    getAnalytics: any;
    /**
     * Get API analytics summary
     */
    getAnalyticsSummary: any;
    /**
     * Get API analytics by version
     */
    getAnalyticsByVersion: any;
    /**
     * Get API analytics summary by version
     */
    getAnalyticsSummaryByVersion: any;
}
export default ApiAnalyticsController;
//# sourceMappingURL=api-analytics.controller.d.ts.map