import { Request, Response } from 'express';
export declare class DashboardWidgetController {
    /**
     * Get all widgets for a dashboard
     */
    getWidgets: (req: Request, res: Response) => Promise<void>;
    /**
     * Get a widget by ID
     */
    getWidgetById: (req: Request, res: Response) => Promise<void>;
    /**
     * Create a new widget
     */
    createWidget: (req: Request, res: Response) => Promise<void>;
    /**
     * Update a widget
     */
    updateWidget: (req: Request, res: Response) => Promise<void>;
    /**
     * Delete a widget
     */
    deleteWidget: (req: Request, res: Response) => Promise<void>;
    /**
     * Reorder widgets
     */
    reorderWidgets: (req: Request, res: Response) => Promise<void>;
}
//# sourceMappingURL=dashboard-widget.controller.d.ts.map