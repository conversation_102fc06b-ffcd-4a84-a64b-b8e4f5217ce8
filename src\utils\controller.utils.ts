// jscpd:ignore-file
import { Request, Response, NextFunction } from 'express';
import { ErrorFactory } from './errors/ErrorFactory';
import { User, Merchant } from '../types';
import { ErrorFactory } from './errors/ErrorFactory';
import { User, Merchant } from '../types';

// Extend the Express Request type to include user property
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role: string;
        merchantId?: string;
      };
    }
  }
}

/**
 * Controller utilities for common controller operations
 */
export class ControllerUtils {
  /**
   * Check if user is authenticated
   * @param req Express request
   * @returns User ID and role
   * @throws AppError if user is not authenticated
   */
  static checkAuth(req: Request): { userId: string; userRole: string; merchantId?: string } {
    const userId = req.user?.id;
    const userRole: unknown = req.user?.role;
    const merchantId: unknown = req.user?.merchantId;

    if (!userId || !userRole) {
      throw ErrorFactory.authentication('Unauthorized');
    }

    return { userId, userRole, merchantId };
  }

  /**
   * Check if user is admin
   * @param req Express request
   * @returns User ID
   * @throws AppError if user is not admin
   */
  static checkAdmin(req: Request): { userId: string } {
    const { userId, userRole } = this.checkAuth(req);

    if (userRole !== 'ADMIN') {
      throw ErrorFactory.authorization('Access denied');
    }

    return { userId };
  }

  /**
   * Check if user is merchant
   * @param req Express request
   * @returns User ID and merchant ID
   * @throws AppError if user is not merchant
   */
  static checkMerchant(req: Request): { userId: string; merchantId: string } {
    const { userId, userRole, merchantId } = this.checkAuth(req);

    if (userRole !== 'MERCHANT') {
      throw ErrorFactory.authorization('Access denied');
    }

    if (!merchantId) {
      throw ErrorFactory.authorization('Merchant ID not found');
    }

    return { userId, merchantId };
  }

  /**
   * Validate required fields
   * @param req Express request
   * @param fields Required fields
   * @throws AppError if any required field is missing
   */
  static validateRequiredFields(req: Request, fields: string[]): void {
    const missingFields = fields.filter((field: string) => {
      const value = req.body[field];
      return value === undefined || value === null || value === '';
    });

    if (missingFields?.length > 0) {
      throw ErrorFactory.missingRequiredField(missingFields);
    }
  }

  /**
   * Validate enum value
   * @param value Value to validate
   * @param enumType Enum type
   * @param fieldName Field name for error message
   * @throws AppError if value is not in enum
   */
  static validateEnum<T extends object>(value: unknown, enumType: T, fieldName: string): void {
    if (!Object.values(enumType).includes(value)) {
      throw ErrorFactory.invalidInput(`Invalid ${fieldName}`);
    }
  }

  /**
   * Parse pagination parameters
   * @param req Express request
   * @returns Pagination parameters
   */
  static parsePagination(req: Request): { page: number; limit: number; offset: number } {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const offset = (page - 1) * limit;

    return { page, limit, offset };
  }

  /**
   * Parse date range
   * @param req Express request
   * @param startDateField Start date field name
   * @param endDateField End date field name
   * @returns Date range
   * @throws AppError if dates are invalid
   */
  static parseDateRange(
    req: Request,
    startDateField = 'startDate',
    endDateField = 'endDate'
  ): { startDate: Date; endDate: Date } {
    const startDateStr = req.query[startDateField] as string;
    const endDateStr: unknown = req.query[endDateField] as string;

    if (!startDateStr || !endDateStr) {
      throw ErrorFactory.missingRequiredField([startDateField, endDateField]);
    }

    const startDate = new Date(startDateStr);
    const endDate = new Date(endDateStr);

    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      throw ErrorFactory.invalidInput('Invalid date format');
    }

    if (startDate > endDate) {
      throw ErrorFactory.invalidInput(`${startDateField} cannot be after ${endDateField}`);
    }

    return { startDate, endDate };
  }

  /**
   * Format success response
   * @param data Response data
   * @returns Formatted response
   */
  static formatSuccessResponse(data: unknown): { success: true; data: unknown } {
    return {
      success: true,
      data,
    };
  }

  /**
   * Format message response
   * @param message Response message
   * @returns Formatted response
   */
  static formatMessageResponse(message: string): { success: true; message: string } {
    return {
      success: true,
      message,
    };
  }

  /**
   * Format paginated response
   * @param data Response data
   * @param total Total number of items
   * @param page Current page
   * @param limit Items per page
   * @returns Formatted response
   */
  static formatPaginatedResponse(
    data: unknown[],
    total: number,
    page: number,
    limit: number
  ): {
    success: true;
    data: unknown[];
    pagination: { total: number; page: number; totalPages: number; limit: number };
  } {
    return {
      success: true,
      data,
      pagination: {
        total,
        page,
        totalPages: Math.ceil(total / limit),
        limit,
      },
    };
  }

  /**
   * Format error response
   * @param message Error message
   * @param code Error code
   * @returns Formatted response
   */
  static formatErrorResponse(
    message: string,
    code?: string
  ): { success: false; error: { message: string; code?: string } } {
    return {
      success: false,
      error: {
        message,
        ...(code && { code }),
      },
    };
  }
}

export default ControllerUtils;
