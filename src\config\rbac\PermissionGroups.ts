// jscpd:ignore-file
/**
 * Permission Groups
 *
 * Defines groups of related permissions for easier management.
 */

// Type definitions for permissions
type Permission = string;

interface PermissionGroup {
  READ?: Permission[];
  WRITE?: Permission[];
  APPROVE?: Permission[];
  METHODS?: Permission[];
  GATEWAYS?: Permission[];
  ROUTING?: Permission[];
  FEES?: Permission[];
  ALERTS?: Permission[];
  ALL: Permission[];
}

/**
 * Permission group for admin access
 */
export const ADMIN_PERMISSIONS: Permission[] = ['admin:access'];

/**
 * Permission group for merchant management
 */
export const MERCHANT_PERMISSIONS: PermissionGroup = {
  READ: ['merchants:view'],
  WRITE: ['merchants:create', 'merchants:update', 'merchants:delete'],
  APPROVE: ['merchant_approvals:view', 'merchant_approvals:approve', 'merchant_approvals:reject'],
  ALL: [
    'merchants:view',
    'merchants:create',
    'merchants:update',
    'merchants:delete',
    'merchant_approvals:view',
    'merchant_approvals:approve',
    'merchant_approvals:reject',
    'merchant_segmentation:view',
    'merchant_support:view',
    'merchant_onboarding:view',
    'merchant_onboarding:update',
  ],
};

/**
 * Permission group for payment management
 */
export const PAYMENT_PERMISSIONS: PermissionGroup = {
  READ: ['payments:view'],
  WRITE: ['payments:process', 'payments:refund', 'payments:cancel'],
  METHODS: [
    'payment_methods:view',
    'payment_methods:create',
    'payment_methods:update',
    'payment_methods:delete',
  ],
  GATEWAYS: [
    'payment_gateways:view',
    'payment_gateways:create',
    'payment_gateways:update',
    'payment_gateways:delete',
  ],
  ROUTING: ['payment_routing:view', 'payment_routing:update'],
  FEES: ['fees:view', 'fees:update'],
  ALL: [
    'payments:view',
    'payments:process',
    'payments:refund',
    'payments:cancel',
    'payment_methods:view',
    'payment_methods:create',
    'payment_methods:update',
    'payment_methods:delete',
    'payment_gateways:view',
    'payment_gateways:create',
    'payment_gateways:update',
    'payment_gateways:delete',
    'payment_routing:view',
    'payment_routing:update',
    'fees:view',
    'fees:update',
  ],
};

/**
 * Permission group for verification management
 */
export const VERIFICATION_PERMISSIONS: PermissionGroup = {
  READ: ['verification_methods:view', 'verification_monitoring:view'],
  WRITE: [
    'verification_methods:create',
    'verification_methods:update',
    'verification_methods:delete',
  ],
  ALL: [
    'verification_methods:view',
    'verification_methods:create',
    'verification_methods:update',
    'verification_methods:delete',
    'verification_monitoring:view',
  ],
};

/**
 * Permission group for subscription management
 */
export const SUBSCRIPTION_PERMISSIONS: PermissionGroup = {
  READ: ['subscription_plans:view'],
  WRITE: ['subscription_plans:create', 'subscription_plans:update', 'subscription_plans:delete'],
  ALL: [
    'subscription_plans:view',
    'subscription_plans:create',
    'subscription_plans:update',
    'subscription_plans:delete',
  ],
};

/**
 * Permission group for analytics
 */
export const ANALYTICS_PERMISSIONS: PermissionGroup = {
  READ: ['analytics:view'],
  WRITE: ['analytics:export', 'analytics:configure'],
  ALL: ['analytics:view', 'analytics:export', 'analytics:configure'],
};

/**
 * Permission group for monitoring
 */
export const MONITORING_PERMISSIONS: PermissionGroup = {
  READ: ['monitoring:view'],
  WRITE: ['monitoring:configure'],
  ALERTS: ['alerts:view', 'alerts:create', 'alerts:update', 'alerts:delete'],
  ALL: [
    'monitoring:view',
    'monitoring:configure',
    'alerts:view',
    'alerts:create',
    'alerts:update',
    'alerts:delete',
    'alert_notifications:view',
    'alert_notifications:update',
  ],
};

/**
 * Permission group for security management
 */
export const SECURITY_PERMISSIONS: PermissionGroup = {
  READ: ['security:view'],
  WRITE: ['security:update'],
  ALL: ['security:view', 'security:update'],
};

/**
 * Permission group for settings management
 */
export const SETTINGS_PERMISSIONS: PermissionGroup = {
  READ: ['settings:view'],
  WRITE: ['settings:update'],
  ALL: ['settings:view', 'settings:update'],
};

/**
 * Permission group for notification management
 */
export const NOTIFICATION_PERMISSIONS: PermissionGroup = {
  READ: ['notifications:view'],
  WRITE: ['notifications:create', 'notifications:update', 'notifications:delete'],
  ALL: [
    'notifications:view',
    'notifications:create',
    'notifications:update',
    'notifications:delete',
  ],
};

/**
 * Permission group for role management
 */
export const ROLE_PERMISSIONS: PermissionGroup = {
  READ: ['roles:view'],
  WRITE: ['roles:create', 'roles:update', 'roles:delete'],
  ALL: ['roles:view', 'roles:create', 'roles:update', 'roles:delete'],
};

/**
 * Permission group for admin user management
 */
export const ADMIN_USER_PERMISSIONS: PermissionGroup = {
  READ: ['admin_users:view'],
  WRITE: ['admin_users:create', 'admin_users:update', 'admin_users:delete'],
  ALL: ['admin_users:view', 'admin_users:create', 'admin_users:update', 'admin_users:delete'],
};

/**
 * All permissions
 */
export const ALL_PERMISSIONS: string[] = [
  ...ADMIN_PERMISSIONS,
  ...MERCHANT_PERMISSIONS.ALL,
  ...PAYMENT_PERMISSIONS.ALL,
  ...VERIFICATION_PERMISSIONS.ALL,
  ...SUBSCRIPTION_PERMISSIONS.ALL,
  ...ANALYTICS_PERMISSIONS.ALL,
  ...MONITORING_PERMISSIONS.ALL,
  ...SECURITY_PERMISSIONS.ALL,
  ...SETTINGS_PERMISSIONS.ALL,
  ...NOTIFICATION_PERMISSIONS.ALL,
  ...ROLE_PERMISSIONS.ALL,
  ...ADMIN_USER_PERMISSIONS.ALL,
];
