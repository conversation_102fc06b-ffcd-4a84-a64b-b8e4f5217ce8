/**
 * Enhanced Prisma Client
 *
 * This module provides an enhanced Prisma client with:
 * - Connection pooling
 * - Retry mechanisms
 * - Proper error handling
 * - Environment-specific logging
 */
declare const prisma: any;
/**
 * Connect to the database with retry mechanism
 * @returns {Promise<boolean>} True if connection successful, false otherwise
 */
export declare const connectWithRetry: any;
/**
 * Disconnect from the database
 * @returns {Promise<void>}
 */
export declare const disconnect: any;
/**
 * Execute a database operation with retry mechanism
 * @param operation Function that performs the database operation
 * @param maxRetries Maximum number of retries (default: 3)
 * @returns Result of the operation
 */
export declare const executeWithRetry: any;
export default prisma;
//# sourceMappingURL=prisma-client.d.ts.map