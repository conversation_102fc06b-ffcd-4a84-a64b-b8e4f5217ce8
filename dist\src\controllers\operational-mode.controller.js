"use strict";
// jscpd:ignore-file
/**
 * Operational Mode Controller
 *
 * Handles operational mode operations.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.setSystemEnabled = exports.setOperationalMode = exports.getCurrentMode = void 0;
const OperationalModeService_1 = require("../services/system/OperationalModeService");
const logger_1 = require("../lib/logger");
const error_middleware_1 = require("../middlewares/error.middleware");
const DIContainer_1 = require("../lib/DIContainer");
/**
 * Get current operational mode
 */
const getCurrentMode = async (req, res, next) => {
    try {
        const operationalModeService = DIContainer_1.container.resolve("operationalModeService");
        const status = await operationalModeService.getSystemStatus();
        res.json({
            success: true,
            status
        });
    }
    catch (error) {
        logger_1.logger.error("Error getting operational mode:", error);
        next(new error_middleware_1.AppError({
            message: "Failed to get operational mode",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};
exports.getCurrentMode = getCurrentMode;
/**
 * Set operational mode
 */
const setOperationalMode = async (req, res, next) => {
    try {
        const { mode } = req.body;
        if (!mode || !Object.values(OperationalModeService_1.OperationalMode).includes(mode)) {
            return next(new error_middleware_1.AppError({
                message: "Invalid operational mode",
                type: ErrorType.VALIDATION,
                code: ErrorCode.INVALID_INPUT
            }));
        }
        const operationalModeService = DIContainer_1.container.resolve("operationalModeService");
        const currentMode = operationalModeService.getCurrentMode();
        // Prevent switching to the same mode
        if (currentMode === mode) {
            return res.json({
                success: true,
                message: `System is already in ${mode} mode`,
                status: await operationalModeService.getSystemStatus()
            });
        }
        // Only production mode is supported
        if (mode !== OperationalModeService_1.OperationalMode.PRODUCTION) {
            logger_1.logger.error(`Attempted to switch to unsupported mode: ${mode}`);
            return next(new error_middleware_1.AppError({
                message: "Only production mode is supported",
                type: ErrorType.VALIDATION,
                code: ErrorCode.INVALID_INPUT
            }));
        }
        // Comprehensive validation for production mode
        logger_1.logger.info("Performing comprehensive validation for production mode");
        // Check database connectivity
        const prisma = DIContainer_1.container.resolve("prisma");
        try {
            await prisma.$queryRaw `SELECT 1`;
        }
        catch (dbError) {
            logger_1.logger.error("Database connectivity check failed:", dbError);
            return next(new error_middleware_1.AppError({
                message: "Database connectivity check failed",
                type: ErrorType.INTERNAL,
                code: ErrorCode.INTERNAL_SERVER_ERROR
            }));
        }
        // Check system health
        const healthStatus = await checkSystemHealth();
        if (!healthStatus.healthy) {
            logger_1.logger.error("System health check failed:", healthStatus.issues);
            return next(new error_middleware_1.AppError(`System health check failed: ${healthStatus.issues.join(", ")}`, 500));
        }
        logger_1.logger.info("All systems are ready in production mode");
        // Set operational mode
        await operationalModeService.setOperationalMode(mode, req.user?.id || "system");
        // Get updated status
        const status = await operationalModeService.getSystemStatus();
        // Record admin action
        await recordAdminAction(req.user?.id || "system", "change_operational_mode", {
            previousMode: currentMode,
            newMode: mode,
            timestamp: new Date()
        });
        res.json({
            success: true,
            message: `Operational mode set to ${mode}`,
            status,
            previousMode: currentMode
        });
    }
    catch (error) {
        logger_1.logger.error("Error setting operational mode:", error);
        next(new error_middleware_1.AppError({
            message: "Failed to set operational mode",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};
exports.setOperationalMode = setOperationalMode;
/**
 * Check system health
 * @returns System health status
 */
const checkSystemHealth = async () => {
    const issues = [];
    try {
        // Check database connectivity
        const prisma = DIContainer_1.container.resolve("prisma");
        try {
            await prisma.$queryRaw `SELECT 1`;
        }
        catch (error) {
            issues.push("Database connectivity check failed");
        }
        // Check available disk space
        // This would be implemented with a system-specific check
        // Check memory usage
        // This would be implemented with a system-specific check
        // Check external service connectivity
        // This would check connectivity to payment gateways, etc.
        return {
            healthy: issues.length === 0,
            issues
        };
    }
    catch (error) {
        logger_1.logger.error("Error checking system health:", error);
        return {
            healthy: false,
            issues: ["System health check failed with an unexpected error"]
        };
    }
};
/**
 * Record admin action
 * @param userId User ID
 * @param action Action
 * @param details Action details
 */
const recordAdminAction = async (userId, action, details) => {
    try {
        const prisma = DIContainer_1.container.resolve("prisma");
        await prisma.adminAction.create({
            data: {
                userId,
                action,
                details,
                timestamp: new Date()
            }
        });
    }
    catch (error) {
        logger_1.logger.error("Error recording admin action:", error);
        // Don't throw here to prevent disrupting the main flow
    }
};
/**
 * Enable or disable the system
 */
const setSystemEnabled = async (req, res, next) => {
    try {
        const { enabled } = req.body;
        if (typeof enabled !== "boolean") {
            return next(new error_middleware_1.AppError({
                message: "Invalid system status",
                type: ErrorType.VALIDATION,
                code: ErrorCode.INVALID_INPUT
            }));
        }
        const operationalModeService = DIContainer_1.container.resolve("operationalModeService");
        // Set system status
        await operationalModeService.setSystemEnabled(enabled, req.user?.id || "system");
        // Get updated status
        const status = await operationalModeService.getSystemStatus();
        res.json({
            success: true,
            message: `System ${enabled ? "enabled" : "disabled"}`,
            status
        });
    }
    catch (error) {
        logger_1.logger.error("Error setting system status:", error);
        next(new error_middleware_1.AppError({
            message: "Failed to set system status",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};
exports.setSystemEnabled = setSystemEnabled;
exports.default = {
    getCurrentMode: exports.getCurrentMode,
    setOperationalMode: exports.setOperationalMode,
    setSystemEnabled: exports.setSystemEnabled
};
//# sourceMappingURL=operational-mode.controller.js.map