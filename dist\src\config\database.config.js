"use strict";
// jscpd:ignore-file
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getDatabaseUrl = exports.closeDatabaseConnection = exports.getPrismaClient = exports.getDatabaseConfig = exports.getEnvironment = void 0;
/**
 * Database Configuration
 *
 * This file provides a centralized configuration for database connections
 * across different environments (development, production, testing).
 *
 * It ensures consistent database access throughout the application.
 */
const client_1 = require("@prisma/client");
const dotenv_1 = __importDefault(require("dotenv"));
const logger_1 = require("../lib/logger");
// Load environment variables
dotenv_1.default.config();
// Get current environment - always returns production
const getEnvironment = () => {
    return 'production';
};
exports.getEnvironment = getEnvironment;
// Load production database configuration
const getDatabaseConfig = () => {
    // Production configuration
    const config = {
        url: process.env.DATABASE_URL || '',
        host: process.env.DB_HOST || 'localhost',
        port: parseInt(process.env.DB_PORT || '5432', 10),
        username: process.env.DB_USERNAME || 'postgres',
        password: process.env.DB_PASSWORD || '',
        database: process.env.DB_NAME || 'Amazingpay',
        ssl: process.env.DB_SSL === 'true',
        connectionPoolMin: parseInt(process.env.DB_CONNECTION_POOL_MIN || '5', 10),
        connectionPoolMax: parseInt(process.env.DB_CONNECTION_POOL_MAX || '20', 10),
        statementTimeout: parseInt(process.env.DB_STATEMENT_TIMEOUT || '30000', 10),
        logQueries: false, // Always disable query logging in production
    };
    return config;
};
exports.getDatabaseConfig = getDatabaseConfig;
// Singleton PrismaClient instance
let prismaInstance = null;
// Get PrismaClient instance
const getPrismaClient = () => {
    if (!prismaInstance) {
        const config = (0, exports.getDatabaseConfig)();
        logger_1.logger.info(`Initializing PrismaClient for database: ${config.database}`);
        // Production-only configuration
        prismaInstance = new client_1.PrismaClient({
            datasources: {
                db: {
                    url: config.url,
                },
            },
            log: [
                { level: 'error', emit: 'stdout' },
                { level: 'info', emit: 'stdout' },
                { level: 'warn', emit: 'stdout' },
            ],
        });
        // Set up basic error logging
        logger_1.logger.info('PrismaClient initialized for production database');
    }
    return prismaInstance;
};
exports.getPrismaClient = getPrismaClient;
// Close database connection
const closeDatabaseConnection = async () => {
    if (prismaInstance) {
        await prismaInstance.$disconnect();
        prismaInstance = null;
        logger_1.logger.info('Database connection closed');
    }
};
exports.closeDatabaseConnection = closeDatabaseConnection;
// Legacy getDatabaseUrl function for backward compatibility
const getDatabaseUrl = () => {
    return (0, exports.getDatabaseConfig)().url;
};
exports.getDatabaseUrl = getDatabaseUrl;
// Export default database configuration
const databaseConfig = {
    url: (0, exports.getDatabaseUrl)(),
    logQueries: false, // Never log queries in production
    useMockData: false, // Always use real data
    getEnvironment: // Always use real data
    exports.getEnvironment,
    getDatabaseConfig: exports.getDatabaseConfig,
    getPrismaClient: exports.getPrismaClient,
    closeDatabaseConnection: exports.closeDatabaseConnection,
};
exports.default = databaseConfig;
//# sourceMappingURL=database.config.js.map