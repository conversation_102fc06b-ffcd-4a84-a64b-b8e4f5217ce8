7c4498cb4ad4e5a6abd26ebe94d707a8
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const vitest_1 = require("vitest");
const supertest_1 = __importDefault(require("supertest"));
const express_1 = __importDefault(require("express"));
const advanced_report_controller_1 = require("../controllers/advanced-report.controller");
const advanced_report_service_1 = require("../services/advanced-report.service");
// Mock the service
vitest_1.vi.mock('../services/advanced-report.service');
const app = (0, express_1.default)();
app.use(express_1.default.json());
// Mock auth middleware
const mockAuthMiddleware = (req, res, next) => {
    req.user = {
        id: 'user-1',
        role: 'MERCHANT',
        email: '<EMAIL>',
    };
    next();
};
// Setup routes
const reportController = new advanced_report_controller_1.AdvancedReportController();
app.post('/api/reports/generate', mockAuthMiddleware, reportController.generateReport);
app.get('/api/reports/templates', mockAuthMiddleware, reportController.getReportTemplates);
app.get('/api/reports/templates/:id', mockAuthMiddleware, reportController.getReportTemplateById);
app.post('/api/reports/templates', mockAuthMiddleware, reportController.createReportTemplate);
app.put('/api/reports/templates/:id', mockAuthMiddleware, reportController.updateReportTemplate);
app.delete('/api/reports/templates/:id', mockAuthMiddleware, reportController.deleteReportTemplate);
app.get('/api/reports/scheduled', mockAuthMiddleware, reportController.getScheduledReports);
app.get('/api/reports/scheduled/:id', mockAuthMiddleware, reportController.getScheduledReportById);
app.post('/api/reports/scheduled', mockAuthMiddleware, reportController.createScheduledReport);
app.put('/api/reports/scheduled/:id', mockAuthMiddleware, reportController.updateScheduledReport);
app.delete('/api/reports/scheduled/:id', mockAuthMiddleware, reportController.deleteScheduledReport);
app.post('/api/reports/scheduled/:id/run', mockAuthMiddleware, reportController.runScheduledReport);
app.get('/api/reports/saved', mockAuthMiddleware, reportController.getSavedReports);
app.get('/api/reports/saved/:id', mockAuthMiddleware, reportController.getSavedReportById);
app.delete('/api/reports/saved/:id', mockAuthMiddleware, reportController.deleteSavedReport);
(0, vitest_1.describe)('AdvancedReportController', () => {
    let mockReportService;
    (0, vitest_1.beforeEach)(() => {
        mockReportService = {
            generateReport: vitest_1.vi.fn(),
            getReportTemplates: vitest_1.vi.fn(),
            getReportTemplateById: vitest_1.vi.fn(),
            createReportTemplate: vitest_1.vi.fn(),
            updateReportTemplate: vitest_1.vi.fn(),
            deleteReportTemplate: vitest_1.vi.fn(),
            getScheduledReports: vitest_1.vi.fn(),
            getScheduledReportById: vitest_1.vi.fn(),
            createScheduledReport: vitest_1.vi.fn(),
            updateScheduledReport: vitest_1.vi.fn(),
            deleteScheduledReport: vitest_1.vi.fn(),
            runScheduledReport: vitest_1.vi.fn(),
            getSavedReports: vitest_1.vi.fn(),
            getSavedReportById: vitest_1.vi.fn(),
            deleteSavedReport: vitest_1.vi.fn(),
        };
        // Mock the service instance
        vitest_1.vi.mocked(advanced_report_service_1.AdvancedReportService).mockImplementation(() => mockReportService);
    });
    (0, vitest_1.afterEach)(() => {
        vitest_1.vi.clearAllMocks();
    });
    (0, vitest_1.describe)('POST /api/reports/generate', () => {
        (0, vitest_1.it)('should generate a report successfully', async () => {
            const mockReport = {
                id: 'report-1',
                filePath: '/path/to/report.csv',
                format: 'CSV',
            };
            mockReportService.generateReport.mockResolvedValue(mockReport);
            const response = await (0, supertest_1.default)(app).post('/api/reports/generate').send({
                type: 'TRANSACTION',
                format: 'CSV',
                startDate: '2023-01-01',
                endDate: '2023-12-31',
            });
            (0, vitest_1.expect)(response.status).toBe(200);
            (0, vitest_1.expect)(response.body.success).toBe(true);
            (0, vitest_1.expect)(response.body.data).toEqual(mockReport);
            (0, vitest_1.expect)(mockReportService.generateReport).toHaveBeenCalledWith('TRANSACTION', {
                startDate: '2023-01-01',
                endDate: '2023-12-31',
                userId: 'user-1',
                userRole: 'MERCHANT',
            }, 'CSV');
        });
        (0, vitest_1.it)('should handle service errors', async () => {
            mockReportService.generateReport.mockRejectedValue(new Error('Service error'));
            const response = await (0, supertest_1.default)(app).post('/api/reports/generate').send({
                type: 'TRANSACTION',
                format: 'CSV',
            });
            (0, vitest_1.expect)(response.status).toBe(500);
            (0, vitest_1.expect)(response.body.success).toBe(false);
            (0, vitest_1.expect)(response.body.message).toBe('Service error');
        });
    });
    (0, vitest_1.describe)('GET /api/reports/templates', () => {
        (0, vitest_1.it)('should get report templates successfully', async () => {
            const mockTemplates = [
                {
                    id: 'template-1',
                    name: 'Test Template',
                    type: 'TRANSACTION',
                },
            ];
            mockReportService.getReportTemplates.mockResolvedValue(mockTemplates);
            const response = await (0, supertest_1.default)(app).get('/api/reports/templates');
            (0, vitest_1.expect)(response.status).toBe(200);
            (0, vitest_1.expect)(response.body.success).toBe(true);
            (0, vitest_1.expect)(response.body.data).toEqual(mockTemplates);
            (0, vitest_1.expect)(mockReportService.getReportTemplates).toHaveBeenCalledWith('user-1', true);
        });
        (0, vitest_1.it)('should handle includeSystem parameter', async () => {
            const mockTemplates = [
                {
                    id: 'template-1',
                    name: 'User Template',
                    type: 'TRANSACTION',
                },
            ];
            mockReportService.getReportTemplates.mockResolvedValue(mockTemplates);
            const response = await (0, supertest_1.default)(app)
                .get('/api/reports/templates')
                .query({ includeSystem: 'false' });
            (0, vitest_1.expect)(response.status).toBe(200);
            (0, vitest_1.expect)(mockReportService.getReportTemplates).toHaveBeenCalledWith('user-1', false);
        });
    });
    (0, vitest_1.describe)('POST /api/reports/templates', () => {
        (0, vitest_1.it)('should create a report template successfully', async () => {
            const templateData = {
                name: 'New Template',
                description: 'Test Description',
                type: 'TRANSACTION',
                config: { columns: ['id', 'amount'] },
            };
            const mockTemplate = {
                id: 'template-1',
                ...templateData,
                createdById: 'user-1',
            };
            mockReportService.createReportTemplate.mockResolvedValue(mockTemplate);
            const response = await (0, supertest_1.default)(app).post('/api/reports/templates').send(templateData);
            (0, vitest_1.expect)(response.status).toBe(201);
            (0, vitest_1.expect)(response.body.success).toBe(true);
            (0, vitest_1.expect)(response.body.data).toEqual(mockTemplate);
            (0, vitest_1.expect)(mockReportService.createReportTemplate).toHaveBeenCalledWith({
                ...templateData,
                createdById: 'user-1',
            });
        });
    });
    (0, vitest_1.describe)('PUT /api/reports/templates/:id', () => {
        (0, vitest_1.it)('should update a report template successfully', async () => {
            const updateData = {
                name: 'Updated Template',
                description: 'Updated Description',
            };
            const mockTemplate = {
                id: 'template-1',
                ...updateData,
            };
            mockReportService.updateReportTemplate.mockResolvedValue(mockTemplate);
            const response = await (0, supertest_1.default)(app).put('/api/reports/templates/template-1').send(updateData);
            (0, vitest_1.expect)(response.status).toBe(200);
            (0, vitest_1.expect)(response.body.success).toBe(true);
            (0, vitest_1.expect)(response.body.data).toEqual(mockTemplate);
            (0, vitest_1.expect)(mockReportService.updateReportTemplate).toHaveBeenCalledWith('template-1', updateData);
        });
    });
    (0, vitest_1.describe)('DELETE /api/reports/templates/:id', () => {
        (0, vitest_1.it)('should delete a report template successfully', async () => {
            mockReportService.deleteReportTemplate.mockResolvedValue({});
            const response = await (0, supertest_1.default)(app).delete('/api/reports/templates/template-1');
            (0, vitest_1.expect)(response.status).toBe(200);
            (0, vitest_1.expect)(response.body.success).toBe(true);
            (0, vitest_1.expect)(response.body.message).toBe('Report template deleted successfully');
            (0, vitest_1.expect)(mockReportService.deleteReportTemplate).toHaveBeenCalledWith('template-1');
        });
    });
    (0, vitest_1.describe)('GET /api/reports/scheduled', () => {
        (0, vitest_1.it)('should get scheduled reports successfully', async () => {
            const mockReports = [
                {
                    id: 'scheduled-1',
                    name: 'Weekly Report',
                    schedule: '0 0 * * 1',
                },
            ];
            mockReportService.getScheduledReports.mockResolvedValue(mockReports);
            const response = await (0, supertest_1.default)(app).get('/api/reports/scheduled');
            (0, vitest_1.expect)(response.status).toBe(200);
            (0, vitest_1.expect)(response.body.success).toBe(true);
            (0, vitest_1.expect)(response.body.data).toEqual(mockReports);
            (0, vitest_1.expect)(mockReportService.getScheduledReports).toHaveBeenCalledWith('user-1');
        });
    });
    (0, vitest_1.describe)('POST /api/reports/scheduled', () => {
        (0, vitest_1.it)('should create a scheduled report successfully', async () => {
            const scheduledReportData = {
                name: 'Weekly Report',
                templateId: 'template-1',
                schedule: '0 0 * * 1',
                isActive: true,
            };
            const mockScheduledReport = {
                id: 'scheduled-1',
                ...scheduledReportData,
                createdById: 'user-1',
            };
            mockReportService.createScheduledReport.mockResolvedValue(mockScheduledReport);
            const response = await (0, supertest_1.default)(app).post('/api/reports/scheduled').send(scheduledReportData);
            (0, vitest_1.expect)(response.status).toBe(201);
            (0, vitest_1.expect)(response.body.success).toBe(true);
            (0, vitest_1.expect)(response.body.data).toEqual(mockScheduledReport);
            (0, vitest_1.expect)(mockReportService.createScheduledReport).toHaveBeenCalledWith({
                ...scheduledReportData,
                createdById: 'user-1',
            });
        });
    });
    (0, vitest_1.describe)('POST /api/reports/scheduled/:id/run', () => {
        (0, vitest_1.it)('should run a scheduled report successfully', async () => {
            const mockResult = {
                id: 'report-1',
                status: 'COMPLETED',
            };
            mockReportService.runScheduledReport.mockResolvedValue(mockResult);
            const response = await (0, supertest_1.default)(app).post('/api/reports/scheduled/scheduled-1/run');
            (0, vitest_1.expect)(response.status).toBe(200);
            (0, vitest_1.expect)(response.body.success).toBe(true);
            (0, vitest_1.expect)(response.body.data).toEqual(mockResult);
            (0, vitest_1.expect)(mockReportService.runScheduledReport).toHaveBeenCalledWith('scheduled-1');
        });
    });
    (0, vitest_1.describe)('GET /api/reports/saved', () => {
        (0, vitest_1.it)('should get saved reports successfully', async () => {
            const mockReports = [
                {
                    id: 'report-1',
                    name: 'Transaction Report',
                    type: 'TRANSACTION',
                    format: 'CSV',
                },
            ];
            mockReportService.getSavedReports.mockResolvedValue(mockReports);
            const response = await (0, supertest_1.default)(app).get('/api/reports/saved');
            (0, vitest_1.expect)(response.status).toBe(200);
            (0, vitest_1.expect)(response.body.success).toBe(true);
            (0, vitest_1.expect)(response.body.data).toEqual(mockReports);
            (0, vitest_1.expect)(mockReportService.getSavedReports).toHaveBeenCalledWith('user-1');
        });
    });
    (0, vitest_1.describe)('DELETE /api/reports/saved/:id', () => {
        (0, vitest_1.it)('should delete a saved report successfully', async () => {
            mockReportService.deleteSavedReport.mockResolvedValue({});
            const response = await (0, supertest_1.default)(app).delete('/api/reports/saved/report-1');
            (0, vitest_1.expect)(response.status).toBe(200);
            (0, vitest_1.expect)(response.body.success).toBe(true);
            (0, vitest_1.expect)(response.body.message).toBe('Saved report deleted successfully');
            (0, vitest_1.expect)(mockReportService.deleteSavedReport).toHaveBeenCalledWith('report-1');
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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