// jscpd:ignore-file
import { BaseService, ServiceError } from './base.service';
import { Customer } from '@prisma/client';
import geoip from 'geoip-lite';
import { ApiErrorCode } from '../middlewares/apiResponseMiddleware';
import { Transaction, PaymentMethod, Merchant } from '../types';

/**
 * Recommendation factor weights
 */
interface RecommendationWeights {
  /**
   * Weight for customer's previous payment method usage
   */
  customerHistory: number;

  /**
   * Weight for payment method's success rate
   */
  successRate: number;

  /**
   * Weight for payment method's popularity
   */
  popularity: number;

  /**
   * Weight for payment method's geographical availability
   */
  geography: number;

  /**
   * Weight for payment method's transaction amount compatibility
   */
  amount: number;

  /**
   * Weight for payment method's device compatibility
   */
  device: number;
}

/**
 * Payment method recommendation
 */
export interface PaymentMethodRecommendation {
  /**
   * Payment method ID
   */
  paymentMethodId: number;

  /**
   * Payment method name
   */
  name: string;

  /**
   * Payment method code
   */
  code: string;

  /**
   * Recommendation score (0-100)
   */
  score: number;

  /**
   * Recommendation factors
   */
  factors: { factor: string; score: number; weight: number }[];

  /**
   * Recommendation reason
   */
  reason: string;
}

/**
 * Payment method recommendation service
 */
export class PaymentRecommendationService extends BaseService {
  /**
   * Default recommendation weights
   */
  private defaultWeights: RecommendationWeights = {
    customerHistory: 0.35,
    successRate: 0.25,
    popularity: 0.15,
    geography: 0.1,
    amount: 0.1,
    device: 0.05,
  };

  /**
   * Get payment method recommendations for a customer
   * @param merchantId Merchant ID
   * @param customerId Customer ID (optional)
   * @param customerEmail Customer email (optional)
   * @param amount Transaction amount
   * @param currency Transaction currency
   * @param ipAddress Customer IP address
   * @param userAgent Customer user agent
   * @param deviceType Customer device type
   * @returns Payment method recommendations
   */
  async getRecommendations(
    merchantId: number,
    customerId?: number,
    customerEmail?: string,
    amount?: string,
    currency?: string,
    ipAddress?: string,
    userAgent?: string,
    deviceType?: string
  ): Promise<PaymentMethodRecommendation[]> {
    try {
      // Get merchant's payment methods
      const paymentMethods: unknown = await this.prisma.paymentMethod.findMany({
        where: {
          merchantId,
          status: 'ACTIVE',
        },
      });

      if (paymentMethods.length === 0) {
        throw this.paymentError('No active payment methods found for this merchant');
      }

      // Get merchant's recommendation weights
      const weights: unknown = await this.getMerchantRecommendationWeights(merchantId);

      // Get customer data if available
      let customer: Customer | null = null;
      if (customerId) {
        customer = await this.prisma.customer.findUnique({
          where: { id: customerId },
        });
      } else if (customerEmail) {
        customer = await this.prisma.customer.findFirst({
          where: { email: customerEmail },
        });
      }

      // Calculate scores for each payment method
      const recommendations: PaymentMethodRecommendation[] = [];

      for (const paymentMethod of paymentMethods) {
        const factors: unknown = await this.calculateFactors(
          paymentMethod,
          merchantId,
          customer,
          amount,
          currency,
          ipAddress,
          userAgent,
          deviceType,
          weights
        );

        // Calculate overall score
        const overallScore: unknown = this.calculateOverallScore(factors);

        // Generate recommendation reason
        const reason: unknown = this.generateRecommendationReason(paymentMethod, factors);

        recommendations.push({
          paymentMethodId: paymentMethod.id,
          name: paymentMethod.name,
          code: paymentMethod.code,
          score: overallScore,
          factors,
          reason,
        });
      }

      // Sort recommendations by score (descending)
      return recommendations.sort((a, b) => b.score - a.score);
    } catch (error) {
      if (error instanceof ServiceError) {
        throw error;
      }

      console.error('Error getting payment method recommendations:', error);
      throw this.paymentError('Failed to get payment method recommendations');
    }
  }

  /**
   * Calculate recommendation factors for a payment method
   * @param paymentMethod Payment method
   * @param merchantId Merchant ID
   * @param customer Customer
   * @param amount Transaction amount
   * @param currency Transaction currency
   * @param ipAddress Customer IP address
   * @param userAgent Customer user agent
   * @param deviceType Customer device type
   * @param weights Recommendation weights
   * @returns Recommendation factors
   */
  private async calculateFactors(
    paymentMethod: PaymentMethod,
    merchantId: number,
    customer: Customer | null,
    amount?: string,
    currency?: string,
    ipAddress?: string,
    userAgent?: string,
    deviceType?: string,
    weights: RecommendationWeights = this.defaultWeights
  ): Promise<{ factor: string; score: number; weight: number }[]> {
    const factors: { factor: string; score: number; weight: number }[] = [];

    // Customer history factor
    const customerHistoryScore: unknown = await this.calculateCustomerHistoryScore(
      paymentMethod.id,
      customer
    );
    factors.push({
      factor: 'customerHistory',
      score: customerHistoryScore,
      weight: weights.customerHistory,
    });

    // Success rate factor
    const successRateScore: unknown = await this.calculateSuccessRateScore(
      paymentMethod.id,
      merchantId
    );
    factors.push({
      factor: 'successRate',
      score: successRateScore,
      weight: weights.successRate,
    });

    // Popularity factor
    const popularityScore: unknown = await this.calculatePopularityScore(paymentMethod.id, merchantId);
    factors.push({
      factor: 'popularity',
      score: popularityScore,
      weight: weights.popularity,
    });

    // Geography factor
    const geographyScore: unknown = this.calculateGeographyScore(paymentMethod.code, ipAddress);
    factors.push({
      factor: 'geography',
      score: geographyScore,
      weight: weights.geography,
    });

    // Amount factor
    const amountScore: unknown = this.calculateAmountScore(paymentMethod.code, amount, currency);
    factors.push({
      factor: 'amount',
      score: amountScore,
      weight: weights.amount,
    });

    // Device factor
    const deviceScore: unknown = this.calculateDeviceScore(paymentMethod.code, userAgent, deviceType);
    factors.push({
      factor: 'device',
      score: deviceScore,
      weight: weights.device,
    });

    return factors;
  }

  /**
   * Calculate overall score from factors
   * @param factors Recommendation factors
   * @returns Overall score (0-100)
   */
  private calculateOverallScore(
    factors: { factor: string; score: number; weight: number }[]
  ): number {
    let totalScore = 0;
    let totalWeight: number = 0;

    factors.forEach((({ score, weight })) => {
      totalScore += score * weight;
      totalWeight += weight;
    });

    return Math.round(totalWeight > 0 ? totalScore / totalWeight : 0);
  }

  /**
   * Calculate customer history score
   * @param paymentMethodId Payment method ID
   * @param customer Customer
   * @returns Score (0-100)
   */
  private async calculateCustomerHistoryScore(
    paymentMethodId: number,
    customer: Customer | null
  ): Promise<number> {
    if (!customer) {
      return 50; // Neutral score for new customers
    }

    try {
      // Get customer's transactions
      const transactions: unknown = await this.prisma.transaction.findMany({
        where: { customerEmail: customer.email, status: 'COMPLETED' },
        orderBy: { createdAt: 'desc' },
        take: 10, // Consider last 10 transactions
      });

      if (transactions.length === 0) {
        return 50; // Neutral score for customers with no transactions
      }

      // Count transactions with this payment method
      const methodTransactions: unknown = transactions.filter((
        (tx)) => tx.paymentMethodId === paymentMethodId
      );

      // Calculate score based on usage frequency
      const usageRate: unknown = methodTransactions.length / transactions.length;

      // Give higher score for recently used methods
      let recencyBonus: number = 0;
      if (methodTransactions.length > 0) {
        const mostRecentTransaction: unknown = transactions[0];
        const mostRecentMethodTransaction: unknown = methodTransactions[0];

        if (mostRecentTransaction.id === mostRecentMethodTransaction.id) {
          recencyBonus = 20; // Bonus if the most recent transaction used this method
        }
      }

      return Math.min(100, Math.round(usageRate * 100) + recencyBonus);
    } catch (error) {
      console.error('Error calculating customer history score:', error);
      return 50; // Default to neutral score on error
    }
  }

  /**
   * Calculate success rate score
   * @param paymentMethodId Payment method ID
   * @param merchantId Merchant ID
   * @returns Score (0-100)
   */
  private async calculateSuccessRateScore(
    paymentMethodId: number,
    merchantId: number
  ): Promise<number> {
    try {
      // Get total transactions for this payment method
      const totalTransactions: unknown = await this.prisma.transaction.count({
        where: {
          paymentMethodId,
          merchantId,
        },
      });

      if (totalTransactions === 0) {
        return 50; // Neutral score for methods with no transactions
      }

      // Get successful transactions for this payment method
      const successfulTransactions: unknown = await this.prisma.transaction.count({
        where: {
          paymentMethodId,
          merchantId,
          status: 'COMPLETED',
        },
      });

      // Calculate success rate
      const successRate: unknown = successfulTransactions / totalTransactions;

      return Math.round(successRate * 100);
    } catch (error) {
      console.error('Error calculating success rate score:', error);
      return 50; // Default to neutral score on error
    }
  }

  /**
   * Calculate popularity score
   * @param paymentMethodId Payment method ID
   * @param merchantId Merchant ID
   * @returns Score (0-100)
   */
  private async calculatePopularityScore(
    paymentMethodId: number,
    merchantId: number
  ): Promise<number> {
    try {
      // Get transaction counts for all payment methods
      const paymentMethodCounts: unknown = await this.prisma.transaction.groupBy({
        by: ['paymentMethodId'],
        where: {
          merchantId,
        },
        _count: { id: true },
      });

      if (paymentMethodCounts.length === 0) {
        return 50; // Neutral score if no transactions
      }

      // Get total transactions
      const totalTransactions: unknown = paymentMethodCounts.reduce(
        (sum, method) => sum + method._count.id,
        0
      );

      if (totalTransactions === 0) {
        return 50; // Neutral score if no transactions
      }

      // Get transactions for this payment method
      const methodCount: unknown = paymentMethodCounts.find(
        (method) => method.paymentMethodId === paymentMethodId
      );

      if (!methodCount) {
        return 30; // Below average score for unused methods
      }

      // Calculate popularity as percentage of total transactions
      const popularity: unknown = methodCount._count.id / totalTransactions;

      return Math.round(popularity * 100);
    } catch (error) {
      console.error('Error calculating popularity score:', error);
      return 50; // Default to neutral score on error
    }
  }

  /**
   * Calculate geography score
   * @param paymentMethodCode Payment method code
   * @param ipAddress Customer IP address
   * @returns Score (0-100)
   */
  private calculateGeographyScore(paymentMethodCode: string, ipAddress?: string): Promise<number> {
    if (!ipAddress) {
      return Promise.resolve(50); // Neutral score if no IP address
    }

    try {
      // Get location from IP
      const geo: unknown = geoip.lookup(ipAddress);

      if (!geo) {
        return Promise.resolve(50); // Neutral score if location not found
      }

      const country: unknown = geo.country;

      // Define regional preferences for payment methods
      const regionalPreferences: Record<string, Record<string, number>> = {
        // Asia
        CN: { binance_pay: 90, binance_c2c: 85, binance_trc20: 80, crypto_transfer: 70 },
        JP: { crypto_transfer: 80, binance_pay: 70, binance_trc20: 65, binance_c2c: 60 },
        KR: { binance_pay: 85, crypto_transfer: 75, binance_trc20: 70, binance_c2c: 65 },

        // North America
        US: { crypto_transfer: 85, binance_trc20: 70, binance_pay: 65, binance_c2c: 60 },
        CA: { crypto_transfer: 80, binance_trc20: 75, binance_pay: 70, binance_c2c: 65 },

        // Europe
        GB: { crypto_transfer: 85, binance_pay: 80, binance_trc20: 75, binance_c2c: 65 },
        DE: { crypto_transfer: 85, binance_pay: 80, binance_trc20: 75, binance_c2c: 65 },
        FR: { crypto_transfer: 80, binance_pay: 75, binance_trc20: 70, binance_c2c: 65 },

        // Default
        DEFAULT: { crypto_transfer: 75, binance_pay: 70, binance_trc20: 65, binance_c2c: 60 },
      };

      // Get preferences for this country or use default
      const countryPreferences: unknown = regionalPreferences[country] || regionalPreferences.DEFAULT;

      // Get score for this payment method
      const score: unknown = countryPreferences[paymentMethodCode] || 50;

      return Promise.resolve(score);
    } catch (error) {
      console.error('Error calculating geography score:', error);
      return Promise.resolve(50); // Default to neutral score on error
    }
  }

  /**
   * Calculate amount score
   * @param paymentMethodCode Payment method code
   * @param amount Transaction amount
   * @param currency Transaction currency
   * @returns Score (0-100)
   */
  private calculateAmountScore(
    paymentMethodCode: string,
    amount?: string,
    currency?: string
  ): number {
    if (!amount || !currency) {
      return 50; // Neutral score if no amount or currency
    }

    try {
      const numAmount: unknown = parseFloat(amount);

      // Define amount ranges for payment methods
      const amountRanges: Record<string, { min: number; max: number; optimal: number }> = {
        binance_pay: { min: 1, max: 10000, optimal: 100 },
        binance_c2c: { min: 10, max: 5000, optimal: 500 },
        binance_trc20: { min: 5, max: 50000, optimal: 1000 },
        crypto_transfer: { min: 1, max: 100000, optimal: 5000 },
      };

      // Get range for this payment method
      const range: unknown = amountRanges[paymentMethodCode] || { min: 0, max: Infinity, optimal: 100 };

      // Check if amount is within range
      if (numAmount < range.min || numAmount > range.max) {
        return 30; // Low score if outside range
      }

      // Calculate score based on distance from optimal amount
      const distanceFromOptimal: unknown = Math.abs(numAmount - range.optimal) / range.optimal;
      const score: number = 100 - Math.min(50, Math.round(distanceFromOptimal * 100));

      return score;
    } catch (error) {
      console.error('Error calculating amount score:', error);
      return 50; // Default to neutral score on error
    }
  }

  /**
   * Calculate device score
   * @param paymentMethodCode Payment method code
   * @param userAgent Customer user agent
   * @param deviceType Customer device type
   * @returns Score (0-100)
   */
  private calculateDeviceScore(
    paymentMethodCode: string,
    userAgent?: string,
    deviceType?: string
  ): number {
    if (!userAgent && !deviceType) {
      return 50; // Neutral score if no user agent or device type
    }

    try {
      // Determine device type from user agent if not provided
      let device: unknown = deviceType ?? 'unknown';

      if (!deviceType && userAgent) {
        if (/mobile|android|iphone|ipad|ipod/i.test(userAgent)) {
          device = 'mobile';
        } else if (/tablet|ipad/i.test(userAgent)) {
          device = 'tablet';
        } else {
          device = 'desktop';
        }
      }

      // Define device preferences for payment methods
      const devicePreferences: Record<string, Record<string, number>> = {
        mobile: { binance_pay: 90, binance_c2c: 80, binance_trc20: 70, crypto_transfer: 60 },
        tablet: { binance_pay: 85, binance_c2c: 75, binance_trc20: 70, crypto_transfer: 65 },
        desktop: { crypto_transfer: 85, binance_trc20: 80, binance_pay: 75, binance_c2c: 70 },
        unknown: { binance_pay: 80, crypto_transfer: 75, binance_trc20: 70, binance_c2c: 65 },
      };

      // Get preferences for this device or use unknown
      const preferences: unknown = devicePreferences[device] || devicePreferences.unknown;

      // Get score for this payment method
      const score: unknown = preferences[paymentMethodCode] || 50;

      return score;
    } catch (error) {
      console.error('Error calculating device score:', error);
      return 50; // Default to neutral score on error
    }
  }

  /**
   * Generate recommendation reason
   * @param paymentMethod Payment method
   * @param factors Recommendation factors
   * @returns Recommendation reason
   */
  private generateRecommendationReason(
    paymentMethod: PaymentMethod,
    factors: { factor: string; score: number; weight: number }[]
  ): string {
    // Sort factors by weighted score (descending)
    const sortedFactors: unknown = [...factors].sort((a, b) => b.score * b.weight - a.score * a.weight);

    // Get top factor
    const topFactor: unknown = sortedFactors[0];

    // Generate reason based on top factor
    switch (topFactor.factor) {
      case 'customerHistory':
        return `You've successfully used ${paymentMethod.name} before`;
      case 'successRate':
        return `${paymentMethod.name} has a high success rate`;
      case 'popularity':
        return `${paymentMethod.name} is popular among our customers`;
      case 'geography':
        return `${paymentMethod.name} is well-suited for your location`;
      case 'amount':
        return `${paymentMethod.name} is ideal for this transaction amount`;
      case 'device':
        return `${paymentMethod.name} works well on your device`;
      default:
        return `${paymentMethod.name} is recommended for this transaction`;
    }
  }

  /**
   * Get merchant's recommendation weights
   * @param merchantId Merchant ID
   * @returns Recommendation weights
   */
  private async getMerchantRecommendationWeights(
    merchantId: number
  ): Promise<RecommendationWeights> {
    try {
      // Get merchant's recommendation settings
      const settings: Record<string, unknown> = await this.prisma.merchantSettings.findUnique({
        where: { merchantId },
        select: { recommendationWeights: true },
      });

      if (!settings || !settings.recommendationWeights) {
        return this.defaultWeights;
      }

      // Parse weights
      const weights: unknown = JSON.parse(settings.recommendationWeights);

      // Validate weights
      const validWeights: RecommendationWeights = {
        customerHistory: this.validateWeight(
          weights.customerHistory,
          this.defaultWeights.customerHistory
        ),
        successRate: this.validateWeight(weights.successRate, this.defaultWeights.successRate),
        popularity: this.validateWeight(weights.popularity, this.defaultWeights.popularity),
        geography: this.validateWeight(weights.geography, this.defaultWeights.geography),
        amount: this.validateWeight(weights.amount, this.defaultWeights.amount),
        device: this.validateWeight(weights.device, this.defaultWeights.device),
      };

      return validWeights;
    } catch (error) {
      console.error('Error getting merchant recommendation weights:', error);
      return this.defaultWeights;
    }
  }

  /**
   * Validate weight value
   * @param weight Weight value
   * @param defaultValue Default weight value
   * @returns Validated weight value
   */
  private validateWeight(weight, defaultValue: number): number {
    if (typeof weight !== 'number' || isNaN(weight) || weight < 0 || weight > 1) {
      return defaultValue;
    }

    return weight;
  }
}
