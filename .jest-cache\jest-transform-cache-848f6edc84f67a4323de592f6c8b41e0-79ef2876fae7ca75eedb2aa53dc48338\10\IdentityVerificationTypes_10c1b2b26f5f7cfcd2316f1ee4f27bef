9de74e9a53838dd30ec367b8ac834c79
"use strict";
/**
 * Identity Verification Types and Interfaces
 *
 * Centralized type definitions for the identity verification system.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.IdentityVerificationErrorCode = void 0;
/**
 * Error codes for identity verification
 */
var IdentityVerificationErrorCode;
(function (IdentityVerificationErrorCode) {
    IdentityVerificationErrorCode["INVALID_SIGNATURE"] = "INVALID_SIGNATURE";
    IdentityVerificationErrorCode["INVALID_ADDRESS"] = "INVALID_ADDRESS";
    IdentityVerificationErrorCode["INVALID_PROOF"] = "INVALID_PROOF";
    IdentityVerificationErrorCode["VERIFICATION_FAILED"] = "VERIFICATION_FAILED";
    IdentityVerificationErrorCode["VERIFICATION_NOT_FOUND"] = "VERIFICATION_NOT_FOUND";
    IdentityVerificationErrorCode["CLAIM_NOT_FOUND"] = "CLAIM_NOT_FOUND";
    IdentityVerificationErrorCode["INTERNAL_ERROR"] = "INTERNAL_ERROR";
    IdentityVerificationErrorCode["PROVIDER_ERROR"] = "PROVIDER_ERROR";
    IdentityVerificationErrorCode["INVALID_PARAMETERS"] = "INVALID_PARAMETERS";
    IdentityVerificationErrorCode["UNAUTHORIZED"] = "UNAUTHORIZED";
})(IdentityVerificationErrorCode || (exports.IdentityVerificationErrorCode = IdentityVerificationErrorCode = {}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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