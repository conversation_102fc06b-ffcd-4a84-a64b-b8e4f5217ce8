/**
 * Express Type Definitions
 *
 * This file contains type definitions for Express request and response handling.
 */

import { Request, Response, NextFunction } from 'express';
import { AuthenticatedUser, UUID, UserRole } from './index';

// Extended Express Request with authenticated user
export interface AuthRequest extends Request {
  user?: AuthenticatedUser;
  merchantId?: UUID;
}

// Controller method types
export type ControllerMethod = (req: Request, res: Response, next: NextFunction) => Promise<unknown>;
export type AuthControllerMethod = (
  req: AuthRequest,
  res: Response,
  next: NextFunction
) => Promise<unknown>;

// Middleware types
export type Middleware = (req: Request, res: Response, next: NextFunction) => void;
export type AuthMiddleware = (req: AuthRequest, res: Response, next: NextFunction) => void;

// Role-based middleware factory
export type RoleMiddlewareFactory = (roles: UserRole[]) => Middleware;

// Error handler type
export type ErrorHandler = (err, req: Request, res: Response, next: NextFunction) => void;

// Validation middleware types
export interface ValidationSchema {
  [key: string]: unknown;
}

export type ValidationMiddlewareFactory = (schema: ValidationSchema) => Middleware;

// Async handler wrapper type
export type AsyncHandler = <T extends ControllerMethod>(fn: T) => T;

// Response utility types
export interface ResponseUtils {
  success: <T>(res: Response, data: T, message?: string, statusCode?: number) => Response;
  error: (res: Response, message: string, statusCode?: number, error?: unknown) => Response;
  notFound: (res: Response, message?: string) => Response;
  badRequest: (res: Response, message?: string, error?: unknown) => Response;
  unauthorized: (res: Response, message?: string) => Response;
  forbidden: (res: Response, message?: string) => Response;
  serverError: (res: Response, message?: string, error?: unknown) => Response;
}

// No default export needed - types and interfaces are already exported
