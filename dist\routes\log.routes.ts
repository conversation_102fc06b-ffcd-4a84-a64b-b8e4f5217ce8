// jscpd:ignore-file

import { Router } from "express";
import { body } from "express-validator";
import logController from "../controllers/log.controller";
import { authenticate, authorize } from "../middlewares/auth.middleware";
import { validate } from "../middlewares/validation.middleware";
import { body } from "express-validator";
import { authenticate, authorize } from "../middlewares/auth.middleware";
import { validate } from "../middlewares/validation.middleware";

const router: any =Router();

// Admin-only route to view system logs
router.get(
    "/",
    authenticate,
    authorize(["admin"]),
    logController.getAllLogs
);

// Internal route to create logs
router.post(
    "/",
    authenticate,
    validate([
        body("level").isIn(["info", "warning", "error"]),
        body("message").notEmpty(),
        body("source").notEmpty()
    ]),
    logController.createLog
);

export default router;
