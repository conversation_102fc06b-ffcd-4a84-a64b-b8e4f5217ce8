"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.VersionController = void 0;
const BaseController_1 = require("../../core/BaseController");
const VersionRegistry_1 = require("../../core/VersionRegistry");
/**
 * Version controller
 * This controller handles version-related requests
 */
class VersionController extends BaseController_1.BaseController {
    /**
     * Create a new version controller
     */
    constructor() {
        super();
        /**
         * Get all versions
         */
        this.getAllVersions = this.createHandler(async (req, res) => {
            const versions = this.versionRegistry.getAllVersions();
            const currentVersion = this.versionRegistry.getCurrentVersion();
            return this.sendSuccess(res, {
                versions,
                current: currentVersion
            });
        });
        /**
         * Get version by name
         */
        this.getVersionByName = this.createHandler(async (req, res) => {
            const { version } = req.params;
            try {
                const versionInfo = this.versionRegistry.getVersion(version);
                return this.sendSuccess(res, versionInfo);
            }
            catch (error) {
                return this.sendError(res, 404, `Version not found: ${version}`);
            }
        });
        /**
         * Get current version
         */
        this.getCurrentVersion = this.createHandler(async (req, res) => {
            const currentVersion = this.versionRegistry.getCurrentVersion();
            const versionInfo = this.versionRegistry.getVersion(currentVersion);
            return this.sendSuccess(res, versionInfo);
        });
        /**
         * Get active versions
         */
        this.getActiveVersions = this.createHandler(async (req, res) => {
            const versions = this.versionRegistry.getActiveVersions();
            return this.sendSuccess(res, {
                versions,
                count: versions.length
            });
        });
        /**
         * Get deprecated versions
         */
        this.getDeprecatedVersions = this.createHandler(async (req, res) => {
            const versions = this.versionRegistry.getDeprecatedVersions();
            return this.sendSuccess(res, {
                versions,
                count: versions.length
            });
        });
        /**
         * Register a new version
         */
        this.registerVersion = this.createHandler(async (req, res) => {
            // Check authorization
            const { userRole } = this.checkAuthorization(req);
            // Only admins can register versions
            if (userRole !== "ADMIN") {
                return this.sendError(res, 403, "Only admins can register versions");
            }
            const { version, status, releaseDate, sunsetDate, description } = req.body;
            // Validate required fields
            if (!version || !status || !releaseDate) {
                return this.sendError(res, 400, "Missing required fields: version, status, releaseDate");
            }
            // Check if version already exists
            if (this.versionRegistry.hasVersion(version)) {
                return this.sendError(res, 409, `Version already exists: ${version}`);
            }
            // Register version
            this.versionRegistry.registerVersion(version, status, new Date(releaseDate), sunsetDate ? new Date(sunsetDate) : undefined, description);
            // Get version info
            const versionInfo = this.versionRegistry.getVersion(version);
            return this.sendSuccess(res, versionInfo, 201);
        });
        /**
         * Update version status
         */
        this.updateVersionStatus = this.createHandler(async (req, res) => {
            // Check authorization
            const { userRole } = this.checkAuthorization(req);
            // Only admins can update version status
            if (userRole !== "ADMIN") {
                return this.sendError(res, 403, "Only admins can update version status");
            }
            const { version } = req.params;
            const { status, sunsetDate } = req.body;
            // Validate required fields
            if (!status) {
                return this.sendError(res, 400, "Missing required field: status");
            }
            // Check if version exists
            if (!this.versionRegistry.hasVersion(version)) {
                return this.sendError(res, 404, `Version not found: ${version}`);
            }
            // Update version status
            this.versionRegistry.updateVersionStatus(version, status, sunsetDate ? new Date(sunsetDate) : undefined);
            // Get version info
            const versionInfo = this.versionRegistry.getVersion(version);
            return this.sendSuccess(res, versionInfo);
        });
        /**
         * Set current version
         */
        this.setCurrentVersion = this.createHandler(async (req, res) => {
            // Check authorization
            const { userRole } = this.checkAuthorization(req);
            // Only admins can set current version
            if (userRole !== "ADMIN") {
                return this.sendError(res, 403, "Only admins can set current version");
            }
            const { version } = req.body;
            // Validate required fields
            if (!version) {
                return this.sendError(res, 400, "Missing required field: version");
            }
            // Check if version exists
            if (!this.versionRegistry.hasVersion(version)) {
                return this.sendError(res, 404, `Version not found: ${version}`);
            }
            // Set current version
            this.versionRegistry.setCurrentVersion(version);
            // Get version info
            const versionInfo = this.versionRegistry.getVersion(version);
            return this.sendSuccess(res, versionInfo);
        });
        this.versionRegistry = VersionRegistry_1.VersionRegistry.getInstance();
        this.initializeVersions();
    }
    /**
     * Initialize versions
     */
    initializeVersions() {
        // Register v1
        if (!this.versionRegistry.hasVersion("v1")) {
            this.versionRegistry.registerVersion("v1", VersionRegistry_1.VersionStatus.ACTIVE, new Date("2023-01-01"), undefined, "Initial API version");
        }
        // Set current version
        this.versionRegistry.setCurrentVersion("v1");
    }
}
exports.VersionController = VersionController;
exports.default = VersionController;
//# sourceMappingURL=version.controller.js.map