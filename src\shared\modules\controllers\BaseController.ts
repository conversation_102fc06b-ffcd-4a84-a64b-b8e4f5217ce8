/**
 * Base Controller
 * 
 * This is a base controller class that provides common functionality
 * for all controllers in the application.
 */

import { Request, Response, NextFunction } from 'express';

export class BaseController {
  /**
   * Send a success response
   */
  protected sendSuccess(res: Response, data: unknown = {}, message: string = 'Success', statusCode: number = 200) {
    return res.status(statusCode).json({
      success: true,
      message,
      data
    });
  }

  /**
   * Send an error response
   */
  protected sendError(res: Response, message: string = 'Error', statusCode: number = 500, error: Error = null) {
    return res.status(statusCode).json({
      success: false,
      message,
      error: error ? ((error as Error).message || error) : null
    });
  }

  /**
   * Handle async controller methods
   */
  protected asyncHandler(fn: (req: Request, res: Response, next: NextFunction) => Promise<unknown>) {
    return async (req: Request, res: Response, next: NextFunction) => {
      try {
        await fn(req, res, next);
      } catch (error) {
        next(error);
      }
    };
  }

  /**
   * Validate request
   */
  protected validateRequest(req: Request, schema: unknown) {
    if (!schema) return true;
    
    const { error } = schema.validate(req.body);
    return error ? error.details[0].message : null;
  }
}