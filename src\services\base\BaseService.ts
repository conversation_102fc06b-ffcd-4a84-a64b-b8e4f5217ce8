import { BaseService } from '../base/BaseService';
import { ServiceError } from '../utils/errors/ServiceError';
import { logger } from '../utils/logger';
import { Repository } from '../types/database';
// jscpd:ignore-file
/**
 * Base Service Class
 * 
 * This class provides common (...args: any[]) => anyality for all services.
 */

import { AppError } from '../utils/appError';
import { ServiceError } from '../utils/errors/ServiceError';
import { logger } from '../utils/logger';

export class BaseService {
  protected model: any;
  
  constructor(model: any) {
    super(null);
    // Initialize service
  }
  /**
   * Create an error
   * @param message Error message
   * @param statusCode HTTP status code
   * @param errorCode Error code
   * @param resource Resource name
   * @param id Resource ID
   * @param validationErrors Validation errors
   * @returns ServiceError
   */
  protected createError(
    message: string,
    statusCode: number = 400,
    errorCode: string = 'SERVICE_ERROR',
    resource?: string,
    id?: string,
    validationErrors?: Record<string, string>
  ): ServiceError {
    return new ServiceError(
      message,
      statusCode,
      errorCode,
      resource,
      id,
      validationErrors
    );
  }

  /**
   * Validate a field
   * @param field Field to validate
   * @param errorMessage Error message
   * @throws ServiceError if field is falsy
   */
  protected validateField(field: unknown, errorMessage: string): any {
    if (!field) {
      throw this.createError(errorMessage, 400, 'VALIDATION_ERROR');
    }
  }

  /**
   * Call a repository method
   * @param repository Repository to call
   * @param method Method to call
   * @param args Arguments to pass to the method
   * @returns Result of the repository method
   */
  protected async callRepository(repository: unknown, method: string, ...args: any[]): Promise<unknown> {
    try {
      return await repository[method](...args);
    } catch (error: any) {
      throw this.handleError(error);
    }
  }

  /**
   * Handle an error
   * @param error Error to handle
   * @returns ServiceError
   */
  protected handleError(error: Error): ServiceError {
    logger.error(`Service error:`, error);

    if (error instanceof ServiceError) {
      return error;
    }

    if (error instanceof AppError) {
      return this.createError(
        (error as Error).message,
        error.statusCode,
        'SERVICE_ERROR'
      );
import { Repository } from '../types/database';


    }

    // Handle database errors
    if (error.code === 'P2002') {
      return this.createError(
        'A record with this value already exists.',
        409,
        'DUPLICATE_RECORD'
      );
    }

    if (error.code === 'P2025') {
      return this.createError(
        'Record not found.',
        404,
        'RECORD_NOT_FOUND'
      );
    }

    return this.createError(
      'An unexpected error occurred.',
      500,
      'INTERNAL_SERVER_ERROR'
    );
  }

  /**
   * Create a success response
   * @param data Response data
   * @returns Success response
   */
  protected createSuccessResponse<T>(data: T): { success: true; data: T } {
    return {
      success: true,
      data
    };
  }

  /**
   * Create a paginated response
   * @param data Response data
   * @param total Total number of records
   * @param page Current page
   * @param limit Records per page
   * @returns Paginated response
   */

}