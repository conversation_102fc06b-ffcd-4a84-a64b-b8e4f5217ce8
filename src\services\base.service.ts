// jscpd:ignore-file
import { PrismaClient } from '@prisma/client';
import { ApiErrorCode } from '../middlewares/apiResponseMiddleware';
import prisma from '../lib/prisma';
import { logger } from '../utils/logger';
import { Transaction } from '../types';

/**
 * Base service error class
 */
export class ServiceError extends Error {
  statusCode: number;
  errorCode: ApiErrorCode;
  resource?: string;
  id?: string | number;
  validationErrors?: Record<string, string[]>;

  constructor(
    message: string,
    statusCode: number = 500,
    errorCode: ApiErrorCode = ApiErrorCode.GENERIC_ERROR,
    resource?: string,
    id?: string | number,
    validationErrors?: Record<string, string[]>
  ) {
    super(message);
    this.name = this.constructor.name;
    this.statusCode = statusCode;
    this.errorCode = errorCode;
    this.resource = resource;
    this.id = id;
    this.validationErrors = validationErrors;

    // Capture stack trace
    Error.captureStackTrace(this, this.constructor);
  }

  /**
   * Create a not found error
   * @param resource Resource that was not found
   * @param id ID of the resource
   * @returns Not found error
   */
  static notFound(resource: string, id?: string | number): ServiceError {
    const message: unknown = id ? `${resource} with ID ${id} not found` : `${resource} not found`;

    return new ServiceError(message, 404, ApiErrorCode.NOT_FOUND, resource, id);
  }

  /**
   * Create a validation error
   * @param validationErrors Validation errors
   * @param message Error message
   * @returns Validation error
   */
  static validation(
    validationErrors: Record<string, string[]>,
    message: string = 'Validation failed'
  ): ServiceError {
    return new ServiceError(
      message,
      400,
      ApiErrorCode.VALIDATION_ERROR,
      undefined,
      undefined,
      validationErrors
    );
  }

  /**
   * Create an authentication error
   * @param message Error message
   * @returns Authentication error
   */
  static authentication(message: string = 'Authentication required'): ServiceError {
    return new ServiceError(message, 401, ApiErrorCode.AUTHENTICATION_ERROR);
  }

  /**
   * Create an authorization error
   * @param message Error message
   * @returns Authorization error
   */
  static authorization(message: string = 'Access denied'): ServiceError {
    return new ServiceError(message, 403, ApiErrorCode.AUTHORIZATION_ERROR);
  }

  /**
   * Create a duplicate error
   * @param resource Resource that is duplicate
   * @param id ID of the resource
   * @returns Duplicate error
   */
  static duplicate(resource: string, id?: string | number): ServiceError {
    const message: unknown = id
      ? `${resource} with ID ${id} already exists`
      : `${resource} already exists`;

    return new ServiceError(message, 409, ApiErrorCode.DUPLICATE, resource, id);
  }

  /**
   * Create a payment error
   * @param message Error message
   * @returns Payment error
   */
  static payment(message: string): ServiceError {
    return new ServiceError(message, 400, ApiErrorCode.PAYMENT_ERROR);
  }

  /**
   * Create a blockchain error
   * @param message Error message
   * @returns Blockchain error
   */
  static blockchain(message: string): ServiceError {
    return new ServiceError(message, 400, ApiErrorCode.BLOCKCHAIN_ERROR);
  }
}

/**
 * Base service class with common functionality for all services
 */
export class BaseService {
  protected prisma: PrismaClient;
  protected logger = logger;

  constructor() {
    this.prisma = prisma;
  }

  /**
   * Create a not found error
   * @param resource Resource that was not found
   * @param id ID of the resource
   * @returns Not found error
   */
  protected notFoundError(resource: string, id?: string | number): ServiceError {
    return ServiceError.notFound(resource, id);
  }

  /**
   * Create a validation error
   * @param validationErrors Validation errors
   * @param message Error message
   * @returns Validation error
   */
  protected validationError(
    validationErrors: Record<string, string[]>,
    message: string = 'Validation failed'
  ): ServiceError {
    return ServiceError.validation(validationErrors, message);
  }

  /**
   * Create an authentication error
   * @param message Error message
   * @returns Authentication error
   */
  protected authenticationError(message: string = 'Authentication required'): ServiceError {
    return ServiceError.authentication(message);
  }

  /**
   * Create an authorization error
   * @param message Error message
   * @returns Authorization error
   */
  protected authorizationError(message: string = 'Access denied'): ServiceError {
    return ServiceError.authorization(message);
  }

  /**
   * Create a duplicate error
   * @param resource Resource that is duplicate
   * @param id ID of the resource
   * @returns Duplicate error
   */
  protected duplicateError(resource: string, id?: string | number): ServiceError {
    return ServiceError.duplicate(resource, id);
  }

  /**
   * Create a payment error
   * @param message Error message
   * @returns Payment error
   */
  protected paymentError(message: string): ServiceError {
    return ServiceError.payment(message);
  }

  /**
   * Create a blockchain error
   * @param message Error message
   * @returns Blockchain error
   */
  protected blockchainError(message: string): ServiceError {
    return ServiceError.blockchain(message);
  }

  /**
   * Create a generic error
   * @param message Error message
   * @param statusCode HTTP status code
   * @param errorCode Error code
   * @returns Generic error
   */
  protected genericError(
    message: string,
    statusCode: number = 500,
    errorCode: ApiErrorCode = ApiErrorCode.GENERIC_ERROR
  ): ServiceError {
    return new ServiceError(message, statusCode, errorCode);
  }

  /**
   * Execute a database operation with error handling
   * @param operation Function that performs the database operation
   * @param errorMessage Error message to use if the operation fails
   * @param resource Resource name for error reporting
   * @returns Result of the operation
   * @throws ServiceError if the operation fails
   */
  protected async executeDbOperation<T>(
    operation: () => Promise<T>,
    errorMessage: string,
    resource?: string
  ): Promise<T> {
    try {
      return await operation();
    } catch (error) {
      this.logger.error(`${errorMessage}: ${(error as Error).message || error}`);

      if (error instanceof ServiceError) {
        throw error;
      }

      throw this.genericError(errorMessage);
    }
  }

  /**
   * Check if a record exists
   * @param model Prisma model name
   * @param where Where condition
   * @param resource Resource name for error reporting
   * @param id ID for error reporting
   * @returns The record if it exists
   * @throws ServiceError if the record doesn't exist
   */
  protected async checkExists<T>(
    model: unknown,
    where: unknown,
    resource: string,
    id?: string | number
  ): Promise<T> {
    return this.executeDbOperation(
      async () => {
        const record: unknown = await model.findUnique({ where });

        if (!record) {
          throw this.notFoundError(resource, id);
        }

        return record;
      },
      `Error checking if ${resource} exists`,
      resource
    );
  }

  /**
   * Create a record with error handling
   * @param model Prisma model name
   * @param data Data to create
   * @param resource Resource name for error reporting
   * @returns Created record
   * @throws ServiceError if the creation fails
   */
  protected async createRecord<T>(model: unknown, data: unknown, resource: string): Promise<T> {
    return this.executeDbOperation(
      () => model.create({ data }),
      `Failed to create ${resource}`,
      resource
    );
  }

  /**
   * Update a record with error handling
   * @param model Prisma model name
   * @param where Where condition
   * @param data Data to update
   * @param resource Resource name for error reporting
   * @returns Updated record
   * @throws ServiceError if the update fails
   */
  protected async updateRecord<T>(model: unknown, where: unknown, data: unknown, resource: string): Promise<T> {
    return this.executeDbOperation(
      () => model.update({ where, data }),
      `Failed to update ${resource}`,
      resource
    );
  }

  /**
   * Delete a record with error handling
   * @param model Prisma model name
   * @param where Where condition
   * @param resource Resource name for error reporting
   * @returns Deleted record
   * @throws ServiceError if the deletion fails
   */
  protected async deleteRecord<T>(model: unknown, where: unknown, resource: string): Promise<T> {
    return this.executeDbOperation(
      () => model.delete({ where }),
      `Failed to delete ${resource}`,
      resource
    );
  }

  /**
   * Find many records with error handling
   * @param model Prisma model name
   * @param args Query arguments
   * @param resource Resource name for error reporting
   * @returns Records
   * @throws ServiceError if the query fails
   */
  protected async findMany<T>(model: unknown, args: unknown, resource: string): Promise<T[]> {
    return this.executeDbOperation(
      () => model.findMany(args),
      `Failed to fetch ${resource} records`,
      resource
    );
  }

  /**
   * Find a unique record with error handling
   * @param model Prisma model name
   * @param args Query arguments
   * @param resource Resource name for error reporting
   * @param throwIfNotFound Whether to throw an error if the record is not found
   * @returns Record or null if not found and throwIfNotFound is false
   * @throws ServiceError if the query fails or if the record is not found and throwIfNotFound is true
   */
  protected async findUnique<T>(
    model: unknown,
    args: unknown,
    resource: string,
    throwIfNotFound = false
  ): Promise<T | null> {
    return this.executeDbOperation(
      async () => {
        const record: unknown = await model.findUnique(args);

        if (!record && throwIfNotFound) {
          throw this.notFoundError(resource, args.where?.id);
        }

        return record;
      },
      `Failed to fetch ${resource}`,
      resource
    );
  }

  /**
   * Execute a transaction with error handling
   * @param fn Transaction function
   * @param errorMessage Error message to use if the transaction fails
   * @returns Result of the transaction
   * @throws ServiceError if the transaction fails
   */
  protected async transaction<T>(
    fn: (prisma: PrismaClient) => Promise<T>,
    errorMessage = 'Transaction failed'
  ): Promise<T> {
    return this.executeDbOperation(() => this.prisma.$transaction(fn), errorMessage);
  }
}
