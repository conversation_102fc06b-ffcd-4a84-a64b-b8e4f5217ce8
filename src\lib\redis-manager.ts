// jscpd:ignore-file
/**
 * Redis Manager
 *
 * Centralized Redis client management for the application.
 * This service provides a singleton Redis client that can be used across the application.
 * It handles connection, authentication, and error handling.
 */

import { createClient, RedisClientType } from 'redis';
import { logger } from './logger';
import { isProduction } from '../utils/environment-validator';
import { logger } from './logger';
import { isProduction } from '../utils/environment-validator';

// In-memory store for fallback when Redis is not available
class MemoryStore {
  private store: Map<string, { value: any; expiry?: number }> = new Map();

  async get(key: string): Promise<any> {
    const item: any = this.store.get(key);
    if (!item) return null;

    // Check if item has expired
    if (item.expiry && item.expiry < Date.now()) {
      this.store.delete(key);
      return null;
    }

    return item.value;
  }

  async set(key: string, value: any, expiry?: number): Promise<void> {
    const expiryMs: unknown = expiry ? Date.now() + expiry * 1000 : undefined;
    this.store.set(key, { value, expiry: expiryMs });
  }

  async del(key: string): Promise<void> {
    this.store.delete(key);
  }

  getIsConnected(): boolean {
    return true; // Memory store is always connected
  }

  // Clear expired items periodically
  startCleanup(): void {}
}

// Create memory store instance
const memoryStore: unknown = new MemoryStore();
memoryStore.startCleanup();

/**
 * Redis Manager Class
 */
class RedisManager {
  private static instance: RedisManager;
  private client: RedisClientType | null = null;
  private isRedisAvailable = false;
  private isConnected = false;
  private connectionAttempts = 0;
  private maxConnectionAttempts = isProduction() ? 5 : 3;

  private constructor() {
    this.initialize();
  }

  /**
   * Get Redis Manager instance (Singleton)
   */
  public static getInstance(): RedisManager {
    if (!RedisManager.instance) {
      RedisManager.instance = new RedisManager();
    }
    return RedisManager.instance;
  }

  /**
   * Initialize Redis client
   */
  private async initialize(): Promise<void> {
    try {
      // Check if Redis URL is configured
      const redisUrl: unknown = process.env.REDIS_URL ?? 'redis://localhost:6379';

      if (!redisUrl) {
        logger.warn('Redis URL not configured, using in-memory store');
        this.isRedisAvailable = false;
        return;
      }

      // Parse Redis URL to extract host, port, and password
      let username: string = '';
      let password = '';
      let host: string = 'localhost';
      let port: number = 6379;

      try {
        const url: unknown = new URL(redisUrl);
        host = url.hostname ?? 'localhost';
        port = parseInt(url.port ?? '6379', 10);

        if (url.username) {
          username = url.username;
        }

        if (url.password) {
          password = url.password;
        }

        logger.info(
          `Redis configuration: host=${host}, port=${port}, auth=${password ? 'yes' : 'no'}`
        );
      } catch (parseError) {
        logger.error('Failed to parse Redis URL:', parseError);
      }

      // Create Redis client with proper configuration
      const redisOptions: unknown = {
        socket: {
          host,
          port,
          reconnectStrategy: (retries: number) => {
            // Maximum retry delay is 5 seconds in production, 3 seconds otherwise
            const maxDelay: unknown = isProduction() ? 5000 : 3000;
            const delay = Math.min(retries * 500, maxDelay);
            return delay;
          },
          connectTimeout: isProduction() ? 10000 : 5000, // 10 seconds timeout in production, 5 seconds otherwise
        },
      };

      // Add username and password if provided
      if (username ?? password) {
        redisOptions['username'] = username;
        redisOptions['password'] = password;
      }

      // Create Redis client
      this.client = createClient(redisOptions);

      // Set up event handlers
      this.client.on('connect', () => {
        logger.info('Redis client connected');
        this.isConnected = true;
        this.isRedisAvailable = true;
      });

      this.client.on('error', (error) => {
        logger.error('Redis client error:', {
          code: error.code,
          message: (error as Error).message,
        });

        this.connectionAttempts++;

        if (this.connectionAttempts >= this.maxConnectionAttempts) {
          logger.warn(
            `Maximum Redis connection attempts (${this.maxConnectionAttempts}) reached, using in-memory store`
          );
          this.isRedisAvailable = false;
          this.isConnected = false;

          // Close Redis client to prevent further connection attempts
          this.client?.quit().catch((err) => {
            logger.error('Error closing Redis client:', err);
          });

          this.client = null;

          // In production, log a warning but continue
          if (isProduction()) {
            logger.warn(
              'Redis is not available in production mode. Using in-memory store as fallback.'
            );
          }
        }
      });

      this.client.on('reconnecting', () => {
        logger.info('Redis client reconnecting');
        this.isConnected = false;
      });

      this.client.on('end', () => {
        logger.info('Redis client disconnected');
        this.isConnected = false;
      });

      // Connect to Redis with proper error handling
      try {
        await this.client.connect();

        // Test connection with a PING command
        const pingResult = await this.client.ping();
        if (pingResult === 'PONG') {
          logger.info('Redis connection test successful');
          this.isRedisAvailable = true;
          this.isConnected = true;
        } else {
          throw new Error(`Unexpected response from Redis ping: ${pingResult}`);
        }
      } catch (connectError) {
        logger.error('Failed to connect to Redis:', connectError);
        logger.warn('Using in-memory store as fallback');
        this.isRedisAvailable = false;
        this.isConnected = false;
        this.client = null;
      }
    } catch (error) {
      logger.error('Failed to initialize Redis client:', error);
      logger.warn('Using in-memory store as fallback');
      this.isRedisAvailable = false;
      this.isConnected = false;
      this.client = null;
    }
  }

  /**
   * Get a value from Redis or memory store
   * @param key Key
   * @returns Value or null if not found
   */
  async get(key: string): Promise<any> {
    try {
      if (this.isRedisAvailable && this.client) {
        return await this.client.get(key);
      } else {
        return await memoryStore.get(key);
      }
    } catch (error) {
      logger.error('Error getting value from Redis:', error);
      return await memoryStore.get(key);
    }
  }

  /**
   * Set a value in Redis or memory store
   * @param key Key
   * @param value Value
   * @param expiry Expiry in seconds
   */
  async set(key: string, value: any, expiry?: number): Promise<void> {
    try {
      if (this.isRedisAvailable && this.client) {
        if (expiry) {
          await this.client.set(key, value, { EX: expiry });
        } else {
          await this.client.set(key, value);
        }
      } else {
        await memoryStore.set(key, value, expiry);
      }
    } catch (error) {
      logger.error('Error setting value in Redis:', error);
      await memoryStore.set(key, value, expiry);
    }
  }

  /**
   * Delete a key from Redis or memory store
   * @param key Key
   */
  async del(key: string): Promise<void> {
    try {
      if (this.isRedisAvailable && this.client) {
        await this.client.del(key);
      } else {
        await memoryStore.del(key);
      }
    } catch (error) {
      logger.error('Error deleting key from Redis:', error);
      await memoryStore.del(key);
    }
  }

  /**
   * Get connection status
   * @returns True if connected to Redis or using memory store
   */
  getIsConnected(): boolean {
    return this.isRedisAvailable ? this.isConnected : memoryStore.getIsConnected();
  }

  /**
   * Get Redis client
   * @returns Redis client or null if not available
   */
  getClient(): RedisClientType | null {
    return this.client;
  }

  /**
   * Check if Redis is available
   * @returns True if Redis is available
   */
  isRedisEnabled(): boolean {
    return this.isRedisAvailable;
  }

  /**
   * Close Redis client
   */
  async close(): Promise<void> {
    if (this.client) {
      try {
        await this.client.quit();
        this.isConnected = false;
        logger.info('Redis client closed');
      } catch (error) {
        logger.error('Error closing Redis client:', error);
      }
    }
  }
}

// Export Redis manager instance
export const redisManager = RedisManager.getInstance();
export default redisManager;
