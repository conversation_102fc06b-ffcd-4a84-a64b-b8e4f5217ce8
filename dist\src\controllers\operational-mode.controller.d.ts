/**
 * Operational Mode Controller
 *
 * Handles operational mode operations.
 */
declare global {
    namespace Express {
        interface Request {
            user?: {
                id: string;
                role: string;
                merchantId?: string;
            };
        }
    }
}
/**
 * Get current operational mode
 */
export declare const getCurrentMode: any;
/**
 * Set operational mode
 */
export declare const setOperationalMode: any;
/**
 * Enable or disable the system
 */
export declare const setSystemEnabled: any;
declare const _default: {
    getCurrentMode: any;
    setOperationalMode: any;
    setSystemEnabled: any;
};
export default _default;
//# sourceMappingURL=operational-mode.controller.d.ts.map