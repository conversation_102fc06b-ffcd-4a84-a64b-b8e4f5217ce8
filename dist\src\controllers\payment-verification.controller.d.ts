import { BaseController } from "./base/BaseController";
export declare class PaymentVerificationController extends BaseController {
    private paymentVerificationService;
    constructor();
    /**
     * Send success response
     * @param res Response
     * @param data Response data
     * @param statusCode Status code
     * @returns Response
     */
    private sendSuccess;
    /**
     * Verify a payment
     */
    verifyPayment: any;
    /**
     * Verify a Binance Pay payment
     */
    verifyBinancePayPayment: any;
    /**
     * Verify a Binance C2C payment
     */
    verifyBinanceC2CPayment: any;
    /**
     * Verify a Binance TRC20 payment
     */
    verifyBinanceTRC20Payment: any;
    /**
     * Verify a crypto transfer payment
     */
    verifyCryptoTransferPayment: any;
}
//# sourceMappingURL=payment-verification.controller.d.ts.map