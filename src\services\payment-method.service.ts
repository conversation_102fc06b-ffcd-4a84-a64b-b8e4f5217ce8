// jscpd:ignore-file
import { PaymentMethod, Prisma } from '@prisma/client';
import { GenericService } from '../../core/GenericService';
import { PaymentMethodRepository } from '../../repositories/refactored/payment-method.repository';
import { ErrorFactory } from '../../utils/errors/ErrorFactory';
import { logger } from '../../lib/logger';
import { RepositoryFactory } from '../../factories/RepositoryFactory';
import { Merchant } from '../types';

/**
 * Payment method service
 * This service handles business logic for payment methods
 */
export class PaymentMethodService extends GenericService<
  PaymentMethod,
  Prisma.PaymentMethodCreateInput,
  Prisma.PaymentMethodUpdateInput
> {
  private paymentMethodRepository: PaymentMethodRepository;

  /**
   * Create a new payment method service
   */
  constructor() {
    const repositoryFactory: unknown = RepositoryFactory.getInstance();
    const repository: unknown = repositoryFactory.getRepository<
      PaymentMethod,
      Prisma.PaymentMethodCreateInput,
      Prisma.PaymentMethodUpdateInput
    >('paymentMethod') as PaymentMethodRepository;

    super(repository, 'PaymentMethod');
    this.paymentMethodRepository = repository;
  }

  /**
   * Get payment methods with pagination
   * @param options Query options
   * @returns Paginated payment methods
   */
  async getPaymentMethods(options: {
    merchantId?: string;
    type?: string;
    limit?: number;
    offset?: number;
  }): Promise<{ data: PaymentMethod[]; total: number }> {
    try {
      return await this.paymentMethodRepository.findPaymentMethods(options);
    } catch (error) {
      logger.error('Error getting payment methods:', error);
      throw ErrorFactory.handle(error);
    }
  }

  /**
   * Get a payment method by ID
   * @param id Payment method ID
   * @returns Payment method or null
   */
  async getPaymentMethodById(id: string): Promise<PaymentMethod | null> {
    try {
      return await this.repository.findById(id);
    } catch (error) {
      logger.error(`Error getting payment method by ID ${id}:`, error);
      throw ErrorFactory.handle(error);
    }
  }

  /**
   * Get default payment method for a merchant
   * @param merchantId Merchant ID
   * @returns Default payment method or null
   */
  async getDefaultPaymentMethod(merchantId: string): Promise<PaymentMethod | null> {
    try {
      return await this.paymentMethodRepository.findDefaultByMerchantId(merchantId);
    } catch (error) {
      logger.error(`Error getting default payment method for merchant ${merchantId}:`, error);
      throw ErrorFactory.handle(error);
    }
  }

  /**
   * Create a new payment method
   * @param data Payment method data
   * @returns Created payment method
   */
  async createPaymentMethod(data: {
    name: string;
    type: string;
    config: Record<string, any>;
    isDefault: boolean;
    merchantId: string;
    createdBy: string;
  }): Promise<PaymentMethod> {
    try {
      // If this is the default payment method, unset unknown existing default
      if (data.isDefault) {
        await this.unsetDefaultPaymentMethods(data.merchantId);
      }

      // Create payment method
      const paymentMethod: unknown = await this.repository.create({
        name: data.name,
        type: data.type,
        config: data.config,
        isDefault: data.isDefault,
        merchantId: data.merchantId,
        createdBy: data.createdBy,
      });

      // Log payment method creation
      logger.info(`Payment method created: ${paymentMethod.id}`, {
        paymentMethodId: paymentMethod.id,
        merchantId: paymentMethod.merchantId,
        type: paymentMethod.type,
      });

      return paymentMethod;
    } catch (error) {
      logger.error('Error creating payment method:', error);
      throw ErrorFactory.handle(error);
    }
  }

  /**
   * Update a payment method
   * @param id Payment method ID
   * @param data Payment method data
   * @returns Updated payment method
   */
  async updatePaymentMethod(
    id: string,
    data: Prisma.PaymentMethodUpdateInput
  ): Promise<PaymentMethod> {
    try {
      // Get payment method
      const paymentMethod: unknown = await this.getPaymentMethodById(id);

      // Check if payment method exists
      if (!paymentMethod) {
        throw ErrorFactory.notFound('Payment method', id);
      }

      // If setting as default, unset unknown existing default
      if (data.isDefault === true && !paymentMethod.isDefault) {
        await this.unsetDefaultPaymentMethods(paymentMethod.merchantId);
      }

      // Update payment method
      const updatedPaymentMethod: unknown = await this.repository.update(id, data);

      // Log payment method update
      logger.info(`Payment method updated: ${id}`, {
        paymentMethodId: id,
        updatedFields: Object.keys(data),
      });

      return updatedPaymentMethod;
    } catch (error) {
      logger.error(`Error updating payment method ${id}:`, error);
      throw ErrorFactory.handle(error);
    }
  }

  /**
   * Delete a payment method
   * @param id Payment method ID
   * @returns Deleted payment method
   */
  async deletePaymentMethod(id: string): Promise<PaymentMethod> {
    try {
      // Get payment method
      const paymentMethod: unknown = await this.getPaymentMethodById(id);

      // Check if payment method exists
      if (!paymentMethod) {
        throw ErrorFactory.notFound('Payment method', id);
      }

      // Delete payment method
      const deletedPaymentMethod: unknown = await this.repository.delete(id);

      // If this was the default payment method, set another one as default
      if (paymentMethod.isDefault) {
        const paymentMethods: unknown = await this.getPaymentMethods({
          merchantId: paymentMethod.merchantId,
          limit: 1,
        });

        if (paymentMethods.data.length > 0) {
          await this.setDefaultPaymentMethod(paymentMethods.data[0].id, paymentMethod.merchantId);
        }
      }

      // Log payment method deletion
      logger.info(`Payment method deleted: ${id}`, {
        paymentMethodId: id,
        merchantId: paymentMethod.merchantId,
      });

      return deletedPaymentMethod;
    } catch (error) {
      logger.error(`Error deleting payment method ${id}:`, error);
      throw ErrorFactory.handle(error);
    }
  }

  /**
   * Set a payment method as default
   * @param id Payment method ID
   * @param merchantId Merchant ID
   * @returns Updated payment method
   */
  async setDefaultPaymentMethod(id: string, merchantId: string): Promise<PaymentMethod> {
    try {
      // Unset unknown existing default payment methods
      await this.unsetDefaultPaymentMethods(merchantId);

      // Set the new default payment method
      const updatedPaymentMethod: unknown = await this.repository.update(id, {
        isDefault: true,
      });

      // Log payment method update
      logger.info(`Payment method set as default: ${id}`, {
        paymentMethodId: id,
        merchantId,
      });

      return updatedPaymentMethod;
    } catch (error) {
      logger.error(`Error setting payment method ${id} as default:`, error);
      throw ErrorFactory.handle(error);
    }
  }

  /**
   * Unset default payment methods for a merchant
   * @param merchantId Merchant ID
   */
  private async unsetDefaultPaymentMethods(merchantId: string): Promise<void> {
    try {
      await this.paymentMethodRepository.unsetDefaultByMerchantId(merchantId);
    } catch (error) {
      logger.error(`Error unsetting default payment methods for merchant ${merchantId}:`, error);
      throw ErrorFactory.handle(error);
    }
  }

  /**
   * Verify a payment method
   * @param id Payment method ID
   * @param verificationData Verification data
   * @returns Verification result
   */
  async verifyPaymentMethod(
    id: string,
    verificationData: unknown
  ): Promise<{
    success: boolean;
    message: string;
    details?: unknown;
  }> {
    try {
      // Get payment method
      const paymentMethod: unknown = await this.getPaymentMethodById(id);

      // Check if payment method exists
      if (!paymentMethod) {
        throw ErrorFactory.notFound('Payment method', id);
      }

      // Verify payment method based on type
      switch (paymentMethod.type) {
        case 'CRYPTO_WALLET':
          return this.verifyCryptoWallet(paymentMethod, verificationData);
        case 'BANK_ACCOUNT':
          return this.verifyBankAccount(paymentMethod, verificationData);
        case 'CREDIT_CARD':
          return this.verifyCreditCard(paymentMethod, verificationData);
        default:
          throw ErrorFactory.validation(
            `Verification not supported for payment method type: ${paymentMethod.type}`
          );
      }
    } catch (error) {
      logger.error(`Error verifying payment method ${id}:`, error);
      throw ErrorFactory.handle(error);
    }
  }

  /**
   * Verify a crypto wallet
   * @param paymentMethod Payment method
   * @param verificationData Verification data
   * @returns Verification result
   */
  private async verifyCryptoWallet(
    paymentMethod: PaymentMethod,
    verificationData: unknown
  ): Promise<{
    success: boolean;
    message: string;
    details?: unknown;
  }> {
    // Implementation would depend on the specific verification process
    // This is a placeholder implementation
    return {
      success: true,
      message: 'Crypto wallet verified successfully',
    };
  }

  /**
   * Verify a bank account
   * @param paymentMethod Payment method
   * @param verificationData Verification data
   * @returns Verification result
   */
  private async verifyBankAccount(
    paymentMethod: PaymentMethod,
    verificationData: unknown
  ): Promise<{
    success: boolean;
    message: string;
    details?: unknown;
  }> {
    // Implementation would depend on the specific verification process
    // This is a placeholder implementation
    return {
      success: true,
      message: 'Bank account verified successfully',
    };
  }

  /**
   * Verify a credit card
   * @param paymentMethod Payment method
   * @param verificationData Verification data
   * @returns Verification result
   */
  private async verifyCreditCard(
    paymentMethod: PaymentMethod,
    verificationData: unknown
  ): Promise<{
    success: boolean;
    message: string;
    details?: unknown;
  }> {
    // Implementation would depend on the specific verification process
    // This is a placeholder implementation
    return {
      success: true,
      message: 'Credit card verified successfully',
    };
  }
}
