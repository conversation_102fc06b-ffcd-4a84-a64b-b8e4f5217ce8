"use strict";
/**
 * Admin Controller Types
 *
 * Type definitions for admin controller components.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.PermissionResource = exports.PermissionAction = exports.RoleType = exports.AdminUserStatus = void 0;
/**
 * Admin user status enum
 */
var AdminUserStatus;
(function (AdminUserStatus) {
    AdminUserStatus["ACTIVE"] = "active";
    AdminUserStatus["INACTIVE"] = "inactive";
    AdminUserStatus["SUSPENDED"] = "suspended";
})(AdminUserStatus || (exports.AdminUserStatus = AdminUserStatus = {}));
/**
 * Role type enum
 */
var RoleType;
(function (RoleType) {
    RoleType["SYSTEM"] = "system";
    RoleType["CUSTOM"] = "custom";
})(RoleType || (exports.RoleType = RoleType = {}));
/**
 * Permission action enum
 */
var PermissionAction;
(function (PermissionAction) {
    PermissionAction["CREATE"] = "create";
    PermissionAction["READ"] = "read";
    PermissionAction["UPDATE"] = "update";
    PermissionAction["DELETE"] = "delete";
    PermissionAction["MANAGE"] = "manage";
})(PermissionAction || (exports.PermissionAction = PermissionAction = {}));
/**
 * Permission resource enum
 */
var PermissionResource;
(function (PermissionResource) {
    PermissionResource["USERS"] = "users";
    PermissionResource["MERCHANTS"] = "merchants";
    PermissionResource["TRANSACTIONS"] = "transactions";
    PermissionResource["ROLES"] = "roles";
    PermissionResource["PERMISSIONS"] = "permissions";
    PermissionResource["SETTINGS"] = "settings";
    PermissionResource["REPORTS"] = "reports";
    PermissionResource["AUDIT_LOGS"] = "audit_logs";
})(PermissionResource || (exports.PermissionResource = PermissionResource = {}));
//# sourceMappingURL=AdminControllerTypes.js.map