// jscpd:ignore-file
/**
 * Verification status
 */
export enum VerificationStatus {
  PENDING = "pending",
  VERIFIED = "verified",
  FAILED = "failed",
  EXPIRED = "expired",
}

/**
 * Verification method
 */
export enum VerificationMethod {
  BLOCKCHAIN_API = "blockchain_api",
  BINANCE_PAY_API = "binance_pay_api",
  BINANCE_C2C_API = "binance_c2c_api",
  BINANCE_TRC20_API = "binance_trc20_api",
  MANUAL = "manual",
}

/**
 * Verification result
 */
export interface VerificationResult {
  success: boolean;
  status: VerificationStatus;
  message?: string;
  fromAddress?: string;
  toAddress?: string;
  amount?: number;
  timestamp?: number;
  confirmations?: number;
  blockNumber?: number;
  fee?: number;
}

/**
 * Payment verification message
 */
export interface PaymentVerificationMessage {
  paymentId: string;
  merchantId: string;
  status: VerificationStatus;
  timestamp: string;
  verificationMethod?: string;
  message?: string;
  transactionDetails?: Record<string, unknown>;
}

/**
 * Verification event
 */
export type VerificationEvent =
  | "verification.started"
  | "verification.updated"
  | "verification.completed"
  | "verification.failed"
  | "transaction.updated";
