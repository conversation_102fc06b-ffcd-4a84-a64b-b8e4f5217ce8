{"file": "F:\\Amazing pay flow\\src\\services\\identity-verification\\utils\\BlockchainUtils.ts", "mappings": ";AAAA;;;;GAIG;;;AAEH,mCAAgC;AAEhC;;GAEG;AACU,QAAA,WAAW,GAAG;IACvB,oGAAoG;IACpG,uDAAuD;IACvD,4DAA4D;IAC5D,iFAAiF;CACpF,CAAC;AAEF;;GAEG;AACU,QAAA,UAAU,GAAG;IACtB,sDAAsD;IACtD,0EAA0E;IAC1E,yGAAyG;CAC5G,CAAC;AAEF;;GAEG;AACU,QAAA,uBAAuB,GAAG;IACnC,wLAAwL;CAC3L,CAAC;AAEF;;GAEG;AACH,MAAa,eAAe;IAGxB;;OAEG;IACH,MAAM,CAAC,WAAW;QACd,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACjB,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,8CAA8C,CAAC;YAC9F,IAAI,CAAC,QAAQ,GAAG,IAAI,eAAM,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QACvD,CAAC;QACD,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,cAAc,CAAC,OAAe;QACjC,IAAI,CAAC;YACD,OAAO,eAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QACrC,CAAC;QAAC,MAAM,CAAC;YACL,OAAO,KAAK,CAAC;QACjB,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,gBAAgB,CAAC,OAAe;QACnC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC;YAChC,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAChD,CAAC;QACD,OAAO,eAAM,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,eAAe,CAAC,OAAe,EAAE,SAAiB,EAAE,eAAuB;QAC9E,IAAI,CAAC;YACD,MAAM,gBAAgB,GAAG,eAAM,CAAC,aAAa,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;YAClE,OAAO,gBAAgB,CAAC,WAAW,EAAE,KAAK,eAAe,CAAC,WAAW,EAAE,CAAC;QAC5E,CAAC;QAAC,MAAM,CAAC;YACL,OAAO,KAAK,CAAC;QACjB,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,cAAc,CAAC,OAAe,EAAE,SAAiB;QACpD,OAAO,eAAM,CAAC,aAAa,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,cAAc,CAAC,OAAe,EAAE,GAAU;QAC7C,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QACpC,OAAO,IAAI,eAAM,CAAC,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,QAAQ,CAAC,CAAC;IACvD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,kBAAkB,CAAC,eAAuB;QAC7C,OAAO,IAAI,CAAC,cAAc,CAAC,eAAe,EAAE,mBAAW,CAAC,CAAC;IAC7D,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,iBAAiB,CAAC,eAAuB;QAC5C,OAAO,IAAI,CAAC,cAAc,CAAC,eAAe,EAAE,kBAAU,CAAC,CAAC;IAC5D,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,oBAAoB,CAAC,eAAuB;QAC/C,OAAO,IAAI,CAAC,cAAc,CAAC,eAAe,EAAE,+BAAuB,CAAC,CAAC;IACzE,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,eAAe,CAAC,GAAW;QAC9B,OAAO,eAAM,CAAC,SAAS,CAAC,eAAM,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,eAAe,CAAC,OAAe;QAClC,OAAO,eAAM,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,sBAAsB,CAAC,MAAc,EAAE,wBAAgC,CAAC;QACjF,IAAI,CAAC;YACD,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;YACpC,MAAM,OAAO,GAAG,MAAM,QAAQ,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;YAE7D,IAAI,CAAC,OAAO,EAAE,CAAC;gBACX,OAAO,KAAK,CAAC;YACjB,CAAC;YAED,MAAM,YAAY,GAAG,MAAM,QAAQ,CAAC,cAAc,EAAE,CAAC;YACrD,MAAM,aAAa,GAAG,YAAY,GAAG,OAAO,CAAC,WAAW,CAAC;YAEzD,OAAO,aAAa,IAAI,qBAAqB,CAAC;QAClD,CAAC;QAAC,MAAM,CAAC;YACL,OAAO,KAAK,CAAC;QACjB,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,qBAAqB,CAAC,MAAc;QAC7C,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QACpC,MAAM,CAAC,WAAW,EAAE,OAAO,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YAC7C,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC;YAC/B,QAAQ,CAAC,qBAAqB,CAAC,MAAM,CAAC;SACzC,CAAC,CAAC;QAEH,OAAO;YACH,WAAW;YACX,OAAO;YACP,aAAa,EAAE,OAAO,CAAC,CAAC,CAAC,MAAM,QAAQ,CAAC,cAAc,EAAE,GAAG,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;SACrF,CAAC;IACN,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,QAAyB,EAAE,MAAc,EAAE,MAAa;QAC7E,OAAO,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAC,WAAW,CAAC,GAAG,MAAM,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,WAAW;QACpB,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QACpC,MAAM,OAAO,GAAG,MAAM,QAAQ,CAAC,UAAU,EAAE,CAAC;QAC5C,OAAO,OAAO,CAAC,QAAQ,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC;IACzC,CAAC;;AArJL,0CAsJC;AArJkB,wBAAQ,GAAkC,IAAI,CAAC", "names": [], "sources": ["F:\\Amazing pay flow\\src\\services\\identity-verification\\utils\\BlockchainUtils.ts"], "sourcesContent": ["/**\n * Blockchain Utilities for Identity Verification\n * \n * Common blockchain operations and utilities.\n */\n\nimport { ethers } from \"ethers\";\n\n/**\n * ERC-1484 Identity Registry ABI (simplified)\n */\nexport const ERC1484_ABI = [\n    \"function getIdentity(uint ein) view returns (address[] memory, address[] memory, address[] memory)\",\n    \"function getEIN(address _address) view returns (uint)\",\n    \"function hasIdentity(address _address) view returns (bool)\",\n    \"function isAssociatedAddressFor(uint ein, address _address) view returns (bool)\"\n];\n\n/**\n * ERC-725 Identity ABI (simplified)\n */\nexport const ERC725_ABI = [\n    \"function getKey(bytes32 _key) view returns (bytes32)\",\n    \"function getKeys(bytes32[] memory _keys) view returns (bytes32[] memory)\",\n    \"function execute(uint256 _operation, address _to, uint256 _value, bytes memory _data) returns (bytes32)\"\n];\n\n/**\n * Polygon ID Verifier ABI (simplified)\n */\nexport const POLYGON_ID_VERIFIER_ABI = [\n    \"function verifyProof(uint256 _circuitId, uint256[] memory _inputs, uint256[2] memory _a, uint256[2][2] memory _b, uint256[2] memory _c, uint256[] memory _signals) view returns (bool)\"\n];\n\n/**\n * Blockchain utility class\n */\nexport class BlockchainUtils {\n    private static provider: ethers.JsonRpcProvider | null = null;\n\n    /**\n     * Get Ethereum provider\n     */\n    static getProvider(): ethers.JsonRpcProvider {\n        if (!this.provider) {\n            const rpcUrl = process.env.ETHEREUM_RPC_URL || \"https://mainnet.infura.io/v3/your-infura-key\";\n            this.provider = new ethers.JsonRpcProvider(rpcUrl);\n        }\n        return this.provider;\n    }\n\n    /**\n     * Validate Ethereum address\n     */\n    static isValidAddress(address: string): boolean {\n        try {\n            return ethers.isAddress(address);\n        } catch {\n            return false;\n        }\n    }\n\n    /**\n     * Normalize Ethereum address\n     */\n    static normalizeAddress(address: string): string {\n        if (!this.isValidAddress(address)) {\n            throw new Error(\"Invalid Ethereum address\");\n        }\n        return ethers.getAddress(address);\n    }\n\n    /**\n     * Verify message signature\n     */\n    static verifySignature(message: string, signature: string, expectedAddress: string): boolean {\n        try {\n            const recoveredAddress = ethers.verifyMessage(message, signature);\n            return recoveredAddress.toLowerCase() === expectedAddress.toLowerCase();\n        } catch {\n            return false;\n        }\n    }\n\n    /**\n     * Recover address from signature\n     */\n    static recoverAddress(message: string, signature: string): string {\n        return ethers.verifyMessage(message, signature);\n    }\n\n    /**\n     * Create contract instance\n     */\n    static createContract(address: string, abi: any[]): ethers.Contract {\n        const provider = this.getProvider();\n        return new ethers.Contract(address, abi, provider);\n    }\n\n    /**\n     * Get ERC-1484 registry contract\n     */\n    static getERC1484Contract(registryAddress: string): ethers.Contract {\n        return this.createContract(registryAddress, ERC1484_ABI);\n    }\n\n    /**\n     * Get ERC-725 identity contract\n     */\n    static getERC725Contract(identityAddress: string): ethers.Contract {\n        return this.createContract(identityAddress, ERC725_ABI);\n    }\n\n    /**\n     * Get Polygon ID verifier contract\n     */\n    static getPolygonIDContract(verifierAddress: string): ethers.Contract {\n        return this.createContract(verifierAddress, POLYGON_ID_VERIFIER_ABI);\n    }\n\n    /**\n     * Convert string to bytes32\n     */\n    static stringToBytes32(str: string): string {\n        return ethers.keccak256(ethers.toUtf8Bytes(str));\n    }\n\n    /**\n     * Convert bytes32 to string\n     */\n    static bytes32ToString(bytes32: string): string {\n        return ethers.toUtf8String(bytes32);\n    }\n\n    /**\n     * Check if transaction is confirmed\n     */\n    static async isTransactionConfirmed(txHash: string, requiredConfirmations: number = 6): Promise<boolean> {\n        try {\n            const provider = this.getProvider();\n            const receipt = await provider.getTransactionReceipt(txHash);\n            \n            if (!receipt) {\n                return false;\n            }\n\n            const currentBlock = await provider.getBlockNumber();\n            const confirmations = currentBlock - receipt.blockNumber;\n            \n            return confirmations >= requiredConfirmations;\n        } catch {\n            return false;\n        }\n    }\n\n    /**\n     * Get transaction details\n     */\n    static async getTransactionDetails(txHash: string): Promise<any> {\n        const provider = this.getProvider();\n        const [transaction, receipt] = await Promise.all([\n            provider.getTransaction(txHash),\n            provider.getTransactionReceipt(txHash)\n        ]);\n\n        return {\n            transaction,\n            receipt,\n            confirmations: receipt ? await provider.getBlockNumber() - receipt.blockNumber : 0\n        };\n    }\n\n    /**\n     * Estimate gas for contract call\n     */\n    static async estimateGas(contract: ethers.Contract, method: string, params: any[]): Promise<bigint> {\n        return await contract[method].estimateGas(...params);\n    }\n\n    /**\n     * Get current gas price\n     */\n    static async getGasPrice(): Promise<bigint> {\n        const provider = this.getProvider();\n        const feeData = await provider.getFeeData();\n        return feeData.gasPrice || BigInt(0);\n    }\n}\n"], "version": 3}