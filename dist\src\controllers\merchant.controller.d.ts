import { BaseController } from "../../core/BaseController";
/**
 * Merchant controller
 * This controller handles merchant-related operations
 */
export declare class MerchantController extends BaseController {
    private merchantService;
    /**
     * Create a new merchant controller
     */
    constructor();
    /**
     * Get all merchants
     */
    getMerchants: any;
    /**
     * Get a merchant by ID
     */
    getMerchant: any;
    /**
     * Create a new merchant
     */
    createMerchant: any;
    /**
     * Update a merchant
     */
    updateMerchant: any;
    /**
     * Delete a merchant
     */
    deleteMerchant: any;
    /**
     * Get current merchant
     */
    getCurrentMerchant: any;
    /**
     * Update current merchant
     */
    updateCurrentMerchant: any;
    /**
     * Get merchant statistics
     */
    getMerchantStats: any;
}
//# sourceMappingURL=merchant.controller.d.ts.map