"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ControllerUtils = void 0;
const ErrorFactory_1 = require("./errors/ErrorFactory");
/**
 * Controller utilities for common controller operations
 */
class ControllerUtils {
    /**
     * Check if user is authenticated
     * @param req Express request
     * @returns User ID and role
     * @throws AppError if user is not authenticated
     */
    static checkAuth(req) {
        const userId = req.user?.id;
        const userRole = req.user?.role;
        const merchantId = req.user?.merchantId;
        if (!userId || !userRole) {
            throw ErrorFactory_1.ErrorFactory.authentication('Unauthorized');
        }
        return { userId, userRole, merchantId };
    }
    /**
     * Check if user is admin
     * @param req Express request
     * @returns User ID
     * @throws AppError if user is not admin
     */
    static checkAdmin(req) {
        const { userId, userRole } = this.checkAuth(req);
        if (userRole !== 'ADMIN') {
            throw ErrorFactory_1.ErrorFactory.authorization('Access denied');
        }
        return { userId };
    }
    /**
     * Check if user is merchant
     * @param req Express request
     * @returns User ID and merchant ID
     * @throws AppError if user is not merchant
     */
    static checkMerchant(req) {
        const { userId, userRole, merchantId } = this.checkAuth(req);
        if (userRole !== 'MERCHANT') {
            throw ErrorFactory_1.ErrorFactory.authorization('Access denied');
        }
        if (!merchantId) {
            throw ErrorFactory_1.ErrorFactory.authorization('Merchant ID not found');
        }
        return { userId, merchantId };
    }
    /**
     * Validate required fields
     * @param req Express request
     * @param fields Required fields
     * @throws AppError if any required field is missing
     */
    static validateRequiredFields(req, fields) {
        const missingFields = fields.filter((field) => {
            const value = req.body[field];
            return value === undefined || value === null || value === '';
        });
        if (missingFields?.length > 0) {
            throw ErrorFactory_1.ErrorFactory.missingRequiredField(missingFields);
        }
    }
    /**
     * Validate enum value
     * @param value Value to validate
     * @param enumType Enum type
     * @param fieldName Field name for error message
     * @throws AppError if value is not in enum
     */
    static validateEnum(value, enumType, fieldName) {
        if (!Object.values(enumType).includes(value)) {
            throw ErrorFactory_1.ErrorFactory.invalidInput(`Invalid ${fieldName}`);
        }
    }
    /**
     * Parse pagination parameters
     * @param req Express request
     * @returns Pagination parameters
     */
    static parsePagination(req) {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const offset = (page - 1) * limit;
        return { page, limit, offset };
    }
    /**
     * Parse date range
     * @param req Express request
     * @param startDateField Start date field name
     * @param endDateField End date field name
     * @returns Date range
     * @throws AppError if dates are invalid
     */
    static parseDateRange(req, startDateField = 'startDate', endDateField = 'endDate') {
        const startDateStr = req.query[startDateField];
        const endDateStr = req.query[endDateField];
        if (!startDateStr || !endDateStr) {
            throw ErrorFactory_1.ErrorFactory.missingRequiredField([startDateField, endDateField]);
        }
        const startDate = new Date(startDateStr);
        const endDate = new Date(endDateStr);
        if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
            throw ErrorFactory_1.ErrorFactory.invalidInput('Invalid date format');
        }
        if (startDate > endDate) {
            throw ErrorFactory_1.ErrorFactory.invalidInput(`${startDateField} cannot be after ${endDateField}`);
        }
        return { startDate, endDate };
    }
    /**
     * Format success response
     * @param data Response data
     * @returns Formatted response
     */
    static formatSuccessResponse(data) {
        return {
            success: true,
            data,
        };
    }
    /**
     * Format message response
     * @param message Response message
     * @returns Formatted response
     */
    static formatMessageResponse(message) {
        return {
            success: true,
            message,
        };
    }
    /**
     * Format paginated response
     * @param data Response data
     * @param total Total number of items
     * @param page Current page
     * @param limit Items per page
     * @returns Formatted response
     */
    static formatPaginatedResponse(data, total, page, limit) {
        return {
            success: true,
            data,
            pagination: {
                total,
                page,
                totalPages: Math.ceil(total / limit),
                limit,
            },
        };
    }
    /**
     * Format error response
     * @param message Error message
     * @param code Error code
     * @returns Formatted response
     */
    static formatErrorResponse(message, code) {
        return {
            success: false,
            error: {
                message,
                ...(code && { code }),
            },
        };
    }
}
exports.ControllerUtils = ControllerUtils;
exports.default = ControllerUtils;
//# sourceMappingURL=controller.utils.js.map