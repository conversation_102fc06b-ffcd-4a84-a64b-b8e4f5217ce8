import { BaseController } from "./base.controller";
declare global {
    namespace Express {
        interface Request {
            user?: {
                id: string;
                role: string;
                merchantId?: string;
            };
        }
    }
}
/**
 * PushNotificationController
 */
export declare class PushNotificationController extends BaseController {
    constructor();
    /**
     * Get VAPID public key for push notifications
     */
    getPublicKey: any;
    /**
     * Subscribe to push notifications
     */
    subscribe: any;
    /**
     * Unsubscribe from push notifications
     */
    unsubscribe: any;
    /**
     * Send test push notification
     */
    sendTest: any;
    /**
     * Get user's push notification subscriptions
     */
    getUserSubscriptions: any;
    /**
     * Get merchant's push notification subscriptions
     */
    getMerchantSubscriptions: any;
    /**
     * Delete a push notification subscription
     */
    deleteSubscription: any;
    catch(error: any): void;
}
declare const _default: PushNotificationController;
export default _default;
//# sourceMappingURL=push-notification.controller.d.ts.map