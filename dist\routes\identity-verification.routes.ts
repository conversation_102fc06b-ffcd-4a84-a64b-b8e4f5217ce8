// jscpd:ignore-file
import express from 'express';
import { authMiddleware as authenticate, authorize } from '../middlewares/auth.middleware';
import { IdentityVerificationController } from '../controllers/identity-verification';

// Create controller instance
const identityVerificationController = new IdentityVerificationController();

// Extract methods from controller
const {
  verifyEthereumSignature,
  verifyERC1484Identity,
  verifyERC725Identity,
  verifyENS,
  verifyPolygonID,
  verifyWorldcoin,
  verifyUnstoppableDomains,
  getVerificationById,
  getVerificationsForUser,
  getVerificationsForMerchant,
  addClaim,
  revokeClaim,
  setVerificationExpiration,
  checkVerificationExpiration,
  getVerificationStats,
  createBlockchainVerificationRequest,
  completeBlockchainVerification,
  getSupportedNetworks,
  verifyENSDomain,
  completeENSVerification,
  verifyUnstoppableDomain,
  completeUnstoppableDomainVerification,
  createPolygonIDVerificationRequest,
  handlePolygonIDCallback,
  checkPolygonIDVerificationStatus,
} = identityVerificationController;

const router: any = express.Router();

// All routes require authentication
router.use(authenticate);

// Verification endpoints
router.post('/ethereum-signature', verifyEthereumSignature);
router.post('/erc1484', verifyERC1484Identity);
router.post('/erc725', verifyERC725Identity);
router.post('/ens', verifyENS);
router.post('/polygon-id', verifyPolygonID);
router.post('/worldcoin', verifyWorldcoin);
router.post('/unstoppable-domains', verifyUnstoppableDomains);

// Claim management
router.post('/claims', authorize(['ADMIN', 'MERCHANT']), addClaim);
router.delete('/claims/:claimId', authorize(['ADMIN', 'MERCHANT']), revokeClaim);

// Expiration management
router.post('/expiration', authorize(['ADMIN']), setVerificationExpiration);
router.post('/check-expiration', authorize(['ADMIN']), checkVerificationExpiration);

// Blockchain-based verification
router.post(
  '/blockchain/request',
  authorize(['USER', 'MERCHANT']),
  createBlockchainVerificationRequest
);
router.post('/blockchain/verify', authorize(['USER', 'MERCHANT']), completeBlockchainVerification);
router.get('/blockchain/networks', getSupportedNetworks);

// ENS verification
router.post('/ens/verify', authorize(['USER', 'MERCHANT']), verifyENSDomain);
router.post('/ens/complete', authorize(['USER', 'MERCHANT']), completeENSVerification);

// Unstoppable Domains verification
router.post('/unstoppable/verify', authorize(['USER', 'MERCHANT']), verifyUnstoppableDomain);
router.post(
  '/unstoppable/complete',
  authorize(['USER', 'MERCHANT']),
  completeUnstoppableDomainVerification
);

// Polygon ID verification
router.post(
  '/polygon-id/request',
  authorize(['USER', 'MERCHANT']),
  createPolygonIDVerificationRequest
);
router.post('/polygon-id/callback', handlePolygonIDCallback);
router.get(
  '/polygon-id/status/:verificationId',
  authorize(['USER', 'MERCHANT']),
  checkPolygonIDVerificationStatus
);

// Worldcoin verification
router.post('/worldcoin/verify', authorize(['USER', 'MERCHANT']), verifyWorldcoinIdentity);

// Statistics
router.get('/stats', authorize(['ADMIN']), getVerificationStats);

// Get verification by ID
router.get('/:id', getVerificationById);

// Get verifications for user
router.get('/user/me', authorize(['USER']), getVerificationsForUser);

// Get verifications for merchant
router.get('/merchant/me', authorize(['MERCHANT']), getVerificationsForMerchant);

export default router;
