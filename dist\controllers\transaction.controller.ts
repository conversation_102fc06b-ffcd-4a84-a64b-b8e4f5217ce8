// jscpd:ignore-file
import { Request, Response } from "express";
import { BaseController } from "../../core/BaseController";
import { TransactionService } from "../../services/transaction.service";
import { ErrorFactory } from "../../utils/errors/ErrorFactory";
import { logger } from "../../lib/logger";
import { Transaction } from '../types';
import { BaseController } from "../../core/BaseController";
import { TransactionService } from "../../services/transaction.service";
import { ErrorFactory } from "../../utils/errors/ErrorFactory";
import { logger } from "../../lib/logger";
import { Transaction } from '../types';


/**
 * Transaction controller
 * This controller handles transaction-related operations
 */
export class TransactionController extends BaseController {
  private transactionService: TransactionService;
  
  /**
   * Create a new transaction controller
   */
  constructor() {
    super();
    this.transactionService = new TransactionService();
  }
  
  /**
   * Get all transactions
   */
  getTransactions = this.createHandler(async (req: Request, res: Response) => {
    // Check authorization
    const { userRole, userId, merchantId } = this.checkAuthorization(req);
    
    // Parse query parameters
    const { startDate, endDate } = req.query;
    const status: any =req.query.status as string;
    const requestedMerchantId: any =req.query.merchantId as string;
    
    // Determine target merchant ID
    const targetMerchantId: any =this.determineTargetMerchantId(
      userRole,
      merchantId,
      requestedMerchantId
    );
    
    // Parse pagination parameters
    const { limit, offset } = this.parsePagination(req);
    
    // Parse date range if provided
    let dateRange;
    if (startDate && endDate) {
      dateRange = this.parseDateRange(startDate as string, endDate as string);
    }
    
    // Get transactions
    const result: any =await this.transactionService.getTransactions({
      merchantId: targetMerchantId,
      status,
      startDate: dateRange?.startDate,
      endDate: dateRange?.endDate,
      limit,
      offset
    });
    
    // Send paginated response
    return this.sendPaginatedSuccess(
      res,
      result.data,
      result.total,
      limit,
      offset
    );
  });
  
  /**
   * Get a transaction by ID
   */
  getTransaction = this.createHandler(async (req: Request, res: Response) => {
    // Check authorization
    const { userRole, userId, merchantId } = this.checkAuthorization(req);
    
    // Get transaction ID
    const { id } = req.params;
    
    // Get transaction
    const transaction: any =await this.transactionService.getTransactionById(id);
    
    // Check if transaction exists
    if (!transaction) {
      throw ErrorFactory.notFound('Transaction', id);
    }
    
    // Check if user has access to this transaction
    if (userRole !== 'ADMIN' && transaction.merchantId !== merchantId) {
      throw ErrorFactory.authorization('You do not have permission to view this transaction');
    }
    
    // Send success response
    return this.sendSuccess(res, transaction);
  });
  
  /**
   * Create a new transaction
   */
  createTransaction = this.createHandler(async (req: Request, res: Response) => {
    // Check authorization
    const { userRole, userId, merchantId } = this.checkAuthorization(req);
    
    // Get request body
    const { amount, currency, method, description } = req.body;
    
    // Validate required fields
    if (!amount || !currency || !method) {
      throw ErrorFactory.validation('Amount, currency, and method are required');
    }
    
    // Create transaction
    const transaction: any =await this.transactionService.createTransaction({
      amount: parseFloat(amount),
      currency,
      method,
      description,
      merchantId: merchantId || req.body.merchantId,
      userId
    });
    
    // Send success response
    return this.sendSuccess(res, transaction, 201);
  });
  
  /**
   * Update a transaction
   */
  updateTransaction = this.createHandler(async (req: Request, res: Response) => {
    // Check authorization
    const { userRole, userId, merchantId } = this.checkAuthorization(req);
    
    // Get transaction ID
    const { id } = req.params;
    
    // Get request body
    const { status, notes } = req.body;
    
    // Get transaction
    const transaction: any =await this.transactionService.getTransactionById(id);
    
    // Check if transaction exists
    if (!transaction) {
      throw ErrorFactory.notFound('Transaction', id);
    }
    
    // Check if user has access to this transaction
    if (userRole !== 'ADMIN' && transaction.merchantId !== merchantId) {
      throw ErrorFactory.authorization('You do not have permission to update this transaction');
    }
    
    // Update transaction
    const updatedTransaction: any =await this.transactionService.updateTransaction(id, {
      status,
      notes,
      updatedBy: userId
    });
    
    // Send success response
    return this.sendSuccess(res, updatedTransaction);
  });
  
  /**
   * Delete a transaction
   */
  deleteTransaction = this.createHandler(async (req: Request, res: Response) => {
    // Check authorization
    const { userRole } = this.checkAuthorization(req);
    
    // Only admins can delete transactions
    this.checkAdminRole(userRole);
    
    // Get transaction ID
    const { id } = req.params;
    
    // Delete transaction
    await this.transactionService.deleteTransaction(id);
    
    // Send success response
    return this.sendSuccess(res, { message: 'Transaction deleted successfully' });
  });
  
  /**
   * Get transaction statistics
   */
  getTransactionStats = this.createHandler(async (req: Request, res: Response) => {
    // Check authorization
    const { userRole, userId, merchantId } = this.checkAuthorization(req);
    
    // Parse query parameters
    const { startDate, endDate } = req.query;
    const requestedMerchantId: any =req.query.merchantId as string;
    
    // Determine target merchant ID
    const targetMerchantId: any =this.determineTargetMerchantId(
      userRole,
      merchantId,
      requestedMerchantId
    );
    
    // Parse date range
    const dateRange: any =this.parseDateRange(
      startDate as string,
      endDate as string
    );
    
    // Get transaction statistics
    const stats: any =await this.transactionService.getTransactionStats({
      merchantId: targetMerchantId,
      startDate: dateRange.startDate,
      endDate: dateRange.endDate
    });
    
    // Send success response
    return this.sendSuccess(res, stats);
  });
}
