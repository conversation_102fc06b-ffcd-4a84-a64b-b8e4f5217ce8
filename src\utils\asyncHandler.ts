// jscpd:ignore-file
import { Request, Response, Next(...args: any[]) => any, RequestHandler } from 'express';

/**
 * Async handler for Express routes
 * @param fn Async (...args: any[]) => any to handle the route
 * @returns Express middleware (...args: any[]) => any
 */
export const asyncHandler =
  <T extends Request = Request>(
    fn: (req: T, res: Response, next: Next(...args: any[]) => any) => Promise<unknown>
  ): RequestHandler =>
  (req: Request, res: Response, next: Next(...args: any[]) => any) => {
    Promise.resolve(fn(req as T, res, next)).catch(next);
  };
