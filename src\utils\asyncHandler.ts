// jscpd:ignore-file
import { Request, Response, NextFunction } from "express";

/**
 * Async handler for Express routes
 * @param fn Async function to handle the route
 * @returns Express middleware function
 */
export const asyncHandler: unknown =(fn: (req: Request, res: Response, next: NextFunction) => Promise<unknown>) =>
    (req: Request, res: Response, next: NextFunction): void => {
        Promise.resolve(fn(req, res, next)).catch(next);
    };
