// jscpd:ignore-file
import { Request, Response, NextFunction, RequestHandler } from 'express';

/**
 * Async handler for Express routes
 * @param fn Async function to handle the route
 * @returns Express middleware function
 */
export const asyncHandler =
  <T extends Request = Request>(
    fn: (req: T, res: Response, next: NextFunction) => Promise<unknown>
  ): RequestHandler =>
  (req: Request, res: Response, next: NextFunction): void => {
    Promise.resolve(fn(req as T, res, next)).catch(next);
  };
