// jscpd:ignore-file

import { Router } from "express";
import { body, param } from "express-validator";
import verificationController from "../controllers/verification.controller";
import { validate } from "../middlewares/validation.middleware";
import { authenticate } from "../middlewares/auth.middleware";
import { body, param } from "express-validator";
import { validate } from "../middlewares/validation.middleware";
import { authenticate } from "../middlewares/auth.middleware";

const router: unknown =Router();

// Public route to verify a payment
// This is used by payment pages to trigger verification
router.post(
    "/",
    validate([
        body("merchantId").notEmpty(),
        body("amount").isNumeric(),
        body("currency").notEmpty(),
        body("paymentMethodId").notEmpty()
    ]),
    verificationController.verifyPayment
);

// Route to verify a payment by ID using the unified verification service
// This is used by the admin dashboard and merchant dashboard
router.get(
    "/payment/:paymentId",
    authenticate,
    validate([
        param("paymentId").notEmpty().withMessage("Payment ID is required")
    ]),
    verificationController.verifyPaymentById
);

// Route to process webhooks from payment providers
router.post(
    "/webhook",
    validate([
        body("provider").notEmpty().isString(),
        body("payload").notEmpty().isObject(),
        body("signature").optional().isString()
    ]),
    verificationController.processWebhook
);

export default router;
