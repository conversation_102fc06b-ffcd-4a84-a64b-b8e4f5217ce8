import { BaseController } from "./base.controller";
declare global {
    namespace Express {
        interface Request {
            user?: {
                id: string;
                role: string;
                merchantId?: string;
            };
        }
    }
}
/**
 * TelegramWebhookController
 */
export declare class TelegramWebhookController extends BaseController {
    constructor();
    /**
     * Handle incoming webhook from Telegram
     */
    handleWebhook: any;
    /**
     * Set webhook URL for Telegram bot
     */
    setWebhook: any;
    /**
     * Delete webhook for Telegram bot
     */
    deleteWebhook: any;
    /**
     * Get webhook info for Telegram bot
     */
    getWebhookInfo: any;
    /**
     * Get bot info for Telegram bot
     */
    getBotInfo: any;
    /**
     * Send test message to a Telegram chat
     */
    sendTestMessage: any;
}
declare const _default: TelegramWebhookController;
export default _default;
//# sourceMappingURL=telegram-webhook.controller.d.ts.map