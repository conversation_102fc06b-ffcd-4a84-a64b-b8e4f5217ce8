{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-27T17:31:44.162Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-27T17:31:44.163Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-27T17:31:44.164Z"}
{"level":"info","message":"Log files directory: F:\\Amazingpayflow\\logs\\development","timestamp":"2025-05-27T17:31:44.164Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-27T17:31:44.234Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-27T17:31:45.010Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-27T17:31:45.347Z"}
