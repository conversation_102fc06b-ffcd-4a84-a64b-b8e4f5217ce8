/**
 * Enhanced Verification Controller
 *
 * Handles verification operations using the new verification service.
 */
/**
 * Verify a payment
 */
export declare const verifyPayment: any;
/**
 * Get verification methods for a payment method
 */
export declare const getVerificationMethodsForPaymentMethod: any;
/**
 * Get all verification methods
 */
export declare const getAllVerificationMethods: any;
/**
 * Get verification method by type
 */
export declare const getVerificationMethodByType: any;
declare const _default: {
    verifyPayment: any;
    getVerificationMethodsForPaymentMethod: any;
    getAllVerificationMethods: any;
    getVerificationMethodByType: any;
};
export default _default;
//# sourceMappingURL=enhanced-verification.controller.d.ts.map