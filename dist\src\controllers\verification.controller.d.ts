/**
 * Verification Controller
 *
 * This controller handles payment verification requests.
 */
import { Request, Response } from "express";
declare class VerificationController {
    /**
   * Unified verification service
   */
    private unifiedVerificationService;
    /**
   * Constructor
   */
    constructor();
    /**
   * Verify a payment using the appropriate verification method
   */
    verifyPayment(req: Request, res: Response): Promise<Response>;
    /**
   * Process webhooks from payment providers
   */
    processWebhook(req: Request, res: Response): Promise<Response>;
    /**
   * Process Binance C2C webhooks
   */
    private processBinanceC2CWebhook;
    /**
   * Process blockchain webhooks
   */
    private processBlockchainWebhook;
    /**
   * Verify webhook signature
   */
    private verifyWebhookSignature;
    /**
   * Verify a payment by ID using the unified verification service
   * @param req Request
   * @param res Response
   */
    verifyPaymentById(req: Request, res: Response): Promise<Response>;
}
declare const _default: VerificationController;
export default _default;
//# sourceMappingURL=verification.controller.d.ts.map