import { Request, Response } from "express";
declare global {
    namespace Express {
        interface Request {
            user?: {
                id: string;
                role: string;
                merchantId?: string;
            };
        }
    }
}
declare class SubscriptionHistoryController {
    /**
     * Get subscription history for a merchant
     * @param req Request
     * @param res Response
     */
    getSubscriptionHistory(req: Request, res: Response): Promise<Response<any, Record<string, any>>>;
    /**
     * Download subscription history as CSV
     * @param req Request
     * @param res Response
     */
    downloadSubscriptionHistory(req: Request, res: Response): Promise<string | Response<any, Record<string, any>>>;
}
declare const _default: SubscriptionHistoryController;
export default _default;
//# sourceMappingURL=subscription-history.controller.d.ts.map