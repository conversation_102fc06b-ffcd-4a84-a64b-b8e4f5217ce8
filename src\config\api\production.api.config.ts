// jscpd:ignore-file
/**
 * Production API Configuration
 *
 * This file contains the configuration for third-party APIs in production.
 * It includes settings for API keys, endpoints, and other production-specific options.
 */

import { logger } from '../../lib/logger';

// Configuration interfaces
interface BinanceApiConfig {
  apiKey: string;
  apiSecret: string;
  apiUrl: string;
  webhookSecret: string;
  timeout: number;
  production: boolean;
  retry: {
    maxRetries: number;
    retryDelay: number;
    useExponentialBackoff: boolean;
  };
}

interface EmailConfig {
  host: string;
  port: number;
  user: string;
  password: string;
  secure: boolean;
  from: string;
  fromName: string;
}

interface SentryConfig {
  dsn: string;
  environment: string;
  tracesSampleRate: number;
  enablePerformanceMonitoring: boolean;
}

/**
 * Binance API configuration for production
 */
export const binanceApiConfig: BinanceApiConfig = {
  /**
   * Binance API key
   */
  apiKey: process.env.BINANCE_API_KEY ?? '',

  /**
   * Binance API secret
   */
  apiSecret: process.env.BINANCE_API_SECRET ?? '',

  /**
   * Binance API URL
   */
  apiUrl: process.env.BINANCE_API_URL ?? 'https://api.binance.com',

  /**
   * Binance webhook secret for verifying webhook requests
   */
  webhookSecret: process.env.BINANCE_WEBHOOK_SECRET ?? '',

  /**
   * Request timeout in milliseconds
   */
  timeout: 30000,

  /**
   * Whether to use production endpoints
   */
  production: true,

  /**
   * Retry configuration
   */
  retry: {
    /**
     * Maximum number of retries
     */
    maxRetries: 3,

    /**
     * Delay between retries in milliseconds
     */
    retryDelay: 1000,

    /**
     * Whether to use exponential backoff
     */
    useExponentialBackoff: true,
  },
};

/**
 * Email configuration for production
 */
export const emailConfig: EmailConfig = {
  /**
   * SMTP host
   */
  host: process.env.SMTP_HOST ?? '',

  /**
   * SMTP port
   */
  port: parseInt(process.env.SMTP_PORT ?? '587', 10),

  /**
   * SMTP username
   */
  user: process.env.SMTP_USER ?? '',

  /**
   * SMTP password
   */
  password: process.env.SMTP_PASSWORD ?? '',

  /**
   * Whether to use secure connection (TLS)
   */
  secure: process.env.SMTP_SECURE === 'true',

  /**
   * From email address
   */
  from: process.env.EMAIL_FROM ?? '<EMAIL>',

  /**
   * From name
   */
  fromName: process.env.EMAIL_FROM_NAME ?? 'AmazingPay',
};

/**
 * Sentry configuration for production
 */
export const sentryConfig: SentryConfig = {
  /**
   * Sentry DSN
   */
  dsn: process.env.SENTRY_DSN ?? '',

  /**
   * Sentry environment
   */
  environment: process.env.SENTRY_ENVIRONMENT ?? 'production',

  /**
   * Traces sample rate
   */
  tracesSampleRate: parseFloat(process.env.SENTRY_TRACES_SAMPLE_RATE ?? '0.1'),

  /**
   * Whether to enable performance monitoring
   */
  enablePerformanceMonitoring: process.env.ENABLE_PERFORMANCE_MONITORING === 'true',
};

/**
 * Initialize third-party API configurations
 * This function validates the configurations and logs warnings for missing values
 */
export const initializeApiConfigurations = (): void => {
  logger.info('Initializing production API configurations');

  // Validate Binance API configuration
  if (!binanceApiConfig.apiKey || !binanceApiConfig.apiSecret) {
    logger.warn(
      'Binance API credentials are missing. Binance payment methods may not work correctly.'
    );
  } else {
    logger.info('Binance API configuration initialized successfully');
  }

  // Validate email configuration
  if (!emailConfig.host || !emailConfig.user || !emailConfig.password) {
    logger.warn('Email configuration is incomplete. Email notifications may not work correctly.');
  } else {
    logger.info('Email configuration initialized successfully');
  }

  // Validate Sentry configuration
  if (!sentryConfig.dsn) {
    logger.warn('Sentry DSN is missing. Error tracking will not be available.');
  } else {
    logger.info('Sentry configuration initialized successfully');
  }
};

/**
 * Production API configuration
 */
export const productionApiConfig = {
  binance: binanceApiConfig,
  email: emailConfig,
  sentry: sentryConfig,
  initialize: initializeApiConfigurations,
} as const;

export default productionApiConfig;
