import { ControllerTestOptions, ServiceTestOptions, RepositoryTestOptions, MiddlewareTestOptions, MockRequest, MockResponse, MockNext, TestResult } from '../core/TestTypes';
/**
 * Test a controller method
 */
export declare function testController(controller: unknown, method: string, options?: ControllerTestOptions): Promise<{
    req: MockRequest;
    res: MockResponse;
    next: MockNext;
    result?: unknown;
}>;
/**
 * Test a service method
 */
export declare function testService(service: unknown, method: string, options?: ServiceTestOptions): Promise<TestResult>;
/**
 * Test a repository method
 */
export declare function testRepository(repository: unknown, method: string, options?: RepositoryTestOptions): Promise<TestResult>;
/**
 * Test middleware
 */
export declare function testMiddleware(middleware: Function, options?: MiddlewareTestOptions): Promise<{
    req: MockRequest;
    res: MockResponse;
    next: MockNext;
}>;
/**
 * Test utility function
 */
export declare function testUtility(utilityFunction: Function, options?: {
    args?: unknown[];
    expectedResult?: unknown;
    expectedError?: unknown;
    setup?: () => void | Promise<void>;
    cleanup?: () => void | Promise<void>;
}): Promise<TestResult>;
//# sourceMappingURL=TestRunners.d.ts.map