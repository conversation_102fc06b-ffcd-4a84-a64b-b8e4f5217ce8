import { BaseController } from "./base.controller";
declare global {
    namespace Express {
        interface Request {
            user?: {
                id: string;
                role: string;
                merchantId?: string;
            };
        }
    }
}
/**
 * NotificationController
 * Controller for handling notification operations
 */
export declare class NotificationController extends BaseController {
    constructor();
    /**
     * Get user notification preferences
     */
    getUserPreferences: any;
    /**
     * Update user notification preferences
     */
    updateUserPreferences: any;
    /**
     * Get merchant notification preferences
     */
    getMerchantPreferences: any;
    /**
     * Update merchant notification preferences
     */
    updateMerchantPreferences: any;
    /**
     * Send a test notification
     */
    sendTestNotification: any;
    /**
     * Get notification templates
     */
    getTemplates: any;
    /**
     * Create a notification template
     */
    createTemplate: any;
    /**
     * Update a notification template
     */
    updateTemplate: any;
    /**
     * Delete a notification template
     */
    deleteTemplate: any;
    /**
     * Helper method to check admin role
     */
    private checkAdminRole;
    /**
     * Helper method to check authorization
     */
    private checkAuthorization;
}
declare const _default: NotificationController;
export default _default;
//# sourceMappingURL=notification.controller.d.ts.map