import { Request, Response } from 'express';
export declare class DashboardController {
    /**
     * Get all dashboards for the current user
     */
    getDashboards: (req: Request, res: Response) => Promise<void>;
    /**
     * Get a dashboard by ID
     */
    getDashboardById: (req: Request, res: Response) => Promise<void>;
    /**
     * Create a new dashboard
     */
    createDashboard: (req: Request, res: Response) => Promise<void>;
    /**
     * Update a dashboard
     */
    updateDashboard: (req: Request, res: Response) => Promise<void>;
    /**
     * Delete a dashboard
     */
    deleteDashboard: (req: Request, res: Response) => Promise<void>;
}
//# sourceMappingURL=dashboard.controller.d.ts.map