import { Request } from 'express';
declare global {
    namespace Express {
        interface Request {
            user?: {
                id: string;
                role: string;
                merchantId?: string;
            };
        }
    }
}
/**
 * Controller utilities for common controller operations
 */
export declare class ControllerUtils {
    /**
     * Check if user is authenticated
     * @param req Express request
     * @returns User ID and role
     * @throws AppError if user is not authenticated
     */
    static checkAuth(req: Request): {
        userId: string;
        userRole: string;
        merchantId?: string;
    };
    /**
     * Check if user is admin
     * @param req Express request
     * @returns User ID
     * @throws AppError if user is not admin
     */
    static checkAdmin(req: Request): {
        userId: string;
    };
    /**
     * Check if user is merchant
     * @param req Express request
     * @returns User ID and merchant ID
     * @throws AppError if user is not merchant
     */
    static checkMerchant(req: Request): {
        userId: string;
        merchantId: string;
    };
    /**
     * Validate required fields
     * @param req Express request
     * @param fields Required fields
     * @throws AppError if any required field is missing
     */
    static validateRequiredFields(req: Request, fields: string[]): void;
    /**
     * Validate enum value
     * @param value Value to validate
     * @param enumType Enum type
     * @param fieldName Field name for error message
     * @throws AppError if value is not in enum
     */
    static validateEnum<T extends object>(value: unknown, enumType: T, fieldName: string): void;
    /**
     * Parse pagination parameters
     * @param req Express request
     * @returns Pagination parameters
     */
    static parsePagination(req: Request): {
        page: number;
        limit: number;
        offset: number;
    };
    /**
     * Parse date range
     * @param req Express request
     * @param startDateField Start date field name
     * @param endDateField End date field name
     * @returns Date range
     * @throws AppError if dates are invalid
     */
    static parseDateRange(req: Request, startDateField?: string, endDateField?: string): {
        startDate: Date;
        endDate: Date;
    };
    /**
     * Format success response
     * @param data Response data
     * @returns Formatted response
     */
    static formatSuccessResponse(data: unknown): {
        success: true;
        data: unknown;
    };
    /**
     * Format message response
     * @param message Response message
     * @returns Formatted response
     */
    static formatMessageResponse(message: string): {
        success: true;
        message: string;
    };
    /**
     * Format paginated response
     * @param data Response data
     * @param total Total number of items
     * @param page Current page
     * @param limit Items per page
     * @returns Formatted response
     */
    static formatPaginatedResponse(data: unknown[], total: number, page: number, limit: number): {
        success: true;
        data: unknown[];
        pagination: {
            total: number;
            page: number;
            totalPages: number;
            limit: number;
        };
    };
    /**
     * Format error response
     * @param message Error message
     * @param code Error code
     * @returns Formatted response
     */
    static formatErrorResponse(message: string, code?: string): {
        success: false;
        error: {
            message: string;
            code?: string;
        };
    };
}
export default ControllerUtils;
//# sourceMappingURL=controller.utils.d.ts.map