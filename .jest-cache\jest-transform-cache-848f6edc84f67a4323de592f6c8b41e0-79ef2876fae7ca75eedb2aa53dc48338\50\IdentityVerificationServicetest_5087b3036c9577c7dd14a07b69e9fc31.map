{"file": "F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts", "mappings": ";AAAA;;;;GAIG;;AAEH,2CAAkF;AAKlF,oBAAoB;AACpB,cAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;AAC5B,cAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AALpB,qFAAkF;AAClF,iFAA8E;AAM9E,IAAA,kBAAQ,EAAC,6BAA6B,EAAE,GAAG,EAAE;IAC3C,IAAI,OAAoC,CAAC;IACzC,IAAI,UAAqC,CAAC;IAE1C,IAAA,oBAAU,EAAC,GAAG,EAAE;QACd,4BAA4B;QAC5B,UAAU,GAAG;YACX,oBAAoB,EAAE;gBACpB,MAAM,EAAE,cAAI,CAAC,EAAE,EAAE;gBACjB,UAAU,EAAE,cAAI,CAAC,EAAE,EAAE;gBACrB,QAAQ,EAAE,cAAI,CAAC,EAAE,EAAE;gBACnB,MAAM,EAAE,cAAI,CAAC,EAAE,EAAE;gBACjB,MAAM,EAAE,cAAI,CAAC,EAAE,EAAE;gBACjB,KAAK,EAAE,cAAI,CAAC,EAAE,EAAE;aACjB;YACD,IAAI,EAAE;gBACJ,UAAU,EAAE,cAAI,CAAC,EAAE,EAAE;gBACrB,MAAM,EAAE,cAAI,CAAC,EAAE,EAAE;gBACjB,MAAM,EAAE,cAAI,CAAC,EAAE,EAAE;aAClB;YACD,QAAQ,EAAE;gBACR,UAAU,EAAE,cAAI,CAAC,EAAE,EAAE;aACtB;SACK,CAAC;QAET,+BAA+B;QAC/B,OAAO,GAAG,IAAI,yDAA2B,CAAC,UAAU,CAAC,CAAC;IACxD,CAAC,CAAC,CAAC;IAEH,IAAA,mBAAS,EAAC,GAAG,EAAE;QACb,cAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,MAAM,qBAAqB,GAAG;YAC5B,OAAO,EAAE,4CAA4C;YACrD,OAAO,EAAE,gCAAgC;YACzC,SAAS,EACP,wIAAwI;YAC1I,MAAM,EAAE,kBAAkB;YAC1B,UAAU,EAAE,sBAAsB;SACnC,CAAC;QAEF,IAAA,YAAE,EAAC,uDAAuD,EAAE,KAAK,IAAI,EAAE;YACrE,UAAU;YACV,MAAM,QAAQ,GAAG;gBACf,EAAE,EAAE,qBAAqB,CAAC,MAAM;gBAChC,KAAK,EAAE,kBAAkB;gBACzB,IAAI,EAAE,MAAM;aACb,CAAC;YAEF,MAAM,YAAY,GAAG;gBACnB,EAAE,EAAE,qBAAqB,CAAC,UAAU;gBACpC,IAAI,EAAE,eAAe;aACtB,CAAC;YAEF,MAAM,sBAAsB,GAAG;gBAC7B,EAAE,EAAE,0BAA0B;gBAC9B,MAAM,EAAE,qBAAqB,CAAC,MAAM;gBACpC,UAAU,EAAE,qBAAqB,CAAC,UAAU;gBAC5C,MAAM,EAAE,oBAAoB;gBAC5B,MAAM,EAAE,UAAU;gBAClB,UAAU,EAAE,IAAI;gBAChB,OAAO,EAAE,qBAAqB,CAAC,OAAO;gBACtC,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YAEF,oBAAoB;YACpB,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YACvD,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;YAC/D,UAAU,CAAC,oBAAoB,CAAC,MAAM,CAAC,iBAAiB,CAAC,sBAAsB,CAAC,CAAC;YAEjF,2BAA2B;YAC3B,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;YACrC,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,eAAe,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;YAC1E,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAE7C,MAAM;YACN,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,uBAAuB,CAAC,qBAAqB,CAAC,CAAC;YAE5E,SAAS;YACT,IAAA,gBAAM,EAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;YAC7B,IAAA,gBAAM,EAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,IAAA,gBAAM,EAAC,MAAM,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,EAAE,CAAC,CAAC;YAC9D,IAAA,gBAAM,EAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YACjD,IAAA,gBAAM,EAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAErC,sBAAsB;YACtB,IAAA,gBAAM,EAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,oBAAoB,CAAC;gBACtD,KAAK,EAAE,EAAE,EAAE,EAAE,qBAAqB,CAAC,MAAM,EAAE;aAC5C,CAAC,CAAC;YACH,IAAA,gBAAM,EAAC,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,oBAAoB,CAAC;gBAC1D,KAAK,EAAE,EAAE,EAAE,EAAE,qBAAqB,CAAC,UAAU,EAAE;aAChD,CAAC,CAAC;YACH,IAAA,gBAAM,EAAC,UAAU,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC;gBAClE,IAAI,EAAE,gBAAM,CAAC,gBAAgB,CAAC;oBAC5B,MAAM,EAAE,qBAAqB,CAAC,MAAM;oBACpC,UAAU,EAAE,qBAAqB,CAAC,UAAU;oBAC5C,MAAM,EAAE,oBAAoB;oBAC5B,MAAM,EAAE,UAAU;oBAClB,OAAO,EAAE,qBAAqB,CAAC,OAAO;iBACvC,CAAC;aACH,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,iDAAiD,EAAE,KAAK,IAAI,EAAE;YAC/D,UAAU;YACV,MAAM,WAAW,GAAG;gBAClB,GAAG,qBAAqB;gBACxB,OAAO,EAAE,iBAAiB;aAC3B,CAAC;YAEF,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;YACrC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YAE9C,eAAe;YACf,MAAM,IAAA,gBAAM,EAAC,OAAO,CAAC,uBAAuB,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CACxE,qDAAyB,CAC1B,CAAC;YAEF,qCAAqC;YACrC,IAAA,gBAAM,EAAC,UAAU,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC;QACxE,CAAC,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,UAAU;YACV,MAAM,QAAQ,GAAG,EAAE,EAAE,EAAE,qBAAqB,CAAC,MAAM,EAAE,CAAC;YACtD,MAAM,YAAY,GAAG,EAAE,EAAE,EAAE,qBAAqB,CAAC,UAAU,EAAE,CAAC;YAE9D,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YACvD,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;YAE/D,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;YACrC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,eAAe,CAAC,oBAAoB,CAAC,CAAC;YAEjE,eAAe;YACf,MAAM,IAAA,gBAAM,EAAC,OAAO,CAAC,uBAAuB,CAAC,qBAAqB,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAClF,qDAAyB,CAC1B,CAAC;YAEF,IAAA,gBAAM,EAAC,UAAU,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC;QACxE,CAAC,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,UAAU;YACV,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAEnD,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;YACrC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAE7C,eAAe;YACf,MAAM,IAAA,gBAAM,EAAC,OAAO,CAAC,uBAAuB,CAAC,qBAAqB,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAClF,qDAAyB,CAC1B,CAAC;YAEF,IAAA,gBAAM,EAAC,UAAU,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC;QACxE,CAAC,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,UAAU;YACV,MAAM,QAAQ,GAAG,EAAE,EAAE,EAAE,qBAAqB,CAAC,MAAM,EAAE,CAAC;YACtD,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YACvD,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAEvD,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;YACrC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAE7C,eAAe;YACf,MAAM,IAAA,gBAAM,EAAC,OAAO,CAAC,uBAAuB,CAAC,qBAAqB,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAClF,qDAAyB,CAC1B,CAAC;YAEF,IAAA,gBAAM,EAAC,UAAU,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC;QACxE,CAAC,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,UAAU;YACV,MAAM,QAAQ,GAAG,EAAE,EAAE,EAAE,qBAAqB,CAAC,MAAM,EAAE,CAAC;YACtD,MAAM,YAAY,GAAG,EAAE,EAAE,EAAE,qBAAqB,CAAC,UAAU,EAAE,CAAC;YAE9D,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YACvD,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;YAC/D,UAAU,CAAC,oBAAoB,CAAC,MAAM,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC;YAEtF,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;YACrC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,eAAe,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;YAE1E,eAAe;YACf,MAAM,IAAA,gBAAM,EAAC,OAAO,CAAC,uBAAuB,CAAC,qBAAqB,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAClF,qDAAyB,CAC1B,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,IAAA,YAAE,EAAC,iDAAiD,EAAE,KAAK,IAAI,EAAE;YAC/D,UAAU;YACV,MAAM,cAAc,GAAG,0BAA0B,CAAC;YAClD,MAAM,gBAAgB,GAAG;gBACvB,EAAE,EAAE,cAAc;gBAClB,MAAM,EAAE,kBAAkB;gBAC1B,UAAU,EAAE,sBAAsB;gBAClC,MAAM,EAAE,oBAAoB;gBAC5B,MAAM,EAAE,UAAU;gBAClB,UAAU,EAAE,IAAI;gBAChB,OAAO,EAAE,4CAA4C;gBACrD,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YAEF,UAAU,CAAC,oBAAoB,CAAC,UAAU,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;YAE/E,MAAM;YACN,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,mBAAmB,CAAC,cAAc,CAAC,CAAC;YAEjE,SAAS;YACT,IAAA,gBAAM,EAAC,MAAM,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;YACzC,IAAA,gBAAM,EAAC,UAAU,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC,oBAAoB,CAAC;gBACtE,KAAK,EAAE,EAAE,EAAE,EAAE,cAAc,EAAE;aAC9B,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,kDAAkD,EAAE,KAAK,IAAI,EAAE;YAChE,UAAU;YACV,MAAM,cAAc,GAAG,8BAA8B,CAAC;YACtD,UAAU,CAAC,oBAAoB,CAAC,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAEnE,eAAe;YACf,MAAM,IAAA,gBAAM,EAAC,OAAO,CAAC,mBAAmB,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CACvE,qDAAyB,CAC1B,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,IAAA,YAAE,EAAC,kDAAkD,EAAE,KAAK,IAAI,EAAE;YAChE,UAAU;YACV,MAAM,MAAM,GAAG,kBAAkB,CAAC;YAClC,MAAM,iBAAiB,GAAG;gBACxB;oBACE,EAAE,EAAE,wBAAwB;oBAC5B,MAAM;oBACN,MAAM,EAAE,oBAAoB;oBAC5B,MAAM,EAAE,UAAU;oBAClB,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB;gBACD;oBACE,EAAE,EAAE,wBAAwB;oBAC5B,MAAM;oBACN,MAAM,EAAE,oBAAoB;oBAC5B,MAAM,EAAE,UAAU;oBAClB,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB;aACF,CAAC;YAEF,UAAU,CAAC,oBAAoB,CAAC,QAAQ,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;YAC9E,UAAU,CAAC,oBAAoB,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;YAE3D,MAAM;YACN,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,oBAAoB,CAAC,MAAM,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC;YAElF,SAAS;YACT,IAAA,gBAAM,EAAC,MAAM,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;YACxD,IAAA,gBAAM,EAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC7B,IAAA,gBAAM,EAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC5B,IAAA,gBAAM,EAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAE9B,IAAA,gBAAM,EAAC,UAAU,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CAAC;gBACpE,KAAK,EAAE,EAAE,MAAM,EAAE;gBACjB,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;gBAC9B,IAAI,EAAE,CAAC;gBACP,IAAI,EAAE,EAAE;aACT,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,6BAA6B,EAAE,KAAK,IAAI,EAAE;YAC3C,UAAU;YACV,MAAM,MAAM,GAAG,oBAAoB,CAAC;YACpC,UAAU,CAAC,oBAAoB,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC;YAC/D,UAAU,CAAC,oBAAoB,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;YAE3D,MAAM;YACN,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;YAE1D,SAAS;YACT,IAAA,gBAAM,EAAC,MAAM,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YACzC,IAAA,gBAAM,EAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,IAAA,YAAE,EAAC,gDAAgD,EAAE,KAAK,IAAI,EAAE;YAC9D,UAAU;YACV,MAAM,cAAc,GAAG,yBAAyB,CAAC;YACjD,MAAM,SAAS,GAAG,UAAU,CAAC;YAC7B,MAAM,MAAM,GAAG,4BAA4B,CAAC;YAE5C,MAAM,uBAAuB,GAAG;gBAC9B,EAAE,EAAE,cAAc;gBAClB,MAAM,EAAE,SAAS;gBACjB,MAAM;gBACN,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YAEF,UAAU,CAAC,oBAAoB,CAAC,MAAM,CAAC,iBAAiB,CAAC,uBAAuB,CAAC,CAAC;YAElF,MAAM;YACN,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,wBAAwB,CAAC,cAAc,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;YAEzF,SAAS;YACT,IAAA,gBAAM,EAAC,MAAM,CAAC,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC;YAChD,IAAA,gBAAM,EAAC,UAAU,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC;gBAClE,KAAK,EAAE,EAAE,EAAE,EAAE,cAAc,EAAE;gBAC7B,IAAI,EAAE;oBACJ,MAAM,EAAE,SAAS;oBACjB,MAAM;oBACN,SAAS,EAAE,gBAAM,CAAC,GAAG,CAAC,IAAI,CAAC;iBAC5B;aACF,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,gDAAgD,EAAE,KAAK,IAAI,EAAE;YAC9D,UAAU;YACV,MAAM,cAAc,GAAG,0BAA0B,CAAC;YAClD,UAAU,CAAC,oBAAoB,CAAC,MAAM,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC,CAAC;YAExF,eAAe;YACf,MAAM,IAAA,gBAAM,EAAC,OAAO,CAAC,wBAAwB,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CACxF,qDAAyB,CAC1B,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,IAAA,YAAE,EAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,UAAU;YACV,MAAM,UAAU,GAAG,oBAAoB,CAAC;YACxC,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC;YACxC,MAAM,MAAM,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC;YAEtC,UAAU,CAAC,oBAAoB,CAAC,KAAK;iBAClC,qBAAqB,CAAC,GAAG,CAAC,CAAC,QAAQ;iBACnC,qBAAqB,CAAC,EAAE,CAAC,CAAC,WAAW;iBACrC,qBAAqB,CAAC,EAAE,CAAC,CAAC,UAAU;iBACpC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW;YAExC,MAAM;YACN,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,yBAAyB,CAAC,UAAU,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;YAErF,SAAS;YACT,IAAA,gBAAM,EAAC,MAAM,CAAC,CAAC,OAAO,CAAC;gBACrB,KAAK,EAAE,GAAG;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,EAAE;gBACX,QAAQ,EAAE,CAAC;gBACX,gBAAgB,EAAE,EAAE;aACrB,CAAC,CAAC;YAEH,wBAAwB;YACxB,IAAA,gBAAM,EAAC,UAAU,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;QACzE,CAAC,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YAChD,UAAU;YACV,UAAU,CAAC,oBAAoB,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;YAE3D,MAAM;YACN,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,yBAAyB,EAAE,CAAC;YAEzD,SAAS;YACT,IAAA,gBAAM,EAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC7B,IAAA,gBAAM,EAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,IAAA,YAAE,EAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,UAAU;YACV,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC;YAEzE,eAAe;YACf,MAAM,IAAA,gBAAM,EAAC,OAAO,CAAC,uBAAuB,CAAC,qBAAqB,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAClF,qDAAyB,CAC1B,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,8BAA8B,EAAE,KAAK,IAAI,EAAE;YAC5C,UAAU;YACV,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAC3C,GAAG,EAAE,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CACtF,CAAC;YAEF,eAAe;YACf,MAAM,IAAA,gBAAM,EAAC,OAAO,CAAC,uBAAuB,CAAC,qBAAqB,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAClF,qDAAyB,CAC1B,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,IAAA,YAAE,EAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAC/C,uBAAuB;YACvB,MAAM,IAAA,gBAAM,EACV,OAAO,CAAC,uBAAuB,CAAC;gBAC9B,OAAO,EAAE,EAAE;gBACX,OAAO,EAAE,MAAM;gBACf,SAAS,EAAE,QAAQ;gBACnB,MAAM,EAAE,qBAAqB;gBAC7B,UAAU,EAAE,yBAAyB;aACtC,CAAC,CACH,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YAEpB,uBAAuB;YACvB,MAAM,IAAA,gBAAM,EACV,OAAO,CAAC,uBAAuB,CAAC;gBAC9B,OAAO,EAAE,4CAA4C;gBACrD,OAAO,EAAE,EAAE;gBACX,SAAS,EAAE,QAAQ;gBACnB,MAAM,EAAE,qBAAqB;gBAC7B,UAAU,EAAE,yBAAyB;aACtC,CAAC,CACH,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YAEpB,yBAAyB;YACzB,MAAM,IAAA,gBAAM,EACV,OAAO,CAAC,uBAAuB,CAAC;gBAC9B,OAAO,EAAE,4CAA4C;gBACrD,OAAO,EAAE,MAAM;gBACf,SAAS,EAAE,EAAE;gBACb,MAAM,EAAE,qBAAqB;gBAC7B,UAAU,EAAE,yBAAyB;aACtC,CAAC,CACH,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;QACtB,CAAC,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,6BAA6B,EAAE,KAAK,IAAI,EAAE;YAC3C,MAAM,IAAA,gBAAM,EACV,OAAO,CAAC,uBAAuB,CAAC;gBAC9B,OAAO,EAAE,4CAA4C;gBACrD,OAAO,EAAE,MAAM;gBACf,SAAS,EAAE,QAAQ;gBACnB,MAAM,EAAE,cAAc;gBACtB,UAAU,EAAE,yBAAyB;aACtC,CAAC,CACH,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;QACtB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts"], "sourcesContent": ["/**\n * Unit Tests for Identity Verification Service\n *\n * Comprehensive test suite covering all functionality of the IdentityVerificationService\n */\n\nimport { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';\nimport { PrismaClient } from '@prisma/client';\nimport { IdentityVerificationService } from '../core/IdentityVerificationService';\nimport { IdentityVerificationError } from '../core/IdentityVerificationError';\n\n// Mock dependencies\njest.mock('@prisma/client');\njest.mock('ethers');\n\ndescribe('IdentityVerificationService', () => {\n  let service: IdentityVerificationService;\n  let mockPrisma: jest.Mocked<PrismaClient>;\n\n  beforeEach(() => {\n    // Create mock Prisma client\n    mockPrisma = {\n      identityVerification: {\n        create: jest.fn(),\n        findUnique: jest.fn(),\n        findMany: jest.fn(),\n        update: jest.fn(),\n        delete: jest.fn(),\n        count: jest.fn(),\n      },\n      user: {\n        findUnique: jest.fn(),\n        create: jest.fn(),\n        update: jest.fn(),\n      },\n      merchant: {\n        findUnique: jest.fn(),\n      },\n    } as any;\n\n    // Initialize service with mock\n    service = new IdentityVerificationService(mockPrisma);\n  });\n\n  afterEach(() => {\n    jest.clearAllMocks();\n  });\n\n  describe('verifyEthereumSignature', () => {\n    const validVerificationData = {\n      address: '******************************************',\n      message: 'Verify identity for AmazingPay',\n      signature:\n        '0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890ab1c',\n      userId: 'user-123-456-789',\n      merchantId: 'merchant-123-456-789',\n    };\n\n    it('should successfully verify a valid Ethereum signature', async () => {\n      // Arrange\n      const mockUser = {\n        id: validVerificationData.userId,\n        email: '<EMAIL>',\n        role: 'USER',\n      };\n\n      const mockMerchant = {\n        id: validVerificationData.merchantId,\n        name: 'Test Merchant',\n      };\n\n      const mockVerificationResult = {\n        id: 'verification-123-456-789',\n        userId: validVerificationData.userId,\n        merchantId: validVerificationData.merchantId,\n        method: 'ethereum_signature',\n        status: 'verified',\n        confidence: 0.95,\n        address: validVerificationData.address,\n        createdAt: new Date(),\n      };\n\n      // Mock Prisma calls\n      mockPrisma.user.findUnique.mockResolvedValue(mockUser);\n      mockPrisma.merchant.findUnique.mockResolvedValue(mockMerchant);\n      mockPrisma.identityVerification.create.mockResolvedValue(mockVerificationResult);\n\n      // Mock ethers verification\n      const { ethers } = require('ethers');\n      ethers.utils.verifyMessage.mockReturnValue(validVerificationData.address);\n      ethers.utils.isAddress.mockReturnValue(true);\n\n      // Act\n      const result = await service.verifyEthereumSignature(validVerificationData);\n\n      // Assert\n      expect(result).toBeDefined();\n      expect(result.success).toBe(true);\n      expect(result.verificationId).toBe(mockVerificationResult.id);\n      expect(result.method).toBe('ethereum_signature');\n      expect(result.confidence).toBe(0.95);\n\n      // Verify Prisma calls\n      expect(mockPrisma.user.findUnique).toHaveBeenCalledWith({\n        where: { id: validVerificationData.userId },\n      });\n      expect(mockPrisma.merchant.findUnique).toHaveBeenCalledWith({\n        where: { id: validVerificationData.merchantId },\n      });\n      expect(mockPrisma.identityVerification.create).toHaveBeenCalledWith({\n        data: expect.objectContaining({\n          userId: validVerificationData.userId,\n          merchantId: validVerificationData.merchantId,\n          method: 'ethereum_signature',\n          status: 'verified',\n          address: validVerificationData.address,\n        }),\n      });\n    });\n\n    it('should throw error for invalid Ethereum address', async () => {\n      // Arrange\n      const invalidData = {\n        ...validVerificationData,\n        address: 'invalid-address',\n      };\n\n      const { ethers } = require('ethers');\n      ethers.utils.isAddress.mockReturnValue(false);\n\n      // Act & Assert\n      await expect(service.verifyEthereumSignature(invalidData)).rejects.toThrow(\n        IdentityVerificationError\n      );\n\n      // Verify no database calls were made\n      expect(mockPrisma.identityVerification.create).not.toHaveBeenCalled();\n    });\n\n    it('should throw error for invalid signature', async () => {\n      // Arrange\n      const mockUser = { id: validVerificationData.userId };\n      const mockMerchant = { id: validVerificationData.merchantId };\n\n      mockPrisma.user.findUnique.mockResolvedValue(mockUser);\n      mockPrisma.merchant.findUnique.mockResolvedValue(mockMerchant);\n\n      const { ethers } = require('ethers');\n      ethers.utils.isAddress.mockReturnValue(true);\n      ethers.utils.verifyMessage.mockReturnValue('0xdifferentaddress');\n\n      // Act & Assert\n      await expect(service.verifyEthereumSignature(validVerificationData)).rejects.toThrow(\n        IdentityVerificationError\n      );\n\n      expect(mockPrisma.identityVerification.create).not.toHaveBeenCalled();\n    });\n\n    it('should throw error when user not found', async () => {\n      // Arrange\n      mockPrisma.user.findUnique.mockResolvedValue(null);\n\n      const { ethers } = require('ethers');\n      ethers.utils.isAddress.mockReturnValue(true);\n\n      // Act & Assert\n      await expect(service.verifyEthereumSignature(validVerificationData)).rejects.toThrow(\n        IdentityVerificationError\n      );\n\n      expect(mockPrisma.identityVerification.create).not.toHaveBeenCalled();\n    });\n\n    it('should throw error when merchant not found', async () => {\n      // Arrange\n      const mockUser = { id: validVerificationData.userId };\n      mockPrisma.user.findUnique.mockResolvedValue(mockUser);\n      mockPrisma.merchant.findUnique.mockResolvedValue(null);\n\n      const { ethers } = require('ethers');\n      ethers.utils.isAddress.mockReturnValue(true);\n\n      // Act & Assert\n      await expect(service.verifyEthereumSignature(validVerificationData)).rejects.toThrow(\n        IdentityVerificationError\n      );\n\n      expect(mockPrisma.identityVerification.create).not.toHaveBeenCalled();\n    });\n\n    it('should handle database errors gracefully', async () => {\n      // Arrange\n      const mockUser = { id: validVerificationData.userId };\n      const mockMerchant = { id: validVerificationData.merchantId };\n\n      mockPrisma.user.findUnique.mockResolvedValue(mockUser);\n      mockPrisma.merchant.findUnique.mockResolvedValue(mockMerchant);\n      mockPrisma.identityVerification.create.mockRejectedValue(new Error('Database error'));\n\n      const { ethers } = require('ethers');\n      ethers.utils.isAddress.mockReturnValue(true);\n      ethers.utils.verifyMessage.mockReturnValue(validVerificationData.address);\n\n      // Act & Assert\n      await expect(service.verifyEthereumSignature(validVerificationData)).rejects.toThrow(\n        IdentityVerificationError\n      );\n    });\n  });\n\n  describe('getVerificationById', () => {\n    it('should return verification details for valid ID', async () => {\n      // Arrange\n      const verificationId = 'verification-123-456-789';\n      const mockVerification = {\n        id: verificationId,\n        userId: 'user-123-456-789',\n        merchantId: 'merchant-123-456-789',\n        method: 'ethereum_signature',\n        status: 'verified',\n        confidence: 0.95,\n        address: '******************************************',\n        createdAt: new Date(),\n        updatedAt: new Date(),\n      };\n\n      mockPrisma.identityVerification.findUnique.mockResolvedValue(mockVerification);\n\n      // Act\n      const result = await service.getVerificationById(verificationId);\n\n      // Assert\n      expect(result).toEqual(mockVerification);\n      expect(mockPrisma.identityVerification.findUnique).toHaveBeenCalledWith({\n        where: { id: verificationId },\n      });\n    });\n\n    it('should throw error for non-existent verification', async () => {\n      // Arrange\n      const verificationId = 'non-existent-verification-id';\n      mockPrisma.identityVerification.findUnique.mockResolvedValue(null);\n\n      // Act & Assert\n      await expect(service.getVerificationById(verificationId)).rejects.toThrow(\n        IdentityVerificationError\n      );\n    });\n  });\n\n  describe('getUserVerifications', () => {\n    it('should return user verifications with pagination', async () => {\n      // Arrange\n      const userId = 'user-123-456-789';\n      const mockVerifications = [\n        {\n          id: 'verification-1-123-456',\n          userId,\n          method: 'ethereum_signature',\n          status: 'verified',\n          createdAt: new Date(),\n        },\n        {\n          id: 'verification-2-123-456',\n          userId,\n          method: 'ethereum_signature',\n          status: 'verified',\n          createdAt: new Date(),\n        },\n      ];\n\n      mockPrisma.identityVerification.findMany.mockResolvedValue(mockVerifications);\n      mockPrisma.identityVerification.count.mockResolvedValue(2);\n\n      // Act\n      const result = await service.getUserVerifications(userId, { page: 1, limit: 10 });\n\n      // Assert\n      expect(result.verifications).toEqual(mockVerifications);\n      expect(result.total).toBe(2);\n      expect(result.page).toBe(1);\n      expect(result.limit).toBe(10);\n\n      expect(mockPrisma.identityVerification.findMany).toHaveBeenCalledWith({\n        where: { userId },\n        orderBy: { createdAt: 'desc' },\n        skip: 0,\n        take: 10,\n      });\n    });\n\n    it('should handle empty results', async () => {\n      // Arrange\n      const userId = 'empty-user-123-456';\n      mockPrisma.identityVerification.findMany.mockResolvedValue([]);\n      mockPrisma.identityVerification.count.mockResolvedValue(0);\n\n      // Act\n      const result = await service.getUserVerifications(userId);\n\n      // Assert\n      expect(result.verifications).toEqual([]);\n      expect(result.total).toBe(0);\n    });\n  });\n\n  describe('updateVerificationStatus', () => {\n    it('should successfully update verification status', async () => {\n      // Arrange\n      const verificationId = 'verification-update-123';\n      const newStatus = 'rejected';\n      const reason = 'Invalid signature detected';\n\n      const mockUpdatedVerification = {\n        id: verificationId,\n        status: newStatus,\n        reason,\n        updatedAt: new Date(),\n      };\n\n      mockPrisma.identityVerification.update.mockResolvedValue(mockUpdatedVerification);\n\n      // Act\n      const result = await service.updateVerificationStatus(verificationId, newStatus, reason);\n\n      // Assert\n      expect(result).toEqual(mockUpdatedVerification);\n      expect(mockPrisma.identityVerification.update).toHaveBeenCalledWith({\n        where: { id: verificationId },\n        data: {\n          status: newStatus,\n          reason,\n          updatedAt: expect.any(Date),\n        },\n      });\n    });\n\n    it('should throw error for invalid verification ID', async () => {\n      // Arrange\n      const verificationId = 'invalid-verification-123';\n      mockPrisma.identityVerification.update.mockRejectedValue(new Error('Record not found'));\n\n      // Act & Assert\n      await expect(service.updateVerificationStatus(verificationId, 'rejected')).rejects.toThrow(\n        IdentityVerificationError\n      );\n    });\n  });\n\n  describe('getVerificationStatistics', () => {\n    it('should return verification statistics', async () => {\n      // Arrange\n      const merchantId = 'merchant-stats-123';\n      const dateFrom = new Date('2024-01-01');\n      const dateTo = new Date('2024-01-31');\n\n      mockPrisma.identityVerification.count\n        .mockResolvedValueOnce(100) // total\n        .mockResolvedValueOnce(85) // verified\n        .mockResolvedValueOnce(10) // pending\n        .mockResolvedValueOnce(5); // rejected\n\n      // Act\n      const result = await service.getVerificationStatistics(merchantId, dateFrom, dateTo);\n\n      // Assert\n      expect(result).toEqual({\n        total: 100,\n        verified: 85,\n        pending: 10,\n        rejected: 5,\n        verificationRate: 85,\n      });\n\n      // Verify database calls\n      expect(mockPrisma.identityVerification.count).toHaveBeenCalledTimes(4);\n    });\n\n    it('should handle zero verifications', async () => {\n      // Arrange\n      mockPrisma.identityVerification.count.mockResolvedValue(0);\n\n      // Act\n      const result = await service.getVerificationStatistics();\n\n      // Assert\n      expect(result.total).toBe(0);\n      expect(result.verificationRate).toBe(0);\n    });\n  });\n\n  describe('Error Handling', () => {\n    it('should handle network errors gracefully', async () => {\n      // Arrange\n      mockPrisma.user.findUnique.mockRejectedValue(new Error('Network error'));\n\n      // Act & Assert\n      await expect(service.verifyEthereumSignature(validVerificationData)).rejects.toThrow(\n        IdentityVerificationError\n      );\n    });\n\n    it('should handle timeout errors', async () => {\n      // Arrange\n      mockPrisma.user.findUnique.mockImplementation(\n        () => new Promise((_, reject) => setTimeout(() => reject(new Error('Timeout')), 100))\n      );\n\n      // Act & Assert\n      await expect(service.verifyEthereumSignature(validVerificationData)).rejects.toThrow(\n        IdentityVerificationError\n      );\n    });\n  });\n\n  describe('Input Validation', () => {\n    it('should validate required fields', async () => {\n      // Test missing address\n      await expect(\n        service.verifyEthereumSignature({\n          address: '',\n          message: 'test',\n          signature: '0xtest',\n          userId: 'user-validation-123',\n          merchantId: 'merchant-validation-123',\n        })\n      ).rejects.toThrow();\n\n      // Test missing message\n      await expect(\n        service.verifyEthereumSignature({\n          address: '******************************************',\n          message: '',\n          signature: '0xtest',\n          userId: 'user-validation-123',\n          merchantId: 'merchant-validation-123',\n        })\n      ).rejects.toThrow();\n\n      // Test missing signature\n      await expect(\n        service.verifyEthereumSignature({\n          address: '******************************************',\n          message: 'test',\n          signature: '',\n          userId: 'user-validation-123',\n          merchantId: 'merchant-validation-123',\n        })\n      ).rejects.toThrow();\n    });\n\n    it('should validate UUID format', async () => {\n      await expect(\n        service.verifyEthereumSignature({\n          address: '******************************************',\n          message: 'test',\n          signature: '0xtest',\n          userId: 'invalid-uuid',\n          merchantId: 'merchant-validation-123',\n        })\n      ).rejects.toThrow();\n    });\n  });\n});\n"], "version": 3}