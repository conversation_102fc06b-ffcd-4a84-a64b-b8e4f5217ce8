/**
 * Service Error
 *
 * This module provides a specialized error class for service-level errors.
 */
import { AppError, ErrorCode } from './AppError';
/**
 * Service Error
 */
export declare class ServiceError extends AppError {
    constructor(options: {
        message: string;
        code?: ErrorCode;
        statusCode?: number;
        details?: unknown;
        path?: string;
        requestId?: string;
        originalError?: Error;
    });
    /**
     * Create a validation error
     */
    static validation(message: string, details?: unknown): ServiceError;
    /**
     * Create a not found error
     */
    static notFound(entity: string, id?: string | number): ServiceError;
    /**
     * Create a conflict error
     */
    static conflict(message: string, details?: unknown): ServiceError;
    /**
     * Create a database error
     */
    static database(message: string, originalError?: Error): ServiceError;
    /**
     * Create an external service error
     */
    static external(message: string, originalError?: Error): ServiceError;
    /**
     * Create a business rule error
     */
    static businessRule(message: string, details?: unknown): ServiceError;
    /**
     * Create an unauthorized error
     */
    static unauthorized(message?: string): ServiceError;
    /**
     * Create a forbidden error
     */
    static forbidden(message?: string): ServiceError;
}
//# sourceMappingURL=ServiceError.d.ts.map