// jscpd:ignore-file
import { Merchant, Prisma } from "@prisma/client";
import { GenericService } from "../../core/GenericService";
import { MerchantRepository } from "../../repositories/refactored/merchant.repository";
import { ErrorFactory } from "../../utils/errors/ErrorFactory";
import { logger } from "../../lib/logger";
import { RepositoryFactory } from "../../factories/RepositoryFactory";
import { Merchant } from '../types';
import { GenericService } from "../../core/GenericService";
import { MerchantRepository } from "../../repositories/refactored/merchant.repository";
import { ErrorFactory } from "../../utils/errors/ErrorFactory";
import { logger } from "../../lib/logger";
import { RepositoryFactory } from "../../factories/RepositoryFactory";
import { Merchant } from '../types';


/**
 * Merchant service
 * This service handles business logic for merchants
 */
export class MerchantService extends GenericService<
  Merchant,
  Prisma.MerchantCreateInput,
  Prisma.MerchantUpdateInput
> {
  private merchantRepository: MerchantRepository;

  /**
   * Create a new merchant service
   */
  constructor() {
    const repositoryFactory: unknown = RepositoryFactory.getInstance();
    const repository: unknown = repositoryFactory.getRepository<
      Merchant,
      Prisma.MerchantCreateInput,
      Prisma.MerchantUpdateInput
    >('merchant') as MerchantRepository;

    super(repository, 'Merchant');
    this.merchantRepository = repository;
  }

  /**
   * Get merchants with pagination
   * @param options Query options
   * @returns Paginated merchants
   */
  async getMerchants(options: {
    limit?: number;
    offset?: number;
    search?: string;
    status?: string;
  }): Promise<{ data: Merchant[]; total: number }> {
    try {
      return await this.merchantRepository.findMerchants(options);
    } catch (error) {
      logger.error('Error getting merchants:', error);
      throw ErrorFactory.handle(error);
    }
  }

  /**
   * Get a merchant by ID
   * @param id Merchant ID
   * @returns Merchant or null
   */
  async getMerchantById(id: string): Promise<Merchant | null> {
    try {
      return await this.repository.findById(id);
    } catch (error) {
      logger.error(`Error getting merchant by ID ${id}:`, error);
      throw ErrorFactory.handle(error);
    }
  }

  /**
   * Get a merchant by email
   * @param email Merchant email
   * @returns Merchant or null
   */
  async getMerchantByEmail(email: string): Promise<Merchant | null> {
    try {
      return await this.merchantRepository.findByEmail(email);
    } catch (error) {
      logger.error(`Error getting merchant by email ${email}:`, error);
      throw ErrorFactory.handle(error);
    }
  }

  /**
   * Create a new merchant
   * @param data Merchant data
   * @returns Created merchant
   */
  async createMerchant(data: { name: string;
    email: string;
    phone?: string;
    website?: string;
    address?: string;
    country?: string;
    businessType?: string;
    taxId?: string;
    status?: string;
  }): Promise<Merchant> {
    try {
      // Check if email is already in use
      const existingMerchant: unknown = await this.getMerchantByEmail(data.email);

      if (existingMerchant) {
        throw ErrorFactory.conflict('Email is already in use');
      }

      // Create merchant
      const merchant: unknown = await this.repository.create({
        name: data.name,
        email: data.email,
        phone: data.phone,
        website: data.website,
        address: data.address,
        country: data.country,
        businessType: data.businessType,
        taxId: data.taxId,
        status: data.status || 'PENDING'
      });

      // Log merchant creation
      logger.info(`Merchant created: ${merchant.id}`, {
        merchantId: merchant.id,
        name: merchant.name,
        email: merchant.email
      });

      return merchant;
    } catch (error) {
      logger.error('Error creating merchant:', error);
      throw ErrorFactory.handle(error);
    }
  }

  /**
   * Update a merchant
   * @param id Merchant ID
   * @param data Merchant data
   * @returns Updated merchant
   */
  async updateMerchant(id: string, data: Prisma.MerchantUpdateInput): Promise<Merchant> {
    try {
      // Get merchant
      const merchant: unknown = await this.getMerchantById(id);

      // Check if merchant exists
      if (!merchant) {
        throw ErrorFactory.notFound('Merchant', id);
      }

      // Check if email is already in use
      if (data.email && data.email !== merchant.email) {
        const existingMerchant: unknown = await this.getMerchantByEmail(data.email as string);

        if (existingMerchant) {
          throw ErrorFactory.conflict('Email is already in use');
        }
      }

      // Update merchant
      const updatedMerchant: unknown = await this.repository.update(id, data);

      // Log merchant update
      logger.info(`Merchant updated: ${id}`, {
        merchantId: id,
        updatedFields: Object.keys(data)
      });

      return updatedMerchant;
    } catch (error) {
      logger.error(`Error updating merchant ${id}:`, error);
      throw ErrorFactory.handle(error);
    }
  }

  /**
   * Delete a merchant
   * @param id Merchant ID
   * @returns Deleted merchant
   */
  async deleteMerchant(id: string): Promise<Merchant> {
    try {
      // Get merchant
      const merchant: unknown = await this.getMerchantById(id);

      // Check if merchant exists
      if (!merchant) {
        throw ErrorFactory.notFound('Merchant', id);
      }

      // Delete merchant
      const deletedMerchant: unknown = await this.repository.delete(id);

      // Log merchant deletion
      logger.info(`Merchant deleted: ${id}`, {
        merchantId: id
      });

      return deletedMerchant;
    } catch (error) {
      logger.error(`Error deleting merchant ${id}:`, error);
      throw ErrorFactory.handle(error);
    }
  }

  /**
   * Get merchant statistics
   * @param merchantId Merchant ID
   * @param options Query options
   * @returns Merchant statistics
   */
  async getMerchantStats(merchantId: string, options: { startDate: Date;
    endDate: Date;
  }): Promise<{
    transactionCount: number;
    transactionAmount: number;
    successRate: number;
    averageTransactionAmount: number;
    byDay: { date: string; count: number; amount: number }[];
    byMethod: { method: string; count: number; amount: number }[];
  }> {
    try {
      // Get merchant
      const merchant: unknown = await this.getMerchantById(merchantId);

      // Check if merchant exists
      if (!merchant) {
        throw ErrorFactory.notFound('Merchant', merchantId);
      }

      // Get merchant statistics
      return await this.merchantRepository.getMerchantStats(merchantId, options);
    } catch (error) {
      logger.error(`Error getting statistics for merchant ${merchantId}:`, error);
      throw ErrorFactory.handle(error);
    }
  }

  /**
   * Verify a merchant
   * @param id Merchant ID
   * @returns Updated merchant
   */
  async verifyMerchant(id: string): Promise<Merchant> {
    try {
      // Get merchant
      const merchant: unknown = await this.getMerchantById(id);

      // Check if merchant exists
      if (!merchant) {
        throw ErrorFactory.notFound('Merchant', id);
      }

      // Check if merchant is already verified
      if (merchant.status === 'ACTIVE') {
        return merchant;
      }

      // Update merchant status
      const updatedMerchant: unknown = await this.repository.update(id, {
        status: 'ACTIVE',
        verifiedAt: new Date()
      });

      // Log merchant verification
      logger.info(`Merchant verified: ${id}`, {
        merchantId: id
      });

      return updatedMerchant;
    } catch (error) {
      logger.error(`Error verifying merchant ${id}:`, error);
      throw ErrorFactory.handle(error);
    }
  }

  /**
   * Suspend a merchant
   * @param id Merchant ID
   * @param reason Suspension reason
   * @returns Updated merchant
   */
  async suspendMerchant(id: string, reason: string): Promise<Merchant> {
    try {
      // Get merchant
      const merchant: unknown = await this.getMerchantById(id);

      // Check if merchant exists
      if (!merchant) {
        throw ErrorFactory.notFound('Merchant', id);
      }

      // Update merchant status
      const updatedMerchant: unknown = await this.repository.update(id, {
        status: 'SUSPENDED',
        suspensionReason: reason
      });

      // Log merchant suspension
      logger.info(`Merchant suspended: ${id}`, {
        merchantId: id,
        reason
      });

      return updatedMerchant;
    } catch (error) {
      logger.error(`Error suspending merchant ${id}:`, error);
      throw ErrorFactory.handle(error);
    }
  }
}
