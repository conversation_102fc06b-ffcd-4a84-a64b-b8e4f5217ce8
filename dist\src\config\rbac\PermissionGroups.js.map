{"version": 3, "file": "PermissionGroups.js", "sourceRoot": "", "sources": ["../../../../src/config/rbac/PermissionGroups.ts"], "names": [], "mappings": ";AAAA,oBAAoB;AACpB;;;;GAIG;;;AAEH;;GAEG;AACU,QAAA,iBAAiB,GAAO;IACjC,cAAc;CACjB,CAAC;AAEF;;GAEG;AACU,QAAA,oBAAoB,GAAQ;IACrC,IAAI,EAAE;QACF,gBAAgB;KACnB;IACD,KAAK,EAAE;QACH,kBAAkB;QAClB,kBAAkB;QAClB,kBAAkB;KACrB;IACD,OAAO,EAAE;QACL,yBAAyB;QACzB,4BAA4B;QAC5B,2BAA2B;KAC9B;IACD,GAAG,EAAE;QACD,gBAAgB;QAChB,kBAAkB;QAClB,kBAAkB;QAClB,kBAAkB;QAClB,yBAAyB;QACzB,4BAA4B;QAC5B,2BAA2B;QAC3B,4BAA4B;QAC5B,uBAAuB;QACvB,0BAA0B;QAC1B,4BAA4B;KAC/B;CACJ,CAAC;AAEF;;GAEG;AACU,QAAA,mBAAmB,GAAQ;IACpC,IAAI,EAAE;QACF,eAAe;KAClB;IACD,KAAK,EAAE;QACH,kBAAkB;QAClB,iBAAiB;QACjB,iBAAiB;KACpB;IACD,OAAO,EAAE;QACL,sBAAsB;QACtB,wBAAwB;QACxB,wBAAwB;QACxB,wBAAwB;KAC3B;IACD,QAAQ,EAAE;QACN,uBAAuB;QACvB,yBAAyB;QACzB,yBAAyB;QACzB,yBAAyB;KAC5B;IACD,OAAO,EAAE;QACL,sBAAsB;QACtB,wBAAwB;KAC3B;IACD,IAAI,EAAE;QACF,WAAW;QACX,aAAa;KAChB;IACD,GAAG,EAAE;QACD,eAAe;QACf,kBAAkB;QAClB,iBAAiB;QACjB,iBAAiB;QACjB,sBAAsB;QACtB,wBAAwB;QACxB,wBAAwB;QACxB,wBAAwB;QACxB,uBAAuB;QACvB,yBAAyB;QACzB,yBAAyB;QACzB,yBAAyB;QACzB,sBAAsB;QACtB,wBAAwB;QACxB,WAAW;QACX,aAAa;KAChB;CACJ,CAAC;AAEF;;GAEG;AACU,QAAA,wBAAwB,GAAQ;IACzC,IAAI,EAAE;QACF,2BAA2B;QAC3B,8BAA8B;KACjC;IACD,KAAK,EAAE;QACH,6BAA6B;QAC7B,6BAA6B;QAC7B,6BAA6B;KAChC;IACD,GAAG,EAAE;QACD,2BAA2B;QAC3B,6BAA6B;QAC7B,6BAA6B;QAC7B,6BAA6B;QAC7B,8BAA8B;KACjC;CACJ,CAAC;AAEF;;GAEG;AACU,QAAA,wBAAwB,GAAQ;IACzC,IAAI,EAAE;QACF,yBAAyB;KAC5B;IACD,KAAK,EAAE;QACH,2BAA2B;QAC3B,2BAA2B;QAC3B,2BAA2B;KAC9B;IACD,GAAG,EAAE;QACD,yBAAyB;QACzB,2BAA2B;QAC3B,2BAA2B;QAC3B,2BAA2B;KAC9B;CACJ,CAAC;AAEF;;GAEG;AACU,QAAA,qBAAqB,GAAQ;IACtC,IAAI,EAAE;QACF,gBAAgB;KACnB;IACD,KAAK,EAAE;QACH,kBAAkB;QAClB,qBAAqB;KACxB;IACD,GAAG,EAAE;QACD,gBAAgB;QAChB,kBAAkB;QAClB,qBAAqB;KACxB;CACJ,CAAC;AAEF;;GAEG;AACU,QAAA,sBAAsB,GAAQ;IACvC,IAAI,EAAE;QACF,iBAAiB;KACpB;IACD,KAAK,EAAE;QACH,sBAAsB;KACzB;IACD,MAAM,EAAE;QACJ,aAAa;QACb,eAAe;QACf,eAAe;QACf,eAAe;KAClB;IACD,GAAG,EAAE;QACD,iBAAiB;QACjB,sBAAsB;QACtB,aAAa;QACb,eAAe;QACf,eAAe;QACf,eAAe;QACf,0BAA0B;QAC1B,4BAA4B;KAC/B;CACJ,CAAC;AAEF;;GAEG;AACU,QAAA,oBAAoB,GAAQ;IACrC,IAAI,EAAE;QACF,eAAe;KAClB;IACD,KAAK,EAAE;QACH,iBAAiB;KACpB;IACD,GAAG,EAAE;QACD,eAAe;QACf,iBAAiB;KACpB;CACJ,CAAC;AAEF;;GAEG;AACU,QAAA,oBAAoB,GAAQ;IACrC,IAAI,EAAE;QACF,eAAe;KAClB;IACD,KAAK,EAAE;QACH,iBAAiB;KACpB;IACD,GAAG,EAAE;QACD,eAAe;QACf,iBAAiB;KACpB;CACJ,CAAC;AAEF;;GAEG;AACU,QAAA,wBAAwB,GAAQ;IACzC,IAAI,EAAE;QACF,oBAAoB;KACvB;IACD,KAAK,EAAE;QACH,sBAAsB;QACtB,sBAAsB;QACtB,sBAAsB;KACzB;IACD,GAAG,EAAE;QACD,oBAAoB;QACpB,sBAAsB;QACtB,sBAAsB;QACtB,sBAAsB;KACzB;CACJ,CAAC;AAEF;;GAEG;AACU,QAAA,gBAAgB,GAAQ;IACjC,IAAI,EAAE;QACF,YAAY;KACf;IACD,KAAK,EAAE;QACH,cAAc;QACd,cAAc;QACd,cAAc;KACjB;IACD,GAAG,EAAE;QACD,YAAY;QACZ,cAAc;QACd,cAAc;QACd,cAAc;KACjB;CACJ,CAAC;AAEF;;GAEG;AACU,QAAA,sBAAsB,GAAQ;IACvC,IAAI,EAAE;QACF,kBAAkB;KACrB;IACD,KAAK,EAAE;QACH,oBAAoB;QACpB,oBAAoB;QACpB,oBAAoB;KACvB;IACD,GAAG,EAAE;QACD,kBAAkB;QAClB,oBAAoB;QACpB,oBAAoB;QACpB,oBAAoB;KACvB;CACJ,CAAC;AAEF;;GAEG;AACU,QAAA,eAAe,GAAO;IAC/B,GAAG,yBAAiB;IACpB,GAAG,4BAAoB,CAAC,GAAG;IAC3B,GAAG,2BAAmB,CAAC,GAAG;IAC1B,GAAG,gCAAwB,CAAC,GAAG;IAC/B,GAAG,gCAAwB,CAAC,GAAG;IAC/B,GAAG,6BAAqB,CAAC,GAAG;IAC5B,GAAG,8BAAsB,CAAC,GAAG;IAC7B,GAAG,4BAAoB,CAAC,GAAG;IAC3B,GAAG,4BAAoB,CAAC,GAAG;IAC3B,GAAG,gCAAwB,CAAC,GAAG;IAC/B,GAAG,wBAAgB,CAAC,GAAG;IACvB,GAAG,8BAAsB,CAAC,GAAG;CAChC,CAAC"}