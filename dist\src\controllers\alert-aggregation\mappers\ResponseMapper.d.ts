/**
 * Response Mapper
 *
 * Handles response formatting for alert aggregation operations.
 */
import { Response } from 'express';
import { ApiResponse, AggregationRuleResponse, CorrelationRuleResponse } from '../types/AlertAggregationTypes';
import { AppError } from '../../../utils/errors/AppError';
/**
 * Response mapper for alert aggregation
 */
export declare class ResponseMapper {
    /**
     * Send success response
     */
    static sendSuccess<T>(res: Response, data: T, message?: string, statusCode?: number, pagination?: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
    }): void;
    /**
     * Send error response
     */
    static sendError(res: Response, error: AppError | Error, statusCode?: number): void;
    /**
     * Send aggregation rules list response
     */
    static sendAggregationRulesList(res: Response, rules: AggregationRuleResponse[], total: number, page?: number, limit?: number): void;
    /**
     * Send single aggregation rule response
     */
    static sendAggregationRule(res: Response, rule: AggregationRuleResponse, message?: string): void;
    /**
     * Send aggregation rule created response
     */
    static sendAggregationRuleCreated(res: Response, rule: AggregationRuleResponse): void;
    /**
     * Send aggregation rule updated response
     */
    static sendAggregationRuleUpdated(res: Response, rule: AggregationRuleResponse): void;
    /**
     * Send aggregation rule deleted response
     */
    static sendAggregationRuleDeleted(res: Response): void;
    /**
     * Send correlation rules list response
     */
    static sendCorrelationRulesList(res: Response, rules: CorrelationRuleResponse[], total: number, page?: number, limit?: number): void;
    /**
     * Send single correlation rule response
     */
    static sendCorrelationRule(res: Response, rule: CorrelationRuleResponse, message?: string): void;
    /**
     * Send correlation rule created response
     */
    static sendCorrelationRuleCreated(res: Response, rule: CorrelationRuleResponse): void;
    /**
     * Send correlation rule updated response
     */
    static sendCorrelationRuleUpdated(res: Response, rule: CorrelationRuleResponse): void;
    /**
     * Send correlation rule deleted response
     */
    static sendCorrelationRuleDeleted(res: Response): void;
    /**
     * Send validation error response
     */
    static sendValidationError(res: Response, errors: any[], message?: string): void;
    /**
     * Send authorization error response
     */
    static sendAuthorizationError(res: Response, message?: string, requiredRole?: string): void;
    /**
     * Send not found error response
     */
    static sendNotFoundError(res: Response, resource?: string): void;
    /**
     * Send internal server error response
     */
    static sendInternalServerError(res: Response, message?: string): void;
    /**
     * Format pagination metadata
     */
    static formatPagination(page: number, limit: number, total: number): {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
        hasNext: boolean;
        hasPrev: boolean;
    };
    /**
     * Create API response wrapper
     */
    static createApiResponse<T>(success: boolean, data?: T, message?: string, error?: any): ApiResponse<T>;
    /**
     * Handle async controller method
     */
    static asyncHandler(fn: Function): (req: any, res: Response, next: Function) => void;
    /**
     * Set response headers for API
     */
    static setApiHeaders(res: Response): void;
    /**
     * Log response for debugging
     */
    static logResponse(method: string, url: string, statusCode: number, responseTime: number): void;
}
//# sourceMappingURL=ResponseMapper.d.ts.map