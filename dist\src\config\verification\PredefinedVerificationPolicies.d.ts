/**
 * Predefined Verification Policies
 *
 * Defines predefined verification policies.
 */
/**
 * High-value transaction policy
 *
 * Requires document and phone verification for transactions over $10,000.
 */
export declare const HIGH_VALUE_POLICY: any;
/**
 * Medium-value transaction policy
 *
 * Requires phone verification for transactions over $1,000.
 */
export declare const MEDIUM_VALUE_POLICY: any;
/**
 * Crypto transaction policy
 *
 * Requires blockchain verification for crypto transactions.
 */
export declare const CRYPTO_TRANSACTION_POLICY: any;
/**
 * New merchant policy
 *
 * Requires additional verification for new merchants.
 */
export declare const NEW_MERCHANT_POLICY: any;
/**
 * High-risk country policy
 *
 * Requires additional verification for transactions from high-risk countries.
 */
export declare const HIGH_RISK_COUNTRY_POLICY: any;
/**
 * Binance Pay policy
 *
 * Specific verification requirements for Binance Pay.
 */
export declare const BINANCE_PAY_POLICY: any;
/**
 * Binance TRC20 policy
 *
 * Specific verification requirements for Binance TRC20.
 */
export declare const BINANCE_TRC20_POLICY: any;
/**
 * All predefined policies
 */
export declare const PREDEFINED_VERIFICATION_POLICIES: any;
//# sourceMappingURL=PredefinedVerificationPolicies.d.ts.map