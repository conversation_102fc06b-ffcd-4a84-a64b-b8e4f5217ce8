"use strict";
// jscpd:ignore-file
Object.defineProperty(exports, "__esModule", { value: true });
const asyncHandler_1 = require("../utils/asyncHandler");
/**
 * Transaction Analytics Controller
 *
 * Provides analytics data for transactions
 */
class TransactionAnalyticsController {
    constructor() {
        /**
         * Get advanced analytics for a merchant
         */
        this.getAdvancedAnalytics = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                const { merchantId } = req.params;
                const { dateRange, filters } = req.query;
                // In a real implementation, this would query for detailed analytics
                // For now, we'll return enhanced mock data
                const advancedAnalytics = {
                    metrics: { totalRevenue: 15000,
                        transactionCount: 250,
                        successfulTransactions: 235,
                        avgTransactionValue: 63.83,
                        successRate: 94,
                        currentMonthRevenue: 5500,
                        previousMonthRevenue: 4800,
                        revenueGrowth: 14.58
                    },
                    revenueData: Array.from({ length: 30 }, (_, i) => ({
                        date: new Date(new Date().setDate(new Date().getDate() - 30 + i)).toISOString().split("T")[0],
                        amount: Math.floor(Math.random() * 500) + 100
                    })),
                    methodDistributionData: [
                        { name: "Binance Pay", value: 120, percent: 48 },
                        { name: "USDT", value: 85, percent: 34 },
                        { name: "Binance C2C", value: 45, percent: 18 }
                    ],
                    statusData: Array.from({ length: 30 }, (_, i) => {
                        const total = Math.floor(Math.random() * 15) + 5;
                        const success = Math.floor(total * (0.85 + Math.random() * 0.15));
                        return {
                            date: new Date(new Date().setDate(new Date().getDate() - 30 + i)).toISOString().split("T")[0],
                            success,
                            failed: total - success,
                            total,
                            successRate: (success / total) * 100
                        };
                    }),
                    hourlyData: Array.from({ length: 24 }, (_, i) => ({
                        hour: i,
                        count: Math.floor(Math.random() * 12) + 1,
                        amount: (Math.floor(Math.random() * 12) + 1) * 75
                    })),
                    forecast: { nextMonthRevenue: 6200,
                        confidenceInterval: [5900, 6500],
                        growth: 12.7
                    }
                };
                return res.status(200).json({
                    status: "success",
                    data: advancedAnalytics
                });
            }
            catch (error) {
                return res.status(500).json({
                    status: "error",
                    message: error.message || "Failed to retrieve advanced analytics data"
                });
            }
        });
    }
}
exports.default = new TransactionAnalyticsController();
//# sourceMappingURL=transaction-analytics.controller.js.map