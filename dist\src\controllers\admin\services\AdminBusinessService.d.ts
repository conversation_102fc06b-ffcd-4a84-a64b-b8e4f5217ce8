/**
 * Admin Business Service
 *
 * Handles business logic for admin operations.
 */
import { PrismaClient } from '@prisma/client';
import { CreateAdminUserRequest, UpdateAdminUserRequest, AdminUserResponse, DashboardDataResponse, AdminUserFilters, PaginationParams, DashboardStatistics } from '../types/AdminControllerTypes';
/**
 * Business service for admin operations
 */
export declare class AdminBusinessService {
    private prisma;
    constructor(prisma: PrismaClient);
    /**
     * Get dashboard data
     */
    getDashboardData(): Promise<DashboardDataResponse>;
    /**
     * Get dashboard statistics
     */
    getDashboardStatistics(): Promise<DashboardStatistics>;
    /**
     * Get all admin users with optional filtering and pagination
     */
    getAdminUsers(filters?: AdminUserFilters, pagination?: PaginationParams): Promise<{
        users: AdminUserResponse[];
        total: number;
    }>;
    /**
     * Get admin user by ID
     */
    getAdminUserById(id: string): Promise<AdminUserResponse>;
    /**
     * Create admin user
     */
    createAdminUser(data: CreateAdminUserRequest, createdById: string): Promise<AdminUserResponse>;
    /**
     * Update admin user
     */
    updateAdminUser(id: string, data: UpdateAdminUserRequest): Promise<AdminUserResponse>;
    /**
     * Delete admin user
     */
    deleteAdminUser(id: string): Promise<void>;
    /**
     * Map admin user to response format
     */
    private mapAdminUserToResponse;
}
//# sourceMappingURL=AdminBusinessService.d.ts.map