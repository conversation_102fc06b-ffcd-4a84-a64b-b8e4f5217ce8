"use strict";
/**
 * Base Response Mapper
 *
 * Centralized response handling to eliminate code duplication across controllers.
 * Provides consistent API responses with proper error handling and logging.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseResponseMapper = void 0;
const AppError_1 = require("../../core/errors/AppError");
const logger_1 = require("../../utils/logger");
/**
 * Base Response Mapper - Eliminates duplication across all controllers
 */
class BaseResponseMapper {
    /**
     * Send successful response
     */
    static sendSuccess(res, data, message = 'Operation completed successfully', statusCode = 200, pagination) {
        const response = {
            success: true,
            data,
            message,
            pagination,
            timestamp: new Date(),
            requestId: res.locals.requestId ?? 'unknown',
        };
        res.status(statusCode).json(response);
    }
    /**
     * Send error response
     */
    static sendError(res, error, statusCode) {
        let errorResponse;
        if (error instanceof AppError_1.AppError) {
            errorResponse = {
                success: false,
                error: {
                    message: error.message,
                    code: error.code,
                    type: error.type,
                    details: error.details,
                },
                timestamp: new Date(),
                requestId: res.locals.requestId ?? 'unknown',
            };
            statusCode = statusCode ?? error.statusCode ?? 400;
        }
        else {
            errorResponse = {
                success: false,
                error: {
                    message: error.message,
                    type: 'INTERNAL',
                },
                timestamp: new Date(),
                requestId: res.locals.requestId ?? 'unknown',
            };
            statusCode = statusCode ?? 500;
        }
        // Log error for monitoring
        logger_1.logger.error('API Error Response', {
            error: error.message,
            stack: error.stack,
            requestId: res.locals.requestId,
            statusCode,
        });
        res.status(statusCode).json(errorResponse);
    }
    /**
     * Send paginated response
     */
    static sendPaginated(res, data, total, page, limit, message = 'Data retrieved successfully') {
        const totalPages = Math.ceil(total / limit);
        const pagination = {
            page,
            limit,
            total,
            totalPages,
            hasNext: page < totalPages,
            hasPrev: page > 1,
        };
        this.sendSuccess(res, data, message, 200, pagination);
    }
    /**
     * Send created response
     */
    static sendCreated(res, data, message = 'Resource created successfully') {
        this.sendSuccess(res, data, message, 201);
    }
    /**
     * Send updated response
     */
    static sendUpdated(res, data, message = 'Resource updated successfully') {
        this.sendSuccess(res, data, message, 200);
    }
    /**
     * Send deleted response
     */
    static sendDeleted(res, message = 'Resource deleted successfully') {
        this.sendSuccess(res, null, message, 200);
    }
    /**
     * Send not found response
     */
    static sendNotFound(res, message = 'Resource not found') {
        const error = new AppError_1.AppError({
            message,
            statusCode: 404,
            type: 'NOT_FOUND',
        });
        this.sendError(res, error);
    }
    /**
     * Send validation error response
     */
    static sendValidationError(res, details, message = 'Validation failed') {
        const error = new AppError_1.AppError({
            message,
            statusCode: 400,
            type: 'VALIDATION_ERROR',
            details,
        });
        this.sendError(res, error);
    }
    /**
     * Send unauthorized response
     */
    static sendUnauthorized(res, message = 'Unauthorized access') {
        const error = new AppError_1.AppError({
            message,
            statusCode: 401,
            type: 'UNAUTHORIZED',
        });
        this.sendError(res, error);
    }
    /**
     * Send forbidden response
     */
    static sendForbidden(res, message = 'Access forbidden') {
        const error = new AppError_1.AppError({
            message,
            statusCode: 403,
            type: 'FORBIDDEN',
        });
        this.sendError(res, error);
    }
    /**
     * Send conflict response
     */
    static sendConflict(res, message = 'Resource conflict') {
        const error = new AppError_1.AppError({
            message,
            statusCode: 409,
            type: 'CONFLICT',
        });
        this.sendError(res, error);
    }
    /**
     * Send rate limit response
     */
    static sendRateLimit(res, message = 'Rate limit exceeded') {
        const error = new AppError_1.AppError({
            message,
            statusCode: 429,
            type: 'RATE_LIMIT',
        });
        this.sendError(res, error);
    }
    /**
     * Send internal server error response
     */
    static sendInternalError(res, message = 'Internal server error') {
        const error = new AppError_1.AppError({
            message,
            statusCode: 500,
            type: 'INTERNAL_ERROR',
        });
        this.sendError(res, error);
    }
    /**
     * Send service unavailable response
     */
    static sendServiceUnavailable(res, message = 'Service temporarily unavailable') {
        const error = new AppError_1.AppError({
            message,
            statusCode: 503,
            type: 'SERVICE_UNAVAILABLE',
        });
        this.sendError(res, error);
    }
    /**
     * Create pagination info
     */
    static createPagination(page, limit, total) {
        const totalPages = Math.ceil(total / limit);
        return {
            page,
            limit,
            total,
            totalPages,
            hasNext: page < totalPages,
            hasPrev: page > 1,
        };
    }
    /**
     * Handle async controller method with automatic error handling
     */
    static async handleAsync(res, asyncFn, successMessage, successStatusCode) {
        try {
            const result = await asyncFn();
            this.sendSuccess(res, result, successMessage, successStatusCode);
        }
        catch (error) {
            this.sendError(res, error);
        }
    }
}
exports.BaseResponseMapper = BaseResponseMapper;
//# sourceMappingURL=BaseResponseMapper.js.map