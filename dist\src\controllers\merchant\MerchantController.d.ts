import { Request } from "express";
import { <PERSON><PERSON><PERSON>ontroller } from "../base/CrudController";
import { Merchant, Prisma } from "@prisma/client";
/**
 * Merchant controller
 */
export declare class MerchantController extends CrudController<Merchant, Prisma.MerchantCreateInput, Prisma.MerchantUpdateInput> {
    private merchantService;
    constructor();
    /**
     * Get merchant statistics
     * @route GET /api/merchants/:id/stats
     */
    getStats: any;
    /**
     * Verify merchant
     * @route POST /api/merchants/:id/verify
     */
    verifyMerchant: any;
    /**
     * Suspend merchant
     * @route POST /api/merchants/:id/suspend
     */
    suspendMerchant: any;
    /**
     * Get all merchants
     * @param page Page number
     * @param limit Items per page
     * @param offset Offset
     * @param filters Filters
     * @returns Merchants and total count
     */
    protected getAllEntities(page: number, limit: number, offset: number, filters: Record<string, any>): Promise<{
        data: Merchant[];
        total: number;
    }>;
    /**
     * Get merchant by ID
     * @param id Merchant ID
     * @returns Merchant
     */
    protected getEntityById(id: string): Promise<Merchant>;
    /**
     * Create merchant
     * @param data Merchant data
     * @returns Created merchant
     */
    protected createEntity(data: Prisma.MerchantCreateInput): Promise<Merchant>;
    /**
     * Update merchant
     * @param id Merchant ID
     * @param data Merchant data
     * @returns Updated merchant
     */
    protected updateEntity(id: string, data: Prisma.MerchantUpdateInput): Promise<Merchant>;
    /**
     * Delete merchant
     * @param id Merchant ID
     */
    protected deleteEntity(id: string): Promise<void>;
    /**
     * Parse filters from request
     * @param req Request
     * @returns Filters
     */
    protected parseFilters(req: Request): Record<string, any>;
    /**
     * Validate create input
     * @param req Request
     */
    protected validateCreateInput(req: Request): void;
    /**
     * Validate update input
     * @param req Request
     */
    protected validateUpdateInput(req: Request): void;
    /**
     * Validate email format
     * @param email Email
     * @returns Whether email is valid
     */
    private isValidEmail;
}
export default MerchantController;
//# sourceMappingURL=MerchantController.d.ts.map