// jscpd:ignore-file
/**
 * Database Verifier
 *
 * This utility provides functions for verifying the database schema and health.
 * It ensures that all required tables and columns exist and are properly configured.
 */

import { Client } from 'pg';
import { logger } from '../lib/logger';
import { getDatabaseConfig } from '../config/database.config';
import { TableSchema, VerificationResult } from '../types';
import { DatabaseConfig } from '../types/database';
import { logger } from '../lib/logger';
import { getDatabaseConfig } from '../config/database.config';
import { TableSchema, VerificationResult } from '../types';
import { DatabaseConfig } from '../types/database';

// Foreign key schema
interface ForeignKeySchema {
  column: string;
  referencedTable: string;
  referencedColumn: string;
}

// Database verifier class
export class DatabaseVerifier {
  private client: Client;
  private requiredTables: TableSchema[];

  /**
   * Constructor
   * @param requiredTables List of required tables
   */
  constructor(requiredTables?: TableSchema[]) {
    const dbConfig: unknown = getDatabaseConfig();
    this.client = new Client(dbConfig);
    this.requiredTables = requiredTables || [
      {
        name: 'users',
        columns: ['id', 'email', 'hashedPassword', 'role', 'createdAt', 'updatedAt'],
      },
      {
        name: 'merchants',
        columns: ['id', 'userId', 'name', 'createdAt', 'updatedAt'],
        foreignKeys: [{ column: 'userId', referencedTable: 'users', referencedColumn: 'id' }],
      },
      {
        name: 'payment_methods',
        columns: ['id', 'name', 'type', 'createdAt', 'updatedAt'],
      },
      {
        name: 'transactions',
        columns: ['id', 'merchantId', 'amount', 'status', 'createdAt', 'updatedAt'],
        foreignKeys: [
          { column: 'merchantId', referencedTable: 'merchants', referencedColumn: 'id' },
        ],
      },
      {
        name: 'permissions',
        columns: ['id', 'resource', 'action', 'description', 'createdAt', 'updatedAt'],
      },
      {
        name: 'roles',
        columns: ['id', 'type', 'name', 'description', 'isSystem', 'createdAt', 'updatedAt'],
      },
      {
        name: 'role_permissions',
        columns: ['id', 'roleId', 'permissionId', 'createdAt', 'updatedAt'],
        foreignKeys: [
          { column: 'roleId', referencedTable: 'roles', referencedColumn: 'id' },
          { column: 'permissionId', referencedTable: 'permissions', referencedColumn: 'id' },
        ],
      },
      {
        name: 'user_roles',
        columns: ['id', 'userId', 'roleId', 'createdAt', 'updatedAt'],
        foreignKeys: [
          { column: 'userId', referencedTable: 'users', referencedColumn: 'id' },
          { column: 'roleId', referencedTable: 'roles', referencedColumn: 'id' },
        ],
      },
      {
        name: 'system_settings',
        columns: ['id', 'key', 'value', 'updatedById', 'updatedAt'],
      },
      {
        name: 'webhooks',
        columns: ['id', 'merchantId', 'url', 'events', 'createdAt', 'updatedAt'],
        foreignKeys: [
          { column: 'merchantId', referencedTable: 'merchants', referencedColumn: 'id' },
        ],
      },
    ];

    logger.info(`Database verifier initialized for database: ${dbConfig.database}`);
  }

  /**
   * Initialize database verifier
   */

  public async initialize(): Promise<void> {
    try {
      await this.client.connect();
      logger.info('Connected to database');
    } catch (error) {
      logger.error('Failed to initialize database verifier:', error);
      throw error;
    }
  }

  /**
   * Check if a table exists
   * @param tableName Table name
   * @returns True if table exists
   */
  public async tableExists(tableName: string): Promise<boolean> {
    try {
      // Try to query the table directly
      const query: Record<string, string | string[]> = `SELECT 1 FROM "${tableName}" LIMIT 1;`;
      await this.client.query(query);
      return true;
    } catch (error) {
      if (error.code === '42P01') {
        // undefined_table
        return false;
      } else {
        // Try the information_schema approach
        try {
          const query: Record<string, string | string[]> = `
            SELECT EXISTS (
              SELECT FROM information_schema.tables
              WHERE table_schema = 'public'
              AND table_name = $1
            );
          `;

          const result: any = await this.client.query(query, [tableName]);
          return result.rows[0].exists;
        } catch (infoError) {
          logger.error(`Error checking if table ${tableName} exists:`, infoError);
          return false;
        }
      }
    }
  }

  /**
   * Check if a column exists in a table
   * @param tableName Table name
   * @param columnName Column name
   * @returns True if column exists
   */
  public async columnExists(tableName: string, columnName: string): Promise<boolean> {
    try {
      const query: Record<string, string | string[]> = `
        SELECT EXISTS (
          SELECT FROM information_schema.columns
          WHERE table_schema = 'public'
          AND table_name = $1
          AND column_name = $2
        );
      `;

      const result: any = await this.client.query(query, [tableName, columnName]);
      return result.rows[0].exists;
    } catch (error) {
      logger.error(`Error checking if column ${columnName} exists in table ${tableName}:`, error);
      return false;
    }
  }

  /**
   * Check if a foreign key exists
   * @param tableName Table name
   * @param columnName Column name
   * @param referencedTable Referenced table
   * @param referencedColumn Referenced column
   * @returns True if foreign key exists
   */
  public async foreignKeyExists(
    tableName: string,
    columnName: string,
    referencedTable: string,
    referencedColumn: string = 'id'
  ): Promise<boolean> {
    try {
      const query: Record<string, string | string[]> = `
        SELECT EXISTS (
          SELECT FROM information_schema.table_constraints tc
          JOIN information_schema.constraint_column_usage ccu
            ON tc.constraint_name = ccu.constraint_name
          JOIN information_schema.key_column_usage kcu
            ON tc.constraint_name = kcu.constraint_name
          WHERE tc.constraint_type = 'FOREIGN KEY'
            AND tc.table_name = $1
            AND kcu.column_name = $2
            AND ccu.table_name = $3
            AND ccu.column_name = $4
        );
      `;

      const result: any = await this.client.query(query, [
        tableName,
        columnName,
        referencedTable,
        referencedColumn,
      ]);
      return result.rows[0].exists;
    } catch (error) {
      logger.error(
        `Error checking if foreign key exists for ${tableName}.${columnName} -> ${referencedTable}.${referencedColumn}:`,
        error
      );
      return false;
    }
  }

  /**
   * Check if a table has data
   * @param tableName Table name
   * @returns True if table has data
   */
  public async tableHasData(tableName: string): Promise<boolean> {
    try {
      const query: Record<string, string | string[]> = `
        SELECT EXISTS (
          SELECT 1 FROM "${tableName}" LIMIT 1
        );
      `;

      const result: any = await this.client.query(query);
      return result.rows[0].exists;
    } catch (error) {
      logger.error(`Error checking if table ${tableName} has data:`, error);
      return false;
    }
  }

  /**
   * Verify database schema
   * @returns Verification result
   */
  public async verifySchema(): Promise<VerificationResult> {
    try {
      const missingTables: string[] = [];
      const missingColumns: { table: string; columns: string[] }[] = [];
      const missingForeignKeys: {
        table: string;
        column: string;
        referencedTable: string;
        referencedColumn: string;
      }[] = [];
      const emptyTables: string[] = [];

      // Check if all required tables exist
      for (const table of this.requiredTables) {
        const tableExists: unknown = await this.tableExists(table.name);

        if (tableExists) {
          // Check if all required columns exist
          const missingColumnsForTable: string[] = [];

          for (const column of table.columns) {
            const columnExists: unknown = await this.columnExists(table.name, column);

            if (!columnExists) {
              missingColumnsForTable.push(column);
            }
          }

          if (missingColumnsForTable.length > 0) {
            missingColumns.push({
              table: table.name,
              columns: missingColumnsForTable,
            });
          }

          // Check if all required foreign keys exist
          if (table.foreignKeys) {
            for (const foreignKey of table.foreignKeys) {
              const referencedTableExists: unknown = await this.tableExists(
                foreignKey.referencedTable
              );

              if (referencedTableExists) {
                const foreignKeyExists: unknown = await this.foreignKeyExists(
                  table.name,
                  foreignKey.column,
                  foreignKey.referencedTable,
                  foreignKey.referencedColumn ?? 'id'
                );

                if (!foreignKeyExists) {
                  missingForeignKeys.push({
                    table: table.name,
                    column: foreignKey.column,
                    referencedTable: foreignKey.referencedTable,
                    referencedColumn: foreignKey.referencedColumn ?? 'id',
                  });
                }
              }
            }
          }

          // Check if table has data
          const tableHasData: unknown = await this.tableHasData(table.name);

          if (!tableHasData) {
            emptyTables.push(table.name);
          }
        } else {
          missingTables.push(table.name);
        }
      }

      // Determine success - only consider missing tables as critical failures
      // Allow missing columns and foreign keys as the schema may have evolved
      const success: unknown = missingTables.length === 0;

      // Generate message
      let message: string = '';

      if (success) {
        message = 'Database schema verification successful';
      } else {
        message = 'Database schema verification failed';

        if (missingTables.length > 0) {
          message += `\nMissing tables: ${missingTables.join(', ')}`;
        }

        if (missingColumns.length > 0) {
          message += '\nMissing columns:';
          for (const { table, columns } of missingColumns) {
            message += `\n  ${table}: ${columns.join(', ')}`;
          }
        }

        if (missingForeignKeys.length > 0) {
          message += '\nMissing foreign keys:';
          for (const { table, column, referencedTable, referencedColumn } of missingForeignKeys) {
            message += `\n  ${table}.${column} -> ${referencedTable}.${referencedColumn}`;
          }
        }
      }

      if (emptyTables.length > 0) {
        message += `\nEmpty tables: ${emptyTables.join(', ')}`;
      }

      return {
        success,
        missingTables,
        missingColumns,
        missingForeignKeys,
        emptyTables,
        message,
      };
    } catch (error) {
      logger.error('Failed to verify database schema:', error);

      return {
        success: false,
        missingTables: [],
        missingColumns: [],
        missingForeignKeys: [],
        emptyTables: [],
        message: `Failed to verify database schema: ${(error as Error).message}`,
      };
    }
  }

  /**
   * Close database verifier
   */
  public async close(): Promise<void> {
    try {
      await this.client.end();
      logger.info('Database verifier closed');
    } catch (error) {
      logger.error('Failed to close database verifier:', error);
    }
  }
}

// Export default database verifier
export default DatabaseVerifier;
