/**
 * Production Database Configuration
 *
 * This file contains the configuration for the production database.
 * It includes settings for connection pooling, SSL, and other production-specific options.
 */
/**
 * Production database configuration
 */
export declare const productionDatabaseConfig: any;
/**
 * Get the Prisma database URL for production
 * @returns The database URL for Prisma
 */
export declare const getPrismaProductionDatabaseUrl: any;
/**
 * Get the Prisma client options for production
 * @returns The Prisma client options for production
 */
export declare const getPrismaProductionOptions: any;
export default productionDatabaseConfig;
//# sourceMappingURL=production.config.d.ts.map