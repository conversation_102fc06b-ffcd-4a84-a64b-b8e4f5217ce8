/**
 * Velocity Risk Detector
 *
 * Detects risk based on transaction frequency and velocity patterns.
 */

import { PrismaClient } from '@prisma/client';
import {
  IRiskDetector,
  RiskFactor,
  RiskFactorResult,
  TransactionContext,
  FraudDetectionConfig,
  VelocityCheckResult,
  FraudDetectionError,
} from '../core/FraudDetectionTypes';
import { logger } from '../../../lib/logger';

/**
 * Velocity-based risk detector
 */
export class VelocityRiskDetector implements IRiskDetector {
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  /**
   * Get detector name
   */
  getName(): string {
    return 'velocity_risk_detector';
  }

  /**
   * Get risk factor
   */
  getFactor(): RiskFactor {
    return RiskFactor.FREQUENCY;
  }

  /**
   * Check if detector is enabled
   */
  isEnabled(config: FraudDetectionConfig): boolean {
    return config.velocitySettings.enabled && (config.factorWeights[RiskFactor.FREQUENCY] ?? 0) > 0;
  }

  /**
   * Detect velocity-based risk
   */
  async detect(
    context: TransactionContext,
    config: FraudDetectionConfig
  ): Promise<RiskFactorResult> {
    try {
      const { transaction, merchant, ipAddress, deviceId, customerEmail } = context;

      // Run multiple velocity checks
      const checks = await Promise.all([
        this.checkMerchantVelocity(merchant.id, config),
        this.checkIpVelocity(ipAddress, config),
        this.checkDeviceVelocity(deviceId, config),
        this.checkCustomerVelocity(customerEmail ?? '', config),
        this.checkAmountVelocity(merchant.id, transaction.amount, config),
      ]);

      // Calculate overall velocity risk
      const velocityResults = checks.filter((check) => check !== null) as VelocityCheckResult[];
      const riskScore = this.calculateVelocityRiskScore(velocityResults);
      const confidence = this.calculateConfidence(velocityResults);
      const reason = this.generateReason(velocityResults);

      logger.debug(`Velocity risk assessment for transaction ${transaction.id}`, {
        merchantId: merchant.id,
        ipAddress,
        deviceId,
        score: riskScore,
        violations: velocityResults.filter((r) => r.isViolation).length,
      });

      return {
        factor: RiskFactor.FREQUENCY,
        score: riskScore,
        reason,
        confidence,
        metadata: {
          velocityChecks: velocityResults,
          merchantId: merchant.id,
          ipAddress,
          deviceId,
        },
      };
    } catch (error) {
      logger.error('Error in velocity risk detection:', error);
      throw FraudDetectionError.detectorFailed(
        `Velocity risk detection failed: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`
      );
    }
  }

  /**
   * Check merchant-level velocity
   */
  private async checkMerchantVelocity(
    merchantId: string,
    config: FraudDetectionConfig
  ): Promise<VelocityCheckResult | null> {
    try {
      const timeWindow = new Date(
        Date.now() - config.velocitySettings.timeWindowMinutes * 60 * 1000
      );

      const recentTransactions = await this.prisma.transaction.findMany({
        where: {
          merchantId,
          createdAt: { gte: timeWindow },
          status: { not: 'FAILED' },
        },
        select: {
          amount: true,
          createdAt: true,
        },
      });

      const currentCount = recentTransactions.length;
      const currentAmount = recentTransactions.reduce((sum, t) => sum + t.amount, 0);
      const maxAllowed = config.maxTransactionsPerHour;
      const maxAmount = config.velocitySettings.maxAmount;

      const countViolation = currentCount > maxAllowed;
      const amountViolation = currentAmount > maxAmount;

      return {
        isViolation: countViolation || amountViolation,
        currentCount,
        currentAmount,
        maxAllowed,
        timeWindow: `${config.velocitySettings.timeWindowMinutes} minutes`,
        violationType:
          countViolation && amountViolation ? 'BOTH' : countViolation ? 'COUNT' : 'AMOUNT',
      };
    } catch (error) {
      logger.error('Error checking merchant velocity:', error);
      return null;
    }
  }

  /**
   * Check IP-based velocity
   */
  private async checkIpVelocity(
    ipAddress: string,
    config: FraudDetectionConfig
  ): Promise<VelocityCheckResult | null> {
    try {
      const timeWindow = new Date(
        Date.now() - config.velocitySettings.timeWindowMinutes * 60 * 1000
      );

      // This would require storing IP addresses with transactions
      // For now, return a simplified check
      const maxTransactionsPerIp = 5; // This should be configurable

      return {
        isViolation: false, // Simplified for now
        currentCount: 1,
        currentAmount: 0,
        maxAllowed: maxTransactionsPerIp,
        timeWindow: `${config.velocitySettings.timeWindowMinutes} minutes`,
        violationType: 'COUNT',
      };
    } catch (error) {
      logger.error('Error checking IP velocity:', error);
      return null;
    }
  }

  /**
   * Check device-based velocity
   */
  private async checkDeviceVelocity(
    deviceId: string,
    config: FraudDetectionConfig
  ): Promise<VelocityCheckResult | null> {
    try {
      if (!deviceId) return null;

      const timeWindow = new Date(
        Date.now() - config.velocitySettings.timeWindowMinutes * 60 * 1000
      );
      const maxTransactionsPerDevice = 3; // This should be configurable

      // This would require storing device IDs with transactions
      // For now, return a simplified check
      return {
        isViolation: false, // Simplified for now
        currentCount: 1,
        currentAmount: 0,
        maxAllowed: maxTransactionsPerDevice,
        timeWindow: `${config.velocitySettings.timeWindowMinutes} minutes`,
        violationType: 'COUNT',
      };
    } catch (error) {
      logger.error('Error checking device velocity:', error);
      return null;
    }
  }

  /**
   * Check customer-based velocity
   */
  private async checkCustomerVelocity(
    customerEmail: string,
    config: FraudDetectionConfig
  ): Promise<VelocityCheckResult | null> {
    try {
      if (!customerEmail) return null;

      const timeWindow = new Date(
        Date.now() - config.velocitySettings.timeWindowMinutes * 60 * 1000
      );

      const recentTransactions = await this.prisma.transaction.findMany({
        where: {
          // customerEmail, // Not in Transaction schema
          createdAt: { gte: timeWindow },
          status: { not: 'FAILED' },
        },
        select: {
          amount: true,
          createdAt: true,
        },
      });

      const currentCount = recentTransactions.length;
      const currentAmount = recentTransactions.reduce((sum, t) => sum + t.amount, 0);
      const maxAllowed = 5; // This should be configurable
      const maxAmount = config.velocitySettings.maxAmount;

      const countViolation = currentCount > maxAllowed;
      const amountViolation = currentAmount > maxAmount;

      return {
        isViolation: countViolation || amountViolation,
        currentCount,
        currentAmount,
        maxAllowed,
        timeWindow: `${config.velocitySettings.timeWindowMinutes} minutes`,
        violationType:
          countViolation && amountViolation ? 'BOTH' : countViolation ? 'COUNT' : 'AMOUNT',
      };
    } catch (error) {
      logger.error('Error checking customer velocity:', error);
      return null;
    }
  }

  /**
   * Check amount-based velocity (rapid high-value transactions)
   */
  private async checkAmountVelocity(
    merchantId: string,
    currentAmount: number,
    config: FraudDetectionConfig
  ): Promise<VelocityCheckResult | null> {
    try {
      const timeWindow = new Date(Date.now() - 30 * 60 * 1000); // 30 minutes for amount velocity

      const recentHighValueTransactions = await this.prisma.transaction.findMany({
        where: {
          merchantId,
          createdAt: { gte: timeWindow },
          amount: { gte: currentAmount * 0.5 }, // Similar high-value transactions
          status: { not: 'FAILED' },
        },
        select: {
          amount: true,
          createdAt: true,
        },
      });

      const currentCount = recentHighValueTransactions.length;
      const totalAmount =
        recentHighValueTransactions.reduce((sum, t) => sum + t.amount, 0) + currentAmount;
      const maxHighValueTransactions = 3; // This should be configurable
      const maxTotalAmount = config.maxTransactionAmount * 2;

      const countViolation = currentCount >= maxHighValueTransactions;
      const amountViolation = totalAmount > maxTotalAmount;

      return {
        isViolation: countViolation || amountViolation,
        currentCount: currentCount + 1, // Include current transaction
        currentAmount: totalAmount,
        maxAllowed: maxHighValueTransactions,
        timeWindow: '30 minutes',
        violationType:
          countViolation && amountViolation ? 'BOTH' : countViolation ? 'COUNT' : 'AMOUNT',
      };
    } catch (error) {
      logger.error('Error checking amount velocity:', error);
      return null;
    }
  }

  /**
   * Calculate overall velocity risk score
   */
  private calculateVelocityRiskScore(results: VelocityCheckResult[]): number {
    if (results.length === 0) return 20; // Default low risk

    const violations = results.filter((r) => r.isViolation);

    if (violations.length === 0) return 10; // No violations

    // Calculate severity based on number and type of violations
    let baseScore = 40; // Base score for any violation

    violations.forEach((violation) => {
      switch (violation.violationType) {
        case 'BOTH':
          baseScore += 30;
          break;
        case 'COUNT':
          baseScore += 20;
          break;
        case 'AMOUNT':
          baseScore += 25;
          break;
      }
    });

    // Additional penalty for multiple violations
    if (violations.length > 1) {
      baseScore += violations.length * 10;
    }

    return Math.min(100, baseScore);
  }

  /**
   * Calculate confidence in the assessment
   */
  private calculateConfidence(results: VelocityCheckResult[]): number {
    if (results.length === 0) return 0.3; // Low confidence with no data

    const successfulChecks = results.length;
    const maxChecks = 5; // Total number of velocity checks we attempt

    // Higher confidence with more successful checks
    return Math.min(1, 0.5 + (successfulChecks / maxChecks) * 0.5);
  }

  /**
   * Generate reason for velocity assessment
   */
  private generateReason(results: VelocityCheckResult[]): string {
    const violations = results.filter((r) => r.isViolation);

    if (violations.length === 0) {
      return 'Normal transaction frequency';
    }

    const violationTypes = violations.map((v) => {
      switch (v.violationType) {
        case 'BOTH':
          return `excessive count and amount in ${v.timeWindow}`;
        case 'COUNT':
          return `too many transactions in ${v.timeWindow}`;
        case 'AMOUNT':
          return `excessive amount in ${v.timeWindow}`;
        default:
          return 'velocity violation';
      }
    });

    return `Velocity violations: ${violationTypes.join(', ')}`;
  }
}
