// jscpd:ignore-file
import { Request, Response, NextFunction } from 'express';
import { BaseController } from "../base/BaseController";
import { AlertService, AlertStatus, AlertType, AlertSeverity } from "../../services/alert.service";
import { asyncHandler } from "../../utils/asyncHandler";
import { AppError } from "../../utils/appError";
import prisma from "../../lib/prisma";
import { Alert, AlertStatus, AlertType, AlertSeverity } from '../types';
import { BaseController } from "../base/BaseController";
import { AlertService, AlertStatus, AlertType, AlertSeverity } from "../../services/alert.service";
import { asyncHandler } from "../../utils/asyncHandler";
import { AppError } from "../../utils/appError";
import { <PERSON><PERSON>, AlertStatus, AlertType, AlertSeverity } from '../types';


/**
 * Alert controller
 */
export class AlertController extends BaseController {
  private alertService: AlertService;

  constructor() {
    super();
    this.alertService = new AlertService();
  }

  /**
   * Get alerts
   * @route GET /api/alerts
   */
  getAlerts = asyncHandler(async (req: Request, res: Response) => {
    // Check authorization
    const { userRole, userId, merchantId } = this.checkAuthorization(req);

    // Get query parameters
    const status: unknown = req.query.status as AlertStatus | undefined;
    const severity: unknown = req.query.severity as AlertSeverity | undefined;
    const type: unknown = req.query.type as AlertType | undefined;
    const startDate: unknown = req.query.startDate ? new Date(req.query.startDate as string) : undefined;
    const endDate = req.query.endDate ? new Date(req.query.endDate as string) : undefined;
    const search = req.query.search as string | undefined;
    const limit: unknown = this.parseInteger(req.query.limit as string, "limit", 10);
    const offset: unknown = this.parseInteger(req.query.offset as string, "offset", 0, 0);
    const sortBy: unknown = req.query.sortBy as string ?? "createdAt";
    const sortOrder: unknown = req.query.sortOrder as "asc" | "desc" || "desc";

    // Determine target merchant ID
    const targetMerchantId: unknown = this.determineTargetMerchantId(
      userRole,
      merchantId,
      req.query.merchantId as string
    );

    // Get alerts
    const alerts: unknown = await this.alertService.getAlerts({
      merchantId: targetMerchantId,
      status,
      severity,
      type,
      startDate,
      endDate,
      search,
      limit,
      offset,
      sortBy,
      sortOrder
    });

    // Return paginated response
    return this.sendPaginatedSuccess(res, alerts.alerts, alerts.total, limit, offset);
  });

  /**
   * Get alert by ID
   * @route GET /api/alerts/:id
   */
  getAlert = asyncHandler(async (req: Request, res: Response) => {
    // Check authorization
    const { userRole, userId, merchantId } = this.checkAuthorization(req);

    // Get alert ID from params
    const alertId: unknown = req.params.id;
    if (!alertId) {
      throw new AppError({
            message: "Alert ID is required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }

    // Get alert
    const alert: unknown = await this.alertService.getAlert(alertId);

    // Check if user is authorized to view this alert
    if (userRole !== "ADMIN" && alert.merchantId !== merchantId) {
      throw new AppError({
            message: "Unauthorized",
            type: ErrorType.AUTHENTICATION,
            code: ErrorCode.INVALID_CREDENTIALS
        });
    }

    // Return alert
    return this.sendSuccess(res, alert);
  });

  /**
   * Update alert status
   * @route PUT /api/alerts/:id/status
   */
  updateAlertStatus = asyncHandler(async (req: Request, res: Response) => {
    // Check authorization
    const { userRole, userId, merchantId } = this.checkAuthorization(req);

    // Get alert ID from params
    const alertId: unknown = req.params.id;
    if (!alertId) {
      throw new AppError({
            message: "Alert ID is required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }

    // Get status from body
    const { status } = req.body;
    if (!status || !Object.values(AlertStatus).includes(status as AlertStatus)) {
      throw new AppError({
            message: "Valid status is required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }

    // Get alert to check authorization
    const alert: unknown = await this.alertService.getAlert(alertId);

    // Check if user is authorized to update this alert
    if (userRole !== "ADMIN" && alert.merchantId !== merchantId) {
      throw new AppError({
            message: "Unauthorized",
            type: ErrorType.AUTHENTICATION,
            code: ErrorCode.INVALID_CREDENTIALS
        });
    }

    // Update alert status
    const updatedAlert: unknown = await this.alertService.updateAlertStatus(
      alertId,
      status as AlertStatus,
      userId
    );

    // Return updated alert
    return this.sendSuccess(res, updatedAlert);
  });

  /**
   * Create a test alert
   * @route POST /api/alerts/test
   */
  createTestAlert = asyncHandler(async (req: Request, res: Response) => {
    // Check authorization
    const { userRole, userId } = this.checkAuthorization(req);

    // Only admins can create test alerts
    this.checkAdminRole(userRole);

    // Get alert data from body
    const { type, severity, title, message, details, targetMerchantId } = req.body;

    // Validate required fields
    if (!type || !severity || !title || !message) {
      throw new AppError({
            message: "Type, severity, title, and message are required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }

    // Validate type and severity
    if (!Object.values(AlertType).includes(type as AlertType)) {
      throw new AppError({
            message: "Invalid alert type",
            type: ErrorType.VALIDATION,
            code: ErrorCode.INVALID_INPUT
        });
    }

    if (!Object.values(AlertSeverity).includes(severity as AlertSeverity)) {
      throw new AppError({
            message: "Invalid alert severity",
            type: ErrorType.VALIDATION,
            code: ErrorCode.INVALID_INPUT
        });
    }

    // If targetMerchantId is provided, check if it exists
    if (targetMerchantId) {
      const merchant: unknown = await prisma.merchant.findUnique({
        where: { id: targetMerchantId }
      });

      if (!merchant) {
        throw new AppError({
            message: "Target merchant not found",
            type: ErrorType.NOT_FOUND,
            code: ErrorCode.RESOURCE_NOT_FOUND
        });
      }
    }

    // Create test alert
    const alertId: unknown = await this.alertService.createAlert({
      type: type as AlertType,
      severity: severity as AlertSeverity,
      title,
      message,
      source: "test",
      details: details ?? {},
      merchantId: targetMerchantId,
      notificationMethods: req.body.notificationMethods ?? []
    });

    // Return success
    return res.status(201).json({
      success: true,
      message: "Test alert created successfully",
      alertId
    });
  });

  /**
   * Get alert count
   * @route GET /api/alerts/count
   */
  getAlertCount = asyncHandler(async (req: Request, res: Response) => {
    // Check authorization
    const { userRole, merchantId } = this.checkAuthorization(req);

    // Get query parameters
    const status: unknown = req.query.status as AlertStatus | undefined;
    const severity: unknown = req.query.severity as AlertSeverity | undefined;
    const type: unknown = req.query.type as AlertType | undefined;
    const startDate: unknown = req.query.startDate ? new Date(req.query.startDate as string) : undefined;
    const endDate = req.query.endDate ? new Date(req.query.endDate as string) : undefined;
    const search = req.query.search as string | undefined;

    // Determine target merchant ID
    const targetMerchantId: unknown = this.determineTargetMerchantId(
      userRole,
      merchantId,
      req.query.merchantId as string
    );

    // Create where clause
    const where: unknown = {};

    // Add filters
    if (status) where.status = status;
    if (severity) where.severity = severity;
    if (type) where.type = type;
    if (targetMerchantId) where.merchantId = targetMerchantId;

    // Add date range filter
    if (startDate ?? endDate) {
      where.createdAt = {};
      if (startDate) where.createdAt.gte = startDate;
      if (endDate) {
        const endOfDay: Date =new Date(endDate);
        endOfDay.setHours(23, 59, 59, 999);
        where.createdAt.lte = endOfDay;
      }
    }

    // Add search filter
    if (search) {
      where.OR = [
        { title: { contains: search, mode: "insensitive" } },
        { message: { contains: search, mode: "insensitive" } },
        { source: { contains: search, mode: "insensitive" } }
      ];
    }

    // Get alert count
    const count: unknown = await prisma.alert.count({ where });

    // Get counts by status, severity, and type
    const statusCounts: unknown = await prisma.$queryRaw`
      SELECT status, COUNT(*) as count
      FROM "Alert"
      WHERE ${where.merchantId ? prisma.sql`"merchantId" = ${where.merchantId}` : prisma.sql`1=1`}
      GROUP BY status
    `;

    const severityCounts: unknown = await prisma.$queryRaw`
      SELECT severity, COUNT(*) as count
      FROM "Alert"
      WHERE ${where.merchantId ? prisma.sql`"merchantId" = ${where.merchantId}` : prisma.sql`1=1`}
      GROUP BY severity
    `;

    const typeCounts: unknown = await prisma.$queryRaw`
      SELECT type, COUNT(*) as count
      FROM "Alert"
      WHERE ${where.merchantId ? prisma.sql`"merchantId" = ${where.merchantId}` : prisma.sql`1=1`}
      GROUP BY type
    `;

    // Return counts
    return this.sendSuccess(res, {
      count,
      statusCounts,
      severityCounts,
      typeCounts
    });
  });
}
