import { BaseService } from '../base/BaseService';
import { config } from '../config';
import { User, Merchant } from '../types';
// jscpd:ignore-file
import { logger } from '../utils/logger';
import prisma from '../lib/prisma';
import { config } from '../config';

import webpush from 'web-push';
import { User, Merchant } from '../types';

/**
 * Push notification service
 */
export class PushNotificationService extends BaseService {
  /**
   * Create a new push notification service
   */
  constructor() {
    super();
    // Initialize service
    webpush.setVapidDetails(
      'mailto:<EMAIL>',
      config.push.publicKey,
      config.push.privateKey
    );
  }

  /**
   * Send push notification
   * @param subscription Push subscription
   * @param title Notification title
   * @param body Notification body
   * @param icon Notification icon (optional)
   * @param data Additional data (optional)
   * @param url URL to open when notification is clicked (optional)
   * @returns Success status
   */
  public async sendNotification(
    subscription: webpush.PushSubscription,
    title: string,
    body: string,
    icon?: string,
    data?: Record<string, any>,
    url?: string
  ): Promise<boolean> {
    try {
      // Validate subscription
      if (!subscription || !subscription.endpoint) {
        logger.error('Invalid push subscription');
        return false;
      }

      // Create payload
      const payload: any = JSON.stringify({
        title,
        body,
        icon: icon || '/logo.png',
        data: data || {},
        url: url || '/',
        timestamp: Date.now(),
      });

      // Send notification
      await webpush.sendNotification(subscription, payload);

      logger.info('Push notification sent successfully', { title });
      return true;
    } catch (error) {
      // Check if subscription is expired or invalid
      if (
        error instanceof Error &&
        ((error as Error).message.includes('410') || (error as Error).message.includes('404'))
      ) {
        logger.warn('Push subscription is expired or invalid', { error });
        return false;
      }

      logger.error('Error sending push notification', { error, title });
      return false;
    }
  }

  /**
   * Send push notification to user
   * @param userId User ID
   * @param title Notification title
   * @param body Notification body
   * @param icon Notification icon (optional)
   * @param data Additional data (optional)
   * @param url URL to open when notification is clicked (optional)
   * @returns Success status
   */
  public async sendNotificationToUser(
    userId: string,
    title: string,
    body: string,
    icon?: string,
    data?: Record<string, any>,
    url?: string
  ): Promise<boolean> {
    try {
      // Get user's push subscriptions
      const subscriptions: any = await prisma.pushSubscription.findMany({
        where: {
          userId,
          active: true,
        },
      });

      if (subscriptions.length === 0) {
        logger.warn('No active push subscriptions found for user', { userId });
        return false;
      }

      // Send notification to all user's subscriptions
      let successCount: number = 0;

      for (const sub of subscriptions) {
        try {
          const subscription: any = JSON.parse(sub.subscription);
          const success: any = await this.sendNotification(
            subscription,
            title,
            body,
            icon,
            data,
            url
          );

          if (success) {
            successCount++;
          }
        } catch (error) {
          logger.error('Error sending notification to subscription', {
            error,
            subscriptionId: sub.id,
          });
        }
      }

      logger.info('Push notifications sent to user', {
        userId,
        total: subscriptions.length,
        successful: successCount,
      });

      return successCount > 0;
    } catch (error) {
      logger.error('Error sending push notifications to user', { error, userId });
      return false;
    }
  }

  /**
   * Send push notification to merchant
   * @param merchantId Merchant ID
   * @param title Notification title
   * @param body Notification body
   * @param icon Notification icon (optional)
   * @param data Additional data (optional)
   * @param url URL to open when notification is clicked (optional)
   * @returns Success status
   */
  public async sendNotificationToMerchant(
    merchantId: string,
    title: string,
    body: string,
    icon?: string,
    data?: Record<string, any>,
    url?: string
  ): Promise<boolean> {
    try {
      // Get merchant's push subscriptions
      const subscriptions: any = await prisma.pushSubscription.findMany({
        where: {
          merchantId,
          active: true,
        },
      });

      if (subscriptions.length === 0) {
        logger.warn('No active push subscriptions found for merchant', { merchantId });
        return false;
      }

      // Send notification to all merchant's subscriptions
      let successCount: number = 0;

      for (const sub of subscriptions) {
        try {
          const subscription: any = JSON.parse(sub.subscription);
          const success: any = await this.sendNotification(
            subscription,
            title,
            body,
            icon,
            data,
            url
          );

          if (success) {
            successCount++;
          }
        } catch (error) {
          logger.error('Error sending notification to subscription', {
            error,
            subscriptionId: sub.id,
          });
        }
      }

      logger.info('Push notifications sent to merchant', {
        merchantId,
        total: subscriptions.length,
        successful: successCount,
      });

      return successCount > 0;
    } catch (error) {
      logger.error('Error sending push notifications to merchant', { error, merchantId });
      return false;
    }
  }

  /**
   * Save push subscription
   * @param subscription Push subscription
   * @param userId User ID (optional)
   * @param merchantId Merchant ID (optional)
   * @param deviceInfo Device information (optional)
   * @returns Subscription ID
   */
  public async saveSubscription(
    subscription: webpush.PushSubscription,
    userId?: string,
    merchantId?: string,
    deviceInfo?: Record<string, any>
  ): Promise<string> {
    try {
      // Validate subscription
      if (!subscription || !subscription.endpoint) {
        throw new Error('Invalid push subscription');
      }

      // Check if subscription already exists
      const existingSubscription: any = await prisma.pushSubscription.findFirst({
        where: { endpoint: subscription.endpoint },
      });

      if (existingSubscription) {
        // Update existing subscription
        const updatedSubscription: any = await prisma.pushSubscription.update({
          where: { id: existingSubscription.id },
          data: {
            subscription: JSON.stringify(subscription),
            userId: userId || existingSubscription.userId,
            merchantId: merchantId || existingSubscription.merchantId,
            deviceInfo: deviceInfo || existingSubscription.deviceInfo,
            active: true,
            updatedAt: new Date(),
          },
        });

        logger.info('Push subscription updated', { id: updatedSubscription.id });
        return updatedSubscription.id;
      } else {
        // Create new subscription
        const newSubscription: any = await prisma.pushSubscription.create({
          data: {
            endpoint: subscription.endpoint,
            subscription: JSON.stringify(subscription),
            userId,
            merchantId,
            deviceInfo,
            active: true,
          },
        });

        logger.info('Push subscription created', { id: newSubscription.id });
        return newSubscription.id;
      }
    } catch (error) {
      logger.error('Error saving push subscription', { error });
      throw error;
    }
  }

  /**
   * Delete push subscription
   * @param endpoint Subscription endpoint
   * @returns Success status
   */
  public async deleteSubscription(endpoint: string): Promise<boolean> {
    try {
      // Find subscription by endpoint
      const subscription: any = await prisma.pushSubscription.findFirst({
        where: { endpoint },
      });

      if (!subscription) {
        logger.warn('Push subscription not found', { endpoint });
        return false;
      }

      // Delete subscription
      await prisma.pushSubscription.delete({
        where: { id: subscription.id },
      });

      logger.info('Push subscription deleted', { id: subscription.id });
      return true;
    } catch (error) {
      logger.error('Error deleting push subscription', { error, endpoint });
      return false;
    }
  }

  /**
   * Get VAPID public key
   * @returns VAPID public key
   */
  public getPublicKey(): string {
    return config.push.publicKey;
  }
}

export default new PushNotificationService();
