/**
 * Authorization Service
 *
 * Handles authorization logic for alert aggregation operations.
 */

import { AppError, ErrorType, ErrorCode } from '../../../utils/errors/AppError';
import { AuthorizationContext, PermissionResult } from '../types/AlertAggregationTypes';

/**
 * Authorization service for alert aggregation
 */
export class AuthorizationService {
  private readonly adminRoles = ['ADMIN', 'SUPER_ADMIN'];
  private readonly managerRoles = ['MANAGER', 'ADMIN', 'SUPER_ADMIN'];
  private readonly userRoles = ['USER', 'MANAGER', 'ADMIN', 'SUPER_ADMIN'];

  /**
   * Check if user is authorized for the given action
   */
  async checkPermission(context: AuthorizationContext): Promise<PermissionResult> {
    const { user, resource, action } = context;

    if (!user) {
      return {
        allowed: false,
        reason: 'User not authenticated',
      };
    }

    // Check role-based permissions
    const rolePermission = this.checkRolePermission(user.role, resource, action);
    if (!rolePermission.allowed) {
      return rolePermission;
    }

    // Check resource-specific permissions
    const resourcePermission = await this.checkResourcePermission(context);
    if (!resourcePermission.allowed) {
      return resourcePermission;
    }

    return { allowed: true };
  }

  /**
   * Check role-based permissions
   */
  private checkRolePermission(
    userRole: string,
    resource: string,
    action: string
  ): PermissionResult {
    switch (resource) {
      case 'aggregation-rules':
        return this.checkAggregationRulePermission(userRole, action);
      case 'correlation-rules':
        return this.checkCorrelationRulePermission(userRole, action);
      default:
        return {
          allowed: false,
          reason: `Unknown resource: ${resource}`,
        };
    }
  }

  /**
   * Check aggregation rule permissions
   */
  private checkAggregationRulePermission(userRole: string, action: string): PermissionResult {
    switch (action) {
      case 'read':
        if (this.userRoles.includes(userRole)) {
          return { allowed: true };
        }
        break;
      case 'create':
      case 'update':
      case 'delete':
        if (this.adminRoles.includes(userRole)) {
          return { allowed: true };
        }
        return {
          allowed: false,
          reason: 'Admin role required for write operations',
          requiredRole: 'ADMIN',
        };
      default:
        return {
          allowed: false,
          reason: `Unknown action: ${action}`,
        };
    }

    return {
      allowed: false,
      reason: 'Insufficient permissions',
      requiredRole: 'USER',
    };
  }

  /**
   * Check correlation rule permissions
   */
  private checkCorrelationRulePermission(userRole: string, action: string): PermissionResult {
    switch (action) {
      case 'read':
        if (this.managerRoles.includes(userRole)) {
          return { allowed: true };
        }
        return {
          allowed: false,
          reason: 'Manager role required for correlation rules',
          requiredRole: 'MANAGER',
        };
      case 'create':
      case 'update':
      case 'delete':
        if (this.adminRoles.includes(userRole)) {
          return { allowed: true };
        }
        return {
          allowed: false,
          reason: 'Admin role required for write operations',
          requiredRole: 'ADMIN',
        };
      default:
        return {
          allowed: false,
          reason: `Unknown action: ${action}`,
        };
    }
  }

  /**
   * Check resource-specific permissions
   */
  private async checkResourcePermission(context: AuthorizationContext): Promise<PermissionResult> {
    const { user, resource } = context;

    // For now, we don't have resource-specific permissions
    // This could be extended to check ownership, merchant-specific access, etc.

    // Example: Check if user can access merchant-specific resources
    if (user.merchantId && resource.includes('merchant')) {
      // Implement merchant-specific authorization logic here
      return { allowed: true };
    }

    return { allowed: true };
  }

  /**
   * Require admin role
   */
  requireAdmin(userRole?: string): void {
    if (!userRole || !this.adminRoles.includes(userRole)) {
      throw new AppError({
        message: 'Admin role required',
        type: ErrorType.AUTHENTICATION,
        code: ErrorCode.INVALID_CREDENTIALS,
      });
    }
  }

  /**
   * Require manager role or higher
   */
  requireManager(userRole?: string): void {
    if (!userRole || !this.managerRoles.includes(userRole)) {
      throw new AppError({
        message: 'Manager role required',
        type: ErrorType.AUTHENTICATION,
        code: ErrorCode.INVALID_CREDENTIALS,
      });
    }
  }

  /**
   * Require authenticated user
   */
  requireAuthenticated(userRole?: string): void {
    if (!userRole || !this.userRoles.includes(userRole)) {
      throw new AppError({
        message: 'Authentication required',
        type: ErrorType.AUTHENTICATION,
        code: ErrorCode.INVALID_CREDENTIALS,
      });
    }
  }

  /**
   * Check if user has specific role
   */
  hasRole(userRole: string, requiredRole: string): boolean {
    const roleHierarchy: Record<string, number> = {
      USER: 1,
      MANAGER: 2,
      ADMIN: 3,
      SUPER_ADMIN: 4,
    };

    const userLevel = roleHierarchy[userRole] || 0;
    const requiredLevel = roleHierarchy[requiredRole] || 0;

    return userLevel >= requiredLevel;
  }

  /**
   * Get user permissions for a resource
   */
  getUserPermissions(userRole: string, resource: string): string[] {
    const permissions: string[] = [];

    switch (resource) {
      case 'aggregation-rules':
        if (this.userRoles.includes(userRole)) {
          permissions.push('read');
        }
        if (this.adminRoles.includes(userRole)) {
          permissions.push('create', 'update', 'delete');
        }
        break;
      case 'correlation-rules':
        if (this.managerRoles.includes(userRole)) {
          permissions.push('read');
        }
        if (this.adminRoles.includes(userRole)) {
          permissions.push('create', 'update', 'delete');
        }
        break;
    }

    return permissions;
  }

  /**
   * Validate authorization context
   */
  validateAuthorizationContext(context: AuthorizationContext): void {
    if (!context.user) {
      throw new AppError({
        message: 'User context is required',
        type: ErrorType.AUTHENTICATION,
        code: ErrorCode.INVALID_CREDENTIALS,
      });
    }

    if (!context.resource) {
      throw new AppError({
        message: 'Resource is required',
        type: ErrorType.VALIDATION,
        code: ErrorCode.MISSING_REQUIRED_FIELD,
      });
    }

    if (!context.action) {
      throw new AppError({
        message: 'Action is required',
        type: ErrorType.VALIDATION,
        code: ErrorCode.MISSING_REQUIRED_FIELD,
      });
    }
  }

  /**
   * Create authorization context from request
   */
  createAuthorizationContext(
    user: any,
    resource: string,
    action: string,
    resourceId?: string
  ): AuthorizationContext {
    return {
      user: {
        id: user?.id,
        role: user?.role,
        merchantId: user?.merchantId,
      },
      resource,
      action,
      resourceId,
    };
  }

  /**
   * Handle authorization error
   */
  handleAuthorizationError(result: PermissionResult): never {
    const message = result.reason ?? 'Access denied';

    throw new AppError({
      message,
      type: ErrorType.AUTHENTICATION,
      code: ErrorCode.INVALID_CREDENTIALS,
      details: {
        requiredRole: result.requiredRole,
        requiredPermissions: result.requiredPermissions,
      },
    });
  }
}
