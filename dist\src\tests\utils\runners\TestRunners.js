"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.testController = testController;
exports.testService = testService;
exports.testRepository = testRepository;
exports.testMiddleware = testMiddleware;
exports.testUtility = testUtility;
// BaseRepository not available - using any type
const TestTypes_1 = require("../core/TestTypes");
const MockFactories_1 = require("../factories/MockFactories");
/**
 * Test a controller method
 */
async function testController(controller, method, options = {}) {
    const startTime = Date.now();
    let req;
    let res;
    let next;
    try {
        // Setup
        req = options.req || (0, MockFactories_1.createMockRequest)();
        res = options.res || (0, MockFactories_1.createMockResponse)();
        next = options.next || (0, MockFactories_1.createMockNext)();
        // Run setup hooks
        if (options.beforeEach) {
            await options.beforeEach();
        }
        if (options.setup) {
            await options.setup();
        }
        if (options.controllerSetup) {
            await options.controllerSetup(controller);
        }
        // Validate request if validator provided
        if (options.validateRequest) {
            await options.validateRequest(req);
        }
        // Execute the controller method
        const result = await controller[method](req, res, next);
        // Validate response if validator provided
        if (options.validateResponse) {
            await options.validateResponse(res);
        }
        // Assert expected status
        if (options.expectedStatus !== undefined) {
            expect(res.status).toHaveBeenCalledWith(options.expectedStatus);
        }
        // Assert expected response
        if (options.expectedResponse !== undefined) {
            if (typeof options.expectedResponse === 'function') {
                expect(res.json).toHaveBeenCalledWith(expect.objectContaining(options.expectedResponse()));
            }
            else {
                expect(res.json).toHaveBeenCalledWith(options.expectedResponse);
            }
        }
        // Run cleanup hooks
        if (options.controllerCleanup) {
            await options.controllerCleanup(controller);
        }
        if (options.cleanup) {
            await options.cleanup();
        }
        if (options.afterEach) {
            await options.afterEach();
        }
        return { req, res, next, result };
    }
    catch (error) {
        // Handle expected errors
        if (options.expectedError) {
            if (typeof options.expectedError === 'function') {
                expect(error).toEqual(expect.objectContaining(options.expectedError()));
            }
            else {
                expect(error).toEqual(options.expectedError);
            }
            return { req: req, res: res, next: next };
        }
        // Wrap unexpected errors
        const testError = new TestTypes_1.TestError(`Controller test failed: ${error instanceof Error ? error.message : 'Unknown error'}`, TestTypes_1.TestErrorType.EXECUTION_ERROR, undefined, error instanceof Error ? error : new Error(String(error)));
        throw testError;
    }
}
/**
 * Test a service method
 */
async function testService(service, method, options = {}) {
    const startTime = Date.now();
    try {
        // Setup
        const args = options.args || [];
        // Run setup hooks
        if (options.beforeEach) {
            await options.beforeEach();
        }
        if (options.setup) {
            await options.setup();
        }
        if (options.serviceSetup) {
            await options.serviceSetup(service);
        }
        // Mock dependencies if provided
        if (options.mockDependencies) {
            Object.entries(options.mockDependencies).forEach(([key, value]) => {
                service[key] = value;
            });
        }
        // Mock methods if provided
        if (options.mockMethods) {
            Object.entries(options.mockMethods).forEach(([key, mockFn]) => {
                service[key] = mockFn;
            });
        }
        // Execute the service method
        const result = await service[method](...args);
        // Validate result if validator provided
        if (options.validateResult) {
            await options.validateResult(result);
        }
        // Assert expected result
        if (options.expectedResult !== undefined) {
            if (typeof options.expectedResult === 'function') {
                expect(result).toEqual(expect.objectContaining(options.expectedResult()));
            }
            else {
                expect(result).toEqual(options.expectedResult);
            }
        }
        // Run cleanup hooks
        if (options.serviceCleanup) {
            await options.serviceCleanup(service);
        }
        if (options.cleanup) {
            await options.cleanup();
        }
        if (options.afterEach) {
            await options.afterEach();
        }
        const executionTime = Date.now() - startTime;
        return {
            success: true,
            result,
            executionTime,
        };
    }
    catch (error) {
        const executionTime = Date.now() - startTime;
        // Handle expected errors
        if (options.expectedError) {
            if (typeof options.expectedError === 'function') {
                expect(error).toEqual(expect.objectContaining(options.expectedError()));
            }
            else {
                expect(error).toEqual(options.expectedError);
            }
            return {
                success: true,
                error: error instanceof Error ? error : new Error(String(error)),
                executionTime,
            };
        }
        // Return test failure result
        return {
            success: false,
            error: error instanceof Error ? error : new Error(String(error)),
            executionTime,
        };
    }
}
/**
 * Test a repository method
 */
async function testRepository(repository, method, options = {}) {
    const startTime = Date.now();
    try {
        // Setup
        const args = options.args || [];
        const mockPrisma = options.mockPrisma || (0, MockFactories_1.createMockPrismaClient)();
        // Replace the repository's prisma client with the mock
        repository.prisma = mockPrisma;
        // Run setup hooks
        if (options.beforeEach) {
            await options.beforeEach();
        }
        if (options.setup) {
            await options.setup();
        }
        if (options.repositorySetup) {
            await options.repositorySetup(repository);
        }
        // Mock transaction if needed
        if (options.mockTransaction) {
            mockPrisma.$transaction.mockImplementation((callback) => {
                return Promise.resolve(options.mockTransactionResult || callback(mockPrisma));
            });
        }
        // Mock specific queries if provided
        if (options.mockQueries) {
            Object.entries(options.mockQueries).forEach(([queryName, mockResult]) => {
                if (mockPrisma[queryName]) {
                    Object.keys(mockPrisma[queryName]).forEach((method) => {
                        if (typeof mockPrisma[queryName][method] === 'function') {
                            mockPrisma[queryName][method].mockResolvedValue(mockResult);
                        }
                    });
                }
            });
        }
        // Execute the repository method
        const result = await repository[method](...args);
        // Validate query if validator provided
        if (options.validateQuery) {
            await options.validateQuery(mockPrisma);
        }
        // Validate result if validator provided
        if (options.validateResult) {
            await options.validateResult(result);
        }
        // Assert expected result
        if (options.expectedResult !== undefined) {
            if (typeof options.expectedResult === 'function') {
                expect(result).toEqual(expect.objectContaining(options.expectedResult()));
            }
            else {
                expect(result).toEqual(options.expectedResult);
            }
        }
        // Run cleanup hooks
        if (options.repositoryCleanup) {
            await options.repositoryCleanup(repository);
        }
        if (options.cleanup) {
            await options.cleanup();
        }
        if (options.afterEach) {
            await options.afterEach();
        }
        const executionTime = Date.now() - startTime;
        return {
            success: true,
            result,
            executionTime,
            metadata: {
                mockPrismaUsed: true,
                transactionMocked: options.mockTransaction,
            },
        };
    }
    catch (error) {
        const executionTime = Date.now() - startTime;
        // Handle expected errors
        if (options.expectedError) {
            if (typeof options.expectedError === 'function') {
                expect(error).toEqual(expect.objectContaining(options.expectedError()));
            }
            else {
                expect(error).toEqual(options.expectedError);
            }
            return {
                success: true,
                error: error instanceof Error ? error : new Error(String(error)),
                executionTime,
            };
        }
        // Return test failure result
        return {
            success: false,
            error: error instanceof Error ? error : new Error(String(error)),
            executionTime,
        };
    }
}
/**
 * Test middleware
 */
async function testMiddleware(middleware, options = {}) {
    const startTime = Date.now();
    // Setup - declare outside try block so they're available in catch
    const req = options.req || (0, MockFactories_1.createMockRequest)();
    const res = options.res || (0, MockFactories_1.createMockResponse)();
    const next = options.next || (0, MockFactories_1.createMockNext)();
    try {
        // Run setup hooks
        if (options.beforeEach) {
            await options.beforeEach();
        }
        if (options.setup) {
            await options.setup();
        }
        if (options.middlewareSetup) {
            await options.middlewareSetup(req, res, next);
        }
        // Execute the middleware
        await middleware(req, res, next);
        // Assert next was called if expected
        if (options.expectNextCalled !== undefined) {
            if (options.expectNextCalled) {
                expect(next).toHaveBeenCalled();
            }
            else {
                expect(next).not.toHaveBeenCalled();
            }
        }
        // Assert next was called with specific arguments
        if (options.expectNextCalledWith !== undefined) {
            expect(next).toHaveBeenCalledWith(options.expectNextCalledWith);
        }
        // Assert expected status
        if (options.expectedStatus !== undefined) {
            expect(res.status).toHaveBeenCalledWith(options.expectedStatus);
        }
        // Assert expected response
        if (options.expectedResponse !== undefined) {
            expect(res.json).toHaveBeenCalledWith(options.expectedResponse);
        }
        // Run cleanup hooks
        if (options.middlewareCleanup) {
            await options.middlewareCleanup(req, res, next);
        }
        if (options.cleanup) {
            await options.cleanup();
        }
        if (options.afterEach) {
            await options.afterEach();
        }
        return { req, res, next };
    }
    catch (error) {
        // Handle expected errors
        if (options.expectedError) {
            if (typeof options.expectedError === 'function') {
                expect(error).toEqual(expect.objectContaining(options.expectedError()));
            }
            else {
                expect(error).toEqual(options.expectedError);
            }
            return { req: req, res: res, next: next };
        }
        // Wrap unexpected errors
        const testError = new TestTypes_1.TestError(`Middleware test failed: ${error instanceof Error ? error.message : 'Unknown error'}`, TestTypes_1.TestErrorType.EXECUTION_ERROR, undefined, error instanceof Error ? error : new Error(String(error)));
        throw testError;
    }
}
/**
 * Test utility function
 */
async function testUtility(utilityFunction, options = {}) {
    const startTime = Date.now();
    try {
        // Setup
        const args = options.args || [];
        if (options.setup) {
            await options.setup();
        }
        // Execute the utility function
        const result = await utilityFunction(...args);
        // Assert expected result
        if (options.expectedResult !== undefined) {
            expect(result).toEqual(options.expectedResult);
        }
        if (options.cleanup) {
            await options.cleanup();
        }
        const executionTime = Date.now() - startTime;
        return {
            success: true,
            result,
            executionTime,
        };
    }
    catch (error) {
        const executionTime = Date.now() - startTime;
        // Handle expected errors
        if (options.expectedError) {
            expect(error).toEqual(options.expectedError);
            return {
                success: true,
                error: error instanceof Error ? error : new Error(String(error)),
                executionTime,
            };
        }
        // Return test failure result
        return {
            success: false,
            error: error instanceof Error ? error : new Error(String(error)),
            executionTime,
        };
    }
}
//# sourceMappingURL=TestRunners.js.map