// jscpd:ignore-file
/**
 * Test Utility Library
 *
 * This file provides utility functions for tests to eliminate duplication in test files.
 */

import { PrismaClient } from '@prisma/client';
import { Request, Response } from 'express';
import { BaseController } from '../../core/BaseController';
import { BaseService } from '../../core/BaseService';
import { BaseRepository } from '../../core/BaseRepository';
import { mockModelFactory } from '../shared/test/mockModelFactory';
import { Request, Response } from 'express';
import { BaseController } from '../../core/BaseController';
import { BaseService } from '../../core/BaseService';
import { BaseRepository } from '../../core/BaseRepository';
import { mockModelFactory } from '../shared/test/mockModelFactory';

/**
 * Mock request
 */
export interface MockRequest extends Request {
  params?: unknown;
  query?: unknown;
  body?: unknown;
  headers?: unknown;
  user?: unknown;
}

/**
 * Mock response
 */
export interface MockResponse extends Response {
  status?: jest.Mock;
  json?: jest.Mock;
  send?: jest.Mock;
  end?: jest.Mock;
  locals?: unknown;
}

/**
 * Test options for controllers
 */
export interface ControllerTestOptions {
  req?: MockRequest;
  res?: MockResponse;
  next?: jest.Mock;
  expectedStatus?: number;
  expectedResponse?: unknown;
  expectedError?: unknown;
  description?: string;
  setup?: (controller: BaseController) => void;
  cleanup?: (controller: BaseController) => void;
  beforeEach?: () => void;
  afterEach?: () => void;
  timeout?: number;
  skip?: boolean;
  only?: boolean;
}

/**
 * Test options for services
 */
export interface ServiceTestOptions {
  args?: unknown[];
  expectedResult?: unknown;
  expectedError?: unknown;
  description?: string;
  setup?: (service: BaseService) => void;
  cleanup?: (service: BaseService) => void;
  beforeEach?: () => void;
  afterEach?: () => void;
  timeout?: number;
  skip?: boolean;
  only?: boolean;
  mockDependencies?: Record<string, unknown>;
}

/**
 * Test options for repositories
 */
export interface RepositoryTestOptions extends ServiceTestOptions {
  mockPrisma?: PrismaClient;
  mockTransaction?: boolean;
  mockTransactionResult?: unknown;
}

/**
 * Test options for middleware
 */
export interface MiddlewareTestOptions {
  req?: MockRequest;
  res?: MockResponse;
  next?: jest.Mock;
  expectedStatus?: number;
  expectedResponse?: unknown;
  expectedError?: unknown;
  description?: string;
  setup?: (req: MockRequest, res: MockResponse, next: jest.Mock) => void;
  cleanup?: (req: MockRequest, res: MockResponse, next: jest.Mock) => void;
  beforeEach?: () => void;
  afterEach?: () => void;
  timeout?: number;
  skip?: boolean;
  only?: boolean;
}

/**
 * Test options for validators
 */
export interface ValidatorTestOptions {
  req?: MockRequest;
  res?: MockResponse;
  next?: jest.Mock;
  expectedStatus?: number;
  expectedResponse?: unknown;
  expectedError?: unknown;
  description?: string;
  setup?: () => void;
  cleanup?: () => void;
  beforeEach?: () => void;
  afterEach?: () => void;
  timeout?: number;
  skip?: boolean;
  only?: boolean;
  args?: unknown[];
}

/**
 * Test options for utilities
 */
export interface UtilityTestOptions {
  args?: unknown[];
  expectedResult?: unknown;
  expectedError?: unknown;
  description?: string;
  setup?: () => void;
  cleanup?: () => void;
  beforeEach?: () => void;
  afterEach?: () => void;
  timeout?: number;
  skip?: boolean;
  only?: boolean;
}

/**
 * Create a mock request
 * @param options Request options
 * @returns Mock request
 */
export function createMockRequest(options: {
  params?: unknown;
  query?: unknown;
  body?: unknown;
  headers?: unknown;
  user?: unknown;
} = {}): MockRequest {
  return {
    params: options.params ?? {},
    query: options.query ?? {},
    body: options.body ?? {},
    headers: options.headers ?? {},
    user: options.user ?? null,
  } as MockRequest;
}

/**
 * Create a mock response
 * @returns Mock response
 */
export function createMockResponse(): MockResponse {
  const res: MockResponse = {
    status: jest.fn().mockReturnThis(),
    json: jest.fn().mockReturnThis(),
    send: jest.fn().mockReturnThis(),
    end: jest.fn().mockReturnThis(),
    locals: {},
  } as MockResponse;
  return res;
}

/**
 * Create a mock next function
 * @returns Mock next function
 */
export function createMockNext(): jest.Mock {
  return jest.fn();
}

/**
 * Create a mock database model
 * @returns Mock database model
 */
function createMockModel(): unknown {
  return mockModelFactory();
}

/**
 * Create a mock Prisma client
 * @returns Mock Prisma client
 */
export function createMockPrismaClient(): PrismaClient {
  // Define models
  const models: unknown =[
    'user',
    'merchant',
    'transaction',
    'paymentMethod',
    'alert',
    'notification',
    'webhook',
    'subscription',
    'payment',
    'verification',
    'audit',
    'setting',
    'role',
    'permission',
  ];

  // Create mock Prisma client
  const mockPrisma: unknown = {
    $connect: jest.fn(),
    $disconnect: jest.fn(),
    $transaction: jest.fn((callback) => callback(mockPrisma)),
  };

  // Add models to mock Prisma client
  models.forEach(model) => {
    mockPrisma[model] = createMockModel();
  });

  return mockPrisma as unknown as PrismaClient;
}

/**
 * Test controller
 * @param controller Controller to test
 * @param method Method to test
 * @param options Test options
 * @returns Test result
 */
export async function testController<T extends BaseController>(
  controller: T,
  method: keyof T,
  options: ControllerTestOptions = {}
): Promise<{ req: MockRequest; res: MockResponse; next: jest.Mock }> {
  const req = options.req || createMockRequest();
  const res: Response =options.res || createMockResponse();
  const next: NextFunction =options.next || createMockNext();

  if (options.beforeEach) {
    options.beforeEach();
  }

  if (options.setup) {
    options.setup(controller);
  }

  try {
    await (controller[method] as Function)(req, res, next);

    if (options.expectedStatus) {
      expect(res.status).toHaveBeenCalledWith(options.expectedStatus);
    }

    if (options.expectedResponse) {
      expect(res.json).toHaveBeenCalledWith(options.expectedResponse);
    }

    if (options.cleanup) {
      options.cleanup(controller);
    }

    if (options.afterEach) {
      options.afterEach();
    }
  } catch (error) {
    if (options.expectedError) {
      expect(error).toEqual(options.expectedError);
    } else {
      throw error;
    }
  }

  return { req, res, next };
}

/**
 * Test service
 * @param service Service to test
 * @param method Method to test
 * @param options Test options
 * @returns Test result
 */
export async function testService<T extends BaseService>(
  service: T,
  method: keyof T,
  options: ServiceTestOptions = {}
): Promise<unknown> {
  const args = options.args ?? [];

  if (options.beforeEach) {
    options.beforeEach();
  }

  if (options.setup) {
    options.setup(service);
  }

  // Mock dependencies if provided
  if (options.mockDependencies) {
    Object.entries(options.mockDependencies).forEach(([key, value]) => {
      (service as unknown)[key] = value;
    });
  }

  try {
    const result: unknown =await (service[method] as Function)(...args);

    if (options.expectedResult !== undefined) {
      expect(result).toEqual(options.expectedResult);
    }

    if (options.cleanup) {
      options.cleanup(service);
    }

    if (options.afterEach) {
      options.afterEach();
    }

    return result;
  } catch (error) {
    if (options.expectedError) {
      expect(error).toEqual(options.expectedError);
    } else {
      throw error;
    }
  }
}

/**
 * Test repository
 * @param repository Repository to test
 * @param method Method to test
 * @param options Test options
 * @returns Test result
 */
export async function testRepository<T extends BaseRepository>(
  repository: T,
  method: keyof T,
  options: RepositoryTestOptions = {}
): Promise<unknown> {
  const args = options.args ?? [];
  const mockPrisma: unknown =options.mockPrisma || createMockPrismaClient();

  // Replace the repository's prisma client with the mock
  (repository as unknown).prisma = mockPrisma;

  if (options.beforeEach) {
    options.beforeEach();
  }

  if (options.setup) {
    options.setup(repository);
  }

  // Mock transaction if needed
  if (options.mockTransaction) {
    mockPrisma.$transaction.mockImplementation((callback) => {
      return Promise.resolve(options.mockTransactionResult || callback(mockPrisma));
    });
  }

  try {
    const result: unknown =await (repository[method] as Function)(...args);

    if (options.expectedResult !== undefined) {
      expect(result).toEqual(options.expectedResult);
    }

    if (options.cleanup) {
      options.cleanup(repository);
    }

    if (options.afterEach) {
      options.afterEach();
    }

    return result;
  } catch (error) {
    if (options.expectedError) {
      expect(error).toEqual(options.expectedError);
    } else {
      throw error;
    }
  }
}

/**
 * Create a test suite for a controller
 * @param name Test suite name
 * @param controllerClass Controller class
 * @param tests Test definitions
 */
export function testControllerSuite(
  name: string,
  controllerClass: new () => BaseController,
  tests: {
    [method: string]: ControllerTestOptions
  }
): void {
  describe(name, () => {
    let controller: BaseController;

    beforeEach(() => {
      controller = new controllerClass();
    });

    Object.entries(tests).forEach(([method, test]) => {
      it(test.description || `should test ${method}`, async () => {
        await testController(
          controller as unknown,
          method as unknown,
          test
        );
      });
    });
  });
}

/**
 * Create a test suite for a service
 * @param name Test suite name
 * @param serviceClass Service class
 * @param tests Test definitions
 * @param setupFn Setup function
 */
export function testServiceSuite(
  name: string,
  serviceClass: new () => BaseService,
  tests: {
    [method: string]: ServiceTestOptions
  },
  setupFn?: (service: BaseService) => void
): void {
  describe(name, () => {
    let service: BaseService;

    beforeEach(() => {
      service = new serviceClass();
      if (setupFn) {
        setupFn(service);
      }
    });

    Object.entries(tests).forEach(([method, test]) => {
      it(test.description || `should test ${method}`, async () => {
        await testService(
          service as unknown,
          method as unknown,
          test
        );
      });
    });
  });
}

/**
 * Create a test suite for a repository
 * @param name Test suite name
 * @param repositoryClass Repository class
 * @param tests Test definitions
 */
export function testRepositorySuite(
  name: string,
  repositoryClass: new () => BaseRepository,
  tests: {
    [method: string]: RepositoryTestOptions
  }
): void {
  describe(name, () => {
    let repository: BaseRepository;

    beforeEach(() => {
      repository = new repositoryClass();
    });

    Object.entries(tests).forEach(([method, test]) => {
      it(test.description || `should test ${method}`, async () => {
        await testRepository(
          repository as unknown,
          method as unknown,
          test
        );
      });
    });
  });
}

/**
 * Test middleware
 * @param middleware Middleware function
 * @param options Test options
 * @returns Test result
 */
export async function testMiddleware(
  middleware: Function,
  options: MiddlewareTestOptions = {}
): Promise<{ req: MockRequest; res: MockResponse; next: jest.Mock }> {
  const req = options.req || createMockRequest();
  const res: Response =options.res || createMockResponse();
  const next: NextFunction =options.next || createMockNext();

  if (options.beforeEach) {
    options.beforeEach();
  }

  if (options.setup) {
    options.setup(req, res, next);
  }

  try {
    await middleware(req, res, next);

    if (options.expectedStatus) {
      expect(res.status).toHaveBeenCalledWith(options.expectedStatus);
    }

    if (options.expectedResponse) {
      expect(res.json).toHaveBeenCalledWith(options.expectedResponse);
    }

    if (options.cleanup) {
      options.cleanup(req, res, next);
    }

    if (options.afterEach) {
      options.afterEach();
    }
  } catch (error) {
    if (options.expectedError) {
      expect(error).toEqual(options.expectedError);
    } else {
      throw error;
    }
  }

  return { req, res, next };
}

/**
 * Test validator
 * @param validator Validator function
 * @param options Test options
 * @returns Test result
 */
export async function testValidator(
  validator: Function,
  options: ValidatorTestOptions = {}
): Promise<unknown> {
  const args = options.args ?? [];
  const req: Request =options.req || createMockRequest();
  const res: Response =options.res || createMockResponse();
  const next: NextFunction =options.next || createMockNext();

  if (options.beforeEach) {
    options.beforeEach();
  }

  if (options.setup) {
    options.setup();
  }

  try {
    const result: unknown =await validator(req, res, next, ...args);

    if (options.expectedStatus) {
      expect(res.status).toHaveBeenCalledWith(options.expectedStatus);
    }

    if (options.expectedResponse) {
      expect(res.json).toHaveBeenCalledWith(options.expectedResponse);
    }

    if (options.cleanup) {
      options.cleanup();
    }

    if (options.afterEach) {
      options.afterEach();
    }

    return result;
  } catch (error) {
    if (options.expectedError) {
      expect(error).toEqual(options.expectedError);
    } else {
      throw error;
    }
  }
}

/**
 * Test utility function
 * @param utility Utility function
 * @param options Test options
 * @returns Test result
 */
export async function testUtility(
  utility: Function,
  options: UtilityTestOptions = {}
): Promise<unknown> {
  const args = options.args ?? [];

  if (options.beforeEach) {
    options.beforeEach();
  }

  if (options.setup) {
    options.setup();
  }

  try {
    const result: unknown =await utility(...args);

    if (options.expectedResult !== undefined) {
      expect(result).toEqual(options.expectedResult);
    }

    if (options.cleanup) {
      options.cleanup();
    }

    if (options.afterEach) {
      options.afterEach();
    }

    return result;
  } catch (error) {
    if (options.expectedError) {
      expect(error).toEqual(options.expectedError);
    } else {
      throw error;
    }
  }
}

/**
 * Create a test suite for middleware
 * @param name Test suite name
 * @param middleware Middleware function
 * @param tests Test definitions
 */
export function testMiddlewareSuite(
  name: string,
  middleware: Function,
  tests: {
    [testName: string]: MiddlewareTestOptions
  }
): void {
  describe(name, () => {
    Object.entries(tests).forEach(([testName, test]) => {
      const testFn: unknown =test.skip ? it.skip : test.only ? it.only : it;

      testFn(test.description || testName, async () => {
        if (test.beforeEach) {
          test.beforeEach();
        }

        await testMiddleware(middleware, test);

        if (test.afterEach) {
          test.afterEach();
        }
      });

      if (test.timeout) {
        testFn.timeout(test.timeout);
      }
    });
  });
}

/**
 * Create a test suite for validators
 * @param name Test suite name
 * @param validator Validator function
 * @param tests Test definitions
 */
export function testValidatorSuite(
  name: string,
  validator: Function,
  tests: {
    [testName: string]: ValidatorTestOptions
  }
): void {
  describe(name, () => {
    Object.entries(tests).forEach(([testName, test]) => {
      const testFn: unknown =test.skip ? it.skip : test.only ? it.only : it;

      testFn(test.description || testName, async () => {
        if (test.beforeEach) {
          test.beforeEach();
        }

        await testValidator(validator, test);

        if (test.afterEach) {
          test.afterEach();
        }
      });

      if (test.timeout) {
        testFn.timeout(test.timeout);
      }
    });
  });
}

/**
 * Create a test suite for utilities
 * @param name Test suite name
 * @param utility Utility function
 * @param tests Test definitions
 */
export function testUtilitySuite(
  name: string,
  utility: Function,
  tests: {
    [testName: string]: UtilityTestOptions
  }
): void {
  describe(name, () => {
    Object.entries(tests).forEach(([testName, test]) => {
      const testFn: unknown =test.skip ? it.skip : test.only ? it.only : it;

      testFn(test.description || testName, async () => {
        if (test.beforeEach) {
          test.beforeEach();
        }

        await testUtility(utility, test);

        if (test.afterEach) {
          test.afterEach();
        }
      });

      if (test.timeout) {
        testFn.timeout(test.timeout);
      }
    });
  });
}

/**
 * Create a complete test suite for a module
 * @param name Module name
 * @param options Test options
 */
export function testModule(
  name: string,
  options: {
    controllerClass?: new () => BaseController;
    serviceClass?: new () => BaseService;
    repositoryClass?: new () => BaseRepository;
    middleware?: Function;
    validators?: { [name: string]: Function };
    utilities?: { [name: string]: Function };
    controllerTests?: { [method: string]: ControllerTestOptions };
    serviceTests?: { [method: string]: ServiceTestOptions };
    repositoryTests?: { [method: string]: RepositoryTestOptions };
    middlewareTests?: { [testName: string]: MiddlewareTestOptions };
    validatorTests?: { [validatorName: string]: { [testName: string]: ValidatorTestOptions } };
    utilityTests?: { [utilityName: string]: { [testName: string]: UtilityTestOptions } };
    setupServiceFn?: (service: BaseService) => void;
  }
): void {
  describe(`${name} Module`, () => {
    // Test controller
    if (options.controllerClass && options.controllerTests) {
      testControllerSuite(`${name}Controller`, options.controllerClass, options.controllerTests);
    }

    // Test service
    if (options.serviceClass && options.serviceTests) {
      testServiceSuite(`${name}Service`, options.serviceClass, options.serviceTests, options.setupServiceFn);
    }

    // Test repository
    if (options.repositoryClass && options.repositoryTests) {
      testRepositorySuite(`${name}Repository`, options.repositoryClass, options.repositoryTests);
    }

    // Test middleware
    if (options.middleware && options.middlewareTests) {
      testMiddlewareSuite(`${name}Middleware`, options.middleware, options.middlewareTests);
    }

    // Test validators
    if (options.validators && options.validatorTests) {
      Object.entries(options.validators).forEach(([validatorName, validator]) => {
        if (options.validatorTests[validatorName]) {
          testValidatorSuite(`${validatorName}`, validator, options.validatorTests[validatorName]);
        }
      });
    }

    // Test utilities
    if (options.utilities && options.utilityTests) {
      Object.entries(options.utilities).forEach(([utilityName, utility]) => {
        if (options.utilityTests[utilityName]) {
          testUtilitySuite(`${utilityName}`, utility, options.utilityTests[utilityName]);
        }
      });
    }
  });
}