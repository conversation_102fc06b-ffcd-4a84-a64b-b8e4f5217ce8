/**
 * Authorization Service
 *
 * Handles authorization logic for alert aggregation operations.
 */
import { AuthorizationContext, PermissionResult } from '../types/AlertAggregationTypes';
/**
 * Authorization service for alert aggregation
 */
export declare class AuthorizationService {
    private readonly adminRoles;
    private readonly managerRoles;
    private readonly userRoles;
    /**
     * Check if user is authorized for the given action
     */
    checkPermission(context: AuthorizationContext): Promise<PermissionResult>;
    /**
     * Check role-based permissions
     */
    private checkRolePermission;
    /**
     * Check aggregation rule permissions
     */
    private checkAggregationRulePermission;
    /**
     * Check correlation rule permissions
     */
    private checkCorrelationRulePermission;
    /**
     * Check resource-specific permissions
     */
    private checkResourcePermission;
    /**
     * Require admin role
     */
    requireAdmin(userRole?: string): void;
    /**
     * Require manager role or higher
     */
    requireManager(userRole?: string): void;
    /**
     * Require authenticated user
     */
    requireAuthenticated(userRole?: string): void;
    /**
     * Check if user has specific role
     */
    hasRole(userRole: string, requiredRole: string): boolean;
    /**
     * Get user permissions for a resource
     */
    getUserPermissions(userRole: string, resource: string): string[];
    /**
     * Validate authorization context
     */
    validateAuthorizationContext(context: AuthorizationContext): void;
    /**
     * Create authorization context from request
     */
    createAuthorizationContext(user: any, resource: string, action: string, resourceId?: string): AuthorizationContext;
    /**
     * Handle authorization error
     */
    handleAuthorizationError(result: PermissionResult): never;
}
//# sourceMappingURL=AuthorizationService.d.ts.map