/**
 * Error Handler Utility
 *
 * This utility provides centralized error handling for the application.
 * It includes functions for handling errors in controllers, services, and middleware.
 */
import { Request, Response, NextFunction } from 'express';
import { AppError } from './errors/AppError';
/**
 * Extended Request interface with id property
 */
interface ExtendedRequest extends Request {
    id?: string;
}
/**
 * Error response structure
 */
export interface ErrorResponse {
    status: string;
    statusCode: number;
    message: string;
    error?: string;
    stack?: string;
    timestamp: string;
    path: string;
    requestId?: string;
    code?: string;
    details?: unknown;
}
/**
 * Handle controller errors
 * @param err Error
 * @param req Request
 * @param res Response
 * @param next NextFunction
 */
export declare const handleControllerError: (err: Error | AppError, req: ExtendedRequest, res: Response, next: NextFunction) => void;
export declare const handleServiceError: (err: Error | AppError, serviceName: string, methodName: string, params?: unknown) => never;
/**
 * Global error handler middleware
 * @param err Error
 * @param req Request
 * @param res Response
 * @param next NextFunction
 */
export declare const globalErrorHandler: (err: Error | AppError, req: ExtendedRequest, res: Response, next: NextFunction) => void;
export declare const handle404Error: (req: ExtendedRequest, res: Response, next: NextFunction) => void;
declare const _default: {
    handleControllerError: (err: Error | AppError, req: ExtendedRequest, res: Response, next: NextFunction) => void;
    handleServiceError: (err: Error | AppError, serviceName: string, methodName: string, params?: unknown) => never;
    globalErrorHandler: (err: Error | AppError, req: ExtendedRequest, res: Response, next: NextFunction) => void;
    handle404Error: (req: ExtendedRequest, res: Response, next: NextFunction) => void;
};
export default _default;
//# sourceMappingURL=error-handler.d.ts.map