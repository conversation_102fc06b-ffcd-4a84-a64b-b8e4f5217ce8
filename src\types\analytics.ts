/**
 * Analytics and reporting type definitions
 */

// ============================================================================
// ANALYTICS TYPES
// ============================================================================

/**
 * Analytics event base interface
 */
export interface AnalyticsEvent {
  id?: string;
  type: string;
  timestamp: Date;
  userId?: string;
  sessionId?: string;
  metadata?: Record<string, unknown>;
}

/**
 * API analytics event
 */
export interface ApiAnalyticsEvent extends AnalyticsEvent {
  path: string;
  method: string;
  statusCode: number;
  responseTime: number;
  userRole?: string;
  userAgent?: string;
  ipAddress?: string;
  apiVersion?: string;
}

/**
 * Payment analytics event
 */
export interface PaymentAnalyticsEvent extends AnalyticsEvent {
  paymentId: string;
  amount: number;
  currency: string;
  paymentMethod: string;
  merchantId: string;
  status: string;
  processingTime?: number;
  fees?: number;
}

/**
 * User analytics event
 */
export interface UserAnalyticsEvent extends AnalyticsEvent {
  action: string;
  resource?: string;
  resourceId?: string;
  userRole: string;
  ipAddress?: string;
  userAgent?: string;
}

// ============================================================================
// REPORTING TYPES
// ============================================================================

/**
 * Report template configuration
 */
export interface ReportConfig {
  columns: string[];
  groupBy?: string[];
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
  filters?: Record<string, unknown>;
  aggregations?: ReportAggregation[];
  timeRange?: string;
  format?: 'json' | 'csv' | 'pdf' | 'excel';
}

/**
 * Report aggregation
 */
export interface ReportAggregation {
  field: string;
  function: 'sum' | 'avg' | 'count' | 'min' | 'max';
  alias?: string;
}

/**
 * Report template
 */
export interface ReportTemplate {
  id: string;
  name: string;
  description?: string;
  type: ReportType;
  config: ReportConfig;
  isSystem: boolean;
  isPublic: boolean;
  createdById: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Report types
 */
export type ReportType = 
  | 'TRANSACTION'
  | 'PAYMENT'
  | 'CUSTOMER'
  | 'MERCHANT'
  | 'PAYMENT_METHOD'
  | 'SUBSCRIPTION'
  | 'ANALYTICS'
  | 'AUDIT'
  | 'FINANCIAL'
  | 'OPERATIONAL';

/**
 * Report execution result
 */
export interface ReportResult {
  id: string;
  templateId: string;
  status: ReportStatus;
  data?: unknown[];
  metadata?: ReportMetadata;
  error?: string;
  startedAt: Date;
  completedAt?: Date;
  createdBy: string;
}

/**
 * Report status
 */
export type ReportStatus = 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';

/**
 * Report metadata
 */
export interface ReportMetadata {
  totalRows: number;
  executionTime: number;
  dataSize: number;
  filters: Record<string, unknown>;
  generatedAt: Date;
  version: string;
}

// ============================================================================
// DASHBOARD TYPES
// ============================================================================

/**
 * Dashboard configuration
 */
export interface Dashboard {
  id: string;
  name: string;
  description?: string;
  layout: DashboardLayout;
  isPublic: boolean;
  createdById: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Dashboard layout
 */
export interface DashboardLayout {
  columns: number;
  rows: number;
  widgets: DashboardWidgetLayout[];
}

/**
 * Dashboard widget layout
 */
export interface DashboardWidgetLayout {
  id: string;
  x: number;
  y: number;
  width: number;
  height: number;
}

/**
 * Dashboard widget
 */
export interface DashboardWidget {
  id: string;
  dashboardId: string;
  title: string;
  type: WidgetType;
  config: WidgetConfig;
  position: WidgetPosition;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Widget types
 */
export type WidgetType = 
  | 'CHART'
  | 'METRIC'
  | 'TABLE'
  | 'STATUS'
  | 'GAUGE'
  | 'MAP'
  | 'TEXT'
  | 'IMAGE';

/**
 * Widget configuration
 */
export interface WidgetConfig {
  dataSource?: string;
  metric?: string;
  chartType?: ChartType;
  groupBy?: string;
  timeRange?: string;
  filters?: Record<string, unknown>;
  thresholds?: Record<string, unknown>;
  refreshInterval?: number;
  [key: string]: unknown;
}

/**
 * Widget position
 */
export interface WidgetPosition {
  x: number;
  y: number;
  width: number;
  height: number;
}

/**
 * Chart types
 */
export type ChartType = 
  | 'line'
  | 'bar'
  | 'pie'
  | 'doughnut'
  | 'area'
  | 'scatter'
  | 'bubble'
  | 'radar'
  | 'polar';

// ============================================================================
// METRICS TYPES
// ============================================================================

/**
 * Metric definition
 */
export interface MetricDefinition {
  name: string;
  description?: string;
  type: MetricType;
  unit?: string;
  aggregation: MetricAggregation;
  dimensions?: string[];
  filters?: Record<string, unknown>;
}

/**
 * Metric types
 */
export type MetricType = 'COUNTER' | 'GAUGE' | 'HISTOGRAM' | 'SUMMARY';

/**
 * Metric aggregation
 */
export type MetricAggregation = 'sum' | 'avg' | 'count' | 'min' | 'max' | 'p50' | 'p95' | 'p99';

/**
 * Metric value
 */
export interface MetricValue {
  metric: string;
  value: number;
  timestamp: Date;
  dimensions?: Record<string, string>;
  tags?: Record<string, string>;
}

/**
 * Time series data point
 */
export interface TimeSeriesDataPoint {
  timestamp: Date;
  value: number;
  dimensions?: Record<string, string>;
}

/**
 * Time series data
 */
export interface TimeSeriesData {
  metric: string;
  dataPoints: TimeSeriesDataPoint[];
  metadata?: {
    unit?: string;
    aggregation?: MetricAggregation;
    timeRange?: string;
  };
}

// ============================================================================
// ALERT TYPES
// ============================================================================

/**
 * Alert rule
 */
export interface AlertRule {
  id: string;
  name: string;
  description?: string;
  metric: string;
  condition: AlertCondition;
  threshold: number;
  severity: AlertSeverity;
  enabled: boolean;
  channels: string[];
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Alert condition
 */
export type AlertCondition = 'gt' | 'gte' | 'lt' | 'lte' | 'eq' | 'ne';

/**
 * Alert severity
 */
export type AlertSeverity = 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';

/**
 * Alert notification
 */
export interface AlertNotification {
  id: string;
  ruleId: string;
  metric: string;
  value: number;
  threshold: number;
  condition: AlertCondition;
  severity: AlertSeverity;
  message: string;
  status: AlertStatus;
  triggeredAt: Date;
  resolvedAt?: Date;
  acknowledgedAt?: Date;
  acknowledgedBy?: string;
}

/**
 * Alert status
 */
export type AlertStatus = 'TRIGGERED' | 'ACKNOWLEDGED' | 'RESOLVED' | 'SUPPRESSED';

/**
 * Alert channel
 */
export interface AlertChannel {
  id: string;
  name: string;
  type: AlertChannelType;
  config: AlertChannelConfig;
  enabled: boolean;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Alert channel types
 */
export type AlertChannelType = 'EMAIL' | 'SLACK' | 'SMS' | 'WEBHOOK' | 'PAGERDUTY';

/**
 * Alert channel configuration
 */
export interface AlertChannelConfig {
  [key: string]: unknown;
}
