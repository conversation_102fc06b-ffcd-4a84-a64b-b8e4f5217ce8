import { Request, Response } from "express";
import { BaseController } from './base/BaseController';
export declare class BlockchainVerificationController extends BaseController {
    private blockchainApiService;
    private binanceApiService;
    constructor();
    /**
     * Verify a blockchain transaction
     */
    verifyBlockchainTransaction: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
    /**
     * Verify a Binance transaction
     */
    verifyBinanceTransaction: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
}
declare const _default: BlockchainVerificationController;
export default _default;
//# sourceMappingURL=blockchain-verification.controller.d.ts.map