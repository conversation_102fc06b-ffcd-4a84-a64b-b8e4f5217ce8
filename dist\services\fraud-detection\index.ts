/**
 * Fraud Detection Module
 * 
 * Centralized exports for the fraud detection system.
 */

// Core exports
export { FraudDetectionService } from './core/FraudDetectionService';
export * from './core/FraudDetectionTypes';

// Detector exports
export { AmountRiskDetector } from './detectors/AmountRiskDetector';
export { VelocityRiskDetector } from './detectors/VelocityRiskDetector';

// Rule engine exports
export { 
  RiskRuleEngine,
  HighRiskCountryRule,
  BlacklistRule,
  SuspiciousAmountPatternRule,
  UnusualTimeRule
} from './rules/RiskRuleEngine';

// Default export - main service class
export { FraudDetectionService as default } from './core/FraudDetectionService';
