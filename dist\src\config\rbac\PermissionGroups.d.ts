/**
 * Permission Groups
 *
 * Defines groups of related permissions for easier management.
 */
/**
 * Permission group for admin access
 */
export declare const ADMIN_PERMISSIONS: any;
/**
 * Permission group for merchant management
 */
export declare const MERCHANT_PERMISSIONS: any;
/**
 * Permission group for payment management
 */
export declare const PAYMENT_PERMISSIONS: any;
/**
 * Permission group for verification management
 */
export declare const VERIFICATION_PERMISSIONS: any;
/**
 * Permission group for subscription management
 */
export declare const SUBSCRIPTION_PERMISSIONS: any;
/**
 * Permission group for analytics
 */
export declare const ANALYTICS_PERMISSIONS: any;
/**
 * Permission group for monitoring
 */
export declare const MONITORING_PERMISSIONS: any;
/**
 * Permission group for security management
 */
export declare const SECURITY_PERMISSIONS: any;
/**
 * Permission group for settings management
 */
export declare const SETTINGS_PERMISSIONS: any;
/**
 * Permission group for notification management
 */
export declare const NOTIFICATION_PERMISSIONS: any;
/**
 * Permission group for role management
 */
export declare const ROLE_PERMISSIONS: any;
/**
 * Permission group for admin user management
 */
export declare const ADMIN_USER_PERMISSIONS: any;
/**
 * All permissions
 */
export declare const ALL_PERMISSIONS: any;
//# sourceMappingURL=PermissionGroups.d.ts.map