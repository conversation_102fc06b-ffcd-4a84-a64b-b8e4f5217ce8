f5f995620fcd7876e679e66475c2ad82
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const responseUtils_1 = require("../../../../shared/modules/utils/responseUtils");
describe('responseUtils', () => {
    let mockResponse;
    beforeEach(() => {
        mockResponse = {
            status: jest.fn().mockReturnThis(),
            json: jest.fn().mockReturnThis()
        };
    });
    describe('sendSuccess', () => {
        it('should send a success response with default values', () => {
            // Act
            (0, responseUtils_1.sendSuccess)(mockResponse);
            // Assert
            expect(mockResponse.status).toHaveBeenCalledWith(200);
            expect(mockResponse.json).toHaveBeenCalledWith({
                success: true,
                message: 'Success',
                data: {}
            });
        });
        it('should send a success response with custom values', () => {
            // Arrange
            const data = { id: 1, name: 'Test' };
            const message = 'Custom message';
            const statusCode = 201;
            // Act
            (0, responseUtils_1.sendSuccess)(mockResponse, data, message, statusCode);
            // Assert
            expect(mockResponse.status).toHaveBeenCalledWith(statusCode);
            expect(mockResponse.json).toHaveBeenCalledWith({
                success: true,
                message,
                data
            });
        });
    });
    describe('sendError', () => {
        it('should send an error response with default values', () => {
            // Act
            (0, responseUtils_1.sendError)(mockResponse);
            // Assert
            expect(mockResponse.status).toHaveBeenCalledWith(500);
            expect(mockResponse.json).toHaveBeenCalledWith({
                success: false,
                message: 'Error',
                error: null
            });
        });
        it('should send an error response with custom values', () => {
            // Arrange
            const message = 'Custom error';
            const statusCode = 400;
            const error = new Error('Test error');
            // Act
            (0, responseUtils_1.sendError)(mockResponse, message, statusCode, error);
            // Assert
            expect(mockResponse.status).toHaveBeenCalledWith(statusCode);
            expect(mockResponse.json).toHaveBeenCalledWith({
                success: false,
                message,
                error: error.message
            });
        });
        it('should handle error objects with message property', () => {
            // Arrange
            const error = { message: 'Error message' };
            // Act
            (0, responseUtils_1.sendError)(mockResponse, 'Error', 500, error);
            // Assert
            expect(mockResponse.json).toHaveBeenCalledWith({
                success: false,
                message: 'Error',
                error: 'Error message'
            });
        });
        it('should handle non-object errors', () => {
            // Arrange
            const error = 'String error';
            // Act
            (0, responseUtils_1.sendError)(mockResponse, 'Error', 500, error);
            // Assert
            expect(mockResponse.json).toHaveBeenCalledWith({
                success: false,
                message: 'Error',
                error: 'String error'
            });
        });
    });
    describe('createApiResponse', () => {
        it('should create a success response', () => {
            // Arrange
            const success = true;
            const message = 'Success message';
            const data = { id: 1 };
            // Act
            const result = (0, responseUtils_1.createApiResponse)(success, message, data);
            // Assert
            expect(result).toEqual({
                success,
                message,
                data,
                error: null
            });
        });
        it('should create an error response', () => {
            // Arrange
            const success = false;
            const message = 'Error message';
            const error = 'Error details';
            // Act
            const result = (0, responseUtils_1.createApiResponse)(success, message, null, error);
            // Assert
            expect(result).toEqual({
                success,
                message,
                data: null,
                error
            });
        });
        it('should use default values when not provided', () => {
            // Arrange
            const success = true;
            const message = 'Message';
            // Act
            const result = (0, responseUtils_1.createApiResponse)(success, message);
            // Assert
            expect(result).toEqual({
                success,
                message,
                data: null,
                error: null
            });
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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