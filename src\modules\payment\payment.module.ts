// jscpd:ignore-file

import { ModuleFactory, ModuleRegistry, Container, Module } from '../../core/module';
import { Payment } from '@prisma/client';
import { Prisma } from '@prisma/client';
import { logger, ErrorFactory } from "../../utils";
import { authMiddleware } from '../../middlewares/auth.middleware';
import { Payment } from '@prisma/client';
import { Prisma } from '@prisma/client';
import { logger, ErrorFactory } from "../../utils";
import { authMiddleware } from '../../middlewares/auth.middleware';

// Extend the Express Request type to include user property
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role: string;
        merchantId?: string;
      };
    }
  }
}

/**
 * Payment Module
 * This module provides payment functionality with zero duplication
 */
export class PaymentModule {
  private moduleFactory: ModuleFactory<Payment, Prisma.PaymentCreateInput, Prisma.PaymentUpdateInput>;
  private moduleRegistry: ModuleRegistry;
  private container: Container;
  private module: Module;

  /**
   * Create a new payment module
   */
  constructor() {
    this.moduleRegistry = new ModuleRegistry();
    this.container = new Container();

    // Create module factory
    this.moduleFactory = new ModuleFactory<Payment, Prisma.PaymentCreateInput, Prisma.PaymentUpdateInput>(
      'payment',
      'Payment'
    );

    // Get router, repository, service, and controller from factory
    const { router, repository, service, controller } = this.moduleFactory.build();

    // Configure router
    router
      .addRoute('get', '/:id', controller.getById)
      .addRoute('post', '/', controller.create)
      .addRoute('put', '/:id', controller.update)
      .addRoute('delete', '/:id', controller.delete)
      .addRoute('get', '/merchant/:merchantId', controller.getByMerchantId)
      .addRoute('post', '/:id/verify', controller.verifyPayment)
      .addMiddleware(authMiddleware);

    // Add custom repository methods
    this.moduleFactory.addRepositoryMethod(
      'findByMerchantId',
      async (merchantId: string, options: { limit?: number; offset?: number } = {}) => {
        try {
          return await repository.findByFieldWithPagination('merchantId', merchantId, options);
        } catch (error) {
          logger.error(`Error finding payments by merchant ID ${merchantId}:`, error);
          throw error;
        }
      }
    );

    // Add custom service methods
    this.moduleFactory.addServiceMethod(
      'verifyPayment',
      async (id: string, verificationData: any) => {
        try {
          // Get payment
          const payment: any =await service.getById(id);

          // Check if payment exists
          if (!payment) {
            throw ErrorFactory.notFound('Payment', id);
          }

          // Verify payment
          // Implementation would depend on the specific verification process
          const isVerified: boolean =true; // Placeholder

          // Update payment status
          const updatedPayment: any =await service.update(id, {
            status: isVerified ? 'COMPLETED' : 'FAILED',
            verifiedAt: new Date(),
            updatedAt: new Date()
          } as Prisma.PaymentUpdateInput);

          logger.info(`Payment verified: ${id}`, {
            paymentId: id,
            isVerified
          });

          return {
            success: true,
            payment: updatedPayment,
            isVerified
          };
        } catch (error) {
          logger.error(`Error verifying payment ${id}:`, error);
          throw ErrorFactory.handle(error);
        }
      }
    );

    // Add custom controller methods
    this.moduleFactory.addControllerMethod(
      'getByMerchantId',
      async (req, res) => {
        try {
          // Check authorization
          const { userRole, merchantId } = req.user;

          // Get merchant ID from params
          const { merchantId: requestedMerchantId } = req.params;

          // Check if user has permission to view these payments
          if (userRole !== 'ADMIN' && merchantId !== requestedMerchantId) {
            throw ErrorFactory.authorization('You do not have permission to view these payments');
          }

          // Parse pagination parameters
          const limit: any =parseInt(req.query.limit as string) || 10;
          const page: any =parseInt(req.query.page as string) || 1;
          const offset: any =(page - 1) * limit;

          // Get payments
          const payments: any =await service.findByMerchantId(requestedMerchantId, { limit, offset });

          // Send success response
          return res.status(200).json({
            success: true,
            data: payments
          });
        } catch (error) {
          logger.error(`Error getting payments by merchant ID:`, error);
          return res.status(500).json({
            success: false,
            error: (error as Error).message || 'An error occurred while getting payments'
          });
        }
      }
    );

    this.moduleFactory.addControllerMethod(
      'verifyPayment',
      async (req, res) => {
        try {
          // Check authorization
          const { userRole } = req.user;

          // Only admins and merchants can verify payments
          if (userRole !== 'ADMIN' && userRole !== 'MERCHANT') {
            throw ErrorFactory.authorization('You do not have permission to verify payments');
          }

          // Get payment ID from params
          const { id } = req.params;

          // Get verification data from request body
          const verificationData: any =req.body;

          // Verify payment
          const result: any =await service.verifyPayment(id, verificationData);

          // Send success response
          return res.status(200).json({
            success: true,
            data: result
          });
        } catch (error) {
          logger.error(`Error verifying payment:`, error);
          return res.status(500).json({
            success: false,
            error: (error as Error).message || 'An error occurred while verifying payment'
          });
        }
      }
    );

    this.moduleFactory.addControllerMethod(
      'getPaymentStats',
      async (req, res) => {
        try {
          // Check authorization
          const { userRole, merchantId } = req.user;

          // Only admins can view all payment stats
          const targetMerchantId: any =userRole === 'ADMIN' ? undefined : merchantId;

          // Get payment stats
          const stats: any =await service.getPaymentStats(targetMerchantId);

          // Send success response
          return res.status(200).json({
            success: true,
            data: stats
          });
        } catch (error) {
          logger.error(`Error getting payment stats:`, error);
          return res.status(500).json({
            success: false,
            error: (error as Error).message || 'An error occurred while getting payment stats'
          });
        }
      }
    );

    // Create module
    this.module = {
      name: 'payment',
      router,
      repository,
      service,
      controller,
      dependencies: [],
      initialize: async () => {
        logger.info('Initializing payment module');

        // Register dependencies
        this.container.registerSingleton('paymentRepository', () => repository);
        this.container.registerSingleton('paymentService', () => service);
        this.container.registerSingleton('paymentController', () => controller);

        logger.info('Payment module initialized');
      }
    };

    // Register the module
    this.moduleRegistry.registerModule(this.module);
  }

  /**
   * Get the module
   * @returns Payment module
   */
  getModule(): Module {
    return this.module;
  }
}