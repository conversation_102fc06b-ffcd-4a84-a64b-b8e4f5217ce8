// jscpd:ignore-file
/**
 * Merchant Segmentation Routes
 * 
 * This file defines the routes for merchant segmentation.
 */

import express from "express";
import { MerchantSegmentationController } from "../controllers/merchant-segmentation.controller";
import { authMiddleware } from '../middlewares/authMiddleware';
import { roleMiddleware } from '../middlewares/roleMiddleware';
import { Merchant } from '../types';
import { authMiddleware } from '../middlewares/authMiddleware';
import { roleMiddleware } from '../middlewares/roleMiddleware';
import { Merchant } from '../types';


const router: any =express.Router();
const merchantSegmentationController: any =new MerchantSegmentationController();

/**
 * @route POST /api/merchant-segmentation/categories
 * @desc Create a new merchant category
 * @access Private (Admin)
 */
router.post(
    "/categories",
    authMiddleware,
    roleMiddleware(["ADMIN"]),
    merchantSegmentationController.createCategory
);

/**
 * @route GET /api/merchant-segmentation/categories
 * @desc Get all merchant categories
 * @access Private (Admin)
 */
router.get(
    "/categories",
    authMiddleware,
    roleMiddleware(["ADMIN"]),
    merchantSegmentationController.getAllCategories
);

/**
 * @route POST /api/merchant-segmentation/categories/:categoryId/merchants/:merchantId
 * @desc Add merchant to category
 * @access Private (Admin)
 */
router.post(
    "/categories/:categoryId/merchants/:merchantId",
    authMiddleware,
    roleMiddleware(["ADMIN"]),
    merchantSegmentationController.addMerchantToCategory
);

/**
 * @route POST /api/merchant-segmentation/segments
 * @desc Create a new merchant segment
 * @access Private (Admin)
 */
router.post(
    "/segments",
    authMiddleware,
    roleMiddleware(["ADMIN"]),
    merchantSegmentationController.createSegment
);

/**
 * @route GET /api/merchant-segmentation/segments
 * @desc Get all merchant segments
 * @access Private (Admin)
 */
router.get(
    "/segments",
    authMiddleware,
    roleMiddleware(["ADMIN"]),
    merchantSegmentationController.getAllSegments
);

/**
 * @route POST /api/merchant-segmentation/segments/:segmentId/apply
 * @desc Apply segment to matching merchants
 * @access Private (Admin)
 */
router.post(
    "/segments/:segmentId/apply",
    authMiddleware,
    roleMiddleware(["ADMIN"]),
    merchantSegmentationController.applySegment
);

/**
 * @route POST /api/merchant-segmentation/performance-tiers
 * @desc Create a new merchant performance tier
 * @access Private (Admin)
 */
router.post(
    "/performance-tiers",
    authMiddleware,
    roleMiddleware(["ADMIN"]),
    merchantSegmentationController.createPerformanceTier
);

/**
 * @route POST /api/merchant-segmentation/performance-tiers/:tierId/apply
 * @desc Apply performance tier to qualifying merchants
 * @access Private (Admin)
 */
router.post(
    "/performance-tiers/:tierId/apply",
    authMiddleware,
    roleMiddleware(["ADMIN"]),
    merchantSegmentationController.applyPerformanceTier
);

export default router;
