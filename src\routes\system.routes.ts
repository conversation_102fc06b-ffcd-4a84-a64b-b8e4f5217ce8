// jscpd:ignore-file

import { Router } from "express";
import { body, param } from "express-validator";
import systemController from "../controllers/system.controller";
import { authenticate, authorize } from "../middlewares/auth.middleware";
import { validate } from "../middlewares/validation.middleware";
import { body, param } from "express-validator";
import { authenticate, authorize } from "../middlewares/auth.middleware";
import { validate } from "../middlewares/validation.middleware";

const router: unknown =Router();

// Admin-only routes for system settings
router.get(
    "/",
    authenticate,
    authorize(["admin"]),
    systemController.getAllSettings
);

router.get(
    "/:key",
    authenticate,
    authorize(["admin"]),
    validate([
        param("key").notEmpty()
    ]),
    systemController.getSettingByKey
);

router.put(
    "/:key",
    authenticate,
    authorize(["admin"]),
    validate([
        param("key").notEmpty(),
        body("value").notEmpty()
    ]),
    systemController.updateSetting
);

router.post(
    "/",
    authenticate,
    authorize(["admin"]),
    validate([
        body("key").notEmpty(),
        body("value").notEmpty()
    ]),
    systemController.createSetting
);

export default router;
