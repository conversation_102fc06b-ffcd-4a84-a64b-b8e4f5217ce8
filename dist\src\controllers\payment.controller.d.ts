import { Request, Response } from "express";
declare class PaymentController {
    /**
     * Get all payments
     */
    getAllPayments(req: Request, res: Response): Promise<Response>;
    /**
     * Get payment by ID
     */
    getPaymentById(req: Request, res: Response): Promise<Response>;
    /**
     * Get payments by merchant ID
     */
    getPaymentsByMerchant(req: Request, res: Response): Promise<Response>;
    /**
     * Create a new payment
     */
    createPayment(req: Request, res: Response): Promise<Response>;
    /**
     * Update an existing payment
     */
    updatePayment(req: Request, res: Response): Promise<Response>;
    /**
     * Delete a payment
     */
    deletePayment(req: Request, res: Response): Promise<Response>;
}
declare const _default: PaymentController;
export default _default;
//# sourceMappingURL=payment.controller.d.ts.map