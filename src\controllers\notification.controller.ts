// jscpd:ignore-file
import { Request, Response, NextFunction } from 'express';
import { BaseController } from './base.controller';
import { asyncHandler } from '../utils/asyncHandler';
import { AppError } from '../utils/errors/AppError';
import { Merchant } from '../types';
import { NotificationService } from '../services/notification.service';
import { PrismaClient } from '@prisma/client';
import { NotificationChannel, NotificationPriority } from '../types/notificationTypes';
import { BaseController } from './base.controller';
import { asyncHandler } from '../utils/asyncHandler';
import { AppError } from '../utils/errors/AppError';
import { Merchant } from '../types';
import { NotificationService } from '../services/notification.service';
import { PrismaClient } from '@prisma/client';
import { NotificationChannel, NotificationPriority } from '../types/notificationTypes';

// Extend the Express Request type to include user property
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role: string;
        merchantId?: string;
      };
    }
  }
}

const prisma: unknown = new PrismaClient();

/**
 * NotificationController
 * Controller for handling notification operations
 */
export class NotificationController extends BaseController {
  constructor() {
    super();
  }

  /**
   * Get user notification preferences
   */
  getUserPreferences = asyncHandler(async (req: Request, res: Response) => {
    try {
      // Check authentication
      const userId: unknown = req.user?.id;

      if (!userId) {
        throw new AppError({
          message: 'Unauthorized',
          type: ErrorType.AUTHENTICATION,
          code: ErrorCode.INVALID_CREDENTIALS,
        });
      }

      // Get notification preferences
      const notificationService: unknown = new NotificationService();
      const preferences: unknown = await notificationService.getUserNotificationPreferences(userId);

      // Return preferences
      return res.status(200).json({
        success: true,
        data: preferences,
      });
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError({
        message: 'Failed to get user notification preferences',
        type: ErrorType.INTERNAL,
        code: ErrorCode.INTERNAL_SERVER_ERROR,
      });
    }
  });

  /**
   * Update user notification preferences
   */
  updateUserPreferences = asyncHandler(async (req: Request, res: Response) => {
    try {
      // Check authentication
      const userId: unknown = req.user?.id;

      if (!userId) {
        throw new AppError({
          message: 'Unauthorized',
          type: ErrorType.AUTHENTICATION,
          code: ErrorCode.INVALID_CREDENTIALS,
        });
      }

      // Get preference data
      const { channel, enabled, channelData } = req.body;

      // Validate required fields
      if (!channel || enabled === undefined) {
        throw new AppError({
          message: 'Channel and enabled are required',
          type: ErrorType.VALIDATION,
          code: ErrorCode.MISSING_REQUIRED_FIELD,
        });
      }

      // Validate channel
      if (!Object.values(NotificationChannel).includes(channel as NotificationChannel)) {
        throw new AppError({
          message: 'Invalid notification channel',
          type: ErrorType.VALIDATION,
          code: ErrorCode.INVALID_INPUT,
        });
      }

      // Validate enabled
      if (typeof enabled !== 'boolean') {
        throw new AppError({
          message: 'Enabled must be a boolean',
          type: ErrorType.VALIDATION,
          code: ErrorCode.INVALID_INPUT,
        });
      }

      // Update preferences
      const notificationService: unknown = new NotificationService();
      const success: unknown = await notificationService.updateUserNotificationPreferences(
        userId,
        channel as NotificationChannel,
        enabled,
        channelData
      );

      if (!success) {
        throw new AppError({
          message: 'Failed to update notification preferences',
          type: ErrorType.INTERNAL,
          code: ErrorCode.INTERNAL_SERVER_ERROR,
        });
      }

      // Return success
      return res.status(200).json({
        success: true,
        message: 'Notification preferences updated successfully',
      });
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError({
        message: 'Failed to update user notification preferences',
        type: ErrorType.INTERNAL,
        code: ErrorCode.INTERNAL_SERVER_ERROR,
      });
    }
  });

  /**
   * Get merchant notification preferences
   */
  getMerchantPreferences = asyncHandler(async (req: Request, res: Response) => {
    try {
      // Get user role and merchant ID
      const userRole: unknown = req.user?.role;
      const userId: unknown = req.user?.id;
      const userMerchantId: unknown = req.user?.merchantId;

      // Get merchant ID from query or user
      let merchantId: unknown = req.query.merchantId as string;

      // If user is not admin, they can only get their own merchant's preferences
      if (userRole !== 'ADMIN') {
        merchantId = userMerchantId;
      }

      // Validate merchant ID
      if (!merchantId) {
        throw new AppError({
          message: 'Merchant ID is required',
          type: ErrorType.VALIDATION,
          code: ErrorCode.MISSING_REQUIRED_FIELD,
        });
      }

      // Get notification preferences
      const notificationService: unknown = new NotificationService();
      const preferences: unknown = await notificationService.getMerchantNotificationPreferences(
        merchantId
      );

      // Return preferences
      return res.status(200).json({
        success: true,
        data: preferences,
      });
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError({
        message: 'Failed to get merchant notification preferences',
        type: ErrorType.INTERNAL,
        code: ErrorCode.INTERNAL_SERVER_ERROR,
      });
    }
  });

  /**
   * Update merchant notification preferences
   */
  updateMerchantPreferences = asyncHandler(async (req: Request, res: Response) => {
    try {
      // Get user role and merchant ID
      const userRole: unknown = req.user?.role;
      const userId: unknown = req.user?.id;
      const userMerchantId: unknown = req.user?.merchantId;

      // Get merchant ID from body or user
      let merchantId: unknown = req.body.merchantId;

      // If user is not admin, they can only update their own merchant's preferences
      if (userRole !== 'ADMIN') {
        merchantId = userMerchantId;
      }

      // Validate merchant ID
      if (!merchantId) {
        throw new AppError({
          message: 'Merchant ID is required',
          type: ErrorType.VALIDATION,
          code: ErrorCode.MISSING_REQUIRED_FIELD,
        });
      }

      // Get preference data
      const { channel, enabled, channelData } = req.body;

      // Validate channel
      if (
        !channel ||
        !Object.values(NotificationChannel).includes(channel as NotificationChannel)
      ) {
        throw new AppError({
          message: 'Invalid notification channel',
          type: ErrorType.VALIDATION,
          code: ErrorCode.INVALID_INPUT,
        });
      }

      // Validate enabled
      if (typeof enabled !== 'boolean') {
        throw new AppError({
          message: 'Enabled must be a boolean',
          type: ErrorType.VALIDATION,
          code: ErrorCode.INVALID_INPUT,
        });
      }

      // Update preferences
      const notificationService: unknown = new NotificationService();
      const success: unknown = await notificationService.updateMerchantNotificationPreferences(
        merchantId,
        channel as NotificationChannel,
        enabled,
        channelData
      );

      if (!success) {
        throw new AppError({
          message: 'Failed to update notification preferences',
          type: ErrorType.INTERNAL,
          code: ErrorCode.INTERNAL_SERVER_ERROR,
        });
      }

      // Return success
      return res.status(200).json({
        success: true,
        message: 'Notification preferences updated successfully',
      });
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError({
        message: 'Failed to update merchant notification preferences',
        type: ErrorType.INTERNAL,
        code: ErrorCode.INTERNAL_SERVER_ERROR,
      });
    }
  });

  /**
   * Send a test notification
   */
  sendTestNotification = asyncHandler(async (req: Request, res: Response) => {
    try {
      // Get user role and ID
      const userRole: unknown = req.user?.role;
      const userId: unknown = req.user?.id;
      const merchantId: unknown = req.user?.merchantId;

      if (!userId) {
        throw new AppError({
          message: 'Unauthorized',
          type: ErrorType.AUTHENTICATION,
          code: ErrorCode.INVALID_CREDENTIALS,
        });
      }

      // Get notification data
      const { channels, priority } = req.body;

      // Validate channels
      if (!channels || !Array.isArray(channels) || channels.length === 0) {
        throw new AppError({
          message: 'Channels must be a non-empty array',
          type: ErrorType.VALIDATION,
          code: ErrorCode.INVALID_INPUT,
        });
      }

      for (const channel of channels) {
        if (!Object.values(NotificationChannel).includes(channel as NotificationChannel)) {
          throw new AppError(`Invalid notification channel: ${channel}`, 400);
        }
      }

      // Validate priority
      if (
        !priority ||
        !Object.values(NotificationPriority).includes(priority as NotificationPriority)
      ) {
        throw new AppError({
          message: 'Invalid notification priority',
          type: ErrorType.VALIDATION,
          code: ErrorCode.INVALID_INPUT,
        });
      }

      // Send test notification
      const notificationService: unknown = new NotificationService();
      const success: unknown = await notificationService.sendNotification({
        userId,
        merchantId,
        channels: channels as NotificationChannel[],
        priority: priority as NotificationPriority,
        subject: 'Test Notification',
        message:
          'This is a test notification from AmazingPay. If you received this notification, your notification settings are working correctly.',
      });

      if (!success) {
        throw new AppError({
          message: 'Failed to send test notification',
          type: ErrorType.INTERNAL,
          code: ErrorCode.INTERNAL_SERVER_ERROR,
        });
      }

      // Return success
      return res.status(200).json({
        success: true,
        message: 'Test notification sent successfully',
      });
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError({
        message: 'Failed to send test notification',
        type: ErrorType.INTERNAL,
        code: ErrorCode.INTERNAL_SERVER_ERROR,
      });
    }
  });

  /**
   * Get notification templates
   */
  getTemplates = asyncHandler(async (req: Request, res: Response) => {
    try {
      // Get user role
      const userRole: unknown = req.user?.role;

      // Check if user is authorized
      if (!userRole || userRole !== 'ADMIN') {
        throw new AppError({
          message: 'Unauthorized',
          type: ErrorType.AUTHENTICATION,
          code: ErrorCode.INVALID_CREDENTIALS,
        });
      }

      // Get templates
      const templates = await prisma.notificationTemplate.findMany({
        orderBy: { name: 'asc' },
      });

      // Return templates
      return res.status(200).json({
        success: true,
        data: templates,
      });
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError({
        message: 'Failed to get notification templates',
        type: ErrorType.INTERNAL,
        code: ErrorCode.INTERNAL_SERVER_ERROR,
      });
    }
  });

  /**
   * Create a notification template
   */
  createTemplate = asyncHandler(async (req: Request, res: Response) => {
    try {
      // Get user role
      const userRole = req.user?.role;

      // Check if user is authorized
      if (!userRole || userRole !== 'ADMIN') {
        throw new AppError({
          message: 'Unauthorized',
          type: ErrorType.AUTHENTICATION,
          code: ErrorCode.INVALID_CREDENTIALS,
        });
      }

      // Get template data
      const { name, description, subject, content, variables } = req.body;

      // Validate required fields
      if (!name || !description || !subject || !content) {
        throw new AppError({
          message: 'Missing required fields',
          type: ErrorType.VALIDATION,
          code: ErrorCode.MISSING_REQUIRED_FIELD,
        });
      }

      // Validate variables
      if (!variables || !Array.isArray(variables)) {
        throw new AppError({
          message: 'Variables must be an array',
          type: ErrorType.VALIDATION,
          code: ErrorCode.INVALID_INPUT,
        });
      }

      // Create template
      const template = await prisma.notificationTemplate.create({
        data: {
          name,
          description,
          subject,
          content,
          variables,
        },
      });

      // Return created template
      return res.status(201).json({
        success: true,
        data: template,
        message: 'Notification template created successfully',
      });
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError({
        message: 'Failed to create notification template',
        type: ErrorType.INTERNAL,
        code: ErrorCode.INTERNAL_SERVER_ERROR,
      });
    }
  });

  /**
   * Update a notification template
   */
  updateTemplate = asyncHandler(async (req: Request, res: Response) => {
    try {
      // Get user role
      const userRole: unknown = req.user?.role;

      // Check if user is authorized
      if (!userRole || userRole !== 'ADMIN') {
        throw new AppError({
          message: 'Unauthorized',
          type: ErrorType.AUTHENTICATION,
          code: ErrorCode.INVALID_CREDENTIALS,
        });
      }

      // Get template ID
      const templateId: unknown = req.params.id;

      // Validate template ID
      if (!templateId) {
        throw new AppError({
          message: 'Template ID is required',
          type: ErrorType.VALIDATION,
          code: ErrorCode.MISSING_REQUIRED_FIELD,
        });
      }

      // Get template data
      const { name, description, subject, content, variables } = req.body;

      // Check if template exists
      const existingTemplate = await prisma.notificationTemplate.findUnique({
        where: { id: templateId },
      });

      if (!existingTemplate) {
        throw new AppError({
          message: 'Template not found',
          type: ErrorType.NOT_FOUND,
          code: ErrorCode.RESOURCE_NOT_FOUND,
        });
      }

      // Update template
      const updatedTemplate = await prisma.notificationTemplate.update({
        where: { id: templateId },
        data: {
          name: name || existingTemplate.name,
          description: description || existingTemplate.description,
          subject: subject || existingTemplate.subject,
          content: content || existingTemplate.content,
          variables: variables || existingTemplate.variables,
        },
      });

      // Return updated template
      return res.status(200).json({
        success: true,
        data: updatedTemplate,
        message: 'Notification template updated successfully',
      });
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError({
        message: 'Failed to update notification template',
        type: ErrorType.INTERNAL,
        code: ErrorCode.INTERNAL_SERVER_ERROR,
      });
    }
  });

  /**
   * Delete a notification template
   */
  deleteTemplate = asyncHandler(async (req: Request, res: Response) => {
    try {
      // Get user role
      const userRole: unknown = req.user?.role;

      // Check if user is authorized
      if (!userRole || userRole !== 'ADMIN') {
        throw new AppError({
          message: 'Unauthorized',
          type: ErrorType.AUTHENTICATION,
          code: ErrorCode.INVALID_CREDENTIALS,
        });
      }

      // Get template ID
      const templateId: unknown = req.params.id;

      // Validate template ID
      if (!templateId) {
        throw new AppError({
          message: 'Template ID is required',
          type: ErrorType.VALIDATION,
          code: ErrorCode.MISSING_REQUIRED_FIELD,
        });
      }

      // Check if template exists
      const existingTemplate = await prisma.notificationTemplate.findUnique({
        where: { id: templateId },
      });

      if (!existingTemplate) {
        throw new AppError({
          message: 'Template not found',
          type: ErrorType.NOT_FOUND,
          code: ErrorCode.RESOURCE_NOT_FOUND,
        });
      }

      // Delete template
      await prisma.notificationTemplate.delete({
        where: { id: templateId },
      });

      // Return success
      return res.status(200).json({
        success: true,
        message: 'Notification template deleted successfully',
      });
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError({
        message: 'Failed to delete notification template',
        type: ErrorType.INTERNAL,
        code: ErrorCode.INTERNAL_SERVER_ERROR,
      });
    }
  });

  /**
   * Helper method to check admin role
   */
  private checkAdminRole(userRole: string | undefined): void {
    if (!userRole || userRole !== 'ADMIN') {
      throw new AppError({
        message: 'Unauthorized. Admin role required.',
        type: ErrorType.AUTHENTICATION,
        code: ErrorCode.INVALID_CREDENTIALS,
      });
    }
  }

  /**
   * Helper method to check authorization
   */
  private checkAuthorization(req: Request): {
    userRole: string;
    userId: string;
    merchantId: string | undefined;
  } {
    const userRole: unknown = req.user?.role;
    const userId: unknown = req.user?.id;
    const merchantId: unknown = req.user?.merchantId;

    if (!userRole || !userId) {
      throw new AppError({
        message: 'Unauthorized',
        type: ErrorType.AUTHENTICATION,
        code: ErrorCode.INVALID_CREDENTIALS,
      });
    }

    return { userRole, userId, merchantId };
  }
}

export default new NotificationController();
