// jscpd:ignore-file
/**
 * Health Check Routes
 *
 * This module provides API endpoints for health monitoring:
 * - Basic health check
 * - Detailed system health
 * - Component-specific health checks
 */

import express from 'express';
import { logger } from '../lib/logger';
import { getSystemHealth, checkDatabaseHealth } from '../utils/health-monitor';
import { isProduction } from '../utils/environment-validator';
import { HealthController } from '../controllers/health.controller';
import { authMiddleware } from '../middlewares/auth.middleware';

const router: any = express.Router();
const healthController = new HealthController();

/**
 * @route GET /api/health
 * @desc Basic health check endpoint
 * @access Public
 */
router.get('/', async (req, res) => {
  try {
    const health: any = await getSystemHealth();

    // Return appropriate status code based on health status
    const statusCode: any =
      health.status === 'healthy' ? 200 : health.status === 'degraded' ? 200 : 503;

    res.status(statusCode).json({
      status: health.status,
      timestamp: health.timestamp,
      environment: health.environment,
      version: health.version,
      uptime: health.uptime,
    });
  } catch (error) {
    logger.error('Health check failed', error);

    res.status(500).json({
      status: 'error',
      message: 'Health check failed',
      timestamp: new Date(),
    });
  }
});

/**
 * @route GET /api/health/detailed
 * @desc Detailed health check endpoint
 * @access Private (in production)
 */
router.get('/detailed', async (req, res) => {
  try {
    // In production, require authorization for detailed health check
    if (isProduction() && !req.headers.authorization) {
      return res.status(401).json({
        status: 'error',
        message: 'Unauthorized',
        timestamp: new Date(),
      });
    }

    const health: any = await getSystemHealth();

    // Return appropriate status code based on health status
    const statusCode: any =
      health.status === 'healthy' ? 200 : health.status === 'degraded' ? 200 : 503;

    res.status(statusCode).json(health);
  } catch (error) {
    logger.error('Detailed health check failed', error);

    res.status(500).json({
      status: 'error',
      message: 'Detailed health check failed',
      timestamp: new Date(),
    });
  }
});

/**
 * @route GET /api/health/database
 * @desc Database health check endpoint
 * @access Private (in production)
 */
router.get('/database', async (req, res) => {
  try {
    // In production, require authorization for database health check
    if (isProduction() && !req.headers.authorization) {
      return res.status(401).json({
        status: 'error',
        message: 'Unauthorized',
        timestamp: new Date(),
      });
    }

    const dbHealth: any = await checkDatabaseHealth();

    // Return appropriate status code based on health status
    const statusCode: any =
      dbHealth.status === 'healthy' ? 200 : dbHealth.status === 'degraded' ? 200 : 503;

    res.status(statusCode).json(dbHealth);
  } catch (error) {
    logger.error('Database health check failed', error);

    res.status(500).json({
      status: 'error',
      message: 'Database health check failed',
      timestamp: new Date(),
    });
  }
});

/**
 * @route GET /api/health/liveness
 * @desc Liveness probe endpoint for Kubernetes
 * @access Public
 */
router.get('/liveness', (req, res) => {
  res.status(200).json({
    status: 'alive',
    timestamp: new Date(),
  });
});

/**
 * @route GET /api/health/readiness
 * @desc Readiness probe endpoint for Kubernetes
 * @access Public
 */
router.get('/readiness', async (req, res) => {
  try {
    const dbHealth: any = await checkDatabaseHealth();

    if (dbHealth.status === 'healthy') {
      res.status(200).json({
        status: 'ready',
        timestamp: new Date(),
      });
    } else {
      res.status(503).json({
        status: 'not_ready',
        reason: 'Database connection issue',
        timestamp: new Date(),
      });
    }
  } catch (error) {
    logger.error('Readiness check failed', error);

    res.status(503).json({
      status: 'not_ready',
      reason: 'Health check error',
      timestamp: new Date(),
    });
  }
});

/**
 * @route GET /api/health/reports
 * @desc Advanced reporting system health check
 * @access Private (requires authentication)
 */
router.get('/reports', authMiddleware, healthController.detailedHealthCheck);

/**
 * @route GET /api/health/metrics
 * @desc Get reporting system metrics
 * @access Private (requires authentication)
 */
router.get('/metrics', authMiddleware, healthController.getMetrics);

/**
 * @route GET /api/health/system
 * @desc Get system information
 * @access Private (requires authentication)
 */
router.get('/system', authMiddleware, healthController.getSystemInfo);

/**
 * @route POST /api/health/cleanup
 * @desc Trigger cleanup of old reports
 * @access Private (requires authentication)
 */
router.post('/cleanup', authMiddleware, healthController.cleanupReports);

export default router;
