// jscpd:ignore-file
/**
 * Payment Router
 *
 * Implements a smart routing system for optimal payment method selection.
 */

import { IPaymentMethod } from '../../../interfaces/payment/IPaymentMethod';
import { PaymentMethodType } from '../../../types/payment-method.types';
import { logger } from '../../../lib/logger';
import { PaymentMethodType } from '../types';
import { PaymentMethodType } from '../../../types/payment-method.types';
import { logger } from '../../../lib/logger';
import { PaymentMethodType } from '../types';

/**
 * Payment routing context
 */
export interface PaymentRoutingContext {
  merchantId: string;
  amount: number;
  currency: string;
  country?: string;
  paymentMethods: IPaymentMethod[];
  metadata?: Record<string, any>;
}

/**
 * Payment routing result
 */
export interface PaymentRoutingResult {
  recommendedMethod?: IPaymentMethod;
  alternativeMethods: IPaymentMethod[];
  scores: Record<PaymentMethodType, number>;
  reason?: string;
}

/**
 * Payment routing rule interface
 */
export interface IPaymentRoutingRule {
  /**
   * Get the rule name
   */
  getName(): string;

  /**
   * Get the rule weight (0-1)
   */
  getWeight(): number;

  /**
   * Apply the rule to score payment methods
   */
  apply(context: PaymentRoutingContext): Record<PaymentMethodType, number>;
}

/**
 * Payment router
 */
export class PaymentRouter {
  private rules: IPaymentRoutingRule[] = [];

  /**
   * Add a routing rule
   *
   * @param rule Payment routing rule
   * @returns This router for chaining
   */
  public addRule(rule: IPaymentRoutingRule): PaymentRouter {
    this.rules.push(rule);
    return this;
  }

  /**
   * Add multiple routing rules
   *
   * @param rules Array of payment routing rules
   * @returns This router for chaining
   */
  public addRules(rules: IPaymentRoutingRule[]): PaymentRouter {
    this.rules.push(...rules);
    return this;
  }

  /**
   * Find the optimal payment method
   *
   * @param context Payment routing context
   * @returns Payment routing result
   */
  public async findOptimalMethod(context: PaymentRoutingContext): Promise<PaymentRoutingResult> {
    logger.debug('Finding optimal payment method', {
      merchantId: context.merchantId,
      amount: context.amount,
      currency: context.currency,
      country: context.country,
      availableMethods: context.paymentMethods.length,
    });

    // Initialize scores
    const scores: Record<PaymentMethodType, number> = {};

    // Initialize all methods with score 0
    context.paymentMethods.forEach((method: any) => {
      scores[method.getType()] = 0;
    });

    // Apply each rule
    for (const rule of this.rules) {
      const ruleName: any = rule.getName();
      const ruleWeight: any = rule.getWeight();

      logger.debug(`Applying routing rule: ${ruleName}`, { ruleWeight });

      try {
        // Get scores from rule
        const ruleScores: Response = rule.apply(context);

        // Apply weighted scores
        Object.entries(ruleScores).forEach(([methodType, score]) => {
          if (scores[methodType] !== undefined) {
            scores[methodType] += score * ruleWeight;
          }
        });
      } catch (error) {
        logger.error(`Error applying routing rule ${ruleName}:`, error);
      }
    }

    // Sort methods by score
    const sortedMethods: any = [...context.paymentMethods].sort((a, b) => {
      const scoreA: any = scores[a.getType()] || 0;
      const scoreB: any = scores[b.getType()] || 0;
      return scoreB - scoreA;
    });

    // Get recommended and alternative methods
    const recommendedMethod: any = sortedMethods.length > 0 ? sortedMethods[0] : undefined;
    const alternativeMethods: any = sortedMethods.slice(1);

    // Generate reason
    let reason: string | undefined;

    if (recommendedMethod) {
      const topScore: any = scores[recommendedMethod.getType()];
      reason = `${recommendedMethod.getDisplayName()} selected with score ${topScore.toFixed(2)}`;
    } else {
      reason = 'No payment methods available';
    }

    logger.debug('Payment routing result', {
      recommendedMethod: recommendedMethod?.getType(),
      alternativeMethods: alternativeMethods.map((m) => m.getType()),
      scores,
      reason,
    });

    return {
      recommendedMethod,
      alternativeMethods,
      scores,
      reason,
    };
  }
}
