// jscpd:ignore-file
import { body, param } from "express-validator";
import { VersionController } from "../controllers/refactored/version.controller";
import routeProvider from "../core/RouteProvider";
import { VersionController } from "../controllers/refactored/version.controller";

// Create version controller
const versionController: unknown =new VersionController();

// Create a route builder for version routes
const routeBuilder: unknown =routeProvider.createRouteBuilder(
  "version",
  "/api/versions",
  "API version management routes"
)
.tags("version", "management");

// Add routes
routeBuilder
  // Get all versions
  .get(
    "/",
    versionController.getAllVersions
  )
  
  // Get current version
  .get(
    "/current",
    versionController.getCurrentVersion
  )
  
  // Get active versions
  .get(
    "/active",
    versionController.getActiveVersions
  )
  
  // Get deprecated versions
  .get(
    "/deprecated",
    versionController.getDeprecatedVersions
  )
  
  // Get version by name
  .get(
    "/:version",
    versionController.getVersionByName,
    [],
    [param("version").notEmpty()]
  )
  
  // Register a new version
  .post(
    "/",
    versionController.registerVersion,
    ["ADMIN"],
    [
      body("version").notEmpty(),
      body("status").notEmpty(),
      body("releaseDate").notEmpty().isISO8601()
    ]
  )
  
  // Update version status
  .put(
    "/:version/status",
    versionController.updateVersionStatus,
    ["ADMIN"],
    [
      param("version").notEmpty(),
      body("status").notEmpty()
    ]
  )
  
  // Set current version
  .post(
    "/current",
    versionController.setCurrentVersion,
    ["ADMIN"],
    [
      body("version").notEmpty()
    ]
  );

// Build the router
const router: unknown =routeBuilder.build();

export default router;
