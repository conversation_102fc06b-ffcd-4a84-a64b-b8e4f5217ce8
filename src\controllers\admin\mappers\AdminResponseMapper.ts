/**
 * Admin Response Mapper
 *
 * Handles response formatting for admin operations.
 */

import { Request, Response, NextFunction } from 'express';
import {
  SuccessResponse,
  ErrorResponse,
  AdminUserResponse,
  RoleResponse,
  PermissionResponse,
  DashboardDataResponse,
  DashboardStatistics,
  SystemHealthStatus,
} from '../types/AdminControllerTypes';
import { AppError } from '../../../utils/errors/AppError';

/**
 * Response mapper for admin operations
 */
export class AdminResponseMapper {
  /**
   * Send success response
   */
  static sendSuccess<T>(
    res: Response,
    data: T,
    message?: string,
    statusCode: number = 200,
    pagination?: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    }
  ): void {
    const response: SuccessResponse<T> = {
      success: true,
      data,
      message,
      pagination,
      timestamp: new Date(),
      requestId: res.locals.requestId ?? 'unknown',
    };

    res.status(statusCode).json(response);
  }

  /**
   * Send error response
   */
  static sendError(res: Response, error: AppError | Error, statusCode?: number): void {
    let errorResponse: ErrorResponse;

    if (error instanceof AppError) {
      errorResponse = {
        success: false,
        error: {
          message: error.message,
          code: error.code,
          type: error.type,
          details: error.details,
        },
        timestamp: new Date(),
        requestId: res.locals.requestId ?? 'unknown',
      };

      statusCode = statusCode || error.statusCode ?? 400;
    } else {
      errorResponse = {
        success: false,
        error: {
          message: error.message ?? 'Internal server error',
          code: 'INTERNAL_SERVER_ERROR',
          type: 'INTERNAL',
        },
        timestamp: new Date(),
        requestId: res.locals.requestId ?? 'unknown',
      };

      statusCode = statusCode ?? 500;
    }

    res.status(statusCode).json(errorResponse);
  }

  /**
   * Send dashboard data response
   */
  static sendDashboardData(res: Response, data: DashboardDataResponse): void {
    this.sendSuccess(res, data, 'Dashboard data retrieved successfully');
  }

  /**
   * Send dashboard statistics response
   */
  static sendDashboardStatistics(res: Response, stats: DashboardStatistics): void {
    this.sendSuccess(res, stats, 'Dashboard statistics retrieved successfully');
  }

  /**
   * Send admin users list response
   */
  static sendAdminUsersList(
    res: Response,
    users: AdminUserResponse[],
    total: number,
    page: number = 1,
    limit: number = 10
  ): void {
    const totalPages = Math.ceil(total / limit);

    this.sendSuccess(res, users, `Retrieved ${users.length} admin users`, 200, {
      page,
      limit,
      total,
      totalPages,
    });
  }

  /**
   * Send single admin user response
   */
  static sendAdminUser(res: Response, user: AdminUserResponse, message?: string): void {
    this.sendSuccess(res, user, message ?? 'Admin user retrieved successfully');
  }

  /**
   * Send admin user created response
   */
  static sendAdminUserCreated(res: Response, user: AdminUserResponse): void {
    this.sendSuccess(res, user, 'Admin user created successfully', 201);
  }

  /**
   * Send admin user updated response
   */
  static sendAdminUserUpdated(res: Response, user: AdminUserResponse): void {
    this.sendSuccess(res, user, 'Admin user updated successfully');
  }

  /**
   * Send admin user deleted response
   */
  static sendAdminUserDeleted(res: Response): void {
    this.sendSuccess(res, null, 'Admin user deleted successfully');
  }

  /**
   * Send roles list response
   */
  static sendRolesList(
    res: Response,
    roles: RoleResponse[],
    total: number,
    page: number = 1,
    limit: number = 10
  ): void {
    const totalPages = Math.ceil(total / limit);

    this.sendSuccess(res, roles, `Retrieved ${roles.length} roles`, 200, {
      page,
      limit,
      total,
      totalPages,
    });
  }

  /**
   * Send single role response
   */
  static sendRole(res: Response, role: RoleResponse, message?: string): void {
    this.sendSuccess(res, role, message ?? 'Role retrieved successfully');
  }

  /**
   * Send role created response
   */
  static sendRoleCreated(res: Response, role: RoleResponse): void {
    this.sendSuccess(res, role, 'Role created successfully', 201);
  }

  /**
   * Send role updated response
   */
  static sendRoleUpdated(res: Response, role: RoleResponse): void {
    this.sendSuccess(res, role, 'Role updated successfully');
  }

  /**
   * Send role deleted response
   */
  static sendRoleDeleted(res: Response): void {
    this.sendSuccess(res, null, 'Role deleted successfully');
  }

  /**
   * Send permissions list response
   */
  static sendPermissionsList(
    res: Response,
    permissions: PermissionResponse[],
    total: number,
    page: number = 1,
    limit: number = 10
  ): void {
    const totalPages = Math.ceil(total / limit);

    this.sendSuccess(res, permissions, `Retrieved ${permissions.length} permissions`, 200, {
      page,
      limit,
      total,
      totalPages,
    });
  }

  /**
   * Send single permission response
   */
  static sendPermission(res: Response, permission: PermissionResponse, message?: string): void {
    this.sendSuccess(res, permission, message ?? 'Permission retrieved successfully');
  }

  /**
   * Send permission created response
   */
  static sendPermissionCreated(res: Response, permission: PermissionResponse): void {
    this.sendSuccess(res, permission, 'Permission created successfully', 201);
  }

  /**
   * Send permission updated response
   */
  static sendPermissionUpdated(res: Response, permission: PermissionResponse): void {
    this.sendSuccess(res, permission, 'Permission updated successfully');
  }

  /**
   * Send permission deleted response
   */
  static sendPermissionDeleted(res: Response): void {
    this.sendSuccess(res, null, 'Permission deleted successfully');
  }

  /**
   * Send system health response
   */
  static sendSystemHealth(res: Response, health: SystemHealthStatus): void {
    const statusCode = health.status === 'healthy' ? 200 : health.status === 'degraded' ? 200 : 503;

    this.sendSuccess(res, health, `System is ${health.status}`, statusCode);
  }

  /**
   * Send validation error response
   */
  static sendValidationError(
    res: Response,
    errors: unknown[],
    message: string = 'Validation failed'
  ): void {
    const error = new AppError({
      message,
      type: 'VALIDATION' as unknown,
      code: 'INVALID_INPUT' as unknown,
      details: { errors },
    });

    this.sendError(res, error, 400);
  }

  /**
   * Send authorization error response
   */
  static sendAuthorizationError(
    res: Response,
    message: string = 'Access denied',
    requiredRole?: string
  ): void {
    const error = new AppError({
      message,
      type: 'AUTHENTICATION' as unknown,
      code: 'INVALID_CREDENTIALS' as unknown,
      details: { requiredRole },
    });

    this.sendError(res, error, 403);
  }

  /**
   * Send not found error response
   */
  static sendNotFoundError(res: Response, resource: string = 'Resource'): void {
    const error = new AppError({
      message: `${resource} not found`,
      type: 'NOT_FOUND' as unknown,
      code: 'RESOURCE_NOT_FOUND' as unknown,
    });

    this.sendError(res, error, 404);
  }

  /**
   * Send internal server error response
   */
  static sendInternalServerError(res: Response, message: string = 'Internal server error'): void {
    const error = new AppError({
      message,
      type: 'INTERNAL' as unknown,
      code: 'INTERNAL_SERVER_ERROR' as unknown,
    });

    this.sendError(res, error, 500);
  }

  /**
   * Handle async controller method
   */
  static asyncHandler(fn: Function) {
    return (req: Request, res: Response, next: Function) => {
      Promise.resolve(fn(req, res, next)).catch((error) => next(error));
    };
  }

  /**
   * Set response headers for API
   */
  static setApiHeaders(res: Response): void {
    res.setHeader('Content-Type', 'application/json');
    res.setHeader('X-API-Version', '1.0');
    res.setHeader('X-Response-Time', Date.now());
  }
}
