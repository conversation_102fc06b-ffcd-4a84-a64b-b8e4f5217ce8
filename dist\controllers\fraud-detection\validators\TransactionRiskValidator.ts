/**
 * Transaction Risk Validator
 * 
 * Focused validator for transaction risk assessment operations.
 */

import { AppError, ErrorType, ErrorCode } from '../../../utils/errors/AppError';
import { AssessTransactionRiskRequest, ValidationError } from '../types/FraudDetectionControllerTypes';
import { BaseValidator } from './BaseValidator';

/**
 * Validator for transaction risk assessment
 */
export class TransactionRiskValidator extends BaseValidator {
  
  /**
   * Validate transaction risk assessment request
   */
  validateAssessTransactionRisk(data: any): AssessTransactionRiskRequest {
    const errors: ValidationError[] = [];

    if (!data.transactionId) {
      errors.push({ field: 'transactionId', message: 'Transaction ID is required' });
    } else if (typeof data.transactionId !== 'string' || !this.isValidUUID(data.transactionId)) {
      errors.push({ field: 'transactionId', message: 'Invalid transaction ID format', value: data.transactionId });
    }

    if (!data.ipAddress) {
      errors.push({ field: 'ipAddress', message: 'IP address is required' });
    } else if (typeof data.ipAddress !== 'string' || !this.isValidIPAddress(data.ipAddress)) {
      errors.push({ field: 'ipAddress', message: 'Invalid IP address format', value: data.ipAddress });
    }

    if (data.userAgent !== undefined && typeof data.userAgent !== 'string') {
      errors.push({ field: 'userAgent', message: 'User agent must be a string', value: data.userAgent });
    }

    if (data.deviceId !== undefined && typeof data.deviceId !== 'string') {
      errors.push({ field: 'deviceId', message: 'Device ID must be a string', value: data.deviceId });
    }

    if (errors.length > 0) {
      throw new AppError({
        message: 'Validation failed',
        type: ErrorType.VALIDATION,
        code: ErrorCode.INVALID_INPUT,
        details: { errors }
      });
    }

    return {
      transactionId: data.transactionId,
      ipAddress: data.ipAddress,
      userAgent: data.userAgent ?? 'Unknown',
      deviceId: data.deviceId ?? 'Unknown'
    };
  }

  /**
   * Validate transaction ID parameter
   */
  validateTransactionId(transactionId: any): string {
    if (!transactionId) {
      throw new AppError({
        message: 'Transaction ID is required',
        type: ErrorType.VALIDATION,
        code: ErrorCode.MISSING_REQUIRED_FIELD
      });
    }

    if (!this.isValidUUID(transactionId)) {
      throw new AppError({
        message: 'Transaction ID must be a valid UUID',
        type: ErrorType.VALIDATION,
        code: ErrorCode.INVALID_INPUT
      });
    }

    return transactionId;
  }
}
