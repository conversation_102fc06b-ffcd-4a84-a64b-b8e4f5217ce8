// jscpd:ignore-file

import { Request, Response, NextFunction } from 'express';
import systemService from '../services/system.service';

// Extend the Express Request type to include user property
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role: string;
        merchantId?: string;
      };
    }
  }
}

class SystemController {
  async getAllSettings(req: Request, res: Response) {
    try {
      const settings: Record<string, any> = await systemService.getAllSettings();

      return res.status(200).json({
        status: 'success',
        data: settings,
      });
    } catch (error) {
      return res.status(500).json({
        status: 'error',
        message: (error as Error).message || 'Failed to retrieve system settings',
      });
    }
  }

  async getSettingByKey(req: Request, res: Response) {
    try {
      const { key } = req.params;
      const setting: any = await systemService.getSettingByKey(key);

      if (!setting) {
        return res.status(404).json({
          status: 'error',
          message: 'Setting not found',
        });
      }

      return res.status(200).json({
        status: 'success',
        data: setting,
      });
    } catch (error) {
      return res.status(500).json({
        status: 'error',
        message: (error as Error).message || 'Failed to retrieve setting',
      });
    }
  }

  async updateSetting(req: Request, res: Response) {
    try {
      const { key } = req.params;
      const { value } = req.body;

      if (!value) {
        return res.status(400).json({
          status: 'error',
          message: 'Value is required',
        });
      }

      // Ensure user is authenticated
      if (!req.user) {
        return res.status(401).json({
          status: 'error',
          message: 'Authentication required',
        });
      }

      const updatedSetting = await systemService.updateSetting(key, value, req.user.id); // Fixed: using id instead of userId

      if (!updatedSetting) {
        return res.status(404).json({
          status: 'error',
          message: 'Setting not found',
        });
      }

      return res.status(200).json({
        status: 'success',
        data: updatedSetting,
      });
    } catch (error) {
      return res.status(500).json({
        status: 'error',
        message: (error as Error).message || 'Failed to update setting',
      });
    }
  }

  async createSetting(req: Request, res: Response) {
    try {
      const { key, value, description } = req.body;

      // Basic validation
      if (!key || !value) {
        return res.status(400).json({
          status: 'error',
          message: 'Key and value are required',
        });
      }

      // Ensure user is authenticated
      if (!req.user) {
        return res.status(401).json({
          status: 'error',
          message: 'Authentication required',
        });
      }

      // Check if setting already exists
      const existingSetting = await systemService.getSettingByKey(key);

      if (existingSetting) {
        return res.status(400).json({
          status: 'error',
          message: 'Setting with this key already exists',
        });
      }

      const newSetting = await systemService.createSetting(
        key,
        value,
        description || '',
        req.user.id // Fixed: using id instead of userId
      );

      return res.status(201).json({
        status: 'success',
        data: newSetting,
      });
    } catch (error) {
      return res.status(500).json({
        status: 'error',
        message: (error as Error).message || 'Failed to create setting',
      });
    }
  }
}

export default new SystemController();
