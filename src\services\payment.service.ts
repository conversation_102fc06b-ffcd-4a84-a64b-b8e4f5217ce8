// jscpd:ignore-file

import { Transaction } from '../types';
import { format, subDays, subMonths, parseISO } from 'date-fns';
import prisma from '../lib/prisma';

class PaymentService {
  async getAllPayments(): Promise<Transaction[]> {
    return await prisma.transaction.findMany();
  }

  async getPaymentById(id: string): Promise<Transaction | undefined> {
    const transaction = await prisma.transaction.findUnique({
      where: { id },
    });
    return transaction || undefined;
  }

  async getPaymentsByMerchantId(merchantId: string): Promise<Transaction[]> {
    return await prisma.transaction.findMany({
      where: { merchantId },
    });
  }

  async createPayment(paymentData: Omit<Transaction, 'id'>): Promise<Transaction> {
    return await prisma.transaction.create({
      data: paymentData,
    });
  }

  async updatePayment(
    id: string,
    paymentData: Partial<Transaction>
  ): Promise<Transaction | undefined> {
    try {
      return await prisma.transaction.update({
        where: { id },
        data: paymentData,
      });
    } catch (error) {
      return undefined;
    }
  }

  async getMerchantAnalytics(merchantId: string, dateRange: string = 'month') {
    // Get merchant transactions
    const merchantTransactions = await this.getPaymentsByMerchantId(merchantId);

    if (!merchantTransactions.length) {
      return {
        metrics: {
          totalRevenue: 0,
          transactionCount: 0,
          successfulTransactions: 0,
          avgTransactionValue: 0,
          successRate: 0,
          currentMonthRevenue: 0,
          previousMonthRevenue: 0,
          revenueGrowth: 0,
        },
        revenueData: [],
        methodDistributionData: [],
        statusData: [],
        hourlyData: [],
      };
    }

    // Filter by date range
    const now: Date = new Date();
    let filterDate: Date = new Date();

    switch (dateRange) {
      case 'today':
        filterDate.setHours(0, 0, 0, 0);
        break;
      case 'week':
        filterDate = subDays(now, 7);
        break;
      case 'month':
        filterDate = subMonths(now, 1);
        break;
      case 'year':
        filterDate.setFullYear(now.getFullYear() - 1);
        break;
      case 'all':
        filterDate = new Date(0); // Beginning of time
        break;
      default:
        filterDate = subMonths(now, 1); // Default to month
    }

    const filteredTransactions: unknown = merchantTransactions.filter(
      (t) => new Date(t.date) >= filterDate
    );

    // Calculate metrics
    const totalTransactions: unknown = filteredTransactions.length;
    const successfulTransactions: unknown = filteredTransactions.filter(
      (t) => t.status === 'success'
    ).length;
    const totalRevenue: unknown = filteredTransactions.reduce(
      (sum, txn) => sum + (txn.status === 'success' ? txn.amount : 0),
      0
    );
    const avgTransactionValue: unknown =
      successfulTransactions > 0 ? totalRevenue / successfulTransactions : 0;
    const successRate =
      totalTransactions > 0 ? (successfulTransactions / totalTransactions) * 100 : 0;

    // Calculate current month vs previous month revenue
    const currentMonthStart: Date = new Date(now.getFullYear(), now.getMonth(), 1);
    const previousMonthStart: Date = new Date(now.getFullYear(), now.getMonth() - 1, 1);

    const currentMonthTransactions: unknown = merchantTransactions.filter(
      (t) => new Date(t.date) >= currentMonthStart && t.status === 'success'
    );
    const previousMonthTransactions: unknown = merchantTransactions.filter(
      (t) =>
        new Date(t.date) >= previousMonthStart &&
        new Date(t.date) < currentMonthStart &&
        t.status === 'success'
    );

    const currentMonthRevenue: unknown = currentMonthTransactions.reduce(
      (sum, txn) => sum + txn.amount,
      0
    );
    const previousMonthRevenue: unknown = previousMonthTransactions.reduce(
      (sum, txn) => sum + txn.amount,
      0
    );

    const revenueGrowth: unknown =
      previousMonthRevenue > 0
        ? ((currentMonthRevenue - previousMonthRevenue) / previousMonthRevenue) * 100
        : currentMonthRevenue > 0
        ? 100
        : 0;

    return {
      metrics: {
        totalRevenue,
        transactionCount: totalTransactions,
        successfulTransactions,
        avgTransactionValue,
        successRate,
        currentMonthRevenue,
        previousMonthRevenue,
        revenueGrowth,
      },
      // Additional data would be calculated here for charts
      revenueData: [],
      methodDistributionData: [],
      statusData: [],
      hourlyData: [],
    };
  }
}

export default new PaymentService();
