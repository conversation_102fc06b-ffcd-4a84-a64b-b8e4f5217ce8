import { Request, Response } from "express";
declare global {
    namespace Express {
        interface Request {
            user?: {
                id: string;
                role: string;
                merchantId?: string;
            };
        }
    }
}
declare class SystemController {
    getAllSettings(req: Request, res: Response): Promise<Response<any, Record<string, any>>>;
    getSettingByKey(req: Request, res: Response): Promise<Response<any, Record<string, any>>>;
    updateSetting(req: Request, res: Response): Promise<Response<any, Record<string, any>>>;
    createSetting(req: Request, res: Response): Promise<Response<any, Record<string, any>>>;
}
declare const _default: SystemController;
export default _default;
//# sourceMappingURL=system.controller.d.ts.map