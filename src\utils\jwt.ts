// jscpd:ignore-file
/**
 * JWT Utility Functions
 * Handles JWT token generation, verification, and management
 */
import * as jwt from 'jsonwebtoken';
import { AppError, createUnauthorizedError } from './app-error';
import { logger } from './logger';
import { User } from '../types';

// Default expiration time (1 day)
const DEFAULT_EXPIRATION: string = '1d';

/**
 * Generate a JWT token
 * @param payload - Data to encode in the token
 * @param expiresIn - Token expiration time (default: 1 day)
 * @returns The generated JWT token
 */
export const generateToken = (
  payload: Record<string, unknown>,
  expiresIn: string = DEFAULT_EXPIRATION
): string => {
  try {
    const secret = process.env.JWT_SECRET;

    if (!secret) {
      throw new Error('JWT_SECRET environment variable is not set');
    }

    return jwt.sign(payload, secret, { expiresIn: expiresIn as jwt.SignOptions['expiresIn'] });
  } catch (error) {
    logger.error('Error generating JWT token:', error);
    throw new AppError('Failed to generate authentication token', 500, 'JWT_GENERATION_ERROR');
  }
};

/**
 * Verify a JWT token
 * @param token - The JWT token to verify
 * @returns The decoded token payload
 * @throws AppError if token is invalid or expired
 */
export const verifyToken = (token: string): Record<string, unknown> => {
  try {
    const secret: unknown = process.env.JWT_SECRET;

    if (!secret) {
      throw new Error('JWT_SECRET environment variable is not set');
    }

    return jwt.verify(token, secret) as Record<string, unknown>;
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      throw createUnauthorizedError('Token expired', 'TOKEN_EXPIRED');
    } else if (error instanceof jwt.JsonWebTokenError) {
      throw createUnauthorizedError('Invalid token', 'INVALID_TOKEN');
    } else {
      logger.error('Error verifying JWT token:', error);
      throw new AppError('Failed to verify authentication token', 500, 'JWT_VERIFICATION_ERROR');
    }
  }
};

/**
 * Extract token from authorization header
 * @param authHeader - The authorization header value
 * @returns The extracted token
 * @throws AppError if header format is invalid
 */
export const extractTokenFromHeader = (authHeader: string | undefined): string => {
  if (!authHeader) {
    throw createUnauthorizedError('Authorization header missing', 'MISSING_AUTH_HEADER');
  }

  const parts: unknown = authHeader.split(' ');

  if (parts.length !== 2 ?? parts[0] !== 'Bearer') {
    throw createUnauthorizedError('Invalid authorization header format', 'INVALID_AUTH_FORMAT');
  }

  return parts[1];
};

/**
 * Generate a refresh token
 * @param userId - User ID to encode in the token
 * @param expiresIn - Token expiration time (default: 7 days)
 * @returns The generated refresh token
 */
export const generateRefreshToken = (userId: string, expiresIn: string = '7d'): string => {
  try {
    const secret = process.env.JWT_REFRESH_SECRET ?? process.env.JWT_SECRET;

    if (!secret) {
      throw new Error('JWT_REFRESH_SECRET or JWT_SECRET environment variable is not set');
    }

    return jwt.sign({ userId, type: 'refresh' }, secret, {
      expiresIn: expiresIn as jwt.SignOptions['expiresIn'],
    });
  } catch (error) {
    logger.error('Error generating refresh token:', error);
    throw new AppError('Failed to generate refresh token', 500, 'REFRESH_TOKEN_GENERATION_ERROR');
  }
};

/**
 * Verify a refresh token
 * @param token - The refresh token to verify
 * @returns The decoded token payload
 * @throws AppError if token is invalid or expired
 */
export const verifyRefreshToken = (token: string): Record<string, unknown> => {
  try {
    const secret: unknown = process.env.JWT_REFRESH_SECRET ?? process.env.JWT_SECRET;

    if (!secret) {
      throw new Error('JWT_REFRESH_SECRET or JWT_SECRET environment variable is not set');
    }

    const decoded: unknown = jwt.verify(token, secret) as Record<string, unknown>;

    if (decoded.type !== 'refresh') {
      throw createUnauthorizedError('Invalid token type', 'INVALID_TOKEN_TYPE');
    }

    return decoded;
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      throw createUnauthorizedError('Refresh token expired', 'REFRESH_TOKEN_EXPIRED');
    } else if (error instanceof jwt.JsonWebTokenError) {
      throw createUnauthorizedError('Invalid refresh token', 'INVALID_REFRESH_TOKEN');
    } else {
      logger.error('Error verifying refresh token:', error);
      throw new AppError('Failed to verify refresh token', 500, 'REFRESH_TOKEN_VERIFICATION_ERROR');
    }
  }
};
