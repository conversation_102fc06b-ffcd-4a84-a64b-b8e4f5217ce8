import { BaseController } from "./base.controller";
declare global {
    namespace Express {
        interface Request {
            user?: {
                id: string;
                role: string;
                merchantId?: string;
            };
        }
    }
}
/**
 * AlertController
 * Controller for handling alert operations
 */
export declare class AlertController extends BaseController {
    constructor();
    /**
     * Get alerts with filtering and pagination
     */
    getAlerts: any;
    /**
     * Get a single alert by ID
     */
    getAlert: any;
    /**
     * Update the status of an alert
     */
    updateAlertStatus: any;
    /**
     * Create a test alert for testing purposes
     */
    createTestAlert: any;
    /**
     * Get the count of alerts with filtering
     */
    getAlertCount: any;
    /**
     * Helper method to check admin role
     */
    private checkAdminRole;
    /**
     * Helper method to check authorization
     */
    private checkAuthorization;
}
declare const _default: AlertController;
export default _default;
//# sourceMappingURL=alert.controller.d.ts.map