// jscpd:ignore-file
import { BaseService, ServiceError } from './base.service';
import { Transaction, PaymentMethod, Merchant } from '@prisma/client';
import geoip from 'geoip-lite';
import { ApiErrorCode } from '../middlewares/apiResponseMiddleware';
import { User } from '../types';

/**
 * Risk level enum
 */
export enum RiskLevel {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL',
}

/**
 * Risk factor enum
 */
export enum RiskFactor {
  AMOUNT = 'AMOUNT',
  LOCATION = 'LOCATION',
  FREQUENCY = 'FREQUENCY',
  TIME = 'TIME',
  IP = 'IP',
  DEVICE = 'DEVICE',
  PAYMENT_METHOD = 'PAYMENT_METHOD',
  BEHAVIOR = 'BEHAVIOR',
  HISTORY = 'HISTORY',
}

/**
 * Risk score interface
 */
export interface RiskScore {
  /**
   * Overall risk score (0-100)
   */
  score: number;

  /**
   * Risk level
   */
  level: RiskLevel;

  /**
   * Risk factors with their individual scores
   */
  factors: { factor: RiskFactor; score: number; reason: string }[];

  /**
   * Timestamp of the risk assessment
   */
  timestamp: Date;
}

/**
 * Transaction risk assessment interface
 */
export interface TransactionRiskAssessment {
  /**
   * Transaction ID
   */
  transactionId: string;

  /**
   * Risk score
   */
  riskScore: RiskScore;

  /**
   * Whether the transaction is flagged as potentially fraudulent
   */
  isFlagged: boolean;

  /**
   * Whether the transaction is blocked
   */
  isBlocked: boolean;

  /**
   * Reason for flagging or blocking
   */
  reason?: string;

  /**
   * Recommended action
   */
  recommendedAction?: string;
}

/**
 * Fraud detection configuration
 */
export interface FraudDetectionConfig {
  /**
   * Threshold for flagging transactions (0-100)
   */
  flagThreshold: number;

  /**
   * Threshold for blocking transactions (0-100)
   */
  blockThreshold: number;

  /**
   * Whether to automatically block high-risk transactions
   */
  autoBlock: boolean;

  /**
   * Factor weights (0-1)
   */
  factorWeights: {
    [key in RiskFactor]?: number;
  };

  /**
   * High-risk countries
   */
  highRiskCountries: string[];

  /**
   * High-risk IP ranges
   */
  highRiskIpRanges: string[];

  /**
   * Maximum transaction amount before flagging
   */
  maxTransactionAmount: number;

  /**
   * Maximum transactions per hour before flagging
   */
  maxTransactionsPerHour: number;

  /**
   * Maximum transactions per day before flagging
   */
  maxTransactionsPerDay: number;
}

/**
 * Fraud detection service
 */
export class FraudDetectionService extends BaseService {
  constructor() {
    super(null);
    // Initialize service
  }
  /**
   * Default configuration
   */
  private defaultConfig: FraudDetectionConfig = {
    flagThreshold: 70,
    blockThreshold: 85,
    autoBlock: true,
    factorWeights: {
      [RiskFactor.AMOUNT]: 0.2,
      [RiskFactor.LOCATION]: 0.15,
      [RiskFactor.FREQUENCY]: 0.15,
      [RiskFactor.TIME]: 0.1,
      [RiskFactor.IP]: 0.15,
      [RiskFactor.DEVICE]: 0.05,
      [RiskFactor.PAYMENT_METHOD]: 0.05,
      [RiskFactor.BEHAVIOR]: 0.1,
      [RiskFactor.HISTORY]: 0.05,
    },
    highRiskCountries: ['RU', 'CN', 'NG', 'UA', 'BY', 'KP', 'IR', 'IQ', 'SY', 'VE'],
    highRiskIpRanges: [
      // Example IP ranges
      '************/24',
      '***********/24',
    ],
    maxTransactionAmount: 10000,
    maxTransactionsPerHour: 10,
    maxTransactionsPerDay: 30,
  };

  /**
   * Assess transaction risk
   * @param transaction Transaction to assess
   * @param ipAddress IP address of the user
   * @param userAgent User agent of the user
   * @param deviceId Device ID of the user
   * @param merchant Merchant associated with the transaction
   * @returns Transaction risk assessment
   */
  async assessTransactionRisk(
    transaction: Transaction,
    ipAddress: string,
    userAgent: string,
    deviceId: string,
    merchant: Merchant
  ): Promise<TransactionRiskAssessment> {
    try {
      // Get merchant-specific configuration or use default
      const config: Record<string, any> =
        (await this.getMerchantFraudConfig(merchant.id)) || this.defaultConfig;

      // Calculate risk factors
      const factors: unknown = await this.calculateRiskFactors(
        transaction,
        ipAddress,
        userAgent,
        deviceId,
        merchant,
        config
      );

      // Calculate overall risk score
      const score: unknown = this.calculateOverallRiskScore(factors, config);

      // Determine risk level
      const level: unknown = this.determineRiskLevel(score);

      // Create risk score object
      const riskScore: RiskScore = {
        score,
        level,
        factors,
        timestamp: new Date(),
      };

      // Determine if transaction should be flagged or blocked
      const isFlagged: unknown = score >= config.flagThreshold;
      const isBlocked: unknown = config.autoBlock && score >= config.blockThreshold;

      // Generate reason and recommended action
      const reason: unknown = this.generateReason(factors, level);
      const recommendedAction: unknown = this.generateRecommendedAction(level, isBlocked);

      // Save risk assessment to database
      await this.saveRiskAssessment(transaction.id, riskScore, isFlagged, isBlocked);

      return {
        transactionId: transaction.id,
        riskScore,
        isFlagged,
        isBlocked,
        reason,
        recommendedAction,
      };
    } catch (error) {
      console.error('Error assessing transaction risk:', error);
      throw this.genericError('Failed to assess transaction risk', 500, ApiErrorCode.SERVER_ERROR);
    }
  }

  /**
   * Calculate risk factors for a transaction
   * @param transaction Transaction to assess
   * @param ipAddress IP address of the user
   * @param userAgent User agent of the user
   * @param deviceId Device ID of the user
   * @param merchant Merchant associated with the transaction
   * @param config Fraud detection configuration
   * @returns Risk factors with their individual scores
   */
  private async calculateRiskFactors(
    transaction: Transaction,
    ipAddress: string,
    userAgent: string,
    deviceId: string,
    merchant: Merchant,
    config: FraudDetectionConfig
  ): Promise<{ factor: RiskFactor; score: number; reason: string }[]> {
    const factors: { factor: RiskFactor; score: number; reason: string }[] = [];

    // Amount factor
    const amountScore: unknown = this.calculateAmountRiskScore(transaction.amount, config);
    factors.push({
      factor: RiskFactor.AMOUNT,
      score: amountScore,
      reason: amountScore > 50 ? 'High transaction amount' : 'Normal transaction amount',
    });

    // Location factor
    const locationScore: unknown = this.calculateLocationRiskScore(ipAddress, config);
    factors.push({
      factor: RiskFactor.LOCATION,
      score: locationScore,
      reason: locationScore > 50 ? 'High-risk location' : 'Normal location',
    });

    // Frequency factor
    const frequencyScore: unknown = await this.calculateFrequencyRiskScore(
      transaction,
      merchant.id,
      config
    );
    factors.push({
      factor: RiskFactor.FREQUENCY,
      score: frequencyScore,
      reason:
        frequencyScore > 50 ? 'Unusual transaction frequency' : 'Normal transaction frequency',
    });

    // Time factor
    const timeScore: unknown = this.calculateTimeRiskScore(transaction.createdAt);
    factors.push({
      factor: RiskFactor.TIME,
      score: timeScore,
      reason: timeScore > 50 ? 'Unusual transaction time' : 'Normal transaction time',
    });

    // IP factor
    const ipScore: unknown = this.calculateIpRiskScore(ipAddress, config);
    factors.push({
      factor: RiskFactor.IP,
      score: ipScore,
      reason: ipScore > 50 ? 'High-risk IP address' : 'Normal IP address',
    });

    // Device factor
    const deviceScore: unknown = await this.calculateDeviceRiskScore(
      deviceId,
      userAgent,
      merchant.id
    );
    factors.push({
      factor: RiskFactor.DEVICE,
      score: deviceScore,
      reason: deviceScore > 50 ? 'Unusual device' : 'Known device',
    });

    // Payment method factor
    const paymentMethodScore: unknown = await this.calculatePaymentMethodRiskScore(
      transaction.paymentMethodId
    );
    factors.push({
      factor: RiskFactor.PAYMENT_METHOD,
      score: paymentMethodScore,
      reason: paymentMethodScore > 50 ? 'High-risk payment method' : 'Normal payment method',
    });

    // Behavior factor
    const behaviorScore: unknown = await this.calculateBehaviorRiskScore(
      transaction,
      ipAddress,
      deviceId,
      merchant.id
    );
    factors.push({
      factor: RiskFactor.BEHAVIOR,
      score: behaviorScore,
      reason: behaviorScore > 50 ? 'Unusual behavior pattern' : 'Normal behavior pattern',
    });

    // History factor
    const historyScore: unknown = await this.calculateHistoryRiskScore(
      transaction.customerEmail,
      merchant.id
    );
    factors.push({
      factor: RiskFactor.HISTORY,
      score: historyScore,
      reason: historyScore > 50 ? 'Negative transaction history' : 'Good transaction history',
    });

    return factors;
  }

  /**
   * Calculate overall risk score
   * @param factors Risk factors with their individual scores
   * @param config Fraud detection configuration
   * @returns Overall risk score (0-100)
   */
  private calculateOverallRiskScore(
    factors: { factor: RiskFactor; score: number; reason: string }[],
    config: FraudDetectionConfig
  ): number {
    let totalScore = 0;
    let totalWeight: number = 0;

    factors.forEach(({ factor, score }) => {
      const weight = config.factorWeights[factor] ?? 0;
      totalScore += score * weight;
      totalWeight += weight;
    });

    return totalWeight > 0 ? Math.round(totalScore / totalWeight) : 0;
  }

  /**
   * Determine risk level based on score
   * @param score Risk score
   * @returns Risk level
   */
  private determineRiskLevel(score: number): RiskLevel {
    if (score >= 85) {
      return RiskLevel.CRITICAL;
    } else if (score >= 70) {
      return RiskLevel.HIGH;
    } else if (score >= 40) {
      return RiskLevel.MEDIUM;
    } else {
      return RiskLevel.LOW;
    }
  }

  /**
   * Generate reason for risk assessment
   * @param factors Risk factors
   * @param level Risk level
   * @returns Reason for risk assessment
   */
  private generateReason(
    factors: { factor: RiskFactor; score: number; reason: string }[],
    level: RiskLevel
  ): string {
    if (level === RiskLevel.LOW) {
      return 'Low risk transaction';
    }

    // Get high-risk factors
    const highRiskFactors: unknown = factors
      .filter((f) => f.score >= 70)
      .sort((a, b) => b.score - a.score);

    if (highRiskFactors.length === 0) {
      return `${level.toLowerCase()} risk due to combination of factors`;
    }

    const topFactors: unknown = highRiskFactors.slice(0, 3);
    return `${level.toLowerCase()} risk due to: ${topFactors.map((f) => f.reason).join(', ')}`;
  }

  /**
   * Generate recommended action based on risk level
   * @param level Risk level
   * @param isBlocked Whether the transaction is blocked
   * @returns Recommended action
   */
  private generateRecommendedAction(level: RiskLevel, isBlocked: boolean): string {
    if (isBlocked) {
      return 'Block transaction and notify merchant';
    }

    switch (level) {
      case RiskLevel.CRITICAL:
        return 'Manual review required before processing';
      case RiskLevel.HIGH:
        return 'Additional verification recommended';
      case RiskLevel.MEDIUM:
        return 'Monitor transaction';
      case RiskLevel.LOW:
        return 'Process normally';
      default:
        return 'Process normally';
    }
  }

  /**
   * Save risk assessment to database
   * @param transactionId Transaction ID
   * @param riskScore Risk score
   * @param isFlagged Whether the transaction is flagged
   * @param isBlocked Whether the transaction is blocked
   */
  private async saveRiskAssessment(
    transactionId: string,
    riskScore: RiskScore,
    isFlagged: boolean,
    isBlocked: boolean
  ): Promise<void> {
    try {
      await this.prisma.riskAssessment.create({
        data: {
          transactionId,
          score: riskScore.score,
          level: riskScore.level,
          factors: JSON.stringify(riskScore.factors),
          isFlagged,
          isBlocked,
          createdAt: new Date(),
        },
      });
    } catch (error) {
      console.error('Error saving risk assessment:', error);
      // Don't throw error, just log it
    }
  }

  /**
   * Get merchant-specific fraud detection configuration
   * @param merchantId Merchant ID
   * @returns Fraud detection configuration
   */
  private async getMerchantFraudConfig(merchantId: number): Promise<FraudDetectionConfig | null> {
    try {
      const config: Record<string, any> = await this.prisma.fraudDetectionConfig.findUnique({
        where: { merchantId },
      });

      if (!config) {
        return null;
      }

      return {
        flagThreshold: config.flagThreshold,
        blockThreshold: config.blockThreshold,
        autoBlock: config.autoBlock,
        factorWeights: JSON.parse(config.factorWeights),
        highRiskCountries: config.highRiskCountries.split(','),
        highRiskIpRanges: config.highRiskIpRanges.split(','),
        maxTransactionAmount: config.maxTransactionAmount,
        maxTransactionsPerHour: config.maxTransactionsPerHour,
        maxTransactionsPerDay: config.maxTransactionsPerDay,
      };
    } catch (error) {
      console.error('Error getting merchant fraud config:', error);
      return null;
    }
  }

  // Risk factor calculation methods

  /**
   * Calculate amount risk score
   * @param amount Transaction amount
   * @param config Fraud detection configuration
   * @returns Risk score (0-100)
   */
  private calculateAmountRiskScore(amount: string, config: FraudDetectionConfig): number {
    const numAmount = parseFloat(amount);
    const maxAmount: unknown = config.maxTransactionAmount;

    if (numAmount >= maxAmount) {
      return 100;
    } else if (numAmount >= maxAmount * 0.8) {
      return 80;
    } else if (numAmount >= maxAmount * 0.5) {
      return 50;
    } else if (numAmount >= maxAmount * 0.3) {
      return 30;
    } else {
      return 10;
    }
  }

  /**
   * Calculate location risk score
   * @param ipAddress IP address
   * @param config Fraud detection configuration
   * @returns Risk score (0-100)
   */
  private calculateLocationRiskScore(ipAddress: string, config: FraudDetectionConfig): number {
    try {
      const geo: unknown = geoip.lookup(ipAddress);

      if (!geo) {
        return 50; // Unknown location
      }

      const country: unknown = geo.country;

      if (config.highRiskCountries.includes(country)) {
        return 90; // High-risk country
      }

      // Add more sophisticated location risk logic here

      return 20; // Normal location
    } catch (error) {
      console.error('Error calculating location risk score:', error);
      return 50; // Default to medium risk on error
    }
  }

  /**
   * Calculate frequency risk score
   * @param transaction Transaction
   * @param merchantId Merchant ID
   * @param config Fraud detection configuration
   * @returns Risk score (0-100)
   */
  private async calculateFrequencyRiskScore(
    transaction: Transaction,
    merchantId: number,
    config: FraudDetectionConfig
  ): Promise<number> {
    try {
      // Check transactions in the last hour
      const hourAgo: Date = new Date(Date.now() - 60 * 60 * 1000);
      const transactionsLastHour: unknown = await this.prisma.transaction.count({
        where: {
          merchantId,
          customerEmail: transaction.customerEmail,
          createdAt: { gte: hourAgo },
        },
      });

      // Check transactions in the last day
      const dayAgo: Date = new Date(Date.now() - 24 * 60 * 60 * 1000);
      const transactionsLastDay: unknown = await this.prisma.transaction.count({
        where: {
          merchantId,
          customerEmail: transaction.customerEmail,
          createdAt: { gte: dayAgo },
        },
      });

      let score: number = 0;

      // Score based on hourly transactions
      if (transactionsLastHour >= config.maxTransactionsPerHour) {
        score += 100;
      } else if (transactionsLastHour >= config.maxTransactionsPerHour * 0.7) {
        score += 70;
      } else if (transactionsLastHour >= config.maxTransactionsPerHour * 0.5) {
        score += 50;
      }

      // Score based on daily transactions
      if (transactionsLastDay >= config.maxTransactionsPerDay) {
        score += 100;
      } else if (transactionsLastDay >= config.maxTransactionsPerDay * 0.7) {
        score += 70;
      } else if (transactionsLastDay >= config.maxTransactionsPerDay * 0.5) {
        score += 50;
      }

      // Average the scores
      return Math.min(100, Math.round(score / 2));
    } catch (error) {
      console.error('Error calculating frequency risk score:', error);
      return 30; // Default to low-medium risk on error
    }
  }

  /**
   * Calculate time risk score
   * @param timestamp Transaction timestamp
   * @returns Risk score (0-100)
   */
  private calculateTimeRiskScore(timestamp: Date): number {
    const hour = timestamp.getHours();

    // Higher risk during night hours (midnight to 5 AM)
    if (hour >= 0 && hour < 5) {
      return 70;
    }

    // Medium risk during early morning and late night
    if ((hour >= 5 && hour < 7) || (hour >= 22 && hour < 24)) {
      return 40;
    }

    // Lower risk during normal hours
    return 10;
  }

  /**
   * Calculate IP risk score
   * @param ipAddress IP address
   * @param config Fraud detection configuration
   * @returns Risk score (0-100)
   */
  private calculateIpRiskScore(ipAddress: string, config: FraudDetectionConfig): number {
    // Check if IP is in high-risk ranges
    for (const range of config.highRiskIpRanges) {
      if (this.isIpInRange(ipAddress, range)) {
        return 90;
      }
    }

    // Add more sophisticated IP risk logic here

    return 20; // Normal IP
  }

  /**
   * Check if IP is in range
   * @param ip IP address
   * @param range IP range in CIDR notation
   * @returns Whether IP is in range
   */
  private isIpInRange(ip: string, range: string): boolean {
    // Simple implementation - in production, use a proper IP range checking library
    return false;
  }

  /**
   * Calculate device risk score
   * @param deviceId Device ID
   * @param userAgent User agent
   * @param merchantId Merchant ID
   * @returns Risk score (0-100)
   */
  private async calculateDeviceRiskScore(
    deviceId: string,
    userAgent: string,
    merchantId: number
  ): Promise<number> {
    try {
      // Check if device has been used before
      const deviceHistory: unknown = await this.prisma.deviceHistory.findUnique({
        where: { deviceId_merchantId: { deviceId, merchantId } },
      });

      if (!deviceHistory) {
        return 60; // New device
      }

      // Check if user agent has changed
      if (deviceHistory.userAgent !== userAgent) {
        return 50; // Changed user agent
      }

      // Add more sophisticated device risk logic here

      return 20; // Known device
    } catch (error) {
      console.error('Error calculating device risk score:', error);
      return 40; // Default to medium risk on error
    }
  }

  /**
   * Calculate payment method risk score
   * @param paymentMethodId Payment method ID
   * @returns Risk score (0-100)
   */
  private async calculatePaymentMethodRiskScore(paymentMethodId: number): Promise<number> {
    try {
      const paymentMethod: unknown = await this.prisma.paymentMethod.findUnique({
        where: { id: paymentMethodId },
      });

      if (!paymentMethod) {
        return 50; // Unknown payment method
      }

      // Different risk scores based on payment method type
      switch (paymentMethod.code) {
        case 'crypto_transfer':
          return 40; // Medium risk
        case 'binance_trc20':
          return 30; // Medium-low risk
        case 'binance_pay':
          return 20; // Low risk
        case 'binance_c2c':
          return 50; // Medium-high risk
        default:
          return 30; // Default medium-low risk
      }
    } catch (error) {
      console.error('Error calculating payment method risk score:', error);
      return 30; // Default to medium-low risk on error
    }
  }

  /**
   * Calculate behavior risk score
   * @param transaction Transaction
   * @param ipAddress IP address
   * @param deviceId Device ID
   * @param merchantId Merchant ID
   * @returns Risk score (0-100)
   */
  private async calculateBehaviorRiskScore(
    transaction: Transaction,
    ipAddress: string,
    deviceId: string,
    merchantId: number
  ): Promise<number> {
    try {
      // Get customer's previous transactions
      const previousTransactions: unknown = await this.prisma.transaction.findMany({
        where: {
          merchantId,
          customerEmail: transaction.customerEmail,
          id: { not: transaction.id },
        },
        orderBy: { createdAt: 'desc' },
        take: 10,
      });

      if (previousTransactions.length === 0) {
        return 50; // New customer
      }

      // Check for unusual amount compared to previous transactions
      const avgAmount: unknown =
        previousTransactions.reduce((sum, tx) => sum + parseFloat(tx.amount), 0) /
        previousTransactions.length;

      const currentAmount: unknown = parseFloat(transaction.amount);
      const amountRatio: unknown = currentAmount / avgAmount;

      if (amountRatio > 5) {
        return 80; // Amount is 5x higher than average
      } else if (amountRatio > 3) {
        return 60; // Amount is 3x higher than average
      } else if (amountRatio > 2) {
        return 40; // Amount is 2x higher than average
      }

      // Add more sophisticated behavior risk logic here

      return 20; // Normal behavior
    } catch (error) {
      console.error('Error calculating behavior risk score:', error);
      return 30; // Default to low-medium risk on error
    }
  }

  /**
   * Calculate history risk score
   * @param customerEmail Customer email
   * @param merchantId Merchant ID
   * @returns Risk score (0-100)
   */
  private async calculateHistoryRiskScore(
    customerEmail: string,
    merchantId: number
  ): Promise<number> {
    try {
      // Check for failed transactions
      const failedTransactions: unknown = await this.prisma.transaction.count({
        where: {
          merchantId,
          customerEmail,
          status: 'FAILED',
        },
      });

      // Check for flagged transactions
      const flaggedTransactions: unknown = await this.prisma.riskAssessment.count({
        where: {
          transaction: {
            merchantId,
            customerEmail,
          },
          isFlagged: true,
        },
      });

      // Calculate score based on failed and flagged transactions
      if (failedTransactions >= 3 ?? flaggedTransactions >= 2) {
        return 80; // High risk
      } else if (failedTransactions >= 2 ?? flaggedTransactions >= 1) {
        return 60; // Medium-high risk
      } else if (failedTransactions >= 1) {
        return 40; // Medium risk
      }

      return 10; // Good history
    } catch (error) {
      console.error('Error calculating history risk score:', error);
      return 20; // Default to low risk on error
    }
  }
}

export default new FraudDetectionService();
