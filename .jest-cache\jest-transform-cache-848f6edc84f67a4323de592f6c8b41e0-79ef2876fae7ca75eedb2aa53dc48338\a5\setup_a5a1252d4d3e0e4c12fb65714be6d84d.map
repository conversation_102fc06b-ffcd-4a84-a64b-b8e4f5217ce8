{"file": "F:\\Amazing pay flow\\src\\tests\\setup.ts", "mappings": ";;;;;AAAA,kBAAkB;AAClB,oDAA4B;AAE5B,yCAAyC;AACzC,gBAAM,CAAC,MAAM,EAAE,CAAC;AAEhB,iCAAiC;AACjC,OAAO,CAAC,GAAG,CAAC,QAAQ,GAAG,MAAM,CAAC;AAC9B,OAAO,CAAC,GAAG,CAAC,UAAU,GAAG,iBAAiB,CAAC;AAC3C,OAAO,CAAC,GAAG,CAAC,YAAY,GAAG,4CAA4C,CAAC;AAExE,4BAA4B;AAC5B,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;AAEvB,wBAAwB;AACxB,SAAS,CAAC,KAAK,IAAI,EAAE;IACnB,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;AACrC,CAAC,CAAC,CAAC;AAEH,uBAAuB;AACvB,QAAQ,CAAC,KAAK,IAAI,EAAE;IAClB,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;AACtC,CAAC,CAAC,CAAC;AAEH,qDAAqD;AACrD,MAAM,oBAAoB,GAAQ,OAAO,CAAC,KAAK,CAAC;AAChD,OAAO,CAAC,KAAK,GAAG,CAAC,GAAG,IAAI,EAAE,EAAE;IAC1B,4DAA4D;IAC5D,IACE,IAAI,CAAC,CAAC,CAAC;QACP,OAAO,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ;QAC3B,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EACvD,CAAC;QACD,oBAAoB,CAAC,GAAG,IAAI,CAAC,CAAC;IAChC,CAAC;IACD,8CAA8C;AAChD,CAAC,CAAC;AAEF,oCAAoC;AACpC,QAAQ,CAAC,GAAG,EAAE;IACZ,OAAO,CAAC,KAAK,GAAG,oBAAoB,CAAC;AACvC,CAAC,CAAC,CAAC", "names": [], "sources": ["F:\\Amazing pay flow\\src\\tests\\setup.ts"], "sourcesContent": ["// Jest setup file\nimport dotenv from 'dotenv';\n\n// Load environment variables for testing\ndotenv.config();\n\n// Set test environment variables\nprocess.env.NODE_ENV = 'test';\nprocess.env.JWT_SECRET = 'test-jwt-secret';\nprocess.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/test';\n\n// Set timeout for all tests\njest.setTimeout(30000);\n\n// Global beforeAll hook\nbeforeAll(async () => {\n  console.log('Starting test suite');\n});\n\n// Global afterAll hook\nafterAll(async () => {\n  console.log('Test suite completed');\n});\n\n// Mock console.error to avoid cluttering test output\nconst originalConsoleError: any = console.error;\nconsole.error = (...args) => {\n  // Check if this is a test-related error that we want to see\n  if (\n    args[0] &&\n    typeof args[0] === 'string' &&\n    (args[0].includes('FAIL') || args[0].includes('ERROR'))\n  ) {\n    originalConsoleError(...args);\n  }\n  // Otherwise suppress the error in test output\n};\n\n// Restore console.error after tests\nafterAll(() => {\n  console.error = originalConsoleError;\n});\n"], "version": 3}