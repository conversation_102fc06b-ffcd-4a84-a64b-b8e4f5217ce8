6d34ddfad8f60f90b184e487997055f0
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createServiceUnavailableError = exports.createInternalServerError = exports.createConflictError = exports.createNotFoundError = exports.createForbiddenError = exports.createUnauthorizedError = exports.createBadRequestError = exports.AppError = void 0;
// jscpd:ignore-file
/**
 * Custom application error class
 * Used for standardized error handling throughout the application
 */
class AppError extends Error {
    constructor(message, statusCode = 500, code = "INTERNAL_SERVER_ERROR", isOperational = true, details) {
        super(message);
        this.statusCode = statusCode;
        this.isOperational = isOperational;
        this.code = code;
        this.details = details;
        // Capture stack trace
        Error.captureStackTrace(this, this.constructor);
        // Set the prototype explicitly
        Object.setPrototypeOf(this, AppError.prototype);
    }
}
exports.AppError = AppError;
/**
 * Factory methods for common error types
 */
const createBadRequestError = (message, code = "BAD_REQUEST", details) => {
    return new AppError(message, 400, code, true, details);
};
exports.createBadRequestError = createBadRequestError;
const createUnauthorizedError = (message, code = "UNAUTHORIZED", details) => {
    return new AppError(message, 401, code, true, details);
};
exports.createUnauthorizedError = createUnauthorizedError;
const createForbiddenError = (message, code = "FORBIDDEN", details) => {
    return new AppError(message, 403, code, true, details);
};
exports.createForbiddenError = createForbiddenError;
const createNotFoundError = (message, code = "NOT_FOUND", details) => {
    return new AppError(message, 404, code, true, details);
};
exports.createNotFoundError = createNotFoundError;
const createConflictError = (message, code = "CONFLICT", details) => {
    return new AppError(message, 409, code, true, details);
};
exports.createConflictError = createConflictError;
const createInternalServerError = (message, code = "INTERNAL_SERVER_ERROR", details) => {
    return new AppError(message, 500, code, true, details);
};
exports.createInternalServerError = createInternalServerError;
const createServiceUnavailableError = (message, code = "SERVICE_UNAVAILABLE", details) => {
    return new AppError(message, 503, code, true, details);
};
exports.createServiceUnavailableError = createServiceUnavailableError;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJmaWxlIjoiRjpcXEFtYXppbmcgcGF5IGZsb3dcXHNyY1xcdXRpbHNcXGFwcC1lcnJvci50cyIsIm1hcHBpbmdzIjoiOzs7QUFBQSxvQkFBb0I7QUFDcEI7OztHQUdHO0FBQ0gsTUFBYSxRQUFTLFNBQVEsS0FBSztJQU0vQixZQUNJLE9BQWUsRUFDZixhQUFxQixHQUFHLEVBQ3hCLE9BQWUsdUJBQXVCLEVBQ3RDLGdCQUF5QixJQUFJLEVBQzdCLE9BQVE7UUFFUixLQUFLLENBQUMsT0FBTyxDQUFDLENBQUM7UUFDZixJQUFJLENBQUMsVUFBVSxHQUFHLFVBQVUsQ0FBQztRQUM3QixJQUFJLENBQUMsYUFBYSxHQUFHLGFBQWEsQ0FBQztRQUNuQyxJQUFJLENBQUMsSUFBSSxHQUFHLElBQUksQ0FBQztRQUNqQixJQUFJLENBQUMsT0FBTyxHQUFHLE9BQU8sQ0FBQztRQUV2QixzQkFBc0I7UUFDdEIsS0FBSyxDQUFDLGlCQUFpQixDQUFDLElBQUksRUFBRSxJQUFJLENBQUMsV0FBVyxDQUFDLENBQUM7UUFFaEQsK0JBQStCO1FBQy9CLE1BQU0sQ0FBQyxjQUFjLENBQUMsSUFBSSxFQUFFLFFBQVEsQ0FBQyxTQUFTLENBQUMsQ0FBQztJQUNwRCxDQUFDO0NBQ0o7QUF6QkQsNEJBeUJDO0FBRUQ7O0dBRUc7QUFDSSxNQUFNLHFCQUFxQixHQUFPLENBQUMsT0FBZSxFQUFFLE9BQWUsYUFBYSxFQUFFLE9BQVEsRUFBRSxFQUFFO0lBQ2pHLE9BQU8sSUFBSSxRQUFRLENBQUMsT0FBTyxFQUFFLEdBQUcsRUFBRSxJQUFJLEVBQUUsSUFBSSxFQUFFLE9BQU8sQ0FBQyxDQUFDO0FBQzNELENBQUMsQ0FBQztBQUZXLFFBQUEscUJBQXFCLHlCQUVoQztBQUVLLE1BQU0sdUJBQXVCLEdBQU8sQ0FBQyxPQUFlLEVBQUUsT0FBZSxjQUFjLEVBQUUsT0FBUSxFQUFFLEVBQUU7SUFDcEcsT0FBTyxJQUFJLFFBQVEsQ0FBQyxPQUFPLEVBQUUsR0FBRyxFQUFFLElBQUksRUFBRSxJQUFJLEVBQUUsT0FBTyxDQUFDLENBQUM7QUFDM0QsQ0FBQyxDQUFDO0FBRlcsUUFBQSx1QkFBdUIsMkJBRWxDO0FBRUssTUFBTSxvQkFBb0IsR0FBTyxDQUFDLE9BQWUsRUFBRSxPQUFlLFdBQVcsRUFBRSxPQUFRLEVBQUUsRUFBRTtJQUM5RixPQUFPLElBQUksUUFBUSxDQUFDLE9BQU8sRUFBRSxHQUFHLEVBQUUsSUFBSSxFQUFFLElBQUksRUFBRSxPQUFPLENBQUMsQ0FBQztBQUMzRCxDQUFDLENBQUM7QUFGVyxRQUFBLG9CQUFvQix3QkFFL0I7QUFFSyxNQUFNLG1CQUFtQixHQUFPLENBQUMsT0FBZSxFQUFFLE9BQWUsV0FBVyxFQUFFLE9BQVEsRUFBRSxFQUFFO0lBQzdGLE9BQU8sSUFBSSxRQUFRLENBQUMsT0FBTyxFQUFFLEdBQUcsRUFBRSxJQUFJLEVBQUUsSUFBSSxFQUFFLE9BQU8sQ0FBQyxDQUFDO0FBQzNELENBQUMsQ0FBQztBQUZXLFFBQUEsbUJBQW1CLHVCQUU5QjtBQUVLLE1BQU0sbUJBQW1CLEdBQU8sQ0FBQyxPQUFlLEVBQUUsT0FBZSxVQUFVLEVBQUUsT0FBUSxFQUFFLEVBQUU7SUFDNUYsT0FBTyxJQUFJLFFBQVEsQ0FBQyxPQUFPLEVBQUUsR0FBRyxFQUFFLElBQUksRUFBRSxJQUFJLEVBQUUsT0FBTyxDQUFDLENBQUM7QUFDM0QsQ0FBQyxDQUFDO0FBRlcsUUFBQSxtQkFBbUIsdUJBRTlCO0FBRUssTUFBTSx5QkFBeUIsR0FBTyxDQUFDLE9BQWUsRUFBRSxPQUFlLHVCQUF1QixFQUFFLE9BQVEsRUFBRSxFQUFFO0lBQy9HLE9BQU8sSUFBSSxRQUFRLENBQUMsT0FBTyxFQUFFLEdBQUcsRUFBRSxJQUFJLEVBQUUsSUFBSSxFQUFFLE9BQU8sQ0FBQyxDQUFDO0FBQzNELENBQUMsQ0FBQztBQUZXLFFBQUEseUJBQXlCLDZCQUVwQztBQUVLLE1BQU0sNkJBQTZCLEdBQU8sQ0FBQyxPQUFlLEVBQUUsT0FBZSxxQkFBcUIsRUFBRSxPQUFRLEVBQUUsRUFBRTtJQUNqSCxPQUFPLElBQUksUUFBUSxDQUFDLE9BQU8sRUFBRSxHQUFHLEVBQUUsSUFBSSxFQUFFLElBQUksRUFBRSxPQUFPLENBQUMsQ0FBQztBQUMzRCxDQUFDLENBQUM7QUFGVyxRQUFBLDZCQUE2QixpQ0FFeEMiLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiRjpcXEFtYXppbmcgcGF5IGZsb3dcXHNyY1xcdXRpbHNcXGFwcC1lcnJvci50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBqc2NwZDppZ25vcmUtZmlsZVxuLyoqXG4gKiBDdXN0b20gYXBwbGljYXRpb24gZXJyb3IgY2xhc3NcbiAqIFVzZWQgZm9yIHN0YW5kYXJkaXplZCBlcnJvciBoYW5kbGluZyB0aHJvdWdob3V0IHRoZSBhcHBsaWNhdGlvblxuICovXG5leHBvcnQgY2xhc3MgQXBwRXJyb3IgZXh0ZW5kcyBFcnJvciB7XG4gICAgcHVibGljIHJlYWRvbmx5IHN0YXR1c0NvZGU6IG51bWJlcjtcbiAgICBwdWJsaWMgcmVhZG9ubHkgaXNPcGVyYXRpb25hbDogYm9vbGVhbjtcbiAgICBwdWJsaWMgcmVhZG9ubHkgY29kZTogc3RyaW5nO1xuICAgIHB1YmxpYyByZWFkb25seSBkZXRhaWxzPztcblxuICAgIGNvbnN0cnVjdG9yKFxuICAgICAgICBtZXNzYWdlOiBzdHJpbmcsXG4gICAgICAgIHN0YXR1c0NvZGU6IG51bWJlciA9IDUwMCxcbiAgICAgICAgY29kZTogc3RyaW5nID0gXCJJTlRFUk5BTF9TRVJWRVJfRVJST1JcIixcbiAgICAgICAgaXNPcGVyYXRpb25hbDogYm9vbGVhbiA9IHRydWUsXG4gICAgICAgIGRldGFpbHM/XG4gICAgKSB7XG4gICAgICAgIHN1cGVyKG1lc3NhZ2UpO1xuICAgICAgICB0aGlzLnN0YXR1c0NvZGUgPSBzdGF0dXNDb2RlO1xuICAgICAgICB0aGlzLmlzT3BlcmF0aW9uYWwgPSBpc09wZXJhdGlvbmFsO1xuICAgICAgICB0aGlzLmNvZGUgPSBjb2RlO1xuICAgICAgICB0aGlzLmRldGFpbHMgPSBkZXRhaWxzO1xuICAgIFxuICAgICAgICAvLyBDYXB0dXJlIHN0YWNrIHRyYWNlXG4gICAgICAgIEVycm9yLmNhcHR1cmVTdGFja1RyYWNlKHRoaXMsIHRoaXMuY29uc3RydWN0b3IpO1xuICAgIFxuICAgICAgICAvLyBTZXQgdGhlIHByb3RvdHlwZSBleHBsaWNpdGx5XG4gICAgICAgIE9iamVjdC5zZXRQcm90b3R5cGVPZih0aGlzLCBBcHBFcnJvci5wcm90b3R5cGUpO1xuICAgIH1cbn1cblxuLyoqXG4gKiBGYWN0b3J5IG1ldGhvZHMgZm9yIGNvbW1vbiBlcnJvciB0eXBlc1xuICovXG5leHBvcnQgY29uc3QgY3JlYXRlQmFkUmVxdWVzdEVycm9yOiBhbnkgPShtZXNzYWdlOiBzdHJpbmcsIGNvZGU6IHN0cmluZyA9IFwiQkFEX1JFUVVFU1RcIiwgZGV0YWlscz8pID0+IHtcbiAgICByZXR1cm4gbmV3IEFwcEVycm9yKG1lc3NhZ2UsIDQwMCwgY29kZSwgdHJ1ZSwgZGV0YWlscyk7XG59O1xuXG5leHBvcnQgY29uc3QgY3JlYXRlVW5hdXRob3JpemVkRXJyb3I6IGFueSA9KG1lc3NhZ2U6IHN0cmluZywgY29kZTogc3RyaW5nID0gXCJVTkFVVEhPUklaRURcIiwgZGV0YWlscz8pID0+IHtcbiAgICByZXR1cm4gbmV3IEFwcEVycm9yKG1lc3NhZ2UsIDQwMSwgY29kZSwgdHJ1ZSwgZGV0YWlscyk7XG59O1xuXG5leHBvcnQgY29uc3QgY3JlYXRlRm9yYmlkZGVuRXJyb3I6IGFueSA9KG1lc3NhZ2U6IHN0cmluZywgY29kZTogc3RyaW5nID0gXCJGT1JCSURERU5cIiwgZGV0YWlscz8pID0+IHtcbiAgICByZXR1cm4gbmV3IEFwcEVycm9yKG1lc3NhZ2UsIDQwMywgY29kZSwgdHJ1ZSwgZGV0YWlscyk7XG59O1xuXG5leHBvcnQgY29uc3QgY3JlYXRlTm90Rm91bmRFcnJvcjogYW55ID0obWVzc2FnZTogc3RyaW5nLCBjb2RlOiBzdHJpbmcgPSBcIk5PVF9GT1VORFwiLCBkZXRhaWxzPykgPT4ge1xuICAgIHJldHVybiBuZXcgQXBwRXJyb3IobWVzc2FnZSwgNDA0LCBjb2RlLCB0cnVlLCBkZXRhaWxzKTtcbn07XG5cbmV4cG9ydCBjb25zdCBjcmVhdGVDb25mbGljdEVycm9yOiBhbnkgPShtZXNzYWdlOiBzdHJpbmcsIGNvZGU6IHN0cmluZyA9IFwiQ09ORkxJQ1RcIiwgZGV0YWlscz8pID0+IHtcbiAgICByZXR1cm4gbmV3IEFwcEVycm9yKG1lc3NhZ2UsIDQwOSwgY29kZSwgdHJ1ZSwgZGV0YWlscyk7XG59O1xuXG5leHBvcnQgY29uc3QgY3JlYXRlSW50ZXJuYWxTZXJ2ZXJFcnJvcjogYW55ID0obWVzc2FnZTogc3RyaW5nLCBjb2RlOiBzdHJpbmcgPSBcIklOVEVSTkFMX1NFUlZFUl9FUlJPUlwiLCBkZXRhaWxzPykgPT4ge1xuICAgIHJldHVybiBuZXcgQXBwRXJyb3IobWVzc2FnZSwgNTAwLCBjb2RlLCB0cnVlLCBkZXRhaWxzKTtcbn07XG5cbmV4cG9ydCBjb25zdCBjcmVhdGVTZXJ2aWNlVW5hdmFpbGFibGVFcnJvcjogYW55ID0obWVzc2FnZTogc3RyaW5nLCBjb2RlOiBzdHJpbmcgPSBcIlNFUlZJQ0VfVU5BVkFJTEFCTEVcIiwgZGV0YWlscz8pID0+IHtcbiAgICByZXR1cm4gbmV3IEFwcEVycm9yKG1lc3NhZ2UsIDUwMywgY29kZSwgdHJ1ZSwgZGV0YWlscyk7XG59O1xuIl0sInZlcnNpb24iOjN9