{"file": "F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationTypes.ts", "mappings": ";AAAA;;;;GAIG;;;AAIH;;GAEG;AACH,IAAY,6BAWX;AAXD,WAAY,6BAA6B;IACvC,wEAAuC,CAAA;IACvC,oEAAmC,CAAA;IACnC,gEAA+B,CAAA;IAC/B,4EAA2C,CAAA;IAC3C,kFAAiD,CAAA;IACjD,oEAAmC,CAAA;IACnC,kEAAiC,CAAA;IACjC,kEAAiC,CAAA;IACjC,0EAAyC,CAAA;IACzC,8DAA6B,CAAA;AAC/B,CAAC,EAXW,6BAA6B,6CAA7B,6BAA6B,QAWxC", "names": [], "sources": ["F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationTypes.ts"], "sourcesContent": ["/**\n * Identity Verification Types and Interfaces\n * \n * Centralized type definitions for the identity verification system.\n */\n\nimport { IdentityVerificationMethodEnum, IdentityVerificationStatusEnum } from \"@prisma/client\";\n\n/**\n * Error codes for identity verification\n */\nexport enum IdentityVerificationErrorCode {\n  INVALID_SIGNATURE = \"INVALID_SIGNATURE\",\n  INVALID_ADDRESS = \"INVALID_ADDRESS\",\n  INVALID_PROOF = \"INVALID_PROOF\",\n  VERIFICATION_FAILED = \"VERIFICATION_FAILED\",\n  VERIFICATION_NOT_FOUND = \"VERIFICATION_NOT_FOUND\",\n  CLAIM_NOT_FOUND = \"CLAIM_NOT_FOUND\",\n  INTERNAL_ERROR = \"INTERNAL_ERROR\",\n  PROVIDER_ERROR = \"PROVIDER_ERROR\",\n  INVALID_PARAMETERS = \"INVALID_PARAMETERS\",\n  UNAUTHORIZED = \"UNAUTHORIZED\",\n}\n\n/**\n * Identity verification result interface\n */\nexport interface IdentityVerificationResult {\n  success: boolean;\n  method: IdentityVerificationMethodEnum;\n  status: IdentityVerificationStatusEnum;\n  message: string;\n  data?: any;\n  error?: string;\n  verificationId?: string;\n}\n\n/**\n * Ethereum signature verification parameters\n */\nexport interface EthereumSignatureParams {\n  address: string;\n  message: string;\n  signature: string;\n  userId?: string;\n  merchantId?: string;\n}\n\n/**\n * ERC-1484 verification parameters\n */\nexport interface ERC1484Params {\n  address: string;\n  ein: string;\n  registryAddress: string;\n  userId?: string;\n  merchantId?: string;\n}\n\n/**\n * ERC-725 verification parameters\n */\nexport interface ERC725Params {\n  address: string;\n  key: string;\n  value: string;\n  userId?: string;\n  merchantId?: string;\n}\n\n/**\n * ENS verification parameters\n */\nexport interface ENSParams {\n  ensName: string;\n  address: string;\n  userId?: string;\n  merchantId?: string;\n}\n\n/**\n * Polygon ID verification parameters\n */\nexport interface PolygonIDParams {\n  proof: any;\n  publicSignals: any[];\n  circuitId: string;\n  userId?: string;\n  merchantId?: string;\n}\n\n/**\n * Worldcoin verification parameters\n */\nexport interface WorldcoinParams {\n  merkleRoot: string;\n  nullifierHash: string;\n  proof: any;\n  userId?: string;\n  merchantId?: string;\n}\n\n/**\n * BrightID verification parameters\n */\nexport interface BrightIDParams {\n  contextId: string;\n  userId?: string;\n  merchantId?: string;\n}\n\n/**\n * Base verification method interface\n */\nexport interface IVerificationMethod {\n  getName(): string;\n  verify(params: any): Promise<IdentityVerificationResult>;\n}\n\n/**\n * Verification configuration\n */\nexport interface VerificationConfig {\n  ethereumRpcUrl?: string;\n  polygonIdVerifierAddress?: string;\n  worldcoinAppId?: string;\n  brightIdApiUrl?: string;\n  enabledMethods: IdentityVerificationMethodEnum[];\n}\n\n/**\n * Verification claim interface\n */\nexport interface VerificationClaim {\n  id: string;\n  verificationId: string;\n  claimType: string;\n  claimValue: any;\n  issuer?: string;\n  issuedAt: Date;\n  expiresAt?: Date;\n  verified: boolean;\n}\n\n/**\n * Verification statistics\n */\nexport interface VerificationStats {\n  totalVerifications: number;\n  successfulVerifications: number;\n  failedVerifications: number;\n  pendingVerifications: number;\n  verificationsByMethod: Record<string, number>;\n  averageVerificationTime: number;\n}\n\n/**\n * Verification filter options\n */\nexport interface VerificationFilters {\n  userId?: string;\n  merchantId?: string;\n  method?: IdentityVerificationMethodEnum;\n  status?: IdentityVerificationStatusEnum;\n  dateFrom?: Date;\n  dateTo?: Date;\n  limit?: number;\n  offset?: number;\n}\n"], "version": 3}