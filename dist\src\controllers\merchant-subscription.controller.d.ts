import { Request, Response } from "express";
declare global {
    namespace Express {
        interface Request {
            user?: {
                id: string;
                role: string;
                merchantId?: string;
            };
        }
    }
}
declare class MerchantSubscriptionController {
    subscribeMerchant(req: Request, res: Response): Promise<Response<any, Record<string, any>>>;
    cancelSubscription(req: Request, res: Response): Promise<Response<any, Record<string, any>>>;
    getSubscriptionStatus(req: Request, res: Response): Promise<Response<any, Record<string, any>>>;
}
declare const _default: MerchantSubscriptionController;
export default _default;
//# sourceMappingURL=merchant-subscription.controller.d.ts.map