// jscpd:ignore-file
import { logger } from "../utils/logger";
import { config } from "../config";
import axios from "axios";
import { config } from "../config";

/**
 * Telegram service
 */
export class TelegramService {
    private botToken: string;
    private apiUrl: string;

    /**
   * Create a new Telegram service
   */
    constructor() {
        this.botToken = config.telegram.botToken;
        this.apiUrl = `https://api.telegram.org/bot${this.botToken}`;
    }

    /**
   * Send message to Telegram chat
   * @param chatId Telegram chat ID
   * @param message Message to send
   * @param parseMode Parse mode (optional, defaults to Markdown)
   * @returns Success status
   */
    public async sendMessage(
        chatId: string,
        message: string,
        parseMode: "Markdown" | "HTML" = "Markdown"
    ): Promise<boolean> {
        try {
            // Validate inputs
            if (!chatId || !message) {
                logger.error("Invalid Telegram message parameters", { chatId, message });
                return false;
            }

            // Check if Telegram is configured
            if (!this.botToken) {
                logger.error("Telegram bot token not configured");
                return false;
            }

            // Send message
            const response: any =await axios.post(`${this.apiUrl}/sendMessage`, {
                chat_id: chatId,
                text: message,
                parse_mode: parseMode
            });

            // Check response
            if (response.data && response.data.ok) {
                logger.info("Telegram message sent successfully", { chatId });
                return true;
            } else {
                logger.error("Failed to send Telegram message", {
                    chatId,
                    response: response.data
                });
                return false;
            }
        } catch (error) {
            logger.error("Error sending Telegram message", { error, chatId });
            return false;
        }
    }

    /**
   * Get bot information
   * @returns Bot information
   */
    public async getMe(): Promise<any> {
        try {
            // Check if Telegram is configured
            if (!this.botToken) {
                logger.error("Telegram bot token not configured");
                return null;
            }

            // Get bot information
            const response: any =await axios.get(`${this.apiUrl}/getMe`);

            // Check response
            if (response.data && response.data.ok) {
                return response.data.result;
            } else {
                logger.error("Failed to get Telegram bot information", {
                    response: response.data
                });
                return null;
            }
        } catch (error) {
            logger.error("Error getting Telegram bot information", { error });
            return null;
        }
    }

    /**
   * Set webhook for bot
   * @param url Webhook URL
   * @returns Success status
   */
    public async setWebhook(url: string): Promise<boolean> {
        try {
            // Validate inputs
            if (!url) {
                logger.error("Invalid webhook URL");
                return false;
            }

            // Check if Telegram is configured
            if (!this.botToken) {
                logger.error("Telegram bot token not configured");
                return false;
            }

            // Set webhook
            const response: any =await axios.post(`${this.apiUrl}/setWebhook`, {
                url
            });

            // Check response
            if (response.data && response.data.ok) {
                logger.info("Telegram webhook set successfully", { url });
                return true;
            } else {
                logger.error("Failed to set Telegram webhook", {
                    url,
                    response: response.data
                });
                return false;
            }
        } catch (error) {
            logger.error("Error setting Telegram webhook", { error, url });
            return false;
        }
    }

    /**
   * Delete webhook
   * @returns Success status
   */
    public async deleteWebhook(): Promise<boolean> {
        try {
            // Check if Telegram is configured
            if (!this.botToken) {
                logger.error("Telegram bot token not configured");
                return false;
            }

            // Delete webhook
            const response: any =await axios.post(`${this.apiUrl}/deleteWebhook`);

            // Check response
            if (response.data && response.data.ok) {
                logger.info("Telegram webhook deleted successfully");
                return true;
            } else {
                logger.error("Failed to delete Telegram webhook", {
                    response: response.data
                });
                return false;
            }
        } catch (error) {
            logger.error("Error deleting Telegram webhook", { error });
            return false;
        }
    }

    /**
   * Get webhook info
   * @returns Webhook info
   */
    public async getWebhookInfo(): Promise<any> {
        try {
            // Check if Telegram is configured
            if (!this.botToken) {
                logger.error("Telegram bot token not configured");
                return null;
            }

            // Get webhook info
            const response: any =await axios.get(`${this.apiUrl}/getWebhookInfo`);

            // Check response
            if (response.data && response.data.ok) {
                return response.data.result;
            } else {
                logger.error("Failed to get Telegram webhook info", {
                    response: response.data
                });
                return null;
            }
        } catch (error) {
            logger.error("Error getting Telegram webhook info", { error });
            return null;
        }
    }

    /**
   * Send photo to Telegram chat
   * @param chatId Telegram chat ID
   * @param photo Photo URL or file ID
   * @param caption Photo caption (optional)
   * @param parseMode Parse mode (optional, defaults to Markdown)
   * @returns Success status
   */
    public async sendPhoto(
        chatId: string,
        photo: string,
        caption?: string,
        parseMode: "Markdown" | "HTML" = "Markdown"
    ): Promise<boolean> {
        try {
            // Validate inputs
            if (!chatId || !photo) {
                logger.error("Invalid Telegram photo parameters", { chatId, photo });
                return false;
            }

            // Check if Telegram is configured
            if (!this.botToken) {
                logger.error("Telegram bot token not configured");
                return false;
            }

            // Prepare request data
            const data: any = {
                chat_id: chatId,
                photo
            };

            // Add optional parameters
            if (caption) {
                data.caption = caption;
                data.parse_mode = parseMode;
            }

            // Send photo
            const response: any =await axios.post(`${this.apiUrl}/sendPhoto`, data);

            // Check response
            if (response.data && response.data.ok) {
                logger.info("Telegram photo sent successfully", { chatId });
                return true;
            } else {
                logger.error("Failed to send Telegram photo", {
                    chatId,
                    response: response.data
                });
                return false;
            }
        } catch (error) {
            logger.error("Error sending Telegram photo", { error, chatId });
            return false;
        }
    }

    /**
   * Process webhook update
   * @param update Webhook update
   * @returns Response message
   */
    public async processUpdate(update): Promise<string | null> {
        try {
            // Check if update contains a message
            if (!update || !update.message) {
                return null;
            }

            const message: unknown =update.message;
            const chatId: unknown =message.chat.id;
            const text: unknown =message.text;

            // Process commands
            if (text && text.startsWith("/")) {
                return await this.processCommand(chatId, text);
            }

            // Default response for non-command messages
            return null;
        } catch (error) {
            logger.error("Error processing Telegram update", { error, update });
            return null;
        }
    }

    /**
   * Process command
   * @param chatId Chat ID
   * @param command Command text
   * @returns Response message
   */
    private async processCommand(chatId: string, command: string): Promise<string | null> {
        try {
            // Extract command name
            const commandName: unknown =command.split(" ")[0].toLowerCase();

            // Process different commands
            switch (commandName) {
            case "/start":
                await this.sendMessage(
                    chatId,
                    "Welcome to the AmazingPay notification bot! Your chat ID is: `" + chatId + "`\n\n" +
            "Please use this ID in your notification preferences to receive alerts."
                );
                return "Welcome message sent";

            case "/help":
                await this.sendMessage(
                    chatId,
                    "*AmazingPay Notification Bot Help*\n\n" +
            "Available commands:\n" +
            "• `/start` - Start the bot and get your chat ID\n" +
            "• `/help` - Show this help message\n" +
            "• `/status` - Check the bot status\n\n" +
            "To receive notifications, add your chat ID to your notification preferences in the AmazingPay dashboard."
                );
                return "Help message sent";

            case "/status":
                await this.sendMessage(
                    chatId,
                    "*Bot Status*\n\n" +
            "✅ Bot is active and ready to send notifications\n" +
            "✅ Your chat ID is registered: `" + chatId + "`"
                );
                return "Status message sent";

            default:
                await this.sendMessage(
                    chatId,
                    "Unknown command. Type `/help` to see available commands."
                );
                return "Unknown command message sent";
            }
        } catch (error) {
            logger.error("Error processing Telegram command", { error, chatId, command });
            return null;
        }
    }
}
