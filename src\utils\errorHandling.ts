// jscpd:ignore-file
/**
 * Error Handling Utilities
 * 
 * This file contains utility functions for error handling.
 */

import { AppError, ErrorType, ErrorCode } from '../utils/errors';
import { logger } from '../utils/logger';
import { logger } from '../utils/logger';

/**
 * Handle an error
 * @param error Error to handle
 * @param resource Resource name
 * @param id Resource ID
 * @returns Formatted error
 */
export function handleError(error: Error, resource?: string, id?: string): Error {
  logger.error(`Error in ${resource || 'unknown'}${id ? ` (${id})` : ''}:`, error);

  // If it's already an AppError, return it
  if (error instanceof AppError) {
    return error;
  }

  // Handle database errors

}