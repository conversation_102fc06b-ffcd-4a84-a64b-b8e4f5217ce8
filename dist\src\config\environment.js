"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.isTest = exports.isDevelopment = exports.isDemo = exports.isProduction = exports.getEnvironment = exports.loadEnvironment = void 0;
// jscpd:ignore-file
const dotenv_1 = __importDefault(require("dotenv"));
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
// Determine which environment file to load
const getEnvFile = () => {
    // Always use production environment
    process.env.NODE_ENV = "production";
    const envFile = `.env.production`;
    // Check if environment-specific file exists
    if (fs_1.default.existsSync(path_1.default.resolve(process.cwd(), envFile))) {
        return envFile;
    }
    // Fall back to .env
    return ".env";
};
// Load environment variables
const loadEnvironment = () => {
    const envFile = getEnvFile();
    console.log(`Loading environment from ${envFile}`);
    const result = dotenv_1.default.config({ path: envFile });
    if (result.error) {
        console.warn(`Error loading ${envFile}: ${result.error.message}`);
        console.warn("Falling back to .env");
        dotenv_1.default.config();
    }
};
exports.loadEnvironment = loadEnvironment;
// Get environment name
const getEnvironment = () => {
    // Always return production
    return "production";
};
exports.getEnvironment = getEnvironment;
// Check if current environment is production
const isProduction = () => {
    // Always return true to force production mode
    return true;
};
exports.isProduction = isProduction;
// Check if current environment is demo
const isDemo = () => {
    // Always return false to disable demo mode
    return false;
};
exports.isDemo = isDemo;
// Check if current environment is development
const isDevelopment = () => {
    // Always return false to disable development mode
    return false;
};
exports.isDevelopment = isDevelopment;
// Check if current environment is test
const isTest = () => {
    // Always return false to disable test mode
    return false;
};
exports.isTest = isTest;
// Export environment variables
exports.default = {
    environment: (0, exports.getEnvironment)(),
    isProduction: (0, exports.isProduction)(),
    isDemo: (0, exports.isDemo)(),
    isDevelopment: (0, exports.isDevelopment)(),
    isTest: (0, exports.isTest)()
};
//# sourceMappingURL=environment.js.map