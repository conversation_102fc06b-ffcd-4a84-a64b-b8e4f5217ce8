// jscpd:ignore-file
/**
 * Error Handler Utility
 *
 * This utility provides centralized error handling for the application.
 * It includes functions for handling errors in controllers, services, and middleware.
 */

import { Request, Response, NextFunction } from 'express';
import { logger } from '../lib/logger';
import { AppError, ErrorType, ErrorCode } from './errors/AppError';
import { isProduction } from './environment-validator';

/**
 * Extended Request interface with id property
 */
interface ExtendedRequest extends Request {
  id?: string;
}

/**
 * Error response structure
 */
export interface ErrorResponse {
  status: string;
  statusCode: number;
  message: string;
  error?: string;
  stack?: string;
  timestamp: string;
  path: string;
  requestId?: string;
  code?: string;
  details?: unknown;
}

/**
 * Handle controller errors
 * @param err Error
 * @param req Request
 * @param res Response
 * @param next NextFunction
 */
export const handleControllerError = (
  err: Error | AppError,
  req: ExtendedRequest,
  res: Response,
  next: NextFunction
): void => {
  // Log error
  logger.error('Controller error:', {
    path: req.path,
    method: req.method,
    error: err.message,
    stack: err.stack,
    requestId: req.id,
  });

  // Create error response
  const errorResponse: ErrorResponse = {
    status: 'error',
    statusCode: err instanceof AppError ? err.statusCode : 500,
    message: err.message ?? 'Internal server error',
    timestamp: new Date().toISOString(),
    path: req.originalUrl ?? req.url,
  };

  // Add request ID if available
  if (req.id) {
    errorResponse.requestId = req.id;
  }

  // Add error code if available
  if (err instanceof AppError && err.code) {
    errorResponse.code = err.code;
  }

  // Add error details if available
  if (err instanceof AppError && err.details) {
    errorResponse.details = err.details;
  }

  // Add stack trace in development
  if (!isProduction()) {
    errorResponse.stack = err.stack;
  }

  // Send error response
  res.status(errorResponse.statusCode).json(errorResponse);
};
export const handleServiceError = (
  err: Error | AppError,
  serviceName: string,
  methodName: string,
  params?: unknown
): never => {
  // Log error
  logger.error(`Service error in ${serviceName}.${methodName}:`, {
    error: err.message,
    stack: err.stack,
    params: params ? JSON.stringify(params) : undefined,
  });

  // Rethrow AppError
  if (err instanceof AppError) {
    throw err;
  }

  // Wrap other errors in AppError
  throw new AppError({
    message: err.message ?? 'Internal server error',
    type: ErrorType.INTERNAL,
    code: ErrorCode.INTERNAL_SERVER_ERROR,
    statusCode: 500,
    details: { serviceName, methodName, originalError: err.message },
  });
};

/**
 * Global error handler middleware
 * @param err Error
 * @param req Request
 * @param res Response
 * @param next NextFunction
 */
export const globalErrorHandler = (
  err: Error | AppError,
  req: ExtendedRequest,
  res: Response,
  next: NextFunction
): void => {
  // Log error
  logger.error('Global error handler:', {
    path: req.path,
    method: req.method,
    error: err.message,
    stack: err.stack,
    requestId: req.id,
  });

  // Create error response
  const errorResponse: ErrorResponse = {
    status: 'error',
    statusCode: err instanceof AppError ? err.statusCode : 500,
    message: err.message ?? 'Internal server error',
    timestamp: new Date().toISOString(),
    path: req.originalUrl ?? req.url,
  };

  // Add request ID if available
  if (req.id) {
    errorResponse.requestId = req.id;
  }

  // Add error code if available
  if (err instanceof AppError && err.code) {
    errorResponse.code = err.code;
  }

  // Add error details if available
  if (err instanceof AppError && err.details) {
    errorResponse.details = err.details;
  }

  // Add stack trace in development
  if (!isProduction()) {
    errorResponse.stack = err.stack;
    errorResponse.error = err.name;
  }

  // Send error response
  res.status(errorResponse.statusCode).json(errorResponse);
};
export const handle404Error = (req: ExtendedRequest, res: Response, next: NextFunction): void => {
  // Create error response
  const errorResponse: ErrorResponse = {
    status: 'error',
    statusCode: 404,
    message: `Route not found: ${req.method} ${req.originalUrl ?? req.url}`,
    timestamp: new Date().toISOString(),
    path: req.originalUrl ?? req.url,
  };

  // Add request ID if available
  if (req.id) {
    errorResponse.requestId = req.id;
  }

  // Log error
  logger.warn('Route not found:', {
    path: req.path,
    method: req.method,
    requestId: req.id,
  });

  // Send error response
  res.status(404).json(errorResponse);
};

export default {
  handleControllerError,
  handleServiceError,
  globalErrorHandler,
  handle404Error,
};
