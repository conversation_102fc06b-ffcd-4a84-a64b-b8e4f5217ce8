/**
 * Validation Service
 *
 * Handles input validation for alert aggregation operations.
 */
import { CreateAggregationRuleRequest, UpdateAggregationRuleRequest } from '../types/AlertAggregationTypes';
/**
 * Validation service for alert aggregation
 */
export declare class ValidationService {
    /**
     * Validate aggregation rule creation request
     */
    validateCreateAggregationRule(data: any): CreateAggregationRuleRequest;
    /**
     * Validate aggregation rule update request
     */
    validateUpdateAggregationRule(data: any): UpdateAggregationRuleRequest;
    /**
     * Validate correlation condition
     */
    private validateCorrelationCondition;
    /**
     * Validate ID parameter
     */
    validateId(id: any, fieldName?: string): string;
    /**
     * Validate pagination parameters
     */
    validatePaginationParams(query: any): {
        page: number;
        limit: number;
        sortBy?: string;
        sortOrder?: 'asc' | 'desc';
    };
    /**
     * Helper validation methods
     */
    private validateNameField;
    private validateDescriptionField;
    private validateType<PERSON>ield;
    private validateSever<PERSON><PERSON>ield;
    private validateTimeWindowField;
    private validateThreshold<PERSON>ield;
    private validateGroupByField;
    private validateEnabledField;
}
//# sourceMappingURL=ValidationService.d.ts.map