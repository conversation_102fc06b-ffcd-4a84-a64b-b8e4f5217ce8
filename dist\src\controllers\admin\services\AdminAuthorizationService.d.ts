/**
 * Admin Authorization Service
 *
 * Handles authorization logic for admin operations.
 */
import { AuthorizationContext, PermissionResult } from '../types/AdminControllerTypes';
/**
 * Authorization service for admin operations
 */
export declare class AdminAuthorizationService {
    private readonly superAdminRoles;
    private readonly adminRoles;
    private readonly managerRoles;
    /**
     * Check if user is authorized for the given action
     */
    checkPermission(context: AuthorizationContext): Promise<PermissionResult>;
    /**
     * Check role-based permissions
     */
    private checkRolePermission;
    /**
     * Check dashboard permissions
     */
    private checkDashboardPermission;
    /**
     * Check admin user permissions
     */
    private checkAdminUserPermission;
    /**
     * Check role management permissions
     */
    private checkRoleManagementPermission;
    /**
     * Check permission permissions
     */
    private checkPermissionPermission;
    /**
     * Check system permissions
     */
    private checkSystemPermission;
    /**
     * Check resource-specific permissions
     */
    private checkResourcePermission;
    /**
     * Require super admin role
     */
    requireSuperAdmin(userRole?: string): void;
    /**
     * Require admin role
     */
    requireAdmin(userRole?: string): void;
    /**
     * Require manager role or higher
     */
    requireManager(userRole?: string): void;
    /**
     * Check if user has specific role
     */
    hasRole(userRole: string, requiredRole: string): boolean;
    /**
     * Get user permissions for a resource
     */
    getUserPermissions(userRole: string, resource: string): string[];
    /**
     * Validate authorization context
     */
    validateAuthorizationContext(context: AuthorizationContext): void;
    /**
     * Create authorization context from request
     */
    createAuthorizationContext(user: any, resource: string, action: string, resourceId?: string): AuthorizationContext;
    /**
     * Handle authorization error
     */
    handleAuthorizationError(result: PermissionResult): never;
}
//# sourceMappingURL=AdminAuthorizationService.d.ts.map