"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PushNotificationController = void 0;
const base_controller_1 = require("./base.controller");
const asyncHandler_1 = require("../utils/asyncHandler");
const AppError_1 = require("../utils/errors/AppError");
const push_notification_service_1 = require("../services/push-notification.service");
const prisma_1 = __importDefault(require("../lib/prisma"));
/**
 * PushNotificationController
 */
class PushNotificationController extends base_controller_1.BaseController {
    constructor() {
        super();
        /**
         * Get VAPID public key for push notifications
         */
        this.getPublicKey = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Create push notification service
                const pushService = new push_notification_service_1.PushNotificationService();
                // Get public key
                const publicKey = pushService.getPublicKey();
                // Return public key
                return res.status(200).json({
                    success: true,
                    data: { publicKey }
                });
            }
            catch (error) {
                if (error instanceof AppError_1.AppError) {
                    throw error;
                }
                throw new AppError_1.AppError({
                    message: "Failed to get public key",
                    type: ErrorType.INTERNAL,
                    code: ErrorCode.INTERNAL_SERVER_ERROR
                });
            }
        });
        /**
         * Subscribe to push notifications
         */
        this.subscribe = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Get user ID and merchant ID
                const userId = req.user?.id;
                const merchantId = req.user?.merchantId;
                // Check if user is authenticated
                if (!userId) {
                    throw new AppError_1.AppError({
                        message: "Unauthorized",
                        type: ErrorType.AUTHENTICATION,
                        code: ErrorCode.INVALID_CREDENTIALS
                    });
                }
                // Get subscription data
                const { subscription, deviceInfo } = req.body;
                // Validate subscription
                if (!subscription || !subscription.endpoint) {
                    throw new AppError_1.AppError({
                        message: "Invalid subscription",
                        type: ErrorType.VALIDATION,
                        code: ErrorCode.INVALID_INPUT
                    });
                }
                // Create push notification service
                const pushService = new push_notification_service_1.PushNotificationService();
                // Save subscription
                const subscriptionId = await pushService.saveSubscription(subscription, userId, merchantId, deviceInfo);
                // Return success
                return res.status(200).json({
                    success: true,
                    data: { subscriptionId },
                    message: "Subscription saved successfully"
                });
            }
            catch (error) {
                if (error instanceof AppError_1.AppError) {
                    throw error;
                }
                throw new AppError_1.AppError({
                    message: "Failed to save subscription",
                    type: ErrorType.INTERNAL,
                    code: ErrorCode.INTERNAL_SERVER_ERROR
                });
            }
        });
        /**
         * Unsubscribe from push notifications
         */
        this.unsubscribe = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Get subscription data
                const { endpoint } = req.body;
                // Validate endpoint
                if (!endpoint) {
                    throw new AppError_1.AppError({
                        message: "Invalid endpoint",
                        type: ErrorType.VALIDATION,
                        code: ErrorCode.INVALID_INPUT
                    });
                }
                // Create push notification service
                const pushService = new push_notification_service_1.PushNotificationService();
                // Delete subscription
                const success = await pushService.deleteSubscription(endpoint);
                // Return success
                return res.status(200).json({
                    success,
                    message: success ? "Subscription deleted successfully" : "Subscription not found"
                });
            }
            catch (error) {
                if (error instanceof AppError_1.AppError) {
                    throw error;
                }
                throw new AppError_1.AppError({
                    message: "Failed to delete subscription",
                    type: ErrorType.INTERNAL,
                    code: ErrorCode.INTERNAL_SERVER_ERROR
                });
            }
        });
        /**
         * Send test push notification
         */
        this.sendTest = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Get user ID and merchant ID
                const userId = req.user?.id;
                const merchantId = req.user?.merchantId;
                // Check if user is authenticated
                if (!userId) {
                    throw new AppError_1.AppError({
                        message: "Unauthorized",
                        type: ErrorType.AUTHENTICATION,
                        code: ErrorCode.INVALID_CREDENTIALS
                    });
                }
                // Create push notification service
                const pushService = new push_notification_service_1.PushNotificationService();
                // Send test notification
                let success = false;
                if (merchantId) {
                    // Send to merchant
                    success = await pushService.sendNotificationToMerchant(merchantId, "Test Notification", "This is a test push notification from AmazingPay.", "/logo.png", { test: true }, "/");
                }
                else {
                    // Send to user
                    success = await pushService.sendNotificationToUser(userId, "Test Notification", "This is a test push notification from AmazingPay.", "/logo.png", { test: true }, "/");
                }
                // Return success
                return res.status(200).json({
                    success,
                    message: success
                        ? "Test notification sent successfully"
                        : "Failed to send test notification"
                });
            }
            catch (error) {
                if (error instanceof AppError_1.AppError) {
                    throw error;
                }
                throw new AppError_1.AppError({
                    message: "Failed to send test notification",
                    type: ErrorType.INTERNAL,
                    code: ErrorCode.INTERNAL_SERVER_ERROR
                });
            }
        });
        /**
         * Get user's push notification subscriptions
         */
        this.getUserSubscriptions = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Get user ID
                const userId = req.user?.id;
                // Check if user is authenticated
                if (!userId) {
                    throw new AppError_1.AppError({
                        message: "Unauthorized",
                        type: ErrorType.AUTHENTICATION,
                        code: ErrorCode.INVALID_CREDENTIALS
                    });
                }
                // Get user subscriptions
                const subscriptions = await prisma_1.default.pushSubscription.findMany({
                    where: {
                        userId,
                        active: true
                    },
                    select: { id: true,
                        endpoint: true,
                        deviceInfo: true,
                        createdAt: true,
                        updatedAt: true
                    }
                });
                // Return subscriptions
                return res.status(200).json({
                    success: true,
                    data: { subscriptions }
                });
            }
            catch (error) {
                if (error instanceof AppError_1.AppError) {
                    throw error;
                }
                throw new AppError_1.AppError({
                    message: "Failed to get user subscriptions",
                    type: ErrorType.INTERNAL,
                    code: ErrorCode.INTERNAL_SERVER_ERROR
                });
            }
        });
        /**
         * Get merchant's push notification subscriptions
         */
        this.getMerchantSubscriptions = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Get user role and merchant ID
                const userRole = req.user?.role;
                const userId = req.user?.id;
                const userMerchantId = req.user?.merchantId;
                // Check if user is authenticated
                if (!userId) {
                    throw new AppError_1.AppError({
                        message: "Unauthorized",
                        type: ErrorType.AUTHENTICATION,
                        code: ErrorCode.INVALID_CREDENTIALS
                    });
                }
                // Get merchant ID from query or user
                let merchantId = req.query.merchantId;
                // If user is not admin, they can only get their own merchant's subscriptions
                if (userRole !== "ADMIN") {
                    // Only admins can access this endpoint with a different merchant ID
                    this.checkAdminRole(userRole);
                    merchantId = userMerchantId;
                }
                // Validate merchant ID
                if (!merchantId) {
                    throw new AppError_1.AppError({
                        message: "Merchant ID is required",
                        type: ErrorType.VALIDATION,
                        code: ErrorCode.MISSING_REQUIRED_FIELD
                    });
                }
                // Get merchant subscriptions
                const subscriptions = await prisma_1.default.pushSubscription.findMany({
                    where: {
                        merchantId,
                        active: true
                    },
                    select: { id: true,
                        endpoint: true,
                        deviceInfo: true,
                        createdAt: true,
                        updatedAt: true
                    }
                });
                // Return subscriptions
                return res.status(200).json({
                    success: true,
                    data: { subscriptions }
                });
            }
            catch (error) {
                if (error instanceof AppError_1.AppError) {
                    throw error;
                }
                throw new AppError_1.AppError({
                    message: "Failed to get merchant subscriptions",
                    type: ErrorType.INTERNAL,
                    code: ErrorCode.INTERNAL_SERVER_ERROR
                });
            }
        });
        /**
         * Delete a push notification subscription
         */
        this.deleteSubscription = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            try {
                // Get user ID and role
                const userId = req.user?.id;
                const userRole = req.user?.role;
                const merchantId = req.user?.merchantId;
                // Check if user is authenticated
                if (!userId || !userRole) {
                    throw new AppError_1.AppError({
                        message: "Unauthorized",
                        type: ErrorType.AUTHENTICATION,
                        code: ErrorCode.INVALID_CREDENTIALS
                    });
                }
                // Get subscription ID
                const subscriptionId = req.params.id;
                // Validate subscription ID
                if (!subscriptionId) {
                    throw new AppError_1.AppError({
                        message: "Subscription ID is required",
                        type: ErrorType.VALIDATION,
                        code: ErrorCode.MISSING_REQUIRED_FIELD
                    });
                }
                // Get subscription
                const subscription = await prisma_1.default.pushSubscription.findUnique({
                    where: { id: subscriptionId }
                });
                // Check if subscription exists
                if (!subscription) {
                    throw new AppError_1.AppError({
                        message: "Subscription not found",
                        type: ErrorType.NOT_FOUND,
                        code: ErrorCode.RESOURCE_NOT_FOUND
                    });
                }
                // Check if user is authorized to delete this subscription
                if (userRole !== "ADMIN" && subscription.id // Fixed: using id instead of userId !== userId && subscription.merchantId !== merchantId) {
                )
                    throw new AppError_1.AppError({
                        message: "Unauthorized to delete this subscription",
                        type: ErrorType.AUTHORIZATION,
                        code: ErrorCode.FORBIDDEN
                    });
            }
            // Delete subscription
            finally {
            }
            // Delete subscription
            await prisma_1.default.pushSubscription.update({
                where: { id: subscriptionId },
                data: { active: false }
            });
            // Return success
            return res.status(200).json({
                success: true,
                message: "Subscription deleted successfully"
            });
        });
    }
    catch(error) {
        if (error instanceof AppError_1.AppError) {
            throw error;
        }
        throw new AppError_1.AppError({
            message: "Failed to delete subscription",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
    }
}
exports.PushNotificationController = PushNotificationController;
;
exports.default = new PushNotificationController();
//# sourceMappingURL=push-notification.controller.js.map