/**
 * Test Utils Module
 *
 * Centralized exports for the test utility system.
 */
export * from './core/TestTypes';
export * from './factories/MockFactories';
export * from './runners/TestRunners';
export * from './suites/TestSuiteBuilders';
export * from './generators/DataGenerators';
export * from './assertions/CustomAssertions';
export { createMockRequest, createMockResponse, createMockNext, createMockPrismaClient, createMockJwtToken, createMockApiResponse, createMockErrorResponse, } from './factories/MockFactories';
export { testController, testService, testRepository, testMiddleware } from './runners/TestRunners';
export { createControllerTestSuite, createServiceTestSuite, createRepositoryTestSuite, createIntegrationTestSuite, createPerformanceTestSuite, createApiTestSuite, } from './suites/TestSuiteBuilders';
export { generateUUID, generateEmail, generateRandomString, generateMockUser, generateMockMerchant, generateMockTransaction, generateMockArray, generateMockDataWithRelationships, } from './generators/DataGenerators';
export { setupCustomMatchers, AssertionHelpers, DatabaseAssertions, } from './assertions/CustomAssertions';
export declare class TestUtils {
    /**
     * Setup test environment
     */
    static setup(): void;
    /**
     * Create a complete test context
     */
    static createTestContext(name: string): {
        mockRequest: unknown;
        mockResponse: unknown;
        mockNext: unknown;
        mockPrisma: unknown;
        mockUser: unknown;
        mockMerchant: unknown;
        cleanup: () => void;
    };
    /**
     * Create a test database context
     */
    static createDatabaseTestContext(): {
        mockPrisma: unknown;
        seedData: () => Promise<void>;
        cleanup: () => Promise<void>;
    };
    /**
     * Create an API test context
     */
    static createApiTestContext(baseUrl?: string): {
        request: unknown;
        authenticate: (token: string) => void;
        expectSuccess: (response: unknown) => void;
        expectError: (response: unknown, status?: number) => void;
    };
}
//# sourceMappingURL=index.d.ts.map