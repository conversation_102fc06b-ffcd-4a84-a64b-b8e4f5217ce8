2f61614798a6355a1f655663ffb7158d
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.createLogger = exports.addRequestId = exports.stream = exports.logger = void 0;
// jscpd:ignore-file
const winston = __importStar(require("winston"));
const path = __importStar(require("path"));
const fs = __importStar(require("fs"));
const date_fns_1 = require("date-fns");
// Get environment-specific logs directory
const getLogsDir = () => {
    const env = process.env.NODE_ENV || "development";
    const logsDir = path.join(process.cwd(), "logs", env);
    try {
        // Ensure logs directory exists
        if (!fs.existsSync(logsDir)) {
            fs.mkdirSync(logsDir, { recursive: true });
        }
    }
    catch (error) {
        // Fallback to a temporary directory if we can't create the logs directory
        console.error('Error creating logs directory:', error);
        return path.join(process.cwd(), 'temp');
    }
    return logsDir;
};
// Get the logs directory
const logsDir = getLogsDir();
// Define log levels
const levels = {
    error: 0,
    warn: 1,
    info: 2,
    http: 3,
    debug: 4
};
// Define log level based on environment
const getLogLevel = () => {
    const env = process.env.NODE_ENV || "development";
    if (process.env.LOG_LEVEL) {
        return process.env.LOG_LEVEL.toLowerCase();
    }
    switch (env) {
        case "production":
            return "info";
        case "test":
            return "warn";
        case "development":
        default:
            return "debug";
    }
};
// Define colors for each level
const colors = {
    error: "red",
    warn: "yellow",
    info: "green",
    http: "magenta",
    debug: "blue"
};
// Add colors to winston
winston.addColors(colors);
// Create a custom format for console output
const consoleFormat = winston.format.combine(winston.format.timestamp({ format: "YYYY-MM-DD, HH:mm:ss:ms" }), winston.format.colorize({ all: true }), winston.format.printf((info) => {
    const { timestamp, level, message, ...rest } = info;
    const metaData = Object.keys(rest).length ? JSON.stringify(rest, null, 2) : "";
    return `${timestamp} ${level}: ${message} ${metaData}`;
}));
// Create a custom format for file output (JSON)
const fileFormat = winston.format.combine(winston.format.timestamp(), winston.format.json());
// Generate date-based log filenames
const getLogFileName = (level) => {
    const date = (0, date_fns_1.format)(new Date(), "yyyy-MM-dd");
    return path.join(logsDir, `${level}-${date}.log`);
};
// Define where to store logs
const transports = [
    // Console transport
    new winston.transports.Console({
        format: consoleFormat
    }),
    // Error log file transport
    new winston.transports.File({
        filename: getLogFileName("error"),
        level: "error",
        format: fileFormat,
        maxsize: 10 * 1024 * 1024, // 10MB
        maxFiles: 30
    }),
    // All logs file transport
    new winston.transports.File({
        filename: getLogFileName("combined"),
        format: fileFormat,
        maxsize: 10 * 1024 * 1024, // 10MB
        maxFiles: 30
    })
];
// Create the logger
exports.logger = winston.createLogger({
    level: getLogLevel(),
    levels,
    transports,
    exitOnError: false,
    handleExceptions: true,
    handleRejections: true
});
// Add exception handlers
exports.logger.exceptions.handle(new winston.transports.File({
    filename: getLogFileName("exceptions"),
    format: fileFormat,
    maxsize: 10 * 1024 * 1024, // 10MB
    maxFiles: 30
}));
// Add rejection handlers
exports.logger.rejections.handle(new winston.transports.File({
    filename: getLogFileName("rejections"),
    format: fileFormat,
    maxsize: 10 * 1024 * 1024, // 10MB
    maxFiles: 30
}));
// Export a stream object for Morgan middleware
exports.stream = {
    write: (message) => {
        exports.logger.http(message.trim());
    }
};
// Add request ID to log context
const addRequestId = (requestId) => {
    return exports.logger.child({ requestId });
};
exports.addRequestId = addRequestId;
// Create a logger with context
const createLogger = (context) => {
    return exports.logger.child(context);
};
exports.createLogger = createLogger;
// Log startup information
exports.logger.info(`Logger initialized with level: ${getLogLevel()}`);
exports.logger.info(`Environment: ${process.env.NODE_ENV || "development"}`);
exports.logger.info(`Operational mode: ${process.env.OPERATIONAL_MODE || "development"}`);
exports.logger.info(`Log files directory: ${logsDir}`);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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