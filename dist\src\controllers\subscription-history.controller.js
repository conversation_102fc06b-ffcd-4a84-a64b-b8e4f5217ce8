"use strict";
// jscpd:ignore-file
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const subscription_service_1 = __importDefault(require("../services/subscription.service"));
const date_fns_1 = require("date-fns");
const logger_1 = require("../utils/logger");
class SubscriptionHistoryController {
    /**
     * Get subscription history for a merchant
     * @param req Request
     * @param res Response
     */
    async getSubscriptionHistory(req, res) {
        try {
            const { merchantId } = req.params;
            const { startDate, endDate } = req.query;
            // Authorization check - ensure user can only view their own subscription history
            if (req.user?.role !== "admin" && req.user?.merchantId !== merchantId) {
                return res.status(403).json({
                    status: "error",
                    message: "You are not authorized to view this merchant's subscription history"
                });
            }
            // Parse dates if provided
            const parsedStartDate = startDate ? new Date(startDate) : undefined;
            const parsedEndDate = endDate ? new Date(endDate) : undefined;
            // Get subscription history
            const history = await subscription_service_1.default.getSubscriptionHistory(merchantId, parsedStartDate, parsedEndDate);
            return res.status(200).json({
                status: "success",
                data: history
            });
        }
        catch (error) {
            logger_1.logger.error("Subscription history error:", error);
            return res.status(500).json({
                status: "error",
                message: error.message || "Failed to retrieve subscription history"
            });
        }
    }
    /**
     * Download subscription history as CSV
     * @param req Request
     * @param res Response
     */
    async downloadSubscriptionHistory(req, res) {
        try {
            const { merchantId } = req.params;
            const { startDate, endDate } = req.query;
            // Authorization check - ensure user can only download their own subscription history
            if (req.user?.role !== "admin" && req.user?.merchantId !== merchantId) {
                return res.status(403).json({
                    status: "error",
                    message: "You are not authorized to download this merchant's subscription history"
                });
            }
            // Parse dates if provided
            const parsedStartDate = startDate ? new Date(startDate) : undefined;
            const parsedEndDate = endDate ? new Date(endDate) : undefined;
            // Get subscription history
            const history = await subscription_service_1.default.getSubscriptionHistory(merchantId, parsedStartDate, parsedEndDate);
            // Convert to CSV
            const csvHeader = "Date,Plan,Amount,Status\n";
            const csvRows = history.map(item);
            {
                const date = (0, date_fns_1.format)(new Date(item.date), "yyyy-MM-dd");
                return `${date},${item.planName},${item.amount},${item.status}`;
            }
            ;
            const csvData = csvHeader + csvRows.join("\n");
            res.setHeader("Content-Type", "text/csv");
            res.setHeader("Content-Disposition", `attachment; filename = subscription-history-${merchantId}.csv`);
            // Send the CSV data
            return res.send(csvData);
        }
        catch (error) {
            logger_1.logger.error("Download error:", error);
            return res.status(500).json({
                status: "error",
                message: error.message || "Failed to download subscription history"
            });
        }
    }
}
exports.default = new SubscriptionHistoryController();
//# sourceMappingURL=subscription-history.controller.js.map