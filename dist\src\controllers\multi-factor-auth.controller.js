"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.disableMFA = exports.enableMFA = exports.getUserVerificationMethods = void 0;
const multi_factor_auth_service_1 = require("../services/multi-factor-auth.service");
const asyncHandler_1 = require("../utils/asyncHandler");
const app_error_1 = require("../utils/app-error");
const multiFactorAuthService = new multi_factor_auth_service_1.MultiFactorAuthService();
/**
 * Get user's verification methods
 */
exports.getUserVerificationMethods = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    // Get user ID from authenticated user
    const userId = req.user?.id; // Fixed: using id instead of userId;
    if (!userId) {
        throw new app_error_1.AppError({
            message: "User ID is required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }
    // Get verification methods
    const methods = await multiFactorAuthService.getUserVerificationMethods(userId);
    // Return methods
    res.status(200).json({
        success: true,
        data: methods,
        message: "User verification methods retrieved successfully"
    });
});
/**
 * Enable MFA for a user
 */
exports.enableMFA = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    const { verificationIds } = req.body;
    // Validate required fields
    if (!verificationIds || !Array.isArray(verificationIds) || verificationIds.length < 2) {
        throw new app_error_1.AppError({
            message: "At least two verification IDs are required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }
    // Get user ID from authenticated user
    const userId = req.user?.id; // Fixed: using id instead of userId;
    if (!userId) {
        throw new app_error_1.AppError({
            message: "User ID is required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }
    // Enable MFA
    const result = await multiFactorAuthService.enableMFA(userId, verificationIds);
    // Return result
    res.status(200).json({
        success: true,
        data: result,
        message: "MFA enabled successfully"
    });
});
/**
 * Disable MFA for a user
 */
exports.disableMFA = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    // Get user ID from authenticated user
    const userId = req.user?.id; // Fixed: using id instead of userId;
    if (!userId) {
        throw new app_error_1.AppError({
            message: "User ID is required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }
    // Disable MFA
    const result = await multiFactorAuthService.disableMFA(userId);
    // Return result
    res.status(200).json({
        success: true,
        data: result,
        message: "MFA disabled successfully"
    });
});
//# sourceMappingURL=multi-factor-auth.controller.js.map