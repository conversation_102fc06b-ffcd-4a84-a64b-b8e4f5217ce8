// jscpd:ignore-file
/**
 * Payment Routing Service
 *
 * This service provides intelligent payment method routing based on success rates,
 * costs, and other factors to optimize payment processing.
 */

import { BaseService, ServiceError } from './base.service';
import { PaymentMethod, Transaction, Merchant } from '../types';
import { logger } from '../lib/logger';
import { ApiErrorCode } from '../middlewares/apiResponseMiddleware';
import { TransactionService } from './transaction.service';
import { PaymentMethodService } from './payment-method.service';
import { MerchantService } from './merchant.service';
import prisma from '../lib/prisma';

/**
 * Routing rule type
 */
export enum RoutingRuleType {
  SUCCESS_RATE = 'SUCCESS_RATE',
  COST = 'COST',
  REGION = 'REGION',
  AMOUNT = 'AMOUNT',
  MERCHANT_PREFERENCE = 'MERCHANT_PREFERENCE',
  CUSTOM = 'CUSTOM',
}

/**
 * Routing rule operator
 */
export enum RoutingRuleOperator {
  EQUALS = 'EQUALS',
  NOT_EQUALS = 'NOT_EQUALS',
  GREATER_THAN = 'GREATER_THAN',
  LESS_THAN = 'LESS_THAN',
  CONTAINS = 'CONTAINS',
  NOT_CONTAINS = 'NOT_CONTAINS',
}

/**
 * Routing rule
 */
export interface RoutingRule {
  /**
   * Rule type
   */
  type: RoutingRuleType;

  /**
   * Rule operator
   */
  operator: RoutingRuleOperator;

  /**
   * Rule value
   */
  value: any;

  /**
   * Payment method IDs to use if rule matches
   */
  paymentMethodIds: string[];

  /**
   * Priority (lower number = higher priority)
   */
  priority: number;
}

/**
 * Payment method with metrics
 */
export interface PaymentMethodWithMetrics extends PaymentMethod {
  /**
   * Success rate (0-100)
   */
  successRate: number;

  /**
   * Average processing time in seconds
   */
  avgProcessingTime: number;

  /**
   * Cost per transaction (percentage)
   */
  costPerTransaction: number;

  /**
   * Transaction count
   */
  transactionCount: number;
}

/**
 * Routing context
 */
export interface RoutingContext {
  /**
   * Merchant
   */
  merchant: Merchant;

  /**
   * Transaction amount
   */
  amount: number;

  /**
   * Currency
   */
  currency: string;

  /**
   * Country code
   */
  country?: string;

  /**
   * IP address
   */
  ipAddress?: string;

  /**
   * Device type
// jscpd:ignore-end
   */
  deviceType?: string;

  /**
   * Custom parameters
   */
  customParams?: Record<string, any>;
}

/**
 * Routing result
 */
export interface RoutingResult {
  /**
   * Recommended payment method
   */
  recommendedPaymentMethod: PaymentMethod;

  /**
   * Alternative payment methods in order of preference
   */
  alternativePaymentMethods: PaymentMethod[];

  /**
   * Routing rule that matched
   */
  matchedRule?: RoutingRule;

  /**
   * Routing score for each payment method (higher is better)
   */
  scores: Record<string, number>;
}

/**
 * Payment routing service
 */
export class PaymentRoutingService extends BaseService {
  private transactionService: TransactionService;
  private paymentMethodService: PaymentMethodService;
  private merchantService: MerchantService;
  private prisma = prisma;

  constructor() {
    super();
    this.transactionService = new TransactionService();
    this.paymentMethodService = new PaymentMethodService();
    this.merchantService = new MerchantService();
  }

  /**
   * Create a generic service error
   * @param message Error message
   * @param statusCode HTTP status code
   * @param errorCode API error code
   * @returns Service error
   */
  private genericError(message: string, statusCode: number, errorCode: ApiErrorCode): ServiceError {
    return new ServiceError(message, statusCode, errorCode);
  }

  /**
   * Get optimal payment method for a transaction
   * @param merchantId Merchant ID
   * @param amount Transaction amount
   * @param currency Currency
   * @param country Country code
   * @param ipAddress IP address
   * @param deviceType Device type
   * @param customParams Custom parameters
   * @returns Routing result
   */
  async getOptimalPaymentMethod(
    merchantId: string,
    amount: number,
    currency: string,
    country?: string,
    ipAddress?: string,
    deviceType?: string,
    customParams?: Record<string, any>
  ): Promise<RoutingResult> {
    try {
      // Get merchant
      const merchant: unknown = await this.merchantService.getMerchantById(merchantId);

      if (!merchant) {
        throw this.genericError('Merchant not found', 404, ApiErrorCode.NOT_FOUND);
      }
      const paymentMethods: unknown = await this.paymentMethodService.getMerchantPaymentMethods(
        merchantId
      );

      if (!paymentMethods ?? paymentMethods.length === 0) {
        throw this.genericError(
          'No payment methods available for this merchant',
          400,
          ApiErrorCode.BAD_REQUEST
        );
      }

      // Get payment method metrics
      const paymentMethodsWithMetrics: unknown = await this.getPaymentMethodMetrics(paymentMethods);

      // Create routing context
      const context: RoutingContext = {
        merchant,
        amount,
        currency,
        country,
        ipAddress,
        deviceType,
        customParams,
      };

      // Get routing rules
      const rules: unknown = await this.getRoutingRules(merchantId);

      // Apply routing rules
      const matchedRule: unknown = this.findMatchingRule(rules, context);

      if (matchedRule) {
        // Get payment methods from matched rule
        const rulePaymentMethods: unknown = paymentMethodsWithMetrics.filter((pm) =>
          matchedRule.paymentMethodIds.includes(pm.id)
        );

        if (rulePaymentMethods.length > 0) {
          // Sort by success rate
          rulePaymentMethods.sort((a, b) => b.successRate - a.successRate);

          return {
            recommendedPaymentMethod: rulePaymentMethods[0],
            alternativePaymentMethods: rulePaymentMethods.slice(1),
            matchedRule,
            scores: this.calculateScores(rulePaymentMethods, context),
          };
        }
      }

      // No matching rule or no payment methods in rule, use scoring
      const scores: Response = this.calculateScores(paymentMethodsWithMetrics, context);

      // Sort payment methods by score
      const sortedPaymentMethods: unknown = [...paymentMethodsWithMetrics].sort(
        (a, b) => scores[b.id] - scores[a.id]
      );

      return {
        recommendedPaymentMethod: sortedPaymentMethods[0],
        alternativePaymentMethods: sortedPaymentMethods.slice(1),
        scores,
      };
    } catch (error) {
      if (error instanceof ServiceError) {
        throw error;
      }
      logger.error('Error getting optimal payment method:', error);
      throw this.genericError(
        'Failed to get optimal payment method',
        500,
        ApiErrorCode.SERVER_ERROR
      );
    }
  }

  /**
   * Record routing decision
   * @param transactionId Transaction ID
   * @param recommendedPaymentMethodId Recommended payment method ID
   * @param selectedPaymentMethodId Selected payment method ID
   * @param matchedRuleId Matched rule ID
   * @param scores Routing scores
   * @returns Created routing decision
   */
  async recordRoutingDecision(
    transactionId: string,
    recommendedPaymentMethodId: string,
    selectedPaymentMethodId: string,
    matchedRuleId?: string,
    scores?: Record<string, number>
  ): Promise<any> {
    try {
      return await this.prisma.paymentRoutingDecision.create({
        data: {
          transactionId,
          recommendedPaymentMethodId,
          selectedPaymentMethodId,
          matchedRuleId,
          scores: scores ? JSON.stringify(scores) : null,
        },
      });
    } catch (error) {
      logger.error('Error recording routing decision:', error);
      throw this.genericError('Failed to record routing decision', 500, ApiErrorCode.SERVER_ERROR);
    }
  }

  /**
   * Create routing rule
   * @param merchantId Merchant ID
   * @param rule Routing rule
   * @returns Created rule
   */
  async createRoutingRule(merchantId: string, rule: RoutingRule): Promise<any> {
    try {
      // Validate payment method IDs
      const paymentMethods: unknown = await this.paymentMethodService.getMerchantPaymentMethods(
        merchantId
      );
      const validPaymentMethodIds: unknown = paymentMethods.map((pm) => pm.id);

      const invalidPaymentMethodIds: unknown = rule.paymentMethodIds.filter(
        (id) => !validPaymentMethodIds.includes(id)
      );

      if (invalidPaymentMethodIds.length > 0) {
        throw this.genericError(
          `Invalid payment method IDs: ${invalidPaymentMethodIds.join(', ')}`,
          400,
          ApiErrorCode.BAD_REQUEST
        );
      }

      // Create rule
      return await this.prisma.paymentRoutingRule.create({
        data: {
          merchantId,
          type: rule.type,
          operator: rule.operator,
          value: JSON.stringify(rule.value),
          paymentMethodIds: rule.paymentMethodIds,
          priority: rule.priority,
        },
      });
    } catch (error) {
      if (error instanceof ServiceError) {
        throw error;
      }
      logger.error('Error creating routing rule:', error);
      throw this.genericError('Failed to create routing rule', 500, ApiErrorCode.SERVER_ERROR);
    }
  }

  /**
   * Get routing rules for merchant
   * @param merchantId Merchant ID
   * @returns List of routing rules
   */
  async getRoutingRules(merchantId: string): Promise<RoutingRule[]> {
    try {
      const rules: unknown = await this.prisma.paymentRoutingRule.findMany({
        where: { merchantId },
        orderBy: { priority: 'asc' },
      });

      return rules.map((rule) => ({
        type: rule.type as RoutingRuleType,
        operator: rule.operator as RoutingRuleOperator,
        value: JSON.parse(rule.value),
        paymentMethodIds: rule.paymentMethodIds,
        priority: rule.priority,
      }));
    } catch (error) {
      logger.error('Error getting routing rules:', error);
      throw this.genericError('Failed to get routing rules', 500, ApiErrorCode.SERVER_ERROR);
    }
  }

  /**
   * Get payment method metrics
   * @param paymentMethods Payment methods
   * @returns Payment methods with metrics
   */
  private async getPaymentMethodMetrics(
    paymentMethods: PaymentMethod[]
  ): Promise<PaymentMethodWithMetrics[]> {
    try {
      const result: PaymentMethodWithMetrics[] = [];

      for (const paymentMethod of paymentMethods) {
        // Get transactions for this payment method
        const transactions: unknown = await this.prisma.transaction.findMany({
          where: { paymentMethodId: paymentMethod.id },
          orderBy: { createdAt: 'desc' },
          take: 100, // Consider last 100 transactions
        });

        // Calculate metrics
        const transactionCount: unknown = transactions.length;

        if (transactionCount === 0) {
          // No transactions, use default values
          result.push({
            ...paymentMethod,
            successRate: 100, // Assume 100% success rate for new payment methods
            avgProcessingTime: 0,
            costPerTransaction: this.getPaymentMethodCost(paymentMethod.type),
            transactionCount: 0,
          });
        } else {
          // Calculate success rate
          const successfulTransactions: unknown = transactions.filter((t) => t.status === 'COMPLETED');
          const successRate: unknown = (successfulTransactions.length / transactionCount) * 100;

          // Calculate average processing time
          const completedTransactionsWithTimes: unknown = successfulTransactions.filter(
            (t) => t.completedAt && t.createdAt
          );

          let avgProcessingTime: number = 0;
          if (completedTransactionsWithTimes.length > 0) {
            const totalProcessingTime: unknown = completedTransactionsWithTimes.reduce((total, t) => {
              const processingTime: Date =
                new Date(t.completedAt!).getTime() - new Date(t.createdAt).getTime();
              return total + processingTime / 1000; // Convert to seconds
            }, 0);
            avgProcessingTime = totalProcessingTime / completedTransactionsWithTimes.length;
          }

          // Get cost per transaction
          const costPerTransaction: unknown = this.getPaymentMethodCost(paymentMethod.type);

          result.push({
            ...paymentMethod,
            successRate,
            avgProcessingTime,
            costPerTransaction,
            transactionCount,
          });
        }
      }

      return result;
    } catch (error) {
      logger.error('Error getting payment method metrics:', error);
      throw this.genericError(
        'Failed to get payment method metrics',
        500,
        ApiErrorCode.SERVER_ERROR
      );
    }
  }

  /**
   * Get payment method cost
   * @param paymentMethodType Payment method type
   * @returns Cost percentage
   */
  private getPaymentMethodCost(paymentMethodType: string): number {
    // These would typically come from a configuration or database
    switch (paymentMethodType) {
      case 'BINANCE_PAY':
        return 1.0; // 1.0%
      case 'BINANCE_C2C':
        return 0.8; // 0.8%
      case 'BINANCE_TRC20':
        return 0.5; // 0.5%
      case 'CRYPTO_TRANSFER':
        return 0.3; // 0.3%
      default:
        return 1.0; // Default 1.0%
    }
  }

  /**
   * Find matching routing rule
   * @param rules Routing rules
   * @param context Routing context
   * @returns Matching rule or undefined
   */
  private findMatchingRule(rules: RoutingRule[], context: RoutingContext): RoutingRule | undefined {
    for (const rule of rules) {
      if (this.ruleMatches(rule, context)) {
        return rule;
      }
    }
    return undefined;
  }

  /**
   * Check if rule matches context
   * @param rule Routing rule
   * @param context Routing context
   * @returns Whether rule matches
   */
  private ruleMatches(rule: RoutingRule, context: RoutingContext): boolean {
    switch (rule.type) {
      case RoutingRuleType.AMOUNT:
        return this.compareValues(context.amount, rule.operator, rule.value);

      case RoutingRuleType.REGION:
        return this.compareValues(context.country, rule.operator, rule.value);

      case RoutingRuleType.MERCHANT_PREFERENCE:
        // Check if merchant has preference set
        return (
          context.merchant.preferredPaymentMethodId !== null &&
          rule.paymentMethodIds.includes(context.merchant.preferredPaymentMethodId)
        );

      case RoutingRuleType.CUSTOM:
        // Check custom parameters
        if (!context.customParams) return false;

        const { param, value } = rule.value;
        return this.compareValues(context.customParams[param], rule.operator, value);

      default:
        return false;
    }
  }

  /**
   * Compare values using operator
   * @param a First value
   * @param operator Operator
   * @param b Second value
   * @returns Comparison result
   */
  private compareValues(a: unknown, operator: RoutingRuleOperator, b: unknown): boolean {
    switch (operator) {
      case RoutingRuleOperator.EQUALS:
        return a === b;

      case RoutingRuleOperator.NOT_EQUALS:
        return a !== b;

      case RoutingRuleOperator.GREATER_THAN:
        return a > b;

      case RoutingRuleOperator.LESS_THAN:
        return a < b;

      case RoutingRuleOperator.CONTAINS:
        return Array.isArray(a) ? a.includes(b) : String(a).includes(String(b));

      case RoutingRuleOperator.NOT_CONTAINS:
        return Array.isArray(a) ? !a.includes(b) : !String(a).includes(String(b));

      default:
        return false;
    }
  }

  /**
   * Calculate scores for payment methods
   * @param paymentMethods Payment methods with metrics
   * @param context Routing context
   * @returns Scores by payment method ID
   */
  private calculateScores(
    paymentMethodsWithMetrics: PaymentMethodWithMetrics[],
    context: RoutingContext
  ): Record<string, number> {
    const scores: Record<string, number> = {};

    // Weights for different factors
    const weights: unknown = {
      successRate: 0.5,
      processingTime: 0.2,
      cost: 0.3,
    };

    // Calculate scores
    for (const pm of paymentMethodsWithMetrics) {
      // Success rate score (higher is better)
      const successRateScore: unknown = pm.successRate;

      // Processing time score (lower is better)
      const processingTimeScore: unknown =
        pm.avgProcessingTime === 0 ? 100 : 100 / (1 + pm.avgProcessingTime / 10);

      // Cost score (lower is better)
      const costScore: number = 100 - pm.costPerTransaction * 10;

      // Calculate weighted score
      scores[pm.id] =
        successRateScore * weights.successRate +
        processingTimeScore * weights.processingTime +
        costScore * weights.cost;

      // Adjust score based on merchant preference
      if (context.merchant.preferredPaymentMethodId === pm.id) {
        scores[pm.id] *= 1.2; // 20% bonus for preferred payment method
      }
    }

    return scores;
  }
}
