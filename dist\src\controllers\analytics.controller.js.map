{"version": 3, "file": "analytics.controller.js", "sourceRoot": "", "sources": ["../../../src/controllers/analytics.controller.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,4CAAyC;AAezC,6GAGyD;AAKzD;;GAEG;AACH,MAAM,mBAAmB,GAAQ;IAC7B;;OAEG;IACH,mBAAmB,EAAE,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;QACvD,IAAI,CAAC;YACD,0BAA0B;YAC1B,MAAM,MAAM,GAAoB,EAAE,CAAC;YAEnC,IAAI,GAAG,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;gBACvB,MAAM,CAAC,UAAU,GAAG,GAAG,CAAC,KAAK,CAAC,UAAoB,CAAC;YACvD,CAAC;YAED,IAAI,GAAG,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;gBACtB,MAAM,CAAC,SAAS,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,SAAmB,CAAC,CAAC;YAC/D,CAAC;YAED,IAAI,GAAG,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;gBACpB,MAAM,CAAC,OAAO,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,OAAiB,CAAC,CAAC;YAC3D,CAAC;YAED,IAAI,GAAG,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;gBACnB,MAAM,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,MAAyB,CAAC;YACxD,CAAC;iBAAM,CAAC;gBACJ,MAAM,CAAC,MAAM,GAAG,2CAAe,CAAC,KAAK,CAAC,CAAC,mBAAmB;YAC9D,CAAC;YAED,MAAM,SAAS,GAAO,MAAM,mCAAuB,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;YAChF,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC3C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YACxD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,iCAAiC,EAAE,CAAC,CAAC;QAC9E,CAAC;IACL,CAAC;IAED;;OAEG;IACH,yBAAyB,EAAE,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;QAC7D,IAAI,CAAC;YACD,MAAM,EAAE,iBAAiB,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;YAEzC,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBACrB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,iCAAiC,EAAE,CAAC,CAAC;YAC9E,CAAC;YAED,0BAA0B;YAC1B,MAAM,MAAM,GAAoB,EAAE,CAAC;YAEnC,IAAI,GAAG,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;gBACvB,MAAM,CAAC,UAAU,GAAG,GAAG,CAAC,KAAK,CAAC,UAAoB,CAAC;YACvD,CAAC;YAED,IAAI,GAAG,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;gBACtB,MAAM,CAAC,SAAS,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,SAAmB,CAAC,CAAC;YAC/D,CAAC;YAED,IAAI,GAAG,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;gBACpB,MAAM,CAAC,OAAO,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,OAAiB,CAAC,CAAC;YAC3D,CAAC;YAED,IAAI,GAAG,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;gBACnB,MAAM,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,MAAyB,CAAC;YACxD,CAAC;iBAAM,CAAC;gBACJ,MAAM,CAAC,MAAM,GAAG,2CAAe,CAAC,KAAK,CAAC,CAAC,mBAAmB;YAC9D,CAAC;YAED,MAAM,SAAS,GAAO,MAAM,mCAAuB,CAAC,yBAAyB,CACzE,iBAAsC,EACtC,MAAM,CACT,CAAC;YAEF,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC3C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,yCAAyC,EAAE,KAAK,CAAC,CAAC;YAC/D,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,wCAAwC,EAAE,CAAC,CAAC;QACrF,CAAC;IACL,CAAC;IAED;;OAEG;IACH,oBAAoB,EAAE,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;QACxD,IAAI,CAAC;YACD,MAAM,EAAE,UAAU,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;YAElC,IAAI,CAAC,UAAU,EAAE,CAAC;gBACd,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,yBAAyB,EAAE,CAAC,CAAC;YACtE,CAAC;YAED,0BAA0B;YAC1B,MAAM,MAAM,GAAoB;gBAC5B,UAAU;aACb,CAAC;YAEF,IAAI,GAAG,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;gBACtB,MAAM,CAAC,SAAS,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,SAAmB,CAAC,CAAC;YAC/D,CAAC;YAED,IAAI,GAAG,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;gBACpB,MAAM,CAAC,OAAO,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,OAAiB,CAAC,CAAC;YAC3D,CAAC;YAED,IAAI,GAAG,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;gBACnB,MAAM,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,MAAyB,CAAC;YACxD,CAAC;iBAAM,CAAC;gBACJ,MAAM,CAAC,MAAM,GAAG,2CAAe,CAAC,KAAK,CAAC,CAAC,mBAAmB;YAC9D,CAAC;YAED,MAAM,SAAS,GAAO,MAAM,mCAAuB,CAAC,oBAAoB,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;YAC7F,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC3C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YACzD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,kCAAkC,EAAE,CAAC,CAAC;QAC/E,CAAC;IACL,CAAC;IAED;;OAEG;IACH,qBAAqB,EAAE,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;QACzD,IAAI,CAAC;YACD,oCAAoC;YACpC,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,IAAoC,CAAC;YAE9D,0BAA0B;YAC1B,MAAM,MAAM,GAAoB,EAAE,CAAC;YAEnC,IAAI,GAAG,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;gBACtB,MAAM,CAAC,SAAS,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,SAAmB,CAAC,CAAC;YAC/D,CAAC;YAED,IAAI,GAAG,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;gBACpB,MAAM,CAAC,OAAO,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,OAAiB,CAAC,CAAC;YAC3D,CAAC;YAED,IAAI,GAAG,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;gBACnB,MAAM,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,MAAyB,CAAC;YACxD,CAAC;iBAAM,CAAC;gBACJ,MAAM,CAAC,MAAM,GAAG,2CAAe,CAAC,KAAK,CAAC,CAAC,mBAAmB;YAC9D,CAAC;YAED,mCAAmC;YACnC,IAAI,SAAS,CAAC;YACd,IAAI,IAAI,KAAK,OAAO,EAAE,CAAC;gBACnB,2BAA2B;gBAC3B,SAAS,GAAG,MAAM,mCAAuB,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;YAC1E,CAAC;iBAAM,IAAI,IAAI,KAAK,UAAU,EAAE,CAAC;gBAC7B,qCAAqC;gBACrC,SAAS,GAAG,MAAM,mCAAuB,CAAC,oBAAoB,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;YAC/E,CAAC;iBAAM,CAAC;gBACJ,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC,CAAC;YAC3D,CAAC;YAED,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC3C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;YAC1D,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,mCAAmC,EAAE,CAAC,CAAC;QAChF,CAAC;IACL,CAAC;CACJ,CAAC;AAEF,kBAAe,mBAAmB,CAAC"}