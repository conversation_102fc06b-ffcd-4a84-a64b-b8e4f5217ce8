// jscpd:ignore-file
import { Request, Response, NextFunction } from "express";
import { v4 as uuidv4 } from "uuid";
import { logger } from "../utils/logger";
import { AppError } from "../utils/errors/AppError";
import { ErrorFactory } from "../utils/errors/ErrorFactory";
import { isDevelopment } from "../utils/environment-validator";
import { v4 as uuidv4 } from "uuid";
import { logger } from "../utils/logger";
import { AppError } from "../utils/errors/AppError";
import { ErrorFactory } from "../utils/errors/ErrorFactory";
import { isDevelopment } from "../utils/environment-validator";

// Extend the Express Request type to include user property
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role: string;
        merchantId?: string;
      };
    }
  }
}

/**
 * Error handler middleware
 * @param err Error object
 * @param req Express request
 * @param res Express response
 * @param next Express next function
 */
export const errorHandler: any = (
  err: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  // Generate unique error ID for tracking
  const errorId = uuidv4();

  // Extract request information for logging
  const requestInfo: any = {
    method: req.method,
    path: req.path,
    ip: req.ip,
    userAgent: req.get("user-agent"),
    requestId: (req as any).requestId || "unknown",
    userId: (req.user as any)?.id || "anonymous",
    errorId
  };

  // Convert error to AppError
  const appError: any = err instanceof AppError
    ? err
    : ErrorFactory.handle(err);

  // Add error ID if not already present
  if (!appError.errorId) {
    (appError as any).errorId = errorId;
  }

  // Log error with appropriate level
  if (appError.isOperational) {
    // Operational errors are expected errors (e.g. validation errors)
    logger.warn(`[${errorId}] ${appError.code}: ${appError.message}`, {
      ...requestInfo,
      statusCode: appError.statusCode,
      details: appError.details || undefined
    });
  } else {
    // Programming or unknown errors need more attention
    logger.error(`[${errorId}] ${appError.code}: ${appError.message}`, {
      ...requestInfo,
      statusCode: appError.statusCode,
      error: err.message,
      stack: err.stack
    });
  }

  // Send response
  res.status(appError.statusCode).json(
    appError.toResponse(isDevelopment())
  );
};

/**
 * Not found middleware
 * @param req Express request
 * @param res Express response
 * @param next Express next function
 */
export const notFoundHandler: any = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const error = ErrorFactory.notFound(`Route ${req.originalUrl}`);
  next(error);
};

/**
 * Async handler to catch errors in async routes
 * @param fn Function to wrap
 * @returns Wrapped function
 */
export const asyncHandler: any = (fn: Function) => (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

// Export all middleware functions as a group
export const errorHandlerMiddleware: any = {
  errorHandler,
  notFoundHandler,
  asyncHandler
};

export default errorHandlerMiddleware;
