import { Request, Response, NextFunction } from 'express';
import { PrismaClient } from '@prisma/client';
import { ReportMonitoringService } from '../services/report-monitoring.service';
import * as fs from 'fs';
import * as path from 'path';

const prisma = new PrismaClient();
const monitoringService = new ReportMonitoringService();

export class HealthController {
  /**
   * Basic health check
   */
  public healthCheck = async (req: Request, res: Response): Promise<void> => {
    try {
      const health = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: process.env.npm_package_version ?? '1.0.0',
        environment: process.env.NODE_ENV ?? 'development',
      };

      res.status(200).json(health);
    } catch (error: Error) {
      res.status(503).json({
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error.message,
      });
    }
  };

  /**
   * Detailed health check including dependencies
   */
  public detailedHealthCheck = async (req: Request, res: Response): Promise<void> => {
    try {
      const checks = await Promise.allSettled([
        this.checkDatabase(),
        this.checkFileSystem(),
        this.checkReportingSystem(),
        this.checkMemoryUsage(),
      ]);

      const results = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        checks: {
          database: this.getCheckResult(checks[0]),
          fileSystem: this.getCheckResult(checks[1]),
          reportingSystem: this.getCheckResult(checks[2]),
          memoryUsage: this.getCheckResult(checks[3]),
        },
      };

      // Determine overall status
      const hasFailures = Object.values(results.checks).some(check => check.status !== 'healthy');
      if (hasFailures) {
        results.status = 'unhealthy';
      }

      const statusCode = results.status === 'healthy' ? 200 : 503;
      res.status(statusCode).json(results);
    } catch (error: Error) {
      res.status(503).json({
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error.message,
      });
    }
  };

  /**
   * Get reporting system metrics
   */
  public getMetrics = async (req: Request, res: Response): Promise<void> => {
    try {
      const timeRange = parseInt(req.query.hours as string) || 24;
      
      const [
        systemHealth,
        performanceStats,
        databaseMetrics,
        fileSystemMetrics,
      ] = await Promise.all([
        monitoringService.getSystemHealth(),
        monitoringService.getPerformanceStats(timeRange),
        monitoringService.getDatabaseMetrics(),
        monitoringService.getFileSystemMetrics(),
      ]);

      res.json({
        success: true,
        data: {
          systemHealth,
          performanceStats,
          databaseMetrics,
          fileSystemMetrics,
          timestamp: new Date().toISOString(),
        },
      });
    } catch (error: Error) {
      console.error('Error getting metrics:', error);
      res.status(500).json({
        success: false,
        message: error.message ?? 'Error getting metrics',
      });
    }
  };

  /**
   * Trigger cleanup of old reports
   */
  public cleanupReports = async (req: Request, res: Response): Promise<void> => {
    try {
      const maxAgeHours = parseInt(req.body.maxAgeHours) || 168; // Default 7 days
      
      const result = await monitoringService.cleanupOldReports(maxAgeHours);
      
      res.json({
        success: true,
        data: result,
        message: `Cleanup completed: ${result.deletedFiles} files deleted, ${Math.round(result.freedSpace / 1024 / 1024)}MB freed`,
      });
    } catch (error: Error) {
      console.error('Error during cleanup:', error);
      res.status(500).json({
        success: false,
        message: error.message ?? 'Error during cleanup',
      });
    }
  };

  /**
   * Get system information
   */
  public getSystemInfo = async (req: Request, res: Response): Promise<void> => {
    try {
      const memoryUsage = process.memoryUsage();
      const cpuUsage = process.cpuUsage();
      
      const systemInfo = {
        node: {
          version: process.version,
          platform: process.platform,
          arch: process.arch,
          uptime: process.uptime(),
        },
        memory: {
          rss: Math.round(memoryUsage.rss / 1024 / 1024), // MB
          heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024), // MB
          heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024), // MB
          external: Math.round(memoryUsage.external / 1024 / 1024), // MB
          arrayBuffers: Math.round(memoryUsage.arrayBuffers / 1024 / 1024), // MB
        },
        cpu: {
          user: cpuUsage.user,
          system: cpuUsage.system,
        },
        environment: {
          nodeEnv: process.env.NODE_ENV,
          timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        },
      };

      res.json({
        success: true,
        data: systemInfo,
      });
    } catch (error: Error) {
      console.error('Error getting system info:', error);
      res.status(500).json({
        success: false,
        message: error.message ?? 'Error getting system info',
      });
    }
  };

  /**
   * Check database connectivity
   */
  private async checkDatabase(): Promise<{ status: string; responseTime: number; error?: string }> {
    const startTime = Date.now();
    
    try {
      await prisma.$queryRaw`SELECT 1`;
      const responseTime = Date.now() - startTime;
      
      return {
        status: 'healthy',
        responseTime,
      };
    } catch (error: Error) {
      return {
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        error: error.message,
      };
    }
  }

  /**
   * Check file system access
   */
  private async checkFileSystem(): Promise<{ status: string; error?: string; details?: unknown }> {
    try {
      const reportsDir = path.join(__dirname, '../../reports');
      
      // Check if reports directory exists and is writable
      if (!fs.existsSync(reportsDir)) {
        fs.mkdirSync(reportsDir, { recursive: true });
      }

      // Test write access
      const testFile = path.join(reportsDir, 'health-check.tmp');
      fs.writeFileSync(testFile, 'health check');
      fs.unlinkSync(testFile);

      // Get directory stats
      const stats = fs.statSync(reportsDir);
      const files = fs.readdirSync(reportsDir);

      return {
        status: 'healthy',
        details: {
          directory: reportsDir,
          fileCount: files.length,
          lastModified: stats.mtime,
        },
      };
    } catch (error: Error) {
      return {
        status: 'unhealthy',
        error: error.message,
      };
    }
  }

  /**
   * Check reporting system health
   */
  private async checkReportingSystem(): Promise<{ status: string; error?: string; details?: unknown }> {
    try {
      const systemHealth = await monitoringService.getSystemHealth();
      
      return {
        status: systemHealth.status === 'critical' ? 'unhealthy' : 'healthy',
        details: {
          systemStatus: systemHealth.status,
          activeReports: systemHealth.metrics.activeReports,
          queuedReports: systemHealth.metrics.queuedReports,
          failedReports: systemHealth.metrics.failedReports,
          alerts: systemHealth.alerts,
        },
      };
    } catch (error: Error) {
      return {
        status: 'unhealthy',
        error: error.message,
      };
    }
  }

  /**
   * Check memory usage
   */
  private async checkMemoryUsage(): Promise<{ status: string; details: unknown }> {
    const memoryUsage = process.memoryUsage();
    const heapUsedPercent = (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100;
    
    let status = 'healthy';
    if (heapUsedPercent > 90) {
      status = 'unhealthy';
    } else if (heapUsedPercent > 80) {
      status = 'warning';
    }

    return {
      status,
      details: {
        heapUsedMB: Math.round(memoryUsage.heapUsed / 1024 / 1024),
        heapTotalMB: Math.round(memoryUsage.heapTotal / 1024 / 1024),
        heapUsedPercent: Math.round(heapUsedPercent),
        rssMB: Math.round(memoryUsage.rss / 1024 / 1024),
      },
    };
  }

  /**
   * Extract result from Promise.allSettled
   */
  private getCheckResult(result: PromiseSettledResult<unknown>): unknown {
    if (result.status === 'fulfilled') {
      return result.value;
    } else {
      return {
        status: 'unhealthy',
        error: result.reason?.message ?? 'Unknown error',
      };
    }
  }
}
