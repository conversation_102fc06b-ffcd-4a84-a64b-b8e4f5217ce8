{"version": 3, "file": "PredefinedVerificationPolicies.js", "sourceRoot": "", "sources": ["../../../../src/config/verification/PredefinedVerificationPolicies.ts"], "names": [], "mappings": ";AAAA,oBAAoB;AACpB;;;;GAIG;;;AAEH,8FAA2F;AAE3F;;;;GAIG;AACU,QAAA,iBAAiB,GAAO,IAAI,uCAAkB,CAAC,mBAAmB,CAAC;KAC3E,cAAc,CAAC,wEAAwE,CAAC;KACxF,cAAc,CAAC,CAAC,uBAAuB,EAAE,oBAAoB,CAAC,CAAC;KAC/D,iBAAiB,CAAC,KAAK,CAAC,CAAC;AAE9B;;;;GAIG;AACU,QAAA,mBAAmB,GAAO,IAAI,uCAAkB,CAAC,qBAAqB,CAAC;KAC/E,cAAc,CAAC,0DAA0D,CAAC;KAC1E,aAAa,CAAC,oBAAoB,CAAC;KACnC,iBAAiB,CAAC,IAAI,CAAC;KACvB,eAAe,CAAC,KAAK,CAAC,CAAC;AAE5B;;;;GAIG;AACU,QAAA,yBAAyB,GAAO,IAAI,uCAAkB,CAAC,2BAA2B,CAAC;KAC3F,cAAc,CAAC,0DAA0D,CAAC;KAC1E,aAAa,CAAC,yBAAyB,CAAC;KACxC,YAAY,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,iBAAiB,CAAC,QAAQ,CAAC,QAAQ,CAAC;IAC9C,OAAO,CAAC,iBAAiB,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;AAE1E;;;;GAIG;AACU,QAAA,mBAAmB,GAAO,IAAI,uCAAkB,CAAC,qBAAqB,CAAC;KAC/E,cAAc,CAAC,oDAAoD,CAAC;KACpE,cAAc,CAAC,CAAC,oBAAoB,EAAE,oBAAoB,CAAC,CAAC;KAC5D,YAAY,CAAC,OAAO,CAAC,CAAA;AAAI,CAAC;IAC3B,oEAAoE;IACpE,qCAAqC;IACjC,OAAO,OAAO,CAAC,QAAQ,EAAE,aAAa,KAAK,IAAI,CAAC;AACpD,CAAC;AAAC,CAAC;AAEP;;;;GAIG;AACU,QAAA,wBAAwB,GAAO,IAAI,uCAAkB,CAAC,0BAA0B,CAAC;KACzF,cAAc,CAAC,4EAA4E,CAAC;KAC5F,cAAc,CAAC,CAAC,uBAAuB,EAAE,oBAAoB,CAAC,CAAC;KAC/D,YAAY,CAAC,OAAO,CAAC,CAAA;AAAI,CAAC;IACvB,MAAM,iBAAiB,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,wBAAwB;IACtE,OAAO,iBAAiB,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;AACjE,CAAC;AAAC,CAAC;AAEP;;;;GAIG;AACU,QAAA,kBAAkB,GAAO,IAAI,uCAAkB,CAAC,oBAAoB,CAAC;KAC7E,cAAc,CAAC,oDAAoD,CAAC;KACpE,aAAa,CAAC,0BAA0B,CAAC;KACzC,gBAAgB,CAAC,aAAa,CAAC,CAAC;AAErC;;;;GAIG;AACU,QAAA,oBAAoB,GAAO,IAAI,uCAAkB,CAAC,sBAAsB,CAAC;KACjF,cAAc,CAAC,sDAAsD,CAAC;KACtE,aAAa,CAAC,4BAA4B,CAAC;KAC3C,gBAAgB,CAAC,eAAe,CAAC,CAAC;AAEvC;;GAEG;AACU,QAAA,gCAAgC,GAAO;IAChD,yBAAiB;IACjB,2BAAmB;IACnB,iCAAyB;IACzB,2BAAmB;IACnB,gCAAwB;IACxB,0BAAkB;IAClB,4BAAoB;CACvB,CAAC"}