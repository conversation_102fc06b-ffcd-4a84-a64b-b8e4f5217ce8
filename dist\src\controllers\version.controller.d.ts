import { BaseController } from "../../core/BaseController";
/**
 * Version controller
 * This controller handles version-related requests
 */
export declare class VersionController extends BaseController {
    private versionRegistry;
    /**
     * Create a new version controller
     */
    constructor();
    /**
     * Initialize versions
     */
    private initializeVersions;
    /**
     * Get all versions
     */
    getAllVersions: any;
    /**
     * Get version by name
     */
    getVersionByName: any;
    /**
     * Get current version
     */
    getCurrentVersion: any;
    /**
     * Get active versions
     */
    getActiveVersions: any;
    /**
     * Get deprecated versions
     */
    getDeprecatedVersions: any;
    /**
     * Register a new version
     */
    registerVersion: any;
    /**
     * Update version status
     */
    updateVersionStatus: any;
    /**
     * Set current version
     */
    setCurrentVersion: any;
}
export default VersionController;
//# sourceMappingURL=version.controller.d.ts.map