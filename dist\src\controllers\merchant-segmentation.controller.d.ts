/**
 * Merchant Segmentation Controller
 *
 * This controller handles API requests related to merchant segmentation.
 */
import { Request, Response } from "express";
import { BaseController } from "./base.controller";
/**
 * Merchant segmentation controller
 */
export declare class MerchantSegmentationController extends BaseController {
    private merchantSegmentationService;
    constructor();
    /**
   * Create a new merchant category
   */
    createCategory: (req: Request, res: Response, next: import("express").NextFunction) => void;
    /**
   * Get all merchant categories
   */
    getAllCategories: (req: Request, res: Response, next: import("express").NextFunction) => void;
    /**
   * Add merchant to category
   */
    addMerchantToCategory: (req: Request, res: Response, next: import("express").NextFunction) => void;
    /**
   * Create a new merchant segment
   */
    createSegment: (req: Request, res: Response, next: import("express").NextFunction) => void;
    /**
   * Get all merchant segments
   */
    getAllSegments: (req: Request, res: Response, next: import("express").NextFunction) => void;
    /**
   * Apply segment to matching merchants
   */
    applySegment: (req: Request, res: Response, next: import("express").NextFunction) => void;
    /**
   * Create a new merchant performance tier
   */
    createPerformanceTier: (req: Request, res: Response, next: import("express").NextFunction) => void;
    /**
   * Apply performance tier to qualifying merchants
   */
    applyPerformanceTier: (req: Request, res: Response, next: import("express").NextFunction) => void;
}
//# sourceMappingURL=merchant-segmentation.controller.d.ts.map