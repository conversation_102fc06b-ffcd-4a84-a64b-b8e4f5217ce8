// jscpd:ignore-file
import { Request, Response } from "express";
import { VerificationMethodService } from "../services/verification-method.service";
import { AppError, asyncHandler } from '../middlewares/error.middleware';
import prisma from "../config/database";
import { VerificationMethodService } from "../services/verification-method.service";
import { AppError, asyncHandler } from '../middlewares/error.middleware';

// Get all verification methods
export const getAllVerificationMethods: any =asyncHandler(async (req: Request, res: Response) => {
    const verificationMethods: any =await prisma.verificationMethod.findMany({
        include: {, paymentMethod: {
                select: {, id: true,
                    name: true,
                    type: true,
                    merchantId: true
                }
            }
        },
        orderBy: {, createdAt: "desc" }
    });
  
    res.status(200).json(verificationMethods);
});

// Get verification method by ID
export const getVerificationMethodById: any =asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
  
    const verificationMethod: any =await VerificationMethodService.getVerificationMethodById(id);
  
    res.status(200).json(verificationMethod);
});

// Create a new verification method
export const createVerificationMethod: any =asyncHandler(async (req: Request, res: Response) => {
    const {
        name,
        type,
        paymentMethodId,
        isActive,
        verificationRequirements,
        internalSettings,
        displaySettings
    } = req.body;
  
    // Validate required fields
    if (!name || !type || !paymentMethodId) {
        throw new AppError({
            message: "Missing required fields",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }
  
    // Create verification method
    const verificationMethod: any =await VerificationMethodService.createVerificationMethod({
        name,
        type,
        paymentMethodId,
        isActive,
        verificationRequirements,
        internalSettings,
        displaySettings
    });
  
    res.status(201).json(verificationMethod);
});

// Update verification method
export const updateVerificationMethod: any =asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    const {
        name,
        isActive,
        verificationRequirements,
        internalSettings,
        displaySettings
    } = req.body;
  
    // Update verification method
    const verificationMethod: any =await VerificationMethodService.updateVerificationMethod(id, {
        name,
        isActive,
        verificationRequirements,
        internalSettings,
        displaySettings
    });
  
    res.status(200).json(verificationMethod);
});

// Delete verification method
export const deleteVerificationMethod: any =asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
  
    const result: any =await VerificationMethodService.deleteVerificationMethod(id);
  
    res.status(200).json(result);
});

// Get verification methods for payment method
export const getVerificationMethodsForPaymentMethod: any =asyncHandler(async (req: Request, res: Response) => {
    const { paymentMethodId } = req.params;
  
    const verificationMethods: any =await VerificationMethodService.getVerificationMethodsForPaymentMethod(paymentMethodId);
  
    res.status(200).json(verificationMethods);
});

// Get verification method types
export const getVerificationMethodTypes: any =asyncHandler(async (req: Request, res: Response) => {
    const types: any =VerificationMethodService.getVerificationMethodTypes();
  
    res.status(200).json(types);
});

// Verify payment
export const verifyPayment: any =asyncHandler(async (req: Request, res: Response) => {
    const {
        transactionId,
        verificationMethodId,
        verificationData
    } = req.body;
  
    // Validate required fields
    if (!transactionId || !verificationMethodId || !verificationData) {
        throw new AppError({
            message: "Missing required fields",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }
  
    // Verify payment
    const result: any =await VerificationMethodService.verifyPayment({
        transactionId,
        verificationMethodId,
        verificationData
    });
  
    res.status(200).json(result);
});
