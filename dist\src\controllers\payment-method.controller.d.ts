import { BaseController } from "../../core/BaseController";
/**
 * Payment method controller
 * This controller handles payment method-related operations
 */
export declare class PaymentMethodController extends BaseController {
    private paymentMethodService;
    /**
     * Create a new payment method controller
     */
    constructor();
    /**
     * Get all payment methods
     */
    getPaymentMethods: any;
    /**
     * Get a payment method by ID
     */
    getPaymentMethod: any;
    /**
     * Create a new payment method
     */
    createPaymentMethod: any;
    /**
     * Update a payment method
     */
    updatePaymentMethod: any;
    /**
     * Delete a payment method
     */
    deletePaymentMethod: any;
    /**
     * Set a payment method as default
     */
    setDefaultPaymentMethod: any;
    /**
     * Verify a payment method
     */
    verifyPaymentMethod: any;
}
//# sourceMappingURL=payment-method.controller.d.ts.map