/**
 * Alert Aggregation Business Service
 *
 * Handles business logic for alert aggregation operations.
 */
import { PrismaClient } from '@prisma/client';
import { CreateAggregationRuleRequest, UpdateAggregationRuleRequest, AggregationRuleResponse, CorrelationRuleResponse, AggregationRuleFilters, CorrelationRuleFilters, PaginationParams } from '../types/AlertAggregationTypes';
/**
 * Business service for alert aggregation
 */
export declare class AlertAggregationBusinessService {
    private prisma;
    constructor(prisma: PrismaClient);
    /**
     * Get all aggregation rules with optional filtering and pagination
     */
    getAggregationRules(filters?: AggregationRuleFilters, pagination?: PaginationParams): Promise<{
        rules: AggregationRuleResponse[];
        total: number;
    }>;
    /**
     * Get a specific aggregation rule by ID
     */
    getAggregationRule(id: string): Promise<AggregationRuleResponse>;
    /**
     * Create a new aggregation rule
     */
    createAggregationRule(data: CreateAggregationRuleRequest): Promise<AggregationRuleResponse>;
    /**
     * Update an existing aggregation rule
     */
    updateAggregationRule(id: string, data: UpdateAggregationRuleRequest): Promise<AggregationRuleResponse>;
    /**
     * Delete an aggregation rule
     */
    deleteAggregationRule(id: string): Promise<void>;
    /**
     * Get all correlation rules with optional filtering and pagination
     */
    getCorrelationRules(filters?: CorrelationRuleFilters, pagination?: PaginationParams): Promise<{
        rules: CorrelationRuleResponse[];
        total: number;
    }>;
    /**
     * Get a specific correlation rule by ID
     */
    getCorrelationRule(id: string): Promise<CorrelationRuleResponse>;
    /**
     * Map aggregation rule to response format
     */
    private mapAggregationRuleToResponse;
    /**
     * Map correlation rule to response format
     */
    private mapCorrelationRuleToResponse;
}
//# sourceMappingURL=AlertAggregationBusinessService.d.ts.map