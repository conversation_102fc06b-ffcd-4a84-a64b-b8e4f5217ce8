// jscpd:ignore-file
import { Request, Response, NextFunction } from "express";
import { MonitoringService } from "../services/monitoring.service";
import { logger } from "../utils/logger";
import { Middleware } from '../types/express';
import { MonitoringService } from "../services/monitoring.service";
import { logger } from "../utils/logger";
import { Middleware } from '../types/express';


/**
 * Global monitoring service instance
 */
const monitoringService: any =new MonitoringService();

/**
 * Middleware to track API requests for monitoring
 */
export const monitoringMiddleware: any =(req: Request, res: Response, next: NextFunction) => {
    // Skip monitoring for monitoring endpoints to avoid infinite loops
    if (req.path.startsWith("/api/monitoring")) {
        return next();
    }

    // Record start time
    const startTime: any =Date.now();
  
    // Store original end method
    const originalEnd: any =res.end;
  
    // Override end method to capture response data
    res.end = function(chunk?, encoding?, callback?) {
    // Restore original end method
        res.end = originalEnd;
    
        // Call original end method
        res.end(chunk, encoding, callback);
    
        // Track request in monitoring service
        try {
            const endpoint: any =`${req.method} ${req.path}`;
            monitoringService.trackApiRequest(endpoint, startTime, res.statusCode);
      
            // Log request details
            logger.debug("API request completed", {
                method: req.method,
                path: req.path,
                statusCode: res.statusCode,
                responseTime: Date.now() - startTime
            });
        } catch (error) {
            logger.error("Error tracking API request", { error });
        }
    };
  
    next();
};

/**
 * Get the monitoring service instance
 * @returns Monitoring service instance
 */
export const getMonitoringService: any =(): MonitoringService => {
    return monitoringService;
};
