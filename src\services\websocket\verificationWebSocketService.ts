// jscpd:ignore-file
import { Server as HttpServer } from 'http';
import { Server as WebSocketServer, Socket } from 'socket.io';
import { EventEmitter } from 'events';
import { logger } from '../../lib/logger';
import { VerificationStatus } from '../../types/verification';
import { Transaction, Merchant, VerificationStatus } from '../types';
import { Server as WebSocketServer, Socket } from 'socket.io';
import { EventEmitter } from 'events';
import { logger } from '../../lib/logger';
import { VerificationStatus } from '../../types/verification';
import { Transaction, Merchant, VerificationStatus } from '../types';

// Verification events
export const verificationEvents: unknown = new EventEmitter();

// Payment verification message
export interface PaymentVerificationMessage {
  paymentId: string;
  merchantId: string;
  status: VerificationStatus;
  timestamp: string;
  verificationMethod?: string;
  message?: string;
  transactionDetails?: Record<string, any>;
}

/**
 * WebSocket service for payment verification
 */
export class VerificationWebSocketService {
  private static instance: VerificationWebSocketService;
  private io: WebSocketServer | null = null;
  private connectedClients: Map<string, Set<string>> = new Map();

  /**
   * Get singleton instance
   */
  public static getInstance(): VerificationWebSocketService {
    if (!VerificationWebSocketService.instance) {
      VerificationWebSocketService.instance = new VerificationWebSocketService();
    }
    return VerificationWebSocketService.instance;
  }

  /**
   * Initialize WebSocket server
   * @param httpServer HTTP server
   */
  public initialize(httpServer: HttpServer): WebSocketServer {
    if (this.io) {
      return this.io;
    }

    // Create WebSocket server
    this.io = new WebSocketServer(httpServer, {
      path: '/ws/verification',
      cors: { origin: '*', methods: ['GET', 'POST'], credentials: true },
      transports: ['websocket', 'polling'],
      pingInterval: 10000,
      pingTimeout: 5000,
      cookie: false,
    });

    // Set up connection handler
    this.io.on('connection', this.handleConnection.bind(this));

    // Set up verification event listeners
    this.setupEventListeners();

    logger.info('Verification WebSocket server initialized');

    return this.io;
  }

  /**
   * Handle WebSocket connection
   * @param socket Socket
   */
  private handleConnection(socket: Socket): void {
    logger.info(`Verification client connected: ${socket.id}`);

    // Get payment ID and merchant ID from query parameters
    const paymentId: unknown = socket.handshake.query.paymentId as string;
    const merchantId: unknown = socket.handshake.query.merchantId as string;

    if (!paymentId) {
      logger.warn(`Client ${socket.id} connected without payment ID, disconnecting`);
      socket.disconnect();
      return;
    }

    // Add client to connected clients
    this.addClient(paymentId, socket.id);

    // Join payment room
    socket.join(`payment:${paymentId}`);

    // Join merchant room if merchant ID is provided
    if (merchantId) {
      socket.join(`merchant:${merchantId}`);
    }

    // Handle join event
    socket.on('join', (data: { paymentId: string; merchantId?: string }) => {
      if (data.paymentId) {
        socket.join(`payment:${data.paymentId}`);
        this.addClient(data.paymentId, socket.id);
        logger.info(`Client ${socket.id} joined payment room: ${data.paymentId}`);
      }

      if (data.merchantId) {
        socket.join(`merchant:${data.merchantId}`);
        logger.info(`Client ${socket.id} joined merchant room: ${data.merchantId}`);
      }
    });

    // Handle disconnect event
    socket.on('disconnect', () => {
      logger.info(`Verification client disconnected: ${socket.id}`);
      this.removeClient(paymentId, socket.id);
    });
  }

  /**
   * Add client to connected clients
   * @param paymentId Payment ID
   * @param socketId Socket ID
   */
  private addClient(paymentId: string, socketId: string): void {
    if (!this.connectedClients.has(paymentId)) {
      this.connectedClients.set(paymentId, new Set());
    }
    this.connectedClients.get(paymentId)?.add(socketId);
  }

  /**
   * Remove client from connected clients
   * @param paymentId Payment ID
   * @param socketId Socket ID
   */
  private removeClient(paymentId: string, socketId: string): void {
    if (this.connectedClients.has(paymentId)) {
      this.connectedClients.get(paymentId)?.delete(socketId);

      // Remove payment ID if no clients are connected
      if (this.connectedClients.get(paymentId)?.size === 0) {
        this.connectedClients.delete(paymentId);
      }
    }
  }

  /**
   * Set up verification event listeners
   */
  private setupEventListeners(): void {
    // Verification started event
    verificationEvents.on('verification.started', (data: PaymentVerificationMessage) => {
      this.emitToPayment(data.paymentId, 'verification.started', data);

      if (data.merchantId) {
        this.emitToMerchant(data.merchantId, 'verification.started', data);
      }
    });

    // Verification updated event
    verificationEvents.on('verification.updated', (data: PaymentVerificationMessage) => {
      this.emitToPayment(data.paymentId, 'verification.updated', data);

      if (data.merchantId) {
        this.emitToMerchant(data.merchantId, 'verification.updated', data);
      }
    });

    // Verification completed event
    verificationEvents.on('verification.completed', (data: PaymentVerificationMessage) => {
      this.emitToPayment(data.paymentId, 'verification.completed', data);

      if (data.merchantId) {
        this.emitToMerchant(data.merchantId, 'verification.completed', data);
      }
    });

    // Verification failed event
    verificationEvents.on('verification.failed', (data: PaymentVerificationMessage) => {
      this.emitToPayment(data.paymentId, 'verification.failed', data);

      if (data.merchantId) {
        this.emitToMerchant(data.merchantId, 'verification.failed', data);
      }
    });

    // Transaction updated event
    verificationEvents.on('transaction.updated', (data: PaymentVerificationMessage) => {
      this.emitToPayment(data.paymentId, 'transaction.updated', data);

      if (data.merchantId) {
        this.emitToMerchant(data.merchantId, 'transaction.updated', data);
      }
    });
  }

  /**
   * Emit event to payment room
   * @param paymentId Payment ID
   * @param event Event name
   * @param data Event data
   */
  public emitToPayment(paymentId: string, event: string, data): void {
    if (!this.io) {
      logger.warn('WebSocket server not initialized');
      return;
    }

    this.io.to(`payment:${paymentId}`).emit(event, data);
  }

  /**
   * Emit event to merchant room
   * @param merchantId Merchant ID
   * @param event Event name
   * @param data Event data
   */
  public emitToMerchant(merchantId: string, event: string, data): void {
    if (!this.io) {
      logger.warn('WebSocket server not initialized');
      return;
    }

    this.io.to(`merchant:${merchantId}`).emit(event, data);
  }

  /**
   * Get connected clients count
   */
  public getConnectedClientsCount(): number {
    let count = 0;
    this.connectedClients.forEach((clients: unknown) => {
      count += clients.size;
    });
    return count;
  }

  /**
   * Get connected payments count
   */
  public getConnectedPaymentsCount(): number {
    return this.connectedClients.size;
  }
}

// Export singleton instance
export const verificationWebSocketService = VerificationWebSocketService.getInstance();

export default verificationWebSocketService;
