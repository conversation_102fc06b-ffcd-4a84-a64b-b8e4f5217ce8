"use strict";
/**
 * Base Validator
 *
 * Common validation utilities shared across fraud detection validators.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseValidator = void 0;
const AppError_1 = require("../../../utils/errors/AppError");
/**
 * Base validator with common validation methods
 */
class BaseValidator {
    /**
     * Validate merchant ID parameter
     */
    validateMerchantId(merchantId) {
        if (!merchantId) {
            throw new AppError_1.AppError({
                message: 'Merchant ID is required',
                type: AppError_1.ErrorType.VALIDATION,
                code: AppError_1.ErrorCode.MISSING_REQUIRED_FIELD
            });
        }
        const parsedId = parseInt(merchantId, 10);
        if (isNaN(parsedId) || parsedId <= 0) {
            throw new AppError_1.AppError({
                message: 'Merchant ID must be a positive integer',
                type: AppError_1.ErrorType.VALIDATION,
                code: AppError_1.ErrorCode.INVALID_INPUT
            });
        }
        return parsedId;
    }
    /**
     * Validate date range parameters
     */
    validateDateRange(startDate, endDate) {
        let start;
        let end;
        if (startDate) {
            start = new Date(startDate);
            if (isNaN(start.getTime())) {
                throw new AppError_1.AppError({
                    message: 'Invalid start date format',
                    type: AppError_1.ErrorType.VALIDATION,
                    code: AppError_1.ErrorCode.INVALID_INPUT
                });
            }
        }
        else {
            // Default to 30 days ago
            start = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        }
        if (endDate) {
            end = new Date(endDate);
            if (isNaN(end.getTime())) {
                throw new AppError_1.AppError({
                    message: 'Invalid end date format',
                    type: AppError_1.ErrorType.VALIDATION,
                    code: AppError_1.ErrorCode.INVALID_INPUT
                });
            }
        }
        else {
            // Default to now
            end = new Date();
        }
        if (start >= end) {
            throw new AppError_1.AppError({
                message: 'Start date must be before end date',
                type: AppError_1.ErrorType.VALIDATION,
                code: AppError_1.ErrorCode.INVALID_INPUT
            });
        }
        // Limit to maximum 1 year range
        const maxRange = 365 * 24 * 60 * 60 * 1000; // 1 year in milliseconds
        if (end.getTime() - start.getTime() > maxRange) {
            throw new AppError_1.AppError({
                message: 'Date range cannot exceed 1 year',
                type: AppError_1.ErrorType.VALIDATION,
                code: AppError_1.ErrorCode.INVALID_INPUT
            });
        }
        return { start, end };
    }
    /**
     * Validate pagination parameters
     */
    validatePaginationParams(query) {
        const page = query.page ? parseInt(query.page, 10) : 1;
        const limit = query.limit ? parseInt(query.limit, 10) : 10;
        if (isNaN(page) || page < 1) {
            throw new AppError_1.AppError({
                message: 'Page must be a positive integer',
                type: AppError_1.ErrorType.VALIDATION,
                code: AppError_1.ErrorCode.INVALID_INPUT
            });
        }
        if (isNaN(limit) || limit < 1 || limit > 100) {
            throw new AppError_1.AppError({
                message: 'Limit must be between 1 and 100',
                type: AppError_1.ErrorType.VALIDATION,
                code: AppError_1.ErrorCode.INVALID_INPUT
            });
        }
        const result = { page, limit };
        if (query.sortBy) {
            const validSortFields = ['createdAt', 'score', 'level', 'isFlagged', 'isBlocked'];
            if (!validSortFields.includes(query.sortBy)) {
                throw new AppError_1.AppError({
                    message: `Invalid sort field. Must be one of: ${validSortFields.join(', ')}`,
                    type: AppError_1.ErrorType.VALIDATION,
                    code: AppError_1.ErrorCode.INVALID_INPUT
                });
            }
            result.sortBy = query.sortBy;
        }
        if (query.sortOrder) {
            if (!['asc', 'desc'].includes(query.sortOrder)) {
                throw new AppError_1.AppError({
                    message: 'Sort order must be either "asc" or "desc"',
                    type: AppError_1.ErrorType.VALIDATION,
                    code: AppError_1.ErrorCode.INVALID_INPUT
                });
            }
            result.sortOrder = query.sortOrder;
        }
        return result;
    }
    /**
     * Check if string is a valid UUID
     */
    isValidUUID(uuid) {
        const uuidRegex = /^[\da-f]{8}-[\da-f]{4}-[1-5][\da-f]{3}-[89ab][\da-f]{3}-[\da-f]{12}$/i;
        return uuidRegex.test(uuid);
    }
    /**
     * Check if string is a valid IP address
     */
    isValidIPAddress(ip) {
        const ipv4Regex = /^(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)$/;
        const ipv6Regex = /^(?:[\da-fA-F]{1,4}:){7}[\da-fA-F]{1,4}$/;
        return ipv4Regex.test(ip) || ipv6Regex.test(ip);
    }
    /**
     * Check if string is a valid country code
     */
    isValidCountryCode(code) {
        // ISO 3166-1 alpha-2 country codes (2 letters)
        return /^[A-Z]{2}$/.test(code);
    }
    /**
     * Check if string is a valid IP range (CIDR notation)
     */
    isValidIPRange(range) {
        const cidrRegex = /^(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\/(?:\d|[1-2]\d|3[0-2])$/;
        return cidrRegex.test(range);
    }
}
exports.BaseValidator = BaseValidator;
//# sourceMappingURL=BaseValidator.js.map