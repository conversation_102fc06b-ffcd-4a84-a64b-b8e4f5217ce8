import { 
  AlertSeverity, 
  AlertType, 
  AlertStatus, 
  AlertNotificationMethod,
  AlertChannel
} from '../../types/alert.types';

describe('Alert Types', () => {
  describe('AlertSeverity', () => {
    it('should have the correct values', () => {
      expect(AlertSeverity.LOW).toBe('LOW');
      expect(AlertSeverity.MEDIUM).toBe('MEDIUM');
      expect(AlertSeverity.HIGH).toBe('HIGH');
      expect(AlertSeverity.CRITICAL).toBe('CRITICAL');
    });

    it('should support legacy values through aliases', () => {
      expect(AlertSeverity.INFO).toBe('LOW');
      expect(AlertSeverity.WARNING).toBe('MEDIUM');
      expect(AlertSeverity.ERROR).toBe('HIGH');
    });
  });

  describe('AlertType', () => {
    it('should have the correct values', () => {
      expect(AlertType.SECURITY).toBe('SECURITY');
      expect(AlertType.PAYMENT).toBe('PAYMENT');
      expect(AlertType.SYSTEM).toBe('SYSTEM');
      expect(AlertType.FRAUD).toBe('FRAUD');
      expect(AlertType.VERIFICATION).toBe('VERIFICATION');
      expect(AlertType.OTHER).toBe('OTHER');
    });

    it('should support legacy values through aliases', () => {
      expect(AlertType.SYSTEM_HEALTH).toBe('SYSTEM');
      expect(AlertType.PAYMENT_FAILURE).toBe('PAYMENT');
      expect(AlertType.VERIFICATION_FAILURE).toBe('VERIFICATION');
      expect(AlertType.WEBHOOK_FAILURE).toBe('OTHER');
      expect(AlertType.PERFORMANCE).toBe('SYSTEM');
      expect(AlertType.CUSTOM).toBe('OTHER');
      expect(AlertType.DATABASE).toBe('SYSTEM');
      expect(AlertType.APPLICATION).toBe('SYSTEM');
    });
  });

  describe('AlertStatus', () => {
    it('should have the correct values', () => {
      expect(AlertStatus.OPEN).toBe('OPEN');
      expect(AlertStatus.IN_PROGRESS).toBe('IN_PROGRESS');
      expect(AlertStatus.RESOLVED).toBe('RESOLVED');
      expect(AlertStatus.CLOSED).toBe('CLOSED');
      expect(AlertStatus.IGNORED).toBe('IGNORED');
    });

    it('should support legacy values through aliases', () => {
      expect(AlertStatus.ACTIVE).toBe('OPEN');
      expect(AlertStatus.ACKNOWLEDGED).toBe('IN_PROGRESS');
    });
  });

  describe('AlertNotificationMethod', () => {
    it('should have the correct values', () => {
      expect(AlertNotificationMethod.EMAIL).toBe('EMAIL');
      expect(AlertNotificationMethod.SMS).toBe('SMS');
      expect(AlertNotificationMethod.WEBHOOK).toBe('WEBHOOK');
      expect(AlertNotificationMethod.DASHBOARD).toBe('DASHBOARD');
      expect(AlertNotificationMethod.TELEGRAM).toBe('TELEGRAM');
      expect(AlertNotificationMethod.PUSH).toBe('PUSH');
      expect(AlertNotificationMethod.SLACK).toBe('SLACK');
    });
  });

  describe('AlertChannel', () => {
    it('should be an alias for AlertNotificationMethod', () => {
      expect(AlertChannel).toBe(AlertNotificationMethod);
    });
  });
});
