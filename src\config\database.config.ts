// jscpd:ignore-file

/**
 * Database Configuration
 *
 * This file provides a centralized configuration for database connections
 * across different environments (development, production, testing).
 *
 * It ensures consistent database access throughout the application.
 */

import { PrismaClient } from '@prisma/client';
import * as dotenv from 'dotenv';
import { logger } from '../lib/logger';

// Load environment variables
dotenv.config();

// Environment types - only production is supported
export type Environment = 'production';

// Database configuration interface
export interface DatabaseConfig {
  url: string;
  host: string;
  port: number;
  username: string;
  password: string;
  database: string;
  ssl: boolean;
  connectionPoolMin: number;
  connectionPoolMax: number;
  statementTimeout: number;
  logQueries: boolean;
}

// Get current environment - always returns production
export const getEnvironment = (): Environment => {
  return 'production';
};

// Load production database configuration
export const getDatabaseConfig = (): DatabaseConfig => {
  // Production configuration
  const config: DatabaseConfig = {
    url: process.env.DATABASE_URL || '',
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432', 10),
    username: process.env.DB_USERNAME || 'postgres',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'Amazingpay',
    ssl: process.env.DB_SSL === 'true',
    connectionPoolMin: parseInt(process.env.DB_CONNECTION_POOL_MIN || '5', 10),
    connectionPoolMax: parseInt(process.env.DB_CONNECTION_POOL_MAX || '20', 10),
    statementTimeout: parseInt(process.env.DB_STATEMENT_TIMEOUT || '30000', 10),
    logQueries: false, // Always disable query logging in production
  };

  return config;
};

// Singleton PrismaClient instance
let prismaInstance: PrismaClient | null = null;

// Get PrismaClient instance
export const getPrismaClient = (): PrismaClient => {
  if (!prismaInstance) {
    const config = getDatabaseConfig();

    logger.info(`Initializing PrismaClient for database: ${config.database}`);

    // Production-only configuration
    prismaInstance = new PrismaClient({
      datasources: {
        db: {
          url: config.url,
        },
      },
      log: [
        { level: 'error' as const, emit: 'stdout' as const },
        { level: 'info' as const, emit: 'stdout' as const },
        { level: 'warn' as const, emit: 'stdout' as const },
      ],
    });

    // Set up basic error logging
    logger.info('PrismaClient initialized for production database');
  }

  return prismaInstance;
};

// Close database connection
export const closeDatabaseConnection = async (): Promise<void> => {
  if (prismaInstance) {
    await prismaInstance.$disconnect();
    prismaInstance = null;
    logger.info('Database connection closed');
  }
};

// Legacy getDatabaseUrl function for backward compatibility
export const getDatabaseUrl = (): string => {
  return getDatabaseConfig().url;
};

// Export default database configuration
const databaseConfig = {
  url: getDatabaseUrl(),
  logQueries: false, // Never log queries in production
  useMockData: false, // Always use real data
  getEnvironment,
  getDatabaseConfig,
  getPrismaClient,
  closeDatabaseConnection,
} as const;

export default databaseConfig;
