"use strict";
// jscpd:ignore-file
/**
 * Verification Monitoring Controller
 *
 * This controller provides endpoints for monitoring the verification system.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VerificationMonitoringController = void 0;
const BaseController_1 = require("../base/BaseController");
const prisma_1 = __importDefault(require("../../lib/prisma"));
const logger_1 = require("../../utils/logger");
/**
 * Verification monitoring controller
 */
class VerificationMonitoringController extends BaseController_1.BaseController {
    /**
     * Constructor
     */
    constructor() {
        super();
    }
    /**
     * Get verification metrics
     * @param req Request
     * @param res Response
     */
    async getVerificationMetrics(req, res) {
        try {
            const { startDate, endDate, period = 'day' } = req.query;
            // Parse dates
            const parsedStartDate = startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
            const parsedEndDate = endDate ? new Date(endDate) : new Date();
            // Validate dates
            if (isNaN(parsedStartDate.getTime()) || isNaN(parsedEndDate.getTime())) {
                return res.status(400).json({
                    success: false,
                    message: 'Invalid date format',
                });
            }
            // Get metrics from database
            const metrics = await prisma_1.default.verificationMetrics.findMany({
                where: { timestamp: {
                        gte: parsedStartDate,
                        lte: parsedEndDate,
                    },
                },
                orderBy: { timestamp: 'asc',
                },
            });
            // Aggregate metrics
            const aggregatedMetrics = this.aggregateMetrics(metrics);
            const errorDistribution = this.aggregateErrorDistribution(metrics);
            // Return metrics
            return res.status(200).json({
                success: true,
                data: { metrics: aggregatedMetrics,
                    errorDistribution,
                    period,
                    startDate: parsedStartDate,
                    endDate: parsedEndDate,
                },
            });
        }
        catch (error) {
            logger_1.logger.error('Error getting verification metrics', {
                error: error.message || error,
            });
            return res.status(500).json({
                success: false,
                message: 'Error getting verification metrics',
                error: error.message || 'Unknown error',
            });
        }
    }
    /**
     * Get verification method metrics
     * @param req Request
     * @param res Response
     */
    async getVerificationMethodMetrics(req, res) {
        try {
            const { startDate, endDate, period = 'day' } = req.query;
            // Parse dates
            const parsedStartDate = startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
            const parsedEndDate = endDate ? new Date(endDate) : new Date();
            // Validate dates
            if (isNaN(parsedStartDate.getTime()) || isNaN(parsedEndDate.getTime())) {
                return res.status(400).json({
                    success: false,
                    message: 'Invalid date format',
                });
            }
            // Get metrics from database
            const metrics = await prisma_1.default.verificationMetrics.findMany({
                where: { timestamp: {
                        gte: parsedStartDate,
                        lte: parsedEndDate,
                    },
                },
                orderBy: { timestamp: 'asc',
                },
            });
            // Aggregate method distribution
            const methodDistribution = this.aggregateMethodDistribution(metrics);
            // Return method distribution
            return res.status(200).json({
                success: true,
                data: {
                    methodDistribution,
                    period,
                    startDate: parsedStartDate,
                    endDate: parsedEndDate,
                },
            });
        }
        catch (error) {
            logger_1.logger.error('Error getting verification method metrics', {
                error: error.message || error,
            });
            return res.status(500).json({
                success: false,
                message: 'Error getting verification method metrics',
                error: error.message || 'Unknown error',
            });
        }
    }
    /**
     * Aggregate metrics
     * @param metrics Metrics
     * @returns Aggregated metrics
     */
    aggregateMetrics(metrics) {
        if (metrics.length === 0) {
            return {
                attempts: 0,
                successes: 0,
                failures: 0,
                successRate: 0,
                avgLatency: 0,
            };
        }
        const totalAttempts = metrics.reduce((sum, metric) => sum + metric.attempts, 0);
        const totalSuccesses = metrics.reduce((sum, metric) => sum + metric.successes, 0);
        const totalFailures = metrics.reduce((sum, metric) => sum + metric.failures, 0);
        const totalLatency = metrics.reduce((sum, metric) => sum + (metric.avgLatency * metric.attempts), 0);
        const successRate = totalAttempts > 0 ? (totalSuccesses / totalAttempts) * 100 : 0;
        const avgLatency = totalAttempts > 0 ? totalLatency / totalAttempts : 0;
        return {
            attempts: totalAttempts,
            successes: totalSuccesses,
            failures: totalFailures,
            successRate,
            avgLatency,
        };
    }
    /**
     * Aggregate error distribution
     * @param metrics Metrics
     * @returns Aggregated error distribution
     */
    aggregateErrorDistribution(metrics) {
        const errorDistribution = {};
        metrics.forEach((metric));
        {
            try {
                const distribution = JSON.parse(metric.errorDistribution || '{}');
                Object.entries(distribution).forEach((([errorType, count])), {
                    if(, count) { }
                } === 'number');
                {
                    errorDistribution[errorType] = (errorDistribution[errorType] || 0) + count;
                }
            }
            finally { }
            ;
        }
        try { }
        catch (error) {
            logger_1.logger.error('Error parsing error distribution', {
                metricId: metric.id,
                errorDistribution: metric.errorDistribution,
                error: error.message || error,
            });
        }
    }
    ;
}
exports.VerificationMonitoringController = VerificationMonitoringController;
return errorDistribution;
aggregateMethodDistribution(metrics, any[]);
Record < string, any > {
    const: methodDistribution
};
{ }
;
metrics.forEach((metric));
{
    try {
        const distribution = JSON.parse(metric.methodDistribution || '{}');
        Object.entries(distribution).forEach((([method, counts])), {
            if(, methodDistribution, [method]) {
                methodDistribution[method] = {
                    attempts: 0,
                    successes: 0,
                    failures: 0,
                    successRate: 0,
                };
            },
            const: methodCounts, any = counts,
            methodDistribution, [method]: .attempts += methodCounts.ATTEMPT || 0,
            methodDistribution, [method]: .successes += methodCounts.SUCCESS || 0,
            methodDistribution, [method]: .failures += methodCounts.FAILURE || 0
        });
    }
    catch (error) {
        logger_1.logger.error('Error parsing method distribution', {
            metricId: metric.id,
            methodDistribution: metric.methodDistribution,
            error: error.message || error,
        });
    }
}
;
// Calculate success rates
Object.values(methodDistribution).forEach(((method)), {
    method, : .successRate = method.attempts > 0 ? (method.successes / method.attempts) * 100 : 0
});
return methodDistribution;
//# sourceMappingURL=verification-monitoring.controller.js.map