"use strict";
/**
 * Alert Aggregation Business Service
 *
 * Handles business logic for alert aggregation operations.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.AlertAggregationBusinessService = void 0;
const AppError_1 = require("../../../utils/errors/AppError");
/**
 * Business service for alert aggregation
 */
class AlertAggregationBusinessService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    /**
     * Get all aggregation rules with optional filtering and pagination
     */
    async getAggregationRules(filters, pagination) {
        try {
            const where = {};
            // Apply filters
            if (filters?.type && filters.type !== 'ANY') {
                where.type = filters.type;
            }
            if (filters?.severity && filters.severity !== 'ANY') {
                where.severity = filters.severity;
            }
            if (filters?.enabled !== undefined) {
                where.enabled = filters.enabled;
            }
            if (filters?.search) {
                where.OR = [
                    { name: { contains: filters.search, mode: 'insensitive' } },
                    { description: { contains: filters.search, mode: 'insensitive' } },
                ];
            }
            // Build query options
            const queryOptions = {
                where,
                orderBy: { createdAt: 'desc' },
            };
            // Apply pagination
            if (pagination) {
                const { page = 1, limit = 10, sortBy, sortOrder } = pagination;
                queryOptions.skip = (page - 1) * limit;
                queryOptions.take = limit;
                if (sortBy) {
                    queryOptions.orderBy = { [sortBy]: sortOrder || 'desc' };
                }
            }
            // Execute queries
            const [rules, total] = await Promise.all([
                this.prisma.alertAggregationRule.findMany(queryOptions),
                this.prisma.alertAggregationRule.count({ where }),
            ]);
            return {
                rules: rules.map(this.mapAggregationRuleToResponse),
                total,
            };
        }
        catch (error) {
            throw new AppError_1.AppError({
                message: 'Failed to get aggregation rules',
                type: AppError_1.ErrorType.INTERNAL,
                code: AppError_1.ErrorCode.INTERNAL_SERVER_ERROR,
                details: { originalError: error instanceof Error ? error.message : error },
            });
        }
    }
    /**
     * Get a specific aggregation rule by ID
     */
    async getAggregationRule(id) {
        try {
            const rule = await this.prisma.alertAggregationRule.findUnique({
                where: { id },
            });
            if (!rule) {
                throw new AppError_1.AppError({
                    message: 'Aggregation rule not found',
                    type: AppError_1.ErrorType.NOT_FOUND,
                    code: AppError_1.ErrorCode.RESOURCE_NOT_FOUND,
                });
            }
            return this.mapAggregationRuleToResponse(rule);
        }
        catch (error) {
            if (error instanceof AppError_1.AppError) {
                throw error;
            }
            throw new AppError_1.AppError({
                message: 'Failed to get aggregation rule',
                type: AppError_1.ErrorType.INTERNAL,
                code: AppError_1.ErrorCode.INTERNAL_SERVER_ERROR,
                details: { originalError: error instanceof Error ? error.message : error },
            });
        }
    }
    /**
     * Create a new aggregation rule
     */
    async createAggregationRule(data) {
        try {
            // Check for duplicate name
            const existingRule = await this.prisma.alertAggregationRule.findFirst({
                where: { name: data.name },
            });
            if (existingRule) {
                throw new AppError_1.AppError({
                    message: 'Aggregation rule with this name already exists',
                    type: AppError_1.ErrorType.VALIDATION,
                    code: 'DUPLICATE_RESOURCE',
                });
            }
            const rule = await this.prisma.alertAggregationRule.create({
                data: {
                    name: data.name,
                    description: data.description,
                    conditions: {
                        type: data.type,
                        severity: data.severity,
                        timeWindow: data.timeWindow,
                        threshold: data.threshold,
                        groupBy: data.groupBy,
                    },
                    actions: {},
                    isActive: data.enabled ?? true,
                },
            });
            return this.mapAggregationRuleToResponse(rule);
        }
        catch (error) {
            if (error instanceof AppError_1.AppError) {
                throw error;
            }
            throw new AppError_1.AppError({
                message: 'Failed to create aggregation rule',
                type: AppError_1.ErrorType.INTERNAL,
                code: AppError_1.ErrorCode.INTERNAL_SERVER_ERROR,
                details: { originalError: error instanceof Error ? error.message : error },
            });
        }
    }
    /**
     * Update an existing aggregation rule
     */
    async updateAggregationRule(id, data) {
        try {
            // Check if rule exists
            const existingRule = await this.prisma.alertAggregationRule.findUnique({
                where: { id },
            });
            if (!existingRule) {
                throw new AppError_1.AppError({
                    message: 'Aggregation rule not found',
                    type: AppError_1.ErrorType.NOT_FOUND,
                    code: AppError_1.ErrorCode.RESOURCE_NOT_FOUND,
                });
            }
            // Check for duplicate name if name is being updated
            if (data.name && data.name !== existingRule.name) {
                const duplicateRule = await this.prisma.alertAggregationRule.findFirst({
                    where: {
                        name: data.name,
                        id: { not: id },
                    },
                });
                if (duplicateRule) {
                    throw new AppError_1.AppError({
                        message: 'Aggregation rule with this name already exists',
                        type: AppError_1.ErrorType.VALIDATION,
                        code: 'DUPLICATE_RESOURCE',
                    });
                }
            }
            const updatedRule = await this.prisma.alertAggregationRule.update({
                where: { id },
                data: {
                    name: data.name || existingRule.name,
                    description: data.description || existingRule.description,
                    conditions: {
                        ...existingRule.conditions,
                        type: data.type || existingRule.conditions?.type,
                        severity: data.severity || existingRule.conditions?.severity,
                        timeWindow: data.timeWindow || existingRule.conditions?.timeWindow,
                        threshold: data.threshold || existingRule.conditions?.threshold,
                        groupBy: data.groupBy || existingRule.conditions?.groupBy,
                    },
                    actions: existingRule.actions,
                    isActive: data.enabled !== undefined ? data.enabled : existingRule.isActive,
                },
            });
            return this.mapAggregationRuleToResponse(updatedRule);
        }
        catch (error) {
            if (error instanceof AppError_1.AppError) {
                throw error;
            }
            throw new AppError_1.AppError({
                message: 'Failed to update aggregation rule',
                type: AppError_1.ErrorType.INTERNAL,
                code: AppError_1.ErrorCode.INTERNAL_SERVER_ERROR,
                details: { originalError: error instanceof Error ? error.message : error },
            });
        }
    }
    /**
     * Delete an aggregation rule
     */
    async deleteAggregationRule(id) {
        try {
            // Check if rule exists
            const existingRule = await this.prisma.alertAggregationRule.findUnique({
                where: { id },
            });
            if (!existingRule) {
                throw new AppError_1.AppError({
                    message: 'Aggregation rule not found',
                    type: AppError_1.ErrorType.NOT_FOUND,
                    code: AppError_1.ErrorCode.RESOURCE_NOT_FOUND,
                });
            }
            await this.prisma.alertAggregationRule.delete({
                where: { id },
            });
        }
        catch (error) {
            if (error instanceof AppError_1.AppError) {
                throw error;
            }
            throw new AppError_1.AppError({
                message: 'Failed to delete aggregation rule',
                type: AppError_1.ErrorType.INTERNAL,
                code: AppError_1.ErrorCode.INTERNAL_SERVER_ERROR,
                details: { originalError: error instanceof Error ? error.message : error },
            });
        }
    }
    /**
     * Get all correlation rules with optional filtering and pagination
     */
    async getCorrelationRules(filters, pagination) {
        try {
            const where = {};
            // Apply filters
            if (filters?.enabled !== undefined) {
                where.enabled = filters.enabled;
            }
            if (filters?.search) {
                where.OR = [
                    { name: { contains: filters.search, mode: 'insensitive' } },
                    { description: { contains: filters.search, mode: 'insensitive' } },
                ];
            }
            // Build query options
            const queryOptions = {
                where,
                orderBy: { createdAt: 'desc' },
            };
            // Apply pagination
            if (pagination) {
                const { page = 1, limit = 10, sortBy, sortOrder } = pagination;
                queryOptions.skip = (page - 1) * limit;
                queryOptions.take = limit;
                if (sortBy) {
                    queryOptions.orderBy = { [sortBy]: sortOrder || 'desc' };
                }
            }
            // Execute queries
            const [rules, total] = await Promise.all([
                this.prisma.alertCorrelationRule.findMany(queryOptions),
                this.prisma.alertCorrelationRule.count({ where }),
            ]);
            return {
                rules: rules.map(this.mapCorrelationRuleToResponse),
                total,
            };
        }
        catch (error) {
            throw new AppError_1.AppError({
                message: 'Failed to get correlation rules',
                type: AppError_1.ErrorType.INTERNAL,
                code: AppError_1.ErrorCode.INTERNAL_SERVER_ERROR,
                details: { originalError: error instanceof Error ? error.message : error },
            });
        }
    }
    /**
     * Get a specific correlation rule by ID
     */
    async getCorrelationRule(id) {
        try {
            const rule = await this.prisma.alertCorrelationRule.findUnique({
                where: { id },
            });
            if (!rule) {
                throw new AppError_1.AppError({
                    message: 'Correlation rule not found',
                    type: AppError_1.ErrorType.NOT_FOUND,
                    code: AppError_1.ErrorCode.RESOURCE_NOT_FOUND,
                });
            }
            return this.mapCorrelationRuleToResponse(rule);
        }
        catch (error) {
            if (error instanceof AppError_1.AppError) {
                throw error;
            }
            throw new AppError_1.AppError({
                message: 'Failed to get correlation rule',
                type: AppError_1.ErrorType.INTERNAL,
                code: AppError_1.ErrorCode.INTERNAL_SERVER_ERROR,
                details: { originalError: error instanceof Error ? error.message : error },
            });
        }
    }
    /**
     * Map aggregation rule to response format
     */
    mapAggregationRuleToResponse(rule) {
        return {
            id: rule.id,
            name: rule.name,
            description: rule.description,
            type: rule.type,
            severity: rule.severity,
            timeWindow: rule.timeWindow,
            threshold: rule.threshold,
            groupBy: rule.groupBy,
            enabled: rule.enabled,
            createdAt: rule.createdAt,
            updatedAt: rule.updatedAt,
        };
    }
    /**
     * Map correlation rule to response format
     */
    mapCorrelationRuleToResponse(rule) {
        return {
            id: rule.id,
            name: rule.name,
            description: rule.description,
            conditions: rule.conditions,
            timeWindow: rule.timeWindow,
            enabled: rule.enabled,
            createdAt: rule.createdAt,
            updatedAt: rule.updatedAt,
        };
    }
}
exports.AlertAggregationBusinessService = AlertAggregationBusinessService;
//# sourceMappingURL=AlertAggregationBusinessService.js.map