"use strict";
// jscpd:ignore-file
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const system_service_1 = __importDefault(require("../services/system.service"));
class SystemController {
    async getAllSettings(req, res) {
        try {
            const settings = await system_service_1.default.getAllSettings();
            return res.status(200).json({
                status: "success",
                data: settings
            });
        }
        catch (error) {
            return res.status(500).json({
                status: "error",
                message: error.message || "Failed to retrieve system settings"
            });
        }
    }
    async getSettingByKey(req, res) {
        try {
            const { key } = req.params;
            const setting = await system_service_1.default.getSettingByKey(key);
            if (!setting) {
                return res.status(404).json({
                    status: "error",
                    message: "Setting not found"
                });
            }
            return res.status(200).json({
                status: "success",
                data: setting
            });
        }
        catch (error) {
            return res.status(500).json({
                status: "error",
                message: error.message || "Failed to retrieve setting"
            });
        }
    }
    async updateSetting(req, res) {
        try {
            const { key } = req.params;
            const { value } = req.body;
            if (!value) {
                return res.status(400).json({
                    status: "error",
                    message: "Value is required"
                });
            }
            // Ensure user is authenticated
            if (!req.user) {
                return res.status(401).json({
                    status: "error",
                    message: "Authentication required"
                });
            }
            const updatedSetting = await system_service_1.default.updateSetting(key, value, req.user.id); // Fixed: using id instead of userId);
            if (!updatedSetting) {
                return res.status(404).json({
                    status: "error",
                    message: "Setting not found"
                });
            }
            return res.status(200).json({
                status: "success",
                data: updatedSetting
            });
        }
        catch (error) {
            return res.status(500).json({
                status: "error",
                message: error.message || "Failed to update setting"
            });
        }
    }
    async createSetting(req, res) {
        try {
            const { key, value, description } = req.body;
            // Basic validation
            if (!key || !value) {
                return res.status(400).json({
                    status: "error",
                    message: "Key and value are required"
                });
            }
            // Ensure user is authenticated
            if (!req.user) {
                return res.status(401).json({
                    status: "error",
                    message: "Authentication required"
                });
            }
            // Check if setting already exists
            const existingSetting = await system_service_1.default.getSettingByKey(key);
            if (existingSetting) {
                return res.status(400).json({
                    status: "error",
                    message: "Setting with this key already exists"
                });
            }
            const newSetting = await system_service_1.default.createSetting(key, value, description || "", req.user.id // Fixed: using id instead of userId
            );
            return res.status(201).json({
                status: "success",
                data: newSetting
            });
        }
        catch (error) {
            return res.status(500).json({
                status: "error",
                message: error.message || "Failed to create setting"
            });
        }
    }
}
exports.default = new SystemController();
//# sourceMappingURL=system.controller.js.map