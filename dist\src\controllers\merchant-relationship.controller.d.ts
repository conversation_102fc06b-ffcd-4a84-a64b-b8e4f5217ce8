/**
 * Merchant Relationship Controller
 *
 * This controller handles API requests related to merchant relationship management.
 */
import { Request, Response } from "express";
import { BaseController } from "./base.controller";
declare global {
    namespace Express {
        interface Request {
            user?: {
                id: string;
                role: string;
                merchantId?: string;
            };
        }
    }
}
/**
 * Merchant relationship controller
 */
export declare class MerchantRelationshipController extends BaseController {
    private merchantRelationshipService;
    constructor();
    /**
   * Send communication to merchant
   */
    sendCommunication: (req: Request, res: Response, next: import("express").NextFunction) => void;
    /**
   * Get merchant communications
   */
    getMerchantCommunications: (req: Request, res: Response, next: import("express").NextFunction) => void;
    /**
   * Mark communication as read
   */
    markCommunicationAsRead: (req: Request, res: Response, next: import("express").NextFunction) => void;
    /**
   * Create support ticket
   */
    createSupportTicket: (req: Request, res: Response, next: import("express").NextFunction) => void;
    /**
   * Get merchant support tickets
   */
    getMerchantSupportTickets: (req: Request, res: Response, next: import("express").NextFunction) => void;
    /**
   * Add message to support ticket
   */
    addMessageToSupportTicket: (req: Request, res: Response, next: import("express").NextFunction) => void;
    /**
   * Update support ticket status
   */
    updateSupportTicketStatus: (req: Request, res: Response, next: import("express").NextFunction) => void;
    /**
   * Initialize merchant onboarding
   */
    initializeOnboarding: (req: Request, res: Response, next: import("express").NextFunction) => void;
    /**
   * Get merchant onboarding
   */
    getMerchantOnboarding: (req: Request, res: Response, next: import("express").NextFunction) => void;
    /**
   * Update onboarding step status
   */
    updateOnboardingStepStatus: (req: Request, res: Response, next: import("express").NextFunction) => void;
}
//# sourceMappingURL=merchant-relationship.controller.d.ts.map