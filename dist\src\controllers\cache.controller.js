"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const analytics_cache_service_1 = __importDefault(require("../services/cache/analytics-cache.service"));
const logger_1 = require("../utils/logger");
/**
 * Cache controller
 */
const cacheController = {
    /**
   * Get cache status
   */
    getStatus: async (req, res) => {
        try {
            const enabled = analytics_cache_service_1.default.isEnabled();
            return res.status(200).json({ enabled });
        }
        catch (error) {
            logger_1.logger.error("Error getting cache status:", error);
            return res.status(500).json({ error: "Failed to get cache status" });
        }
    },
    /**
   * Enable cache
   */
    enable: async (req, res) => {
        try {
            analytics_cache_service_1.default.setEnabled(true);
            return res.status(200).json({ success: true, message: "Cache enabled" });
        }
        catch (error) {
            logger_1.logger.error("Error enabling cache:", error);
            return res.status(500).json({ error: "Failed to enable cache" });
        }
    },
    /**
   * Disable cache
   */
    disable: async (req, res) => {
        try {
            analytics_cache_service_1.default.setEnabled(false);
            return res.status(200).json({ success: true, message: "Cache disabled" });
        }
        catch (error) {
            logger_1.logger.error("Error disabling cache:", error);
            return res.status(500).json({ error: "Failed to disable cache" });
        }
    },
    /**
   * Clear cache
   */
    clear: async (req, res) => {
        try {
            analytics_cache_service_1.default.clear();
            return res.status(200).json({ success: true, message: "Cache cleared" });
        }
        catch (error) {
            logger_1.logger.error("Error clearing cache:", error);
            return res.status(500).json({ error: "Failed to clear cache" });
        }
    }
};
exports.default = cacheController;
//# sourceMappingURL=cache.controller.js.map