"use strict";
// jscpd:ignore-file
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_validator_1 = require("express-validator");
const verification_service_1 = __importDefault(require("../services/verification.service"));
const blockchain_verification_service_1 = __importDefault(require("../services/blockchain-verification.service"));
const unified_verification_service_1 = require("../services/verification/unified-verification.service");
const logger_1 = require("../utils/logger");
const verification_error_handler_1 = require("../utils/verification-error-handler");
class VerificationController {
    /**
   * Constructor
   */
    constructor() {
        this.unifiedVerificationService = new unified_verification_service_1.UnifiedVerificationService();
    }
    /**
   * Verify a payment using the appropriate verification method
   */
    async verifyPayment(req, res) {
        // Check for validation errors
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ success: false, errors: errors.array() });
        }
        try {
            const { merchantId, amount, currency, paymentMethodId, verificationData } = req.body;
            // Check for blockchain verification
            if (verificationData.verificationMethod === "blockchain") {
                const { network, senderAddress, destinationAddress, txHash } = verificationData;
                // Validate required fields
                if (!network || !senderAddress) {
                    return res.status(400).json({
                        success: false,
                        message: "Missing required blockchain verification parameters"
                    });
                }
                // Perform blockchain verification
                const result = await blockchain_verification_service_1.default.verifyTransaction({
                    network,
                    senderAddress,
                    destinationAddress: destinationAddress || "",
                    amount,
                    currency,
                    txHash
                });
                return res.json({
                    verified: result.success,
                    status: result.success ? "success" : "pending",
                    txHash: result.txHash,
                    confirmations: result.confirmations,
                    message: result.message
                });
            }
            // Check for Binance C2C verification
            if (verificationData.verificationMethod === "binance_c2c") {
                const { note } = verificationData;
                // Validate required fields
                if (!note) {
                    return res.status(400).json({
                        success: false,
                        message: "Missing required Binance C2C verification parameters"
                    });
                }
                // Verify the note format (typically a UUID or specific format)
                const isValidNote = /^[a-zA-Z0-9-_]{6,36}$/.test(note);
                if (!isValidNote) {
                    return res.status(400).json({
                        success: false,
                        message: "Invalid note format"
                    });
                }
                // For now, pass to the standard verification service
                // In a real implementation, you would call the Binance API
                const verificationResult = await verification_service_1.default.verifyPayment({
                    merchantId,
                    amount,
                    currency,
                    paymentMethodId,
                    verificationData
                });
                return res.json({
                    verified: verificationResult.success,
                    status: verificationResult.success ? "success" : "pending",
                    message: verificationResult.message,
                    details: verificationResult.details
                });
            }
            // Check for Binance TRC20 Direct verification
            if (verificationData.verificationMethod === "binance_trc20") {
                const { txId } = verificationData;
                // Validate required fields
                if (!txId) {
                    return res.status(400).json({
                        success: false,
                        message: "Missing required Binance TRC20 verification parameters"
                    });
                }
                // Get the payment method to access the API credentials
                const paymentMethod = await verification_service_1.default.getPaymentMethod(paymentMethodId);
                if (!paymentMethod) {
                    return res.status(400).json({
                        success: false,
                        message: "Payment method not found"
                    });
                }
                // Verify that this is a Binance TRC20 payment method
                if (paymentMethod.type !== "binance_trc20") {
                    return res.status(400).json({
                        success: false,
                        message: "Invalid payment method type for Binance TRC20 verification"
                    });
                }
                // Verify the transaction using the Binance API
                const verificationResult = await verification_service_1.default.verifyBinanceTrc20Payment({
                    merchantId,
                    amount,
                    currency,
                    paymentMethodId,
                    txId,
                    paymentMethod
                });
                return res.json({
                    verified: verificationResult.success,
                    status: verificationResult.success ? "success" : "pending",
                    message: verificationResult.message,
                    details: verificationResult.details,
                    transactionId: verificationResult.transactionId
                });
            }
            // For other verification methods, use the standard verification service
            const verificationResult = await verification_service_1.default.verifyPayment({
                merchantId,
                amount,
                currency,
                paymentMethodId,
                verificationData
            });
            return res.json({
                verified: verificationResult.success,
                status: verificationResult.success ? "success" : "failed",
                message: verificationResult.message,
                details: verificationResult.details
            });
        }
        catch (error) {
            console.error("Verification error:", error);
            return res.status(500).json({
                success: false,
                message: "Internal server error during verification"
            });
        }
    }
    /**
   * Process webhooks from payment providers
   */
    async processWebhook(req, res) {
        try {
            const { provider, payload, signature } = req.body;
            // Validate signature if provided
            if (signature) {
                const isValid = await this.verifyWebhookSignature(provider, payload, signature);
                if (!isValid) {
                    return res.status(401).json({
                        success: false,
                        message: "Invalid webhook signature"
                    });
                }
            }
            // Process based on provider
            switch (provider) {
                case "binance_c2c":
                    return this.processBinanceC2CWebhook(req, res);
                case "blockchain":
                    return this.processBlockchainWebhook(req, res);
                default:
                    // Process generic webhook
                    const result = await verification_service_1.default.processWebhook(payload);
                    return res.json({
                        success: result.success,
                        message: result.message
                    });
            }
        }
        catch (error) {
            console.error("Webhook processing error:", error);
            return res.status(500).json({
                success: false,
                message: "Internal server error processing webhook"
            });
        }
    }
    /**
   * Process Binance C2C webhooks
   */
    async processBinanceC2CWebhook(req, res) {
        try {
            const { payload } = req.body;
            // Validate required fields
            if (!payload.transactionId || !payload.merchantId || !payload.status) {
                return res.status(400).json({
                    success: false,
                    message: "Missing required Binance C2C webhook fields"
                });
            }
            // Process the webhook
            const result = await verification_service_1.default.processBinanceC2CWebhook(payload);
            return res.json({
                success: result.success,
                message: result.message
            });
        }
        catch (error) {
            console.error("Binance C2C webhook processing error:", error);
            return res.status(500).json({
                success: false,
                message: "Internal server error processing Binance C2C webhook"
            });
        }
    }
    /**
   * Process blockchain webhooks
   */
    async processBlockchainWebhook(req, res) {
        try {
            const { payload } = req.body;
            // Validate required fields
            if (!payload.transactionId || !payload.merchantId || !payload.blockchain?.network || !payload.blockchain?.txHash) {
                return res.status(400).json({
                    success: false,
                    message: "Missing required blockchain webhook fields"
                });
            }
            // Process the webhook
            const result = await blockchain_verification_service_1.default.processBlockchainWebhook(payload);
            return res.json({
                success: result.success,
                message: result.message,
                confirmations: result.confirmations
            });
        }
        catch (error) {
            console.error("Blockchain webhook processing error:", error);
            return res.status(500).json({
                success: false,
                message: "Internal server error processing blockchain webhook"
            });
        }
    }
    /**
   * Verify webhook signature
   */
    async verifyWebhookSignature(provider, payload, signature) {
        try {
            // Delegate to the appropriate service based on provider
            switch (provider) {
                case "binance_c2c":
                    // In a real implementation, you would verify using Binance's signature mechanism
                    return true;
                case "blockchain":
                    // For blockchain webhooks, verify based on the network
                    if (payload.blockchain?.network) {
                        return blockchain_verification_service_1.default.verifyWebhookSignature(payload.blockchain.network, payload, signature);
                    }
                    return false;
                default:
                    // Generic signature verification
                    return verification_service_1.default.verifyWebhookSignature(payload, signature);
            }
        }
        catch (error) {
            console.error("Signature verification error:", error);
            return false;
        }
    }
    /**
   * Verify a payment by ID using the unified verification service
   * @param req Request
   * @param res Response
   */
    async verifyPaymentById(req, res) {
        try {
            const { paymentId } = req.params;
            // Validate payment ID
            if (!paymentId) {
                return res.status(400).json({
                    success: false,
                    message: "Payment ID is required"
                });
            }
            // Log verification request
            logger_1.logger.info("Payment verification by ID requested", { paymentId });
            // Verify payment using the unified verification service
            const result = await this.unifiedVerificationService.verifyPayment(paymentId);
            // Return response based on verification status
            switch (result.status) {
                case unified_verification_service_1.PaymentVerificationStatus.VERIFIED:
                    return res.json({
                        success: true,
                        status: "verified",
                        paymentId,
                        transactionId: result.transactionId,
                        paymentMethod: result.paymentMethod,
                        verifiedAt: result.verifiedAt
                    });
                case unified_verification_service_1.PaymentVerificationStatus.PENDING:
                    return res.json({
                        success: true,
                        status: "pending",
                        paymentId,
                        transactionId: result.transactionId,
                        paymentMethod: result.paymentMethod,
                        message: "Payment verification is in progress"
                    });
                case unified_verification_service_1.PaymentVerificationStatus.EXPIRED:
                    return res.json({
                        success: false,
                        status: "expired",
                        paymentId,
                        message: "Payment has expired"
                    });
                case unified_verification_service_1.PaymentVerificationStatus.FAILED:
                    return res.status(400).json({
                        success: false,
                        status: "failed",
                        paymentId,
                        message: result.errorMessage || "Payment verification failed",
                        errorCode: result.errorCode || verification_error_handler_1.VerificationErrorType.UNKNOWN_ERROR,
                        details: result.rawData
                    });
                default:
                    return res.status(500).json({
                        success: false,
                        status: "unknown",
                        paymentId,
                        message: "Unknown verification status"
                    });
            }
        }
        catch (error) {
            // Log error
            logger_1.logger.error("Error in payment verification by ID controller", {
                error: error.message || error,
                path: req.path
            });
            // Return error response
            return res.status(500).json({
                success: false,
                message: error.message || "An error occurred during payment verification"
            });
        }
    }
}
exports.default = new VerificationController();
//# sourceMappingURL=verification.controller.js.map