"use strict";
// jscpd:ignore-file
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const auth_service_1 = __importDefault(require("../services/auth.service"));
const logger_1 = require("../utils/logger");
const rbac_service_1 = require("../services/rbac.service");
const prisma_1 = __importDefault(require("../lib/prisma"));
class AuthController {
    constructor() {
        this.rbacService = new rbac_service_1.RBACService();
    }
    async login(req, res) {
        try {
            const { email, password, twoFactorToken } = req.body;
            if (!email || !password) {
                return res.status(400).json({
                    status: 'error',
                    message: 'Email and password are required',
                });
            }
            // First step: Validate credentials
            const result = await auth_service_1.default.login(email, password);
            // Check if 2FA is enabled for the user
            if (result.user.twoFactorEnabled) {
                // If 2FA token is not provided, return a response indicating 2FA is required
                if (!twoFactorToken) {
                    return res.status(200).json({
                        status: 'success',
                        requiresTwoFactor: true,
                        userId: result.user.id,
                        // Don't include the token in the response when 2FA is required
                        data: {
                            user: {
                                id: result.user.id,
                                email: result.user.email,
                                role: result.user.role,
                            },
                        },
                    });
                }
                // Verify 2FA token
                const twoFactorVerification = await auth_service_1.default.verifyTwoFactorToken(result.user.id, twoFactorToken);
                if (!twoFactorVerification.success) {
                    return res.status(401).json({
                        status: 'error',
                        message: 'Invalid two-factor authentication code',
                    });
                }
            }
            // If 2FA is not enabled or token is valid, return the full login response
            return res.status(200).json({
                status: 'success',
                data: result,
            });
        }
        catch (error) {
            logger_1.logger.error('Login error:', error);
            return res.status(401).json({
                status: 'error',
                message: error.message || 'Authentication failed',
            });
        }
    }
    async logout(req, res) {
        try {
            if (!req.user) {
                return res.status(401).json({
                    status: 'error',
                    message: 'Not authenticated',
                });
            }
            // Invalidate the token
            await auth_service_1.default.logout(req.user.id); // Fixed: using id instead of userId
            return res.status(200).json({
                status: 'success',
                message: 'Logged out successfully',
            });
        }
        catch (error) {
            logger_1.logger.error('Logout error:', error);
            return res.status(500).json({
                status: 'error',
                message: error.message || 'Failed to logout',
            });
        }
    }
    async getCurrentUser(req, res) {
        try {
            if (!req.user) {
                return res.status(401).json({
                    status: 'error',
                    message: 'Not authenticated',
                });
            }
            const user = await auth_service_1.default.getUserById(req.user.id); // Fixed: using id instead of userId
            return res.status(200).json({
                status: 'success',
                data: user,
            });
        }
        catch (error) {
            return res.status(500).json({
                status: 'error',
                message: error.message || 'Failed to get current user',
            });
        }
    }
    async getTwoFactorStatus(req, res) {
        try {
            if (!req.user) {
                return res.status(401).json({
                    status: 'error',
                    message: 'Not authenticated',
                });
            }
            const status = await auth_service_1.default.getTwoFactorStatus(req.user.id); // Fixed: using id instead of userId
            return res.status(200).json({
                status: 'success',
                data: status,
            });
        }
        catch (error) {
            logger_1.logger.error('Get 2FA status error:', error);
            return res.status(500).json({
                status: 'error',
                message: error.message || 'Failed to get two-factor authentication status',
            });
        }
    }
    async setupTwoFactor(req, res) {
        try {
            if (!req.user) {
                return res.status(401).json({
                    status: 'error',
                    message: 'Not authenticated',
                });
            }
            const setupData = await auth_service_1.default.setupTwoFactor(req.user.id); // Fixed: using id instead of userId
            return res.status(200).json({
                status: 'success',
                data: setupData,
            });
        }
        catch (error) {
            logger_1.logger.error('Setup 2FA error:', error);
            return res.status(500).json({
                status: 'error',
                message: error.message || 'Failed to setup two-factor authentication',
            });
        }
    }
    async verifyAndEnableTwoFactor(req, res) {
        try {
            if (!req.user) {
                return res.status(401).json({
                    status: 'error',
                    message: 'Not authenticated',
                });
            }
            const { code, secret } = req.body;
            if (!code || !secret) {
                return res.status(400).json({
                    status: 'error',
                    message: 'Code and secret are required',
                });
            }
            const result = await auth_service_1.default.verifyAndEnableTwoFactor(req.user.id, code, secret); // Fixed: using id instead of userId
            return res.status(200).json({
                status: 'success',
                data: result,
            });
        }
        catch (error) {
            logger_1.logger.error('Verify and enable 2FA error:', error);
            return res.status(500).json({
                status: 'error',
                message: error.message || 'Failed to verify and enable two-factor authentication',
            });
        }
    }
    async recoverWithBackupCode(req, res) {
        try {
            if (!req.user) {
                return res.status(401).json({
                    status: 'error',
                    message: 'Not authenticated',
                });
            }
            const { backupCode } = req.body;
            if (!backupCode) {
                return res.status(400).json({
                    status: 'error',
                    message: 'Backup code is required',
                });
            }
            const result = await auth_service_1.default.recoverWithBackupCode(req.user.id, backupCode); // Fixed: using id instead of userId
            return res.status(200).json({
                status: 'success',
                data: result,
            });
        }
        catch (error) {
            logger_1.logger.error('Recover with backup code error:', error);
            return res.status(500).json({
                status: 'error',
                message: error.message || 'Failed to recover with backup code',
            });
        }
    }
    async registerMerchant(req, res) {
        try {
            const { name, email, password, businessName, contactPhone, merchantLocation, country, governorate, } = req.body;
            // Validate required fields
            if (!name ||
                !email ||
                !password ||
                !contactPhone ||
                !merchantLocation ||
                !country ||
                !governorate) {
                return res.status(400).json({
                    status: 'error',
                    message: 'Missing required fields',
                });
            }
            logger_1.logger.info(`Registering merchant with email: ${email}`);
            const result = await auth_service_1.default.registerMerchant({
                name,
                email,
                password,
                businessName,
                contactPhone,
                merchantLocation,
                country,
                governorate,
            });
            return res.status(201).json({
                status: 'success',
                data: result,
            });
        }
        catch (error) {
            logger_1.logger.error(`Merchant registration error: ${error.message}`);
            return res.status(400).json({
                status: 'error',
                message: error.message || 'Registration failed',
            });
        }
    }
    /**
     * Get user permissions
     * @param req Request
     * @param res Response
     */
    async getUserPermissions(req, res) {
        try {
            if (!req.user) {
                return res.status(401).json({
                    status: 'error',
                    message: 'Not authenticated',
                });
            }
            // Get user permissions
            const permissions = await this.rbacService.getUserPermissions(req.user.id); // Fixed: using id instead of userId
            // Get user roles
            const user = await prisma_1.default.user.findUnique({
                where: { id: req.user.id }, // Fixed: using id instead of userId
                include: {
                    roles: {
                        select: { type: true },
                    },
                },
            });
            return res.status(200).json({
                status: 'success',
                data: {
                    permissions,
                    roles: user?.roles.map((role) => role.type) || [],
                },
            });
        }
        catch (error) {
            logger_1.logger.error('Get user permissions error:', error);
            return res.status(500).json({
                status: 'error',
                message: error.message || 'Failed to get user permissions',
            });
        }
    }
}
exports.default = new AuthController();
//# sourceMappingURL=auth.controller.js.map